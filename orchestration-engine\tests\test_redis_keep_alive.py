"""
Test script to verify the Redis keep-alive functionality in the RedisEventListener.

This script:
1. Creates a RedisEventListener instance
2. Starts the listener
3. Waits for a period of time to observe keep-alive behavior
4. Stops the listener

Usage:
    poetry run python test_redis_keep_alive.py
"""

import time
import logging
import sys
import uuid
from app.services.db_connections.redis_event_listener import RedisEventListener
from app.core_.state_manager import WorkflowStateManager
from app.services.db_connections.redis_connections import RedisManager
from app.services.db_connections.postgres_connections import get_postgres_manager
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Set to DEBUG for more detailed logs
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("RedisKeepAliveTest")

def main():
    """
    Main test function.
    """
    logger.info("Starting Redis keep-alive test")
    
    # Generate a unique workflow ID for this test
    workflow_id = f"test-keep-alive-{uuid.uuid4()}"
    logger.info(f"Using test workflow ID: {workflow_id}")
    
    # Initialize Redis managers
    results_redis_manager = RedisManager(
        db_index=int(settings.redis_results_db_index)
    )
    state_redis_manager = RedisManager(
        db_index=int(settings.redis_state_db_index)
    )
    
    # Initialize PostgreSQL manager
    postgres_manager = get_postgres_manager()
    
    # Initialize workflow state manager
    state_manager = WorkflowStateManager(workflow_id=workflow_id)
    state_manager.results_redis_manager = results_redis_manager
    state_manager.state_redis_manager = state_redis_manager
    state_manager.postgres_manager = postgres_manager
    
    # Initialize a dedicated Redis event listener for this test
    logger.info("Creating RedisEventListener with keep-alive functionality")
    redis_event_listener = RedisEventListener(state_manager)
    
    # Adjust keep-alive interval for testing (10 seconds instead of default 60)
    redis_event_listener.keep_alive_interval = 10
    logger.info(f"Set keep-alive interval to {redis_event_listener.keep_alive_interval} seconds")
    
    # Start the Redis event listener
    logger.info("Starting Redis event listener...")
    redis_event_listener.start()
    
    # Wait for the Redis event listener to initialize
    logger.info("Waiting for Redis event listener to initialize...")
    time.sleep(5)
    
    # Store test data in Redis
    logger.info("Storing test data in Redis...")
    workflow_state = {
        "pending_transitions": ["transition1", "transition2"],
        "completed_transitions": [],
        "waiting_transitions": [],
        "terminated": False,
        "paused": False
    }
    
    # Store workflow state with a short TTL
    state_key = f"workflow_state:{workflow_id}"
    state_redis_manager.set_value(state_key, str(workflow_state), ttl=30)
    logger.info(f"Stored workflow state in Redis with key '{state_key}' and TTL of 30 seconds")
    
    # Wait for keep-alive events to occur
    test_duration = 120  # 2 minutes
    logger.info(f"Test running for {test_duration} seconds to observe keep-alive behavior...")
    
    # Monitor for the specified duration
    start_time = time.time()
    while time.time() - start_time < test_duration:
        # Print a status message every 10 seconds
        if int(time.time() - start_time) % 10 == 0:
            elapsed = int(time.time() - start_time)
            logger.info(f"Test running for {elapsed}/{test_duration} seconds...")
        time.sleep(1)
    
    # Stop the Redis event listener
    logger.info("Stopping Redis event listener...")
    redis_event_listener.stop()
    
    logger.info("Test completed successfully")

if __name__ == "__main__":
    main()
