/**
 * Credentials API Route Handler
 * 
 * This file provides API routes for credential operations, acting as a server-side
 * proxy between the client and the external credential API.
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getAccessToken } from '@/lib/cookies';
import { 
  fetchCredentials, 
  createCredential, 
  deleteCredential,
  Credential,
  CredentialCreate
} from '@/lib/api';

/**
 * GET handler for fetching credentials
 */
export async function GET(request: NextRequest) {
  try {
    // Get access token from cookies
    const accessToken = await getAccessToken();
    
    // Fetch credentials
    const credentials = await fetchCredentials();
    return NextResponse.json(credentials);
  } catch (error: any) {
    console.error('Error fetching credentials:', error);
    return NextResponse.json(
      { 
        error: error.message || 'Failed to fetch credentials',
        details: error.response?.data || null
      }, 
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * POST handler for creating a new credential
 */
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.name || !data.type || !data.value) {
      return NextResponse.json(
        { error: 'Missing required fields: name, type, and value are required' },
        { status: 400 }
      );
    }
    
    // Create credential
    const credential: CredentialCreate = {
      name: data.name,
      type: data.type,
      value: data.value
    };
    
    const result = await createCredential(credential);
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error creating credential:', error);
    return NextResponse.json(
      { 
        error: error.message || 'Failed to create credential',
        details: error.response?.data || null
      }, 
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * DELETE handler for deleting a credential
 */
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Missing credential ID' },
        { status: 400 }
      );
    }
    
    const result = await deleteCredential(id);
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error deleting credential:', error);
    return NextResponse.json(
      { 
        error: error.message || 'Failed to delete credential',
        details: error.response?.data || null
      }, 
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
