# api/models.py
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from autogen_core.models import LLMMessage
from autogen_core import AgentType

from datetime import datetime


# Message models used in agents
class UserLogin(BaseModel):
    """
    Represents a user login.
    """

    pass


class UserTask(BaseModel):
    """
    Represents a task assigned to a user.

    Attributes:
        context (List[LLMMessage]): The context of the task, including previous messages.
        registered_agent_types (Optional[Dict[str, AgentType]]): A dictionary of registered agent types.
        task_id (Optional[str]): An optional ID for the task.
    """

    context: List[LLMMessage]
    registered_agent_types: Optional[Dict[str, AgentType]] = (
        None  # Add registered_agent_types to UserTask
    )
    task_id: Optional[str] = None


class AgentResponse(BaseModel):
    """
    Represents a response from an agent.

    Attributes:
        reply_to_topic_type (str): The topic type to reply to.
        context (List[LLMMessage]): The context of the response, including previous messages.
        task_id (str): The ID of the task this response relates to. # Changed to required string
        is_final (bool): Flag indicating if this is the final message for the task. # Added is_final
        final_response (Optional[str]): An optional final response message (legacy, can be removed later).
    """

    reply_to_topic_type: str
    context: List[LLMMessage]
    task_id: Optional[str] = None
    is_final: bool = False  # Default to False
    final_response: Optional[str] = (
        None  # Keep for now, but is_final is primary  # Add task_id to AgentResponse
    )


class TaskRequest(BaseModel):
    """
    Represents a request for a task.

    Attributes:
        message (str): The message associated with the task.
        session_id (Optional[str]): An optional ID for the session.
        user_token (Optional[str]): An optional token for the user.
    """

    message: str
    session_id: Optional[str] = None
    user_token: Optional[str] = (
        None  # Make sure this line is present and spelled correctly
    )


class TaskResponse(BaseModel):
    """
    Represents a response to a task request.

    Attributes:
        session_id (str): The ID of the session.
        final_response (str): The final response message.
    """

    session_id: str
    final_response: str


class ChatRequest(BaseModel):
    """
    Represents a chat request.

    Attributes:
        department (str): The department associated with the chat.
        message (str): The message in the chat.
        conversation (Optional[List[Dict]]): An optional list of previous conversation messages.
    """

    department: str  # one of "HR", "Finance", "Document", or "Sales"
    message: str
    # Optionally include a previous conversation context (list of messages as dict)
    conversation: Optional[List[Dict]] = None


class ChatMessage(BaseModel):
    """
    Represents a message in a chat.

    Attributes:
        role (str): The role of the message sender.
        content (str): The content of the message.
        timestamp (datetime): The timestamp of the message.
    """

    role: str
    content: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ChatHistory:
    """
    Manages the history of a chat.
    """

    def __init__(self):
        """
        Initializes an empty chat history.
        """
        self.messages: List[ChatMessage] = []

    def add_message(self, role: str, content: str) -> None:
        """
        Adds a message to the chat history.

        Args:
            role (str): The role of the message sender.
            content (str): The content of the message.
        """
        self.messages.append(ChatMessage(role=role, content=content))

    def get_history(self) -> List[ChatMessage]:
        """
        Retrieves the chat history.

        Returns:
            List[ChatMessage]: The list of messages in the chat history.
        """
        return self.messages


class ChatHistoryManager:
    """
    Manages multiple chat histories.
    """

    def __init__(self):
        """
        Initializes an empty collection of chat histories.
        """
        self._histories: Dict[str, ChatHistory] = {}

    def get_or_create_history(self, session_id: str) -> ChatHistory:
        """
        Retrieves the chat history for a session, creating it if it doesn't exist.

        Args:
            session_id (str): The ID of the session.

        Returns:
            ChatHistory: The chat history for the session.
        """
        if session_id not in self._histories:
            self._histories[session_id] = ChatHistory()
        return self._histories[session_id]

    def add_message(self, session_id: str, role: str, content: str) -> None:
        """
        Adds a message to the chat history for a session.

        Args:
            session_id (str): The ID of the session.
            role (str): The role of the message sender.
            content (str): The content of the message.
        """
        history = self.get_or_create_history(session_id)
        history.add_message(role=role, content=content)

    def get_history(self, session_id: str) -> Optional[List[ChatMessage]]:
        """
        Retrieves the chat history for a session.

        Args:
            session_id (str): The ID of the session.

        Returns:
            Optional[List[ChatMessage]]: The list of messages in the chat history, or None if the session doesn't exist.
        """
        history = self._histories.get(session_id)
        return history.get_history() if history else None

    def clear_history(self, session_id: str) -> None:
        """
        Clears the chat history for a session.

        Args:
            session_id (str): The ID of the session.
        """
        if session_id in self._histories:
            del self._histories[session_id]


class RegisterAgentRequest(BaseModel):
    """
    Represents a request to register an agent.

    Attributes:
        user_token (str): The token of the user for whom to register the agent.
    """

    user_token: str
