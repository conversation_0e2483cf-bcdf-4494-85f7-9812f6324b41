syntax = "proto3";

package agent_graph;

// Enums for Agent and Department properties
enum Visibility {
  PRIVATE = 0;
  PUBLIC = 1;
}

enum Status {
  ACTIVE = 0;
  INACTIVE = 1;
  BENCH = 2;  // New value
}

enum CreatorRole {
  MEMBER = 0;
  CREATOR = 1;
  VIEWER = 2;
}

// The AgentGraph service definition
// This service focuses on graph-specific operations for agents
service AgentGraphService {
  // Create a new agent with relationships to owner, department, and users
  rpc CreateAgent (CreateAgentRequest) returns (CreateAgentResponse) {}
  
  // Get agent details by ID
  rpc GetAgent (GetAgentRequest) returns (AgentResponse) {}
  
  // Get all agents that a user has access to
  rpc ListAgentsByUserId (ListAgentsByUserIdRequest) returns (ListAgentsResponse) {}
  
  // Get all agents from all departments of an organization
  rpc GetAllAgentsFromOrganisation (GetAllAgentsFromOrganisationRequest) returns (ListAgentsResponse) {}
  
  // Get all agents from a specific department
  rpc GetAgentsFromDepartment (GetAgentsFromDepartmentRequest) returns (ListAgentsResponse) {}
  
  // Check if a user has access to a specific agent
  rpc CheckUserAgentAccess (CheckUserAgentAccessRequest) returns (CheckAccessResponse) {}
}

// Request message for getting all agents from an organization
message GetAllAgentsFromOrganisationRequest {
  string organisation_id = 1;
}

// Request message for getting all agents from a department
message GetAgentsFromDepartmentRequest {
  string department_id = 1;
}

// Request message for checking if a user has access to an agent
message CheckUserAgentAccessRequest {
  string user_id = 1;
  string agent_id = 2;
}

// Response message for checking access
message CheckAccessResponse {
  bool success = 1;
  bool has_access = 2;
}

// Message for owner information
message Owner {
  string id = 1;
}

// Request message for creating an agent
message CreateAgentRequest {
  string name = 1;
  string description = 2;
  string department = 3;
  Owner owner = 4;
  repeated string user_ids = 5;
  optional string id = 6;
  Visibility visibility = 7;
  Status status = 8;
  CreatorRole creator_role = 9;
}

// Response message for creating an agent
message CreateAgentResponse {
  bool success = 1;
  string message = 2;
}

// Request message for getting an agent by ID
message GetAgentRequest {
  string id = 1;
}

// Response message containing an agent
message AgentResponse {
  bool success = 1;
  string message = 2;
  Agent agent = 3;
}

// Request message for listing agents by user ID
message ListAgentsByUserIdRequest {
  string owner_id = 1;
  int32 page = 2;
  int32 page_size = 3;
}

// Agent entity model
message Agent {
  string id = 1;
  string name = 2;
  string description = 3;
  string department = 4;
  string owner_id = 5;
  repeated string user_ids = 6;
  string created_at = 7;
  string updated_at = 8;
  Visibility visibility = 9;
  Status status = 10;
  CreatorRole creator_role = 11;
}

// Response containing a list of agents
message ListAgentsResponse {
  bool success = 1;
  repeated Agent agents = 2;
  int32 total = 3;
  int32 page = 4;
  int32 total_pages = 5;
}