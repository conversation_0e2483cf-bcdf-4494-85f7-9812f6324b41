import grpc
from typing import List, Optional
from app.core.config import settings
from app.grpc_ import organisation_pb2, organisation_pb2_grpc
import requests
from typing import List, Optional
from app.services.user_service import UserServiceClient


class OrganisationServiceClient:

    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.ORGANISATION_SERVICE_HOST}:{settings.ORGANISATION_SERVICE_PORT}"
        )

        self.stub = organisation_pb2_grpc.OrganisationServiceStub(self.channel)
    
    # Organization related services
    async def create_organization(self, name: str, website_url: Optional[str] = None, industry: Optional[str] = None, created_by: str = None):
        """
        Create a new organization in the system.
        
        Args:
            name: The name of the organization (required)
            website_url: The organization's website URL (optional)
            industry: The industry sector of the organization (optional)
            created_by: User ID of the creator (optional, defaults to authenticated user)
            
        Returns:
            The gRPC response from the organization service
            
        Raises:
            GrpcError: If the gRPC call fails
        """

        response = await UserServiceClient().get_user(created_by)

        # Create the request object with the updated field names
        request = organisation_pb2.CreateOrganisationRequest(
            name=name,
            website_url=website_url if website_url else "",
            industry=industry if industry else "",
            created_by=created_by if created_by else "",
            admin_name=response.user.fullName,
            admin_email=response.user.email,
        )
        
        try:
            response = self.stub.CreateOrganisation(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_organization(self, organization_id: str, requester_user_id: str):
        request = organisation_pb2.GetOrganisationRequest(
            id=organization_id
        )
        try:
            response = self.stub.GetOrganisation(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def invite_user(
        self,
        email: str,
        organisation_id: str,
        created_by: str,
        role: Optional[str] = None,
        department: Optional[str] = None,
        permissions: Optional[str] = None
    ):
        """
        Create a new invitation for a user to join an organization.
        
        Args:
            email: Email address of the user to invite
            organisation_id: ID of the organization
            created_by: User ID of the person creating the invite
            role: Role to assign to the user (optional)
            department: Department to assign the user to (optional)
            permissions: List of specific permissions for the user (optional)
            
        Returns:
            The gRPC response from the organization service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object
        request = organisation_pb2.InviteUserRequest(
            email=email,
            organisation_id=organisation_id,
            role=role if role else "",
            department=department if department else "",
            permissions=permissions if permissions else "",
            created_by=created_by
        )

        try:
            response = self.stub.InviteUser(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
        
    async def accept_invite_by_link(self, invite_link: str, auth_user_id: str, user_name: str, auth_user_email: str):
        """
        Accept an invitation using an invite link.
        
        Args:
            invite_link: The encoded invite link
            auth_user_id: User ID from authentication token
            auth_user_email: User email from authentication token
            
        Returns:
            The gRPC response containing the invite details
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object with just the invite link
        # The email will be extracted from auth token and passed to the service
        request = organisation_pb2.AcceptInviteByLinkRequest(
            invite_link=invite_link,
            current_user_email=auth_user_email,
            user_id=auth_user_id,
            user_name=user_name
        )

        try:
            return self.stub.AcceptInviteByLink(request)
        except grpc.RpcError as e:
            raise self._handle_error(e)
        
    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            from fastapi import HTTPException

            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            from fastapi import HTTPException

            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            from fastapi import HTTPException

            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            from fastapi import HTTPException

            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            from fastapi import HTTPException

            raise HTTPException(status_code=412, detail=details)

        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            from fastapi import HTTPException

            raise HTTPException(status_code=400, detail=details)
        else:
            from fastapi import HTTPException

            raise HTTPException(status_code=500, detail="Internal server error")
            
    async def get_user_organisations(self, user_id: str):
        """
        Fetch all organizations that the user belongs to.
        
        Args:
            user_id: ID of the user to get organizations for
            
        Returns:
            UserOrganisationsResponse containing all organizations the user belongs to,
            with information about primary organization and admin status
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.GetUserOrganisationsRequest(
            user_id=user_id
        )
        
        try:
            response = self.stub.GetUserOrganisations(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def create_department(
        self,
        organisation_id: str,
        name: str,
        description: Optional[str] = None,
        parent_department_id: Optional[str] = None,
        created_by: str = None,
        visibility: Optional[str] = None
    ):
        """
        Create a new department within an organization.
        
        Args:
            organisation_id: ID of the organization the department belongs to
            name: Name of the department
            description: Optional description of the department
            parent_department_id: Optional ID of the parent department (for hierarchical departments)
            created_by: User ID of the creator
            visibility: Optional visibility setting ("PUBLIC" or "PRIVATE")
            
        Returns:
            The gRPC response from the organization service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object
        request = organisation_pb2.CreateDepartmentRequest(
            organisation_id=organisation_id,
            name=name,
            description=description if description else "",
            parent_department_id=parent_department_id if parent_department_id else "",
            created_by=created_by if created_by else "",
            visibility=visibility if visibility else ""
        )
        
        try:
            response = self.stub.CreateDepartment(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)