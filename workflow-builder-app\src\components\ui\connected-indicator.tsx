import * as React from "react";
import { cn } from "@/lib/utils";
import { LinkIcon } from "lucide-react";

interface ConnectedIndicatorProps {
  className?: string;
  showText?: boolean;
}

/**
 * A reusable component for displaying a "Connected via handle" indicator
 * Used to show when an input is connected to another node via a handle
 */
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from "./tooltip";

export function ConnectedIndicator({ className, showText = true }: ConnectedIndicatorProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              "pointer-events-none absolute inset-0 flex items-center justify-center",
              className,
            )}
          >
            <span className="text-muted-foreground bg-background/80 border-primary/30 flex items-center gap-1 rounded border px-2 py-1 text-xs">
              <LinkIcon className="text-primary h-3 w-3" />
              {showText && "Connected via handle"}
            </span>
          </div>
        </TooltipTrigger>
        <TooltipContent side="right">
          This input is controlled by another node via handle and cannot be edited directly.
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
