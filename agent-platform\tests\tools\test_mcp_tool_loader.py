import pytest
import asyncio

from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_ext.models.openai import OpenAIChatCompletionClient
from app.tools.mcp_tool_loader import load_mcp_tool_adapters_from_schema

# Example schema for a running MCP server (update sse_url as needed)
EXAMPLE_SCHEMA = {"mcp": {"sse_url": "http://localhost:8931/sse"}}


@pytest.mark.asyncio
async def test_mcp_tool_adapter_integration():
    # Load all tool adapters from the MCP server
    tool_adapters = await load_mcp_tool_adapters_from_schema(EXAMPLE_SCHEMA)
    assert tool_adapters, "No tool adapters found on the MCP server."

    # Create a model client (ensure your OpenAI key/model is configured)
    model_client = OpenAIChatCompletionClient(model="gpt-4.1-nano")

    # Create an agent with the loaded tools
    agent = AssistantAgent(
        name="test_agent",
        model_client=model_client,
        tools=tool_adapters,
        reflect_on_tool_use=True,
    )

    # Run a simple task (update the task as appropriate for your MCP server/tools)
    result = await agent.run(task="Say hello using any available tool.")
    print("Agent result:", result)

    # Optionally, assert on result content
    assert result is not None
