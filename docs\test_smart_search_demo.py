#!/usr/bin/env python3
"""
Comprehensive demonstration of the Smart Field Search enhancement.

This script demonstrates the new Smart Field Search functionality
that allows users to find fields by name anywhere in nested JSON structures
without knowing the exact path.
"""

import asyncio
import sys
import os

# Add the app directory to sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

# Import the components
try:
    from components.processing.select_data import SelectDataComponent
    from models.workflow_builder.context import WorkflowContext
    print("✓ Successfully imported workflow-service components")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


def create_context(input_data, selector, search_mode="Exact Path", data_type="Auto-Detect"):
    """Helper function to create WorkflowContext with given inputs."""
    context = WorkflowContext()
    context.current_node_id = "demo_node"
    context.node_outputs["demo_node"] = {
        "input_data": input_data,
        "selector": selector,
        "search_mode": search_mode,
        "data_type": data_type
    }
    return context


async def demo_smart_search():
    """Demonstrate the Smart Field Search functionality."""
    print("🔍 Smart Field Search Enhancement Demo")
    print("=" * 60)

    # Create a complex nested JSON structure
    complex_data = {
        "user": {
            "profile": {
                "personal": {
                    "email": "<EMAIL>",
                    "name": "John Doe",
                    "age": 30
                },
                "preferences": {
                    "theme": "dark",
                    "notifications": True
                }
            },
            "account": {
                "subscription": {
                    "plan": "premium",
                    "status": "active"
                }
            }
        },
        "company": {
            "details": {
                "name": "Tech Corp",
                "email": "<EMAIL>",  # Duplicate field name
                "address": {
                    "street": "123 Tech Street",
                    "city": "San Francisco",
                    "country": "USA"
                }
            },
            "employees": [
                {
                    "name": "Alice Smith",
                    "email": "<EMAIL>",
                    "department": "Engineering"
                },
                {
                    "name": "Bob Johnson",
                    "email": "<EMAIL>",
                    "department": "Marketing"
                }
            ]
        },
        "metadata": {
            "created": "2024-01-01",
            "version": "1.0",
            "tags": ["production", "verified"]
        }
    }

    component = SelectDataComponent()

    print("\n📊 Sample Data Structure:")
    print("- user.profile.personal.email: '<EMAIL>'")
    print("- company.details.email: '<EMAIL>'")
    print("- company.employees[0].email: '<EMAIL>'")
    print("- company.employees[1].email: '<EMAIL>'")
    print("- user.profile.personal.name: 'John Doe'")
    print("- company.details.name: 'Tech Corp'")

    # Test cases
    test_cases = [
        {
            "description": "🎯 Smart Search: Find 'email' (should return first occurrence)",
            "selector": "email",
            "search_mode": "Smart Search",
            "expected": "<EMAIL>"
        },
        {
            "description": "🎯 Smart Search: Find 'name' (should return first occurrence)",
            "selector": "name",
            "search_mode": "Smart Search",
            "expected": "John Doe"
        },
        {
            "description": "🎯 Smart Search: Find 'plan' (nested in subscription)",
            "selector": "plan",
            "search_mode": "Smart Search",
            "expected": "premium"
        },
        {
            "description": "🎯 Smart Search: Find 'city' (nested in address)",
            "selector": "city",
            "search_mode": "Smart Search",
            "expected": "San Francisco"
        },
        {
            "description": "🎯 Smart Search: Find 'department' (in array)",
            "selector": "department",
            "search_mode": "Smart Search",
            "expected": "Engineering"
        },
        {
            "description": "❌ Smart Search: Field not found",
            "selector": "nonexistent",
            "search_mode": "Smart Search",
            "expected": None
        },
        {
            "description": "📍 Exact Path: Traditional path notation",
            "selector": "user.profile.personal.email",
            "search_mode": "Exact Path",
            "expected": "<EMAIL>"
        },
        {
            "description": "📍 Exact Path: Access nested object",
            "selector": "company.details.name",
            "search_mode": "Exact Path",
            "expected": "Tech Corp"
        }
    ]

    print(f"\n🧪 Running {len(test_cases)} test cases...")
    print("-" * 60)

    passed = 0
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['description']}")
        print(f"   Selector: '{test_case['selector']}'")
        print(f"   Mode: {test_case['search_mode']}")

        # Create context and execute
        context = create_context(
            complex_data,
            test_case['selector'],
            test_case['search_mode']
        )

        result = await component.execute(context)
        output = result.outputs.get("output_data")
        error = result.outputs.get("error")

        # Check result
        if error is None and output == test_case['expected']:
            print(f"   ✅ PASS: Found '{output}'")
            passed += 1
        elif error is None and test_case['expected'] is None and output is None:
            print(f"   ✅ PASS: Correctly returned None (field not found)")
            passed += 1
        else:
            print(f"   ❌ FAIL: Expected '{test_case['expected']}', got '{output}'")
            if error:
                print(f"   Error: {error}")

    print(f"\n📈 Results: {passed}/{len(test_cases)} tests passed")

    if passed == len(test_cases):
        print("🎉 All tests passed! Smart Field Search is working correctly.")
        return True
    else:
        print("❌ Some tests failed.")
        return False


async def demo_use_cases():
    """Demonstrate practical use cases for Smart Field Search."""
    print("\n\n💡 Practical Use Cases")
    print("=" * 60)

    use_cases = [
        {
            "title": "API Response Processing",
            "description": "Extract user email from unknown API response structure",
            "data": {
                "response": {
                    "data": {
                        "user_info": {
                            "contact": {
                                "email": "<EMAIL>"
                            }
                        }
                    }
                }
            },
            "search": "email"
        },
        {
            "title": "Configuration File Processing",
            "description": "Find database connection string in nested config",
            "data": {
                "app": {
                    "database": {
                        "primary": {
                            "connection_string": "postgresql://localhost:5432/mydb"
                        }
                    }
                }
            },
            "search": "connection_string"
        },
        {
            "title": "Log Analysis",
            "description": "Extract error message from complex log structure",
            "data": {
                "log_entry": {
                    "timestamp": "2024-01-01T10:00:00Z",
                    "level": "ERROR",
                    "details": {
                        "exception": {
                            "message": "Database connection failed"
                        }
                    }
                }
            },
            "search": "message"
        }
    ]

    component = SelectDataComponent()

    for i, use_case in enumerate(use_cases, 1):
        print(f"\n{i}. {use_case['title']}")
        print(f"   {use_case['description']}")
        print(f"   Searching for: '{use_case['search']}'")

        context = create_context(use_case['data'], use_case['search'], "Smart Search")
        result = await component.execute(context)
        output = result.outputs.get("output_data")

        if output:
            print(f"   ✅ Found: '{output}'")
        else:
            print(f"   ❌ Not found")


async def main():
    """Run the complete demonstration."""
    print("Smart Field Search Enhancement - Complete Demo")
    print("=" * 80)

    # Run main demo
    success = await demo_smart_search()

    # Run use cases
    await demo_use_cases()

    print("\n" + "=" * 80)
    print("📋 Summary:")
    print("• Smart Search Mode: Finds fields by name anywhere in the structure")
    print("• Exact Path Mode: Uses traditional dot notation for precise access")
    print("• Backward Compatible: All existing functionality preserved")
    print("• First Match Priority: Returns first occurrence when duplicates exist")
    print("• Array Support: Searches within array elements")
    print("• Graceful Fallback: Returns None when field not found")

    if success:
        print("\n🎉 Smart Field Search enhancement is ready for production!")
        return 0
    else:
        print("\n❌ Some issues detected. Please review the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
