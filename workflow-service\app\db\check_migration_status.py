#!/usr/bin/env python
"""
Check migration status script.
Usage: python -m app.db.check_migration_status
"""

import subprocess
import sys


def check_migration_status():
    try:
        # Run alembic current to see current revision
        print("Current migration revision:")
        current_result = subprocess.run(
            ["alembic", "current"], check=True, capture_output=True, text=True
        )
        print(current_result.stdout)

        # Run alembic history to see all revisions
        print("\nMigration history:")
        history_result = subprocess.run(
            ["alembic", "history"], check=True, capture_output=True, text=True
        )
        print(history_result.stdout)

        # Check if there are any pending migrations
        print("\nChecking for pending migrations:")
        check_result = subprocess.run(
            ["alembic", "check"],
            check=False,  # Don't raise exception if there are pending migrations
            capture_output=True,
            text=True,
        )
        if check_result.returncode == 0:
            print("Database is up to date with all migrations.")
        else:
            print("There are pending migrations that need to be applied.")
            print("Run 'python -m app.db.apply_migrations' to apply them.")

    except subprocess.CalledProcessError as e:
        print(f"Error checking migration status: {e}")
        print(f"Error output: {e.stderr}")
        sys.exit(1)


if __name__ == "__main__":
    check_migration_status()
