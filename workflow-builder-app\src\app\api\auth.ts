// src/app/api/auth.ts
import axios from "axios";
import { setAccessToken, setRefreshToken, clearAuthCookies } from "@/utils/authCookies";
import { LoginType, SignupType as RegisterType, ResetPasswordType } from "@/lib/schemas/auth";

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
console.log("the api base url is", API_BASE_URL);

// Auth API endpoints
const AUTH_ENDPOINTS = {
  LOGIN: `${API_BASE_URL}/auth/login`,
  REGISTER: `${API_BASE_URL}/auth/register`,
  LOGOUT: `${API_BASE_URL}/auth/logout`,
  REFRESH_TOKEN: `${API_BASE_URL}/auth/refresh`,
  FORGOT_PASSWORD: `${API_BASE_URL}/auth/forgot-password`,
  RESET_PASSWORD: `${API_BASE_URL}/auth/reset-password`,
  VERIFY_EMAIL: `${API_BASE_URL}/auth/verify-email`,
  VERIFY_EMAIL_OTP: `${API_BASE_URL}/auth/verify-email-otp`,
  UPDATE_PASSWORD: `${API_BASE_URL}/auth/update-password`,
};

// Auth API interface
export const authApi = {
  /**
   * Login user with email and password
   * @param data LoginType containing email and password
   * @returns User data and tokens
   */
  login: async (data: LoginType) => {
    try {
      const response = await axios.post(AUTH_ENDPOINTS.LOGIN, data);

      // Set tokens in cookies
      if (response.data.access_token) {
        setAccessToken(response.data.access_token);
      }

      if (response.data.refresh_token) {
        setRefreshToken(response.data.refresh_token);
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Login failed",
      );
    }
  },

  /**
   * Register a new user
   * @param data RegisterType containing user registration data
   * @returns Success message
   */
  register: async (data: RegisterType) => {
    try {
      const response = await axios.post(AUTH_ENDPOINTS.REGISTER, data);
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Registration failed",
      );
    }
  },

  /**
   * Logout user
   * @returns Success message
   */
  logout: async () => {
    try {
      const response = await axios.post(AUTH_ENDPOINTS.LOGOUT);
      await clearAuthCookies();
      return response.data;
    } catch (error: any) {
      // Clear cookies even if the API call fails
      await clearAuthCookies();
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Logout failed",
      );
    }
  },

  /**
   * Generate a new access token using refresh token
   * @param refreshToken The refresh token
   * @returns New access token
   */
  generateAccessToken: async (refreshToken: string) => {
    try {
      const response = await axios.post(AUTH_ENDPOINTS.REFRESH_TOKEN, {
        refresh_token: refreshToken,
      });

      if (response.data.access_token) {
        setAccessToken(response.data.access_token);
      }

      return {
        success: true,
        access_token: response.data.access_token,
      };
    } catch (error: any) {
      return {
        success: false,
        error:
          error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to refresh token",
      };
    }
  },

  /**
   * Send password reset email
   * @param email User's email address
   * @returns Success message
   */
  forgotPassword: async (email: string) => {
    try {
      const response = await axios.post(AUTH_ENDPOINTS.FORGOT_PASSWORD, { email });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to send reset email",
      );
    }
  },

  /**
   * Reset user's password using token
   * @param token The reset token from the email
   * @param data ResetPasswordType containing new password
   * @returns Success message
   */
  resetPassword: async (token: string, data: ResetPasswordType) => {
    try {
      const response = await axios.post(AUTH_ENDPOINTS.RESET_PASSWORD, {
        token,
        new_password: data.newPassword,
        confirm_password: data.confirmNewPassword,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Password reset failed",
      );
    }
  },

  /**
   * Verify user's email
   * @param token The verification token from the email
   * @returns Success message
   */
  verifyEmail: async (token: string) => {
    try {
      const response = await axios.post(AUTH_ENDPOINTS.VERIFY_EMAIL, { token });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Email verification failed",
      );
    }
  },

  /**
   * Verify user's email with OTP
   * @param token The OTP token from the email
   * @returns Success message
   */
  verifyEmailOtp: async (token: string) => {
    try {
      const response = await axios.post(AUTH_ENDPOINTS.VERIFY_EMAIL_OTP, { token });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Email verification failed",
      );
    }
  },
};
