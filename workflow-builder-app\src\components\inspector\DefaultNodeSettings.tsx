import React from "react";
import { Node } from "reactflow";
import { WorkflowNodeData, InputDefinition } from "@/types";
import { checkInputVisibility } from "@/utils/inputVisibility";
import { FormField } from "./FormField";

interface DefaultNodeSettingsProps {
  node: Node<WorkflowNodeData>;
  onConfigChange: (inputName: string, value: any) => void;
  isInputConnected: (inputName: string) => boolean;
  shouldDisableInput: (inputName: string) => boolean;
  getConnectionInfo: (inputName: string) => {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };
}

/**
 * Component for default node settings
 */
export function DefaultNodeSettings({
  node,
  onConfigChange,
  isInputConnected,
  shouldDisableInput,
  getConnectionInfo,
}: DefaultNodeSettingsProps) {
  return (
    <div className="space-y-4">
      {/* Regular inputs for all components */}
      {node.data.definition?.inputs.map((inputDef) => {
        // Check if this input should be visible based on rules
        const isVisible = checkInputVisibility(
          inputDef,
          node,
          node.data.config || {},
        );
        if (!isVisible) return null;

        return (
          <FormField
            key={inputDef.name}
            inputDef={inputDef}
            node={node}
            onConfigChange={onConfigChange}
            isInputConnected={isInputConnected}
            shouldDisableInput={shouldDisableInput}
            getConnectionInfo={getConnectionInfo}
          />
        );
      })}

      {/* Special handling for dynamic inputs in the config */}
      {node.data.config?.inputs?.filter((input: InputDefinition) => 
        !node.data.definition?.inputs?.some(defInput => defInput.name === input.name)
      ).map((inputDef: InputDefinition) => {
        // Check if this input should be visible based on rules
        const isVisible = checkInputVisibility(
          inputDef,
          node,
          node.data.config || {},
        );
        if (!isVisible) return null;

        return (
          <FormField
            key={inputDef.name}
            inputDef={inputDef}
            node={node}
            onConfigChange={onConfigChange}
            isInputConnected={isInputConnected}
            shouldDisableInput={shouldDisableInput}
            getConnectionInfo={getConnectionInfo}
          />
        );
      })}
    </div>
  );
}
