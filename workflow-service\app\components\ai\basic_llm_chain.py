from typing import Dict, Any, List, ClassVar
import importlib

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, DictInput
from app.models.workflow_builder.components import Output


class BasicLLMChain(BaseAgentComponent):
    """
    Runs a simple LLMChain with a prompt template.

    This component creates and runs a LangChain LLMChain that combines
    a language model with a prompt template.
    """

    name: ClassVar[str] = "BasicLLMChain"
    display_name: ClassVar[str] = "LLM Chain (Basic)"
    description: ClassVar[str] = "Runs a simple LLMChain with a prompt template."

    icon: ClassVar[str] = "Chain"
    is_abstract: ClassVar[bool] = False  # Explicitly set to False to ensure it's not abstract

    # Use the component logger to log class loading

    print("DEBUG: BasicLLMChain class loaded")

    # Define component-specific inputs
    component_inputs: ClassVar[List[InputBase]] = [
        # LLM - connection handle
        HandleInput(
            name="llm_handle",
            display_name="LLM Object",
            required=True,
            is_handle=True,
            info="Connect a language model object from another node.",
        ),
        # Prompt template - connection handle
        HandleInput(
            name="prompt_template_handle",
            display_name="Prompt Template",
            required=True,
            is_handle=True,
            info="Connect a prompt template object from another node.",
        ),
        # Input variables - connection handle
        HandleInput(
            name="input_variables_handle",
            display_name="Input Variables",
            required=True,
            is_handle=True,
            input_types=["dict"],
            info="Connect a dictionary of variables from another node.",
        ),
        # Input variables - direct input in inspector
        DictInput(
            name="input_variables",
            display_name="Input Variables (Direct)",
            required=False,
            is_handle=False,
            value={},
            info="Dictionary of variables to fill the prompt template. Used if no connection is provided.",
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="output_text", display_name="Generated Text", output_type="string"),
        Output(name="full_response", display_name="Full Chain Response", output_type="dict"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def _check_langchain_installed(self) -> bool:
        """
        Checks if the langchain package is installed.

        Returns:
            True if langchain is installed, False otherwise.
        """
        try:
            importlib.import_module("langchain")
            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the BasicLLMChain.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Executes the LLM chain with the provided inputs.

        Args:
            **kwargs: Contains the input values:
                - llm: Language model object
                - prompt_template: Prompt template object
                - input_variables: Dictionary of variables for the prompt

        Returns:
            A dictionary with:
                - output_text: The generated text
                - full_response: The full response from the chain
                - error: An error message if the operation failed
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if langchain is installed
        if not self._check_langchain_installed():
            return {
                "error": "The langchain package is required but not installed. Please install it with 'pip install langchain'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "")

        llm = kwargs.get("llm_handle")
        prompt_template = kwargs.get("prompt_template_handle")
        input_variables_handle = kwargs.get("input_variables_handle")
        input_variables_direct = kwargs.get("input_variables", {})

        # Process inputs - prioritize handle inputs over direct inputs
        input_variables = (
            input_variables_handle if input_variables_handle is not None else input_variables_direct
        )

        # Validate inputs
        if not llm:
            # If no LLM is provided via handle, create one based on model client configuration
            if api_key:
                try:
                    # Import the appropriate LLM based on model_provider
                    if model_provider == "OpenAI" or model_provider == "Custom":
                        from langchain.llms import OpenAI

                        llm_kwargs = {"openai_api_key": api_key}

                        if model_name:
                            llm_kwargs["model_name"] = model_name

                        if model_provider == "Custom" and base_url:
                            llm_kwargs["openai_api_base"] = base_url

                        llm = OpenAI(**llm_kwargs)

                    elif model_provider == "Azure OpenAI":
                        from langchain.llms import AzureOpenAI

                        llm_kwargs = {"openai_api_key": api_key}

                        if model_name:
                            llm_kwargs["deployment_name"] = model_name

                        if base_url:
                            llm_kwargs["openai_api_base"] = base_url

                        llm = AzureOpenAI(**llm_kwargs)

                    elif model_provider == "Anthropic":
                        from langchain.llms import Anthropic

                        llm_kwargs = {"anthropic_api_key": api_key}

                        if model_name:
                            llm_kwargs["model"] = model_name

                        llm = Anthropic(**llm_kwargs)

                    else:
                        return {"error": f"Unsupported model provider: {model_provider}"}
                except Exception as e:
                    return {"error": f"Error creating LLM: {str(e)}"}
            else:
                return {"error": "Either an LLM object or an API key is required."}
        if not prompt_template:
            return {"error": "Prompt template is required."}
        if not input_variables:
            return {"error": "Input variables are required."}

        try:
            # Import langchain modules
            from langchain.chains import LLMChain

            # Create the chain
            chain = LLMChain(llm=llm, prompt=prompt_template)

            # Run the chain
            result = chain(input_variables)

            # Extract the output text
            output_text = result.get("text", "")
            if not output_text and "output" in result:
                output_text = result["output"]

            print(f"  Chain execution completed successfully.")
            return {"output_text": output_text, "full_response": result}

        except Exception as e:
            error_msg = f"Error executing LLM chain: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
