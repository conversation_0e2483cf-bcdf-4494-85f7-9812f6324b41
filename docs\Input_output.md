# Component Input and Output Parameters

This document outlines the required input parameters and expected output formats for all components in the system.

## API Component (ApiRequestNode)

### Input Parameters
- **url** (str, required): The target URL.
- **method** (str, optional): HTTP method (e.g., "GET", "POST"). Defaults to "GET".
- **headers** (dict, optional): Request headers.
- **query_params** (dict, optional): URL query parameters.
- **body** (any, optional): Raw request body (e.g., string, bytes). Used if 'json' is not provided.
- **json** (any, optional): JSON request body. Takes precedence over 'body' if both are present.
- **timeout** (float, optional): Request timeout in seconds. Defaults to settings.api_default_timeout.
- **max_content_length** (int, optional): Max response body size in bytes. Defaults to settings.api_default_max_content_length (0 = no limit).
- **target_component** (str, optional): Metadata field.

### Output Format
```json
{
  "component_type": "ApiRequestNode",
  "request_id": "request_id",
  "status": "success",
  "response": {
    "result": "Response content",
    "status_code": 200,
    "response_headers": {},
    "error": null
  }
}
```

## Combine Text Component (CombineTextComponent)

### Input Parameters
- One of the following is required:
  - **input_data** (str, required if no alternatives): The main input data to combine. Treated as a string, not a list.
  - **input_datta** (str, optional): Alternative field name for input_data (typo in some workflows).
  - **text_inputs** (str, optional): Alternative field name for input_data (used in some workflows).
- **additional_texts** (list, optional): Additional text entries to include. Default is an empty list.
- **additional_data** (str/list, optional): Additional data from another source.
- **separator** (str, optional): The separator to use for joining. Default is " " (space).

### Output Format
```json
{
  "status": "success",
  "result": "Combined text result"
}
```

## Data To DataFrame Component (DataToDataFrameComponent)

### Input Parameters
- **input_data** (any, required): The data to convert to a DataFrame.
- **orientation** (str, optional): The orientation of the input data: 'records' (list of dicts), 'columns' (dict of lists), or 'auto-detect'. Default is "auto-detect".

### Output Format
```json
{
  "status": "success",
  "dataframe": {
    "records": [{"col1": "value1", "col2": "value2"}, ...],
    "columns": {"col1": ["value1", ...], "col2": ["value2", ...]},
    "shape": [rows, columns],
    "column_names": ["col1", "col2", ...]
  }
}
```

## Doc Component (DocComponent)

### Input Parameters
- One of the following is required:
  - **file_path** (str, required if no URL): Path to the document file.
  - **url** (str, required if no file_path): URL to fetch document from.
- **file_type** (str, optional): Type of the document (auto, pdf, docx, txt, etc.). Default is "auto" for auto-detection.
- **extract_text** (bool, optional): Whether to extract text content. Default is true.
- **extract_metadata** (bool, optional): Whether to extract document metadata. Default is false.
- **max_content_length** (int, optional): Maximum length of text to extract. Default is 0 (no limit).
- **page_range** (list, optional): Range of pages to extract for PDF documents. Default is all pages.

### Output Format
```json
{
  "status": "success",
  "content": {
    "text": "Extracted text content",
    "pages": 10
  },
  "metadata": {
    "title": "Document Title",
    "author": "Author Name",
    "creation_date": "Creation Date",
    "file_size": 12345
  }
}
```


## Gmail Component (GmailComponent)

### Input Parameters
- **receiver_email** (str, required): Email address of the recipient.
- **message** (str, required): Email message content (HTML or plain text).
- **subject** (str, required): Email subject.
- **cc_emails** (list, optional): CC recipients. Default is an empty list.
- **bcc_emails** (list, optional): BCC recipients. Default is an empty list.
- **industry** (str, optional): Industry category for the email.
- **organization** (str, optional): Organization sending the email.
- **user_id** (str, optional): User ID sending the email.
- **db_type** (str, optional): Database type (sqlite or mongodb). Default is "sqlite".
- **db_config** (dict, optional): Database configuration. Default is an empty dict.
- **smtp_server** (str, optional): SMTP server address. If not provided, uses environment variable.
- **smtp_port** (int, optional): SMTP server port. Default is 587.
- **sender_email** (str, optional): Sender email address. If not provided, uses environment variable.
- **sender_password** (str, optional): Sender email password. If not provided, uses environment variable.

### Output Format
```json
{
  "success": true,
  "message_id": "<EMAIL>",
  "recipient": "<EMAIL>"
}
```

or on error:
```json
{
  "success": false,
  "error": "Error message"
}
```

## Gmail Tracker Component (GmailTrackerComponent)

### Input Parameters
- **forward_to** (list, required): List of email addresses to forward replies to.
- **excluded_subject_terms** (list, optional): List of terms to exclude from subject lines. Default includes common auto-reply terms.
- **excluded_domains** (list, optional): List of domains to exclude from forwarding. Default includes common system domains.
- **organization** (str, optional): Organization filter for tracking.
- **user_id** (str, optional): User ID filter for tracking.
- **db_type** (str, optional): Database type (sqlite or mongodb). Default is "sqlite".
- **db_config** (dict, optional): Database configuration. Default is an empty dict.
- **imap_server** (str, optional): IMAP server address. If not provided, uses environment variable.
- **smtp_server** (str, optional): SMTP server address. If not provided, uses environment variable.
- **smtp_port** (int, optional): SMTP server port. Default is 587.
- **sender_email** (str, optional): Sender email address. If not provided, uses environment variable.
- **sender_password** (str, optional): Sender email password. If not provided, uses environment variable.

### Output Format
```json
{
  "success": true,
  "forwarded_count": 5,
  "message": "Forwarded 5 replies"
}
```

or on error:
```json
{
  "success": false,
  "error": "Error message"
}
```

## ID Generator Component (IdGeneratorComponent)

### Input Parameters
- **id_type** (str, optional): The type of ID to generate. Options: "UUIDv4", "Timestamp ID", "Short ID". Default is "UUIDv4".
- **short_id_length** (int, optional): Length of short ID if id_type is "Short ID". Default is 8.

### Output Format
```json
{
  "result": "generated-id-value"
}
```

or on error:
```json
{
  "error": "Error message"
}
```

## Merge Data Component (MergeDataComponent)

### Input Parameters
- **input_data_1** (any, required): The first data structure to merge.
- **input_data_2** (any, required): The second data structure to merge.
- **merge_strategy** (str, optional): How to handle conflicts when merging dictionaries: 'Overwrite', 'Deep Merge', or 'Error on Conflict'. Default is "Overwrite".

### Output Format
```json
{
  "status": "success",
  "result": "The merged data structure"
}
```

or on error:
```json
{
  "status": "error",
  "error": "Error message"
}
```

## Parse JSON Component (ParseJSONDataComponent)

### Input Parameters
- One of the following is required:
  - **input_json_string** or **json_string** (str, required if no alternatives): The JSON string to parse.
  - **json_file_path** (str, required if no JSON string): Path to a JSON file to parse.
  - **json_object** (dict, required if no other inputs): Direct JSON object to use (bypasses parsing).
- **strict_mode** (bool, optional): Whether to use strict mode for parsing. Default is false.

### Output Format
```json
{
  "status": "success",
  "result": {
    "parsed_data": "The parsed JSON data structure"
  }
}
```

## Select Data Component (SelectDataComponent)

### Input Parameters
For data selection:
- **input_data** (any, required): The data to select from.
- **selector** (str, required): The selector to use for selection.
- **data_type** (str, optional): The type of data to select from. Options: "Auto-Detect", "List", "Dictionary". Default is "Auto-Detect".

For file operations:
- **operation** (str, required): The operation to perform. Options: "get_all", "get_by_id", "filter", "sort".
- **data_file** (str, required): The path to the data file.
- Additional parameters based on operation:
  - For "get_by_id": **id** (str/int, required): The ID to look for.
  - For "filter": **filter_criteria** (dict, required): The criteria to filter by.
  - For "sort": **sort_key** (str, required): The key to sort by, **ascending** (bool, optional): Sort direction. Default is true.

### Output Format
```json
{
  "status": "success",
  "result": "The selected data or operation result"
}
```

## Split Text Component (SplitTextComponent)

### Input Parameters
- **input_text** (str, required): The text to split.
- **delimiter** (str, optional): The delimiter to use for splitting. Default from settings.
- **max_splits** (int, optional): Maximum number of splits to perform. Default from settings.
- **include_delimiter** (bool, optional): Whether to include the delimiter in the result. Default from settings.

### Output Format
```json
{
  "status": "success",
  "output_list": ["Split", "text", "result"]
}
```

## Text Analysis Component (TextAnalysisComponent)

### Input Parameters
- **text** (str, required): The text to analyze.
- **operations** (list, required): List of operations to perform. Options: "count", "entities", "sentiment", "keywords".
- **language** (str, optional): Language code. Default is "en".
- **max_results** (int, optional): Maximum number of results to return. Default from settings.

### Output Format
```json
{
  "status": "success",
  "results": {
    "count": {
      "characters": 123,
      "words": 25,
      "sentences": 3
    },
    "entities": ["Entity1", "Entity2"],
    "sentiment": {
      "sentiment": "positive",
      "score": 0.8,
      "positive_count": 5,
      "negative_count": 1
    },
    "keywords": ["keyword1", "keyword2"]
  }
}
```

## Update Data Component (UpdateDataComponent)

### Input Parameters
For dictionary updates:
- **input_dict** (dict, required): The dictionary to update.
- **updates** (dict, required): The updates to apply.
- **deep_update** (bool, optional): Whether to perform a deep update. Default is false.

For file operations:
- **operation** (str, required): The operation to perform. Options: "create", "update", "delete".
- **data_file** (str, required): The path to the data file.
- **data** (dict, required for "create" and "update"): The data to write to the file.

### Output Format
For dictionary updates:
```json
{
  "result": "The updated dictionary"
}
```

For file operations:
```json
{
  "success": true,
  "message": "Operation result message"
}
```
