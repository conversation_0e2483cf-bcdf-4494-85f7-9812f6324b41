# Python Code Wrapper Component - Implementation Tasklist

## Overview
This tasklist implements the Python Code Wrapper Component following the established patterns in the workflow platform. The implementation uses **Phase 1: Simple Convention (Type Hints + Single Return Dict)** approach with Test-Driven Development (TDD) methodology.

---

## Phase 1: Setup and Dependencies

### 1.1 Environment Setup
- [ ] **Verify workspace structure** - Confirm all microservice directories are accessible
- [ ] **Check Poetry installation** - Ensure Poetry is available for dependency management
- [ ] **Verify Python version** - Confirm Python 3.11+ is available
- [ ] **Check Docker availability** - Verify Docker is installed for future containerization

### 1.2 Dependencies Installation
- [ ] **Add RestrictedPython to node-executor-service**
  ```bash
  cd node-executor-service
  poetry add RestrictedPython==6.0
  ```
- [ ] **Add psutil to node-executor-service**
  ```bash
  cd node-executor-service  
  poetry add psutil==5.9.8
  ```
- [ ] **Verify dependency installation**
  ```bash
  poetry show RestrictedPython psutil
  ```

---

## Phase 2: Test-Driven Development Setup

### 2.1 Workflow Service Test Structure
- [ ] **Create test directory structure**
  ```
  workflow-service/tests/components/processing/
  ```
- [ ] **Create test file**: `workflow-service/tests/components/processing/test_python_code_wrapper.py`
- [ ] **Write basic test class structure** with fixtures for component and mock context
- [ ] **Write test for component metadata** (name, display_name, category, inputs, outputs)
- [ ] **Write test for valid code execution** (parameter preparation)
- [ ] **Write test for empty code validation**
- [ ] **Write test for invalid timeout validation**
- [ ] **Write test for input validation edge cases**

### 2.2 Node Executor Service Test Structure  
- [ ] **Create test directory structure**
  ```
  node-executor-service/tests/components/
  ```
- [ ] **Create test file**: `node-executor-service/tests/components/test_python_code_wrapper_component.py`
- [ ] **Write basic test class structure** with executor fixture
- [ ] **Write test for valid code processing** (simple calculation)
- [ ] **Write test for print statement capture**
- [ ] **Write test for syntax error handling**
- [ ] **Write test for runtime error handling**
- [ ] **Write test for restricted operations blocking**
- [ ] **Write test for allowed modules functionality**
- [ ] **Write test for safe globals creation**

### 2.3 Security and Performance Tests
- [ ] **Write security test cases** for malicious code attempts
- [ ] **Write performance test cases** for execution time limits
- [ ] **Write memory usage test cases** (basic monitoring)
- [ ] **Write concurrent execution test cases**

---

## Phase 3: Workflow Service Implementation

### 3.1 Component File Creation
- [ ] **Create component file**: `workflow-service/app/components/processing/python_code_wrapper.py`
- [ ] **Import required dependencies** (BaseNode, InputBase, Output, etc.)
- [ ] **Define class structure** inheriting from BaseNode
- [ ] **Set component metadata** (name, display_name, description, category, icon)

### 3.2 Input/Output Definition
- [ ] **Define python_code input** using create_dual_purpose_input with input_type="code"
- [ ] **Define input_data input** using create_dual_purpose_input for Any type data
- [ ] **Define timeout_seconds input** using IntInput with range 1-30
- [ ] **Define enable_debugging input** using BoolInput for detailed logs
- [ ] **Define result output** for execution results (Any type)
- [ ] **Define output_logs output** for captured print statements
- [ ] **Define execution_time output** for performance monitoring
- [ ] **Define error output** for error messages

### 3.3 Execute Method Implementation
- [ ] **Implement execute method signature** with WorkflowContext parameter
- [ ] **Add execution timing** (start_time tracking)
- [ ] **Implement input value extraction** using get_input_value helper
- [ ] **Add input validation logic** (empty code, timeout range)
- [ ] **Package tool_parameters** for orchestration engine
- [ ] **Return NodeResult.success** with tool_parameters
- [ ] **Add comprehensive error handling** with NodeResult.error
- [ ] **Add context logging** for debugging and monitoring

---

## Phase 4: Node Executor Service Implementation

### 4.1 Component File Creation
- [ ] **Create component file**: `node-executor-service/app/components/python_code_wrapper_component.py`
- [ ] **Import required dependencies** (BaseComponent, register_component, RestrictedPython, etc.)
- [ ] **Define class structure** inheriting from BaseComponent
- [ ] **Add @register_component decorator** with "PythonCodeWrapperComponent"

### 4.2 RestrictedPython Environment Setup
- [ ] **Implement _setup_restricted_environment method**
- [ ] **Define allowed modules list** (json, re, math, datetime, collections, copy, itertools)
- [ ] **Import and store allowed modules** with error handling
- [ ] **Create _create_safe_globals method**
- [ ] **Configure safe_globals and limited_builtins**
- [ ] **Remove dangerous builtins** (open, exec, eval, __import__, etc.)
- [ ] **Add input data and result placeholder** to execution environment

### 4.3 Code Execution Logic
- [ ] **Implement process method** with payload parameter handling
- [ ] **Extract tool_parameters** from payload
- [ ] **Implement _execute_code method** with RestrictedPython
- [ ] **Add stdout/stderr capture** using StringIO and redirect
- [ ] **Compile code using compile_restricted**
- [ ] **Execute code in safe environment**
- [ ] **Extract result from execution globals**
- [ ] **Capture and format output logs**

### 4.4 Error Handling and Security
- [ ] **Handle compilation errors** (syntax errors, restricted operations)
- [ ] **Handle runtime errors** (exceptions during execution)
- [ ] **Add timeout enforcement** (basic implementation)
- [ ] **Add memory monitoring** (basic psutil integration)
- [ ] **Format error messages** with debugging information
- [ ] **Return structured responses** (status, result, logs, execution_time)

---

## Phase 5: Component Registration

### 5.1 Workflow Service Registration
- [ ] **Add import to processing __init__.py**
  ```python
  from .python_code_wrapper import PythonCodeWrapperComponent
  ```
- [ ] **Add to __all__ list** in processing/__init__.py
- [ ] **Verify component discovery** through component service
- [ ] **Test component appears in UI** (if applicable)

### 5.2 Node Executor Service Registration  
- [ ] **Add import to components __init__.py**
  ```python
  from . import python_code_wrapper_component
  ```
- [ ] **Verify component registration** in component registry
- [ ] **Test component instantiation** through component manager
- [ ] **Verify component appears in logs** during startup

---

## Phase 6: Testing and Validation

### 6.1 Unit Test Execution
- [ ] **Run workflow service tests**
  ```bash
  cd workflow-service
  poetry run pytest tests/components/processing/test_python_code_wrapper.py -v
  ```
- [ ] **Run node executor service tests**
  ```bash
  cd node-executor-service  
  poetry run pytest tests/components/test_python_code_wrapper_component.py -v
  ```
- [ ] **Verify all tests pass** with expected coverage
- [ ] **Fix any failing tests** and update implementation

### 6.2 Integration Testing
- [ ] **Create end-to-end test script** combining both services
- [ ] **Test basic code execution** (simple calculation)
- [ ] **Test data processing** (list/dict manipulation)
- [ ] **Test allowed modules usage** (json, math, etc.)
- [ ] **Test error scenarios** (syntax errors, runtime errors)
- [ ] **Test security restrictions** (blocked operations)

### 6.3 Performance and Security Validation
- [ ] **Test execution timeout enforcement**
- [ ] **Test memory usage monitoring**
- [ ] **Validate security restrictions** with malicious code examples
- [ ] **Test concurrent execution** (multiple requests)
- [ ] **Measure execution performance** and optimize if needed

---

## Phase 7: Documentation and Finalization

### 7.1 Code Documentation
- [ ] **Add comprehensive docstrings** to all methods
- [ ] **Add inline comments** for complex logic
- [ ] **Update type hints** for all parameters and returns
- [ ] **Add usage examples** in docstrings

### 7.2 Testing Documentation
- [ ] **Document test coverage** and results
- [ ] **Create usage examples** for different scenarios
- [ ] **Document security limitations** and restrictions
- [ ] **Create troubleshooting guide** for common issues

### 7.3 Final Validation
- [ ] **Run complete test suite** for both services
- [ ] **Verify component registration** in both services
- [ ] **Test with real workflow execution** (if orchestration engine available)
- [ ] **Validate error handling** and logging
- [ ] **Confirm security measures** are working properly

---

## Success Criteria Checklist

### Functional Requirements
- [ ] Execute Python code safely using RestrictedPython
- [ ] Handle various input data types (dict, list, string, numbers)  
- [ ] Provide structured output (result, logs, execution_time, error)
- [ ] Enforce timeout limits (1-30 seconds)
- [ ] Integrate seamlessly with existing workflow patterns

### Security Requirements  
- [ ] Prevent file system access
- [ ] Prevent network access
- [ ] Prevent dangerous imports
- [ ] Enforce resource limits
- [ ] Handle malicious code gracefully

### Quality Requirements
- [ ] Follow established codebase patterns
- [ ] Use TDD methodology
- [ ] Comprehensive error handling
- [ ] Clear logging and debugging
- [ ] 90%+ test coverage

---

## Notes
- **TDD Approach**: Write tests first, then implement to pass tests
- **Security First**: Validate all security restrictions before proceeding
- **Pattern Consistency**: Follow existing component patterns exactly
- **Error Handling**: Comprehensive error handling at every level
- **Documentation**: Document everything for future maintenance

**Total Tasks: ~85 individual tasks**
**Estimated Time: 2-3 days for experienced developer**
**Dependencies: RestrictedPython, psutil, existing workflow platform**
