"""
<PERSON><PERSON>t to update imports after refactoring.

This script updates imports from 'definitions' to 'schemas' and from 'engine.models' to 'models'.
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple, Set

# Define the root directory
ROOT_DIR = Path(__file__).parent.parent


def find_python_files() -> List[Path]:
    """Find all Python files in the codebase."""
    python_files = []
    for path in ROOT_DIR.glob("**/*.py"):
        # Skip files in the old directory
        if "old" not in path.parts:
            python_files.append(path)
    return python_files


def update_imports_in_file(file_path: Path) -> Tuple[bool, List[str]]:
    """
    Update imports in a file.

    Returns:
        Tuple[bool, List[str]]: (was_updated, list_of_changes)
    """
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    original_content = content
    changes = []

    # Update imports from definitions to schemas
    definitions_import_pattern = r"from\s+definitions\.([a-zA-Z_]+)\s+import\s+(.+)"
    for match in re.finditer(definitions_import_pattern, content):
        module = match.group(1)
        imports = match.group(2)

        # Map old modules to new modules
        module_mapping = {
            "inputs": "components",
            "outputs": "components",
            "requests": "requests",
        }

        new_module = module_mapping.get(module, module)
        old_import = match.group(0)
        new_import = f"from app.models.workflow_builder.{new_module} import {imports}"

        content = content.replace(old_import, new_import)
        changes.append(f"Updated: {old_import} -> {new_import}")

    # Update imports from engine.models to models
    engine_import_pattern = r"from\s+engine\.models\s+import\s+(.+)"
    for match in re.finditer(engine_import_pattern, content):
        imports = match.group(1)

        old_import = match.group(0)
        new_import = f"from app.models.workflow_builder.context import {imports}"

        content = content.replace(old_import, new_import)
        changes.append(f"Updated: {old_import} -> {new_import}")

    # Write the updated content back to the file if changes were made
    if content != original_content:
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        return True, changes

    return False, changes


def main():
    """Main function."""
    python_files = find_python_files()

    updated_files = 0
    for file_path in python_files:
        was_updated, changes = update_imports_in_file(file_path)
        if was_updated:
            updated_files += 1
            print(f"Updated {file_path}:")
            for change in changes:
                print(f"  {change}")

    print(f"\nUpdated {updated_files} files.")


if __name__ == "__main__":
    main()
