"use client";

import React from "react";
import { SignupForm } from "@/components/auth/SignupForm";
import Link from "next/link";
import { Workflow } from "lucide-react";

export default function SignupPage() {
  return (
    <div className="bg-background flex min-h-screen flex-col items-center justify-center p-4">
      <div className="bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg">
        <div className="flex flex-col items-center space-y-2">
          <div className="rounded-md bg-blue-600 p-2 shadow-md">
            <Workflow className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-center text-2xl font-bold">Workflow Builder</h1>
          <p className="text-muted-foreground text-center text-sm">
            Create an account to get started
          </p>
        </div>

        <SignupForm />

        <div className="text-center text-sm">
          <p className="text-muted-foreground">
            Already have an account?{" "}
            <Link href="/login" className="text-primary font-medium hover:underline">
              Log in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
