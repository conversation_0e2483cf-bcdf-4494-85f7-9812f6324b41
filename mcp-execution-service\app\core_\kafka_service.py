# kafka_service.py
import asyncio
import json
import logging
from typing import Optional, Any

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer  # type: ignore
from aiokafka.structs import TopicPartition  # type: ignore
from aiokafka.errors import KafkaError, IllegalStateError  # type: ignore
from app.config.config import settings
from app.core_.mcp_executor import MC<PERSON><PERSON>xecutor
from werkzeug.exceptions import InternalServerError  # type: ignore


class InfiniteSemaphore(asyncio.Semaphore):
    """A semaphore that never times out when acquiring."""

    async def acquire(self):
        """Acquire the semaphore with no timeout."""
        # Call the parent's acquire method directly without timeout
        # This will wait indefinitely until a slot is available
        return await super().acquire()


class KafkaMCPService:

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.kafka_broker: str = settings.kafka_bootstrap_servers
        self.consumer_topic: str = settings.kafka_consumer_topic
        self.group_id: str = settings.kafka_consumer_group_id
        self.reply_topic = settings.kafka_results_topic

        self.consumer: AIOKafkaConsumer = AIOKafkaConsumer(
            self.consumer_topic,
            bootstrap_servers=self.kafka_broker,
            group_id=self.group_id,
            auto_offset_reset="latest",
            enable_auto_commit=False,
        )
        self.producer: AIOKafkaProducer = AIOKafkaProducer(
            bootstrap_servers=self.kafka_broker,
            max_request_size=524288000,
            value_serializer=lambda v: json.dumps(v).encode("utf-8"),
        )

        self.mcp_executor = MCPExecutor(producer=self.producer, logger=self.logger)

        self.max_concurrent_tasks = settings.max_concurrent_tasks
        # Use InfiniteSemaphore instead of standard asyncio.Semaphore to prevent timeout issues
        self.semaphore = InfiniteSemaphore(self.max_concurrent_tasks)

        self.logger.info(
            f"KafkaMCPService initialized. Consumer Topic: {self.consumer_topic}, Group ID: {self.group_id}"
        )
        self._consumer_task: Optional[asyncio.Task] = None

    async def start_consumer(self) -> None:
        """Starts the Kafka consumer and producer, and runs the message processing loop."""
        self.logger.info("Starting Kafka consumer and producer...")
        try:
            while True:
                try:
                    await self.consumer.start()
                    await self.producer.start()
                    self.logger.info(
                        "Kafka consumer and producer started successfully."
                    )
                except KafkaError as e:
                    self.logger.error(
                        f"Failed to start Kafka consumer/producer: {e}", exc_info=True
                    )
                    raise

                try:
                    async for msg in self.consumer:
                        self.logger.debug(
                            f"Received message: Topic={msg.topic}, Partition={msg.partition}, Offset={msg.offset}"
                        )
                        await self.semaphore.acquire()
                        asyncio.create_task(self.process_message(msg, self.semaphore))

                except IllegalStateError:
                    self.logger.warning(
                        "Kafka consumer is not running. Likely stopped."
                    )
                except asyncio.CancelledError:
                    self.logger.info("Consumer task cancelled.")
                except Exception as e:
                    self.logger.error(
                        f"Unexpected error in consumer loop: {e}", exc_info=True
                    )
        except asyncio.CancelledError:
            self.logger.info("Consumer task cancelled.")
        finally:
            self.logger.info("Consumer loop finished or terminated. Cleaning up...")
            await self.stop_consumer()

    async def stop_consumer(self) -> None:
        """Stops the Kafka consumer and producer."""
        self.logger.info("Attempting to stop Kafka consumer and producer...")
        if self.consumer._running:
            try:
                await self.consumer.stop()
                self.logger.info("Kafka consumer stopped.")
            except Exception as e:
                self.logger.error(f"Error stopping Kafka consumer: {e}", exc_info=True)
        else:
            self.logger.info("Kafka consumer already stopped or not started.")

        if self.producer._sender._running:
            try:
                await self.producer.stop()
                self.logger.info("Kafka producer stopped.")
            except Exception as e:
                self.logger.error(f"Error stopping Kafka producer: {e}", exc_info=True)
        else:
            self.logger.info("Kafka producer already stopped or not started.")

    async def send_error_response(
        self,
        reply_topic: str,
        error_message: str,
        request_id: str,
    ) -> None:
        message = {
            "request_id": request_id,
            "error": error_message,
            "mcp_status": "error",
        }
        if reply_topic:
            await self.producer.send_and_wait(reply_topic, value=message)
            self.logger.info(f"Sent error response to topic '{reply_topic}': {message}")

    async def process_message(self, msg, semaphore: asyncio.Semaphore):
        """Processes a single Kafka message, executes the MCP tool, and handles commits."""
        # Release the semaphore immediately after task creation
        # This allows the consumer to process more messages without waiting for this one to complete
        semaphore.release()
        self.logger.debug(
            f"Semaphore released early. Current queue size: {self.semaphore._value}"
        )

        topic_partition = TopicPartition(msg.topic, msg.partition)
        offset = msg.offset
        commit_offset = True

        payload = None
        try:
            payload = json.loads(msg.value.decode("utf-8"))
            self.logger.info(f"Processing request from offset {offset}: {payload}")

            server_script_path = payload.get("server_script_path")
            tool_name = payload.get("tool_name")
            tool_parameters = payload.get("tool_parameters")
            correlation_id = payload.get("correlation_id")

            if not all(
                [server_script_path, tool_name, isinstance(tool_parameters, dict)]
            ):
                self.logger.error(
                    f"Invalid message payload at offset {offset}. Missing/invalid fields. Payload: {payload}"
                )

                commit_offset = True
                await self._commit_offset(topic_partition, offset, commit_offset)
                return

            retries = payload.get("retries", settings.default_mcp_retries)

            # Execute the tool asynchronously
            try:
                result = await self.mcp_executor.execute_tool(
                    server_script_path=server_script_path,
                    tool_name=tool_name,
                    tool_parameters=tool_parameters,
                    retries=retries,
                    correlation_id=correlation_id,
                )

                self.logger.info(
                    f"Successfully processed request offset={offset} for {tool_name}. Result: {result}"
                )

                def parse_result(result):
                    if not isinstance(result, list) or not result:
                        raise ValueError("Result must be a non-empty list.")

                    first_item = result[0]

                    if isinstance(first_item, str):
                        try:
                            parsed = json.loads(first_item)
                        except json.JSONDecodeError as e:
                            raise ValueError(f"Failed to parse JSON string: {e}")
                    elif isinstance(first_item, dict):
                        parsed = first_item
                    else:
                        raise TypeError(
                            f"Unexpected type in result list: {type(first_item)}"
                        )

                    return parsed

                parsed = parse_result(result)

                if parsed.get("is_error") is True:
                    raise InternalServerError(
                        f"MCP returned error status: {parsed.get("message")}"
                    )

                await self.send_result(result, payload.get("request_id"))
                commit_offset = True
            except InternalServerError as e:
                self.logger.error(
                    f"MCP Execution failed permanently for request at offset {offset} after retries: {e}"
                )
                await self.send_error_response(
                    reply_topic=self.reply_topic,
                    error_message=str(e),
                    request_id=payload.get("request_id"),
                )

                dead_letter_topic = "mcp_dead_letter_queue"
                dlq_payload = {
                    "original_topic": msg.topic,
                    "partition": msg.partition,
                    "offset": offset,
                    "payload": payload,
                    "error": str(e),
                }
                try:
                    await self.producer.send_and_wait(
                        dead_letter_topic, value=dlq_payload
                    )
                    self.logger.info(
                        f"Sent message to DLQ '{dead_letter_topic}': {dlq_payload}"
                    )
                except Exception as dlq_err:
                    self.logger.error(
                        f"Failed to send message to DLQ '{dead_letter_topic}': {dlq_err}",
                        exc_info=True,
                    )
                commit_offset = True
            except Exception as e:
                self.logger.error(
                    f"Unexpected error executing MCP tool at offset {offset}: {e}",
                    exc_info=True,
                )
                commit_offset = False  # DO NOT COMMIT - let Kafka redeliver

        except json.JSONDecodeError:
            self.logger.error(
                f"Failed to decode JSON message at offset {offset}: {msg.value.decode('utf-8', errors='ignore')}"
            )
            commit_offset = True
        except Exception as e:
            self.logger.error(
                f"Unexpected error processing message at offset {offset}: {e}",
                exc_info=True,
            )
            commit_offset = False  # DO NOT COMMIT - let Kafka redeliver
        finally:
            # Commit the offset if needed
            await self._commit_offset(topic_partition, offset, commit_offset)

    async def _commit_offset(self, topic_partition, offset, commit_offset):
        """Helper method to commit Kafka offset"""
        if commit_offset:
            try:
                self.logger.debug(
                    f"Committing offset {offset + 1} for partition {topic_partition}"
                )
                await self.consumer.commit({topic_partition: offset + 1})
            except Exception as e:
                self.logger.error(
                    f"Failed to commit offset {offset + 1} for partition {topic_partition}: {e}",
                    exc_info=True,
                )

    async def send_result(self, result_data: Any, request_id: Optional[str] = None):
        try:
            message = {
                "request_id": request_id,
                "result": result_data,
                "mcp_status": "success",
            }
            self.logger.info(f"Sending result to topic '{self.reply_topic}': {message}")
            await self.producer.send_and_wait(self.reply_topic, value=message)
        except KafkaError as e:
            self.logger.error(
                f"Failed to send result to Kafka topic '{self.reply_topic}': {e}",
                exc_info=True,
            )
        except Exception as e:
            self.logger.error(f"Unexpected error sending result: {e}", exc_info=True)
