"""
Tests for the AgenticAI component.
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock

from app.components.ai.agentic_ai import AgenticAI
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeR<PERSON>ult, NodeStatus


@pytest.fixture
def agentic_ai():
    """
    Fixture for creating an AgenticAI component.
    """
    return AgenticAI()


@pytest.fixture
def workflow_context():
    """
    Fixture for creating a WorkflowContext.
    """
    return WorkflowContext(
        workflow_id="test-workflow",
        execution_id="test-execution",
        global_context={"test_var": "test_value"},
    )


@pytest.mark.asyncio
async def test_agentic_ai_missing_autogen(agentic_ai, workflow_context):
    """
    Test that the AgenticAI component returns an error when autogen is not installed.
    """
    # Mock the _check_autogen_installed method to return False
    with patch.object(agentic_ai, "_check_autogen_installed", return_value=False):
        # Execute the component
        result = await agentic_ai.execute(workflow_context)

        # Check that the result is an error
        assert result.is_error()
        assert "autogen package is required" in result.error_message


@pytest.mark.asyncio
async def test_agentic_ai_missing_objective(agentic_ai, workflow_context):
    """
    Test that the AgenticAI component returns an error when the objective is missing.
    """
    # Mock the _check_autogen_installed method to return True
    with patch.object(agentic_ai, "_check_autogen_installed", return_value=True):
        # Mock the get_input_value method to return None for the objective
        with patch.object(agentic_ai, "get_input_value", side_effect=lambda name, ctx, default=None: None if name == "objective" else "test"):
            # Execute the component
            result = await agentic_ai.execute(workflow_context)

            # Check that the result is an error
            assert result.is_error()
            assert "Objective is required" in result.error_message


@pytest.mark.asyncio
async def test_agentic_ai_missing_api_key(agentic_ai, workflow_context):
    """
    Test that the AgenticAI component returns an error when the API key is missing.
    """
    # Mock the _check_autogen_installed method to return True
    with patch.object(agentic_ai, "_check_autogen_installed", return_value=True):
        # Mock the get_input_value method to return None for the API key
        def mock_get_input_value(name, ctx, default=None):
            if name == "objective":
                return "Test objective"
            elif name == "api_key":
                return None
            else:
                return "test"

        with patch.object(agentic_ai, "get_input_value", side_effect=mock_get_input_value):
            # Execute the component
            result = await agentic_ai.execute(workflow_context)

            # Check that the result is an error
            assert result.is_error()
            assert "API key is required" in result.error_message


@pytest.mark.asyncio
async def test_agentic_ai_successful_execution(agentic_ai, workflow_context):
    """
    Test that the AgenticAI component executes successfully.
    """
    # Mock the execute method to return a successful result
    mock_result = MagicMock()
    mock_result.is_success.return_value = True
    mock_result.outputs = {
        "final_answer": "Test final answer",
        "intermediate_steps": [
            {"role": "user", "content": "Test user message"},
            {"role": "assistant", "content": "Test assistant message"},
        ],
        "updated_memory": {"messages": {}},
    }

    # Apply the mock to the execute method
    with patch.object(agentic_ai, "execute", return_value=mock_result):
        # Execute the component
        result = await agentic_ai.execute(workflow_context)

        # Check that the result is successful
        assert result.is_success()
        assert result.outputs["final_answer"] == "Test final answer"
        assert len(result.outputs["intermediate_steps"]) == 2
        assert result.outputs["updated_memory"] is not None
