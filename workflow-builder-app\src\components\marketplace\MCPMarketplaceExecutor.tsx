import { useState } from "react";
import { Node } from "reactflow";
import { WorkflowNodeData, MCPSchemaInfo } from "@/types";
import { fixMCPMarketplaceNode } from "@/lib/mcp_marketplace_transform";

interface MCPMarketplaceExecutorProps {
  node: Node<WorkflowNodeData>;
  onExecutionComplete: (result: any) => void;
}

/**
 * Component for executing MCP Marketplace components in the workflow
 */
export function MCPMarketplaceExecutor({ node, onExecutionComplete }: MCPMarketplaceExecutorProps) {
  const [isExecuting, setIsExecuting] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Execute the MCP Marketplace component
  const executeComponent = async () => {
    if (!node || !node.data || !node.data.definition) {
      setError("Invalid node data");
      return;
    }

    setIsExecuting(true);
    setError(null);

    try {
      // Fix the node to match the expected template
      const fixedNode = fixMCPMarketplaceNode(node);

      // Extract the MCP info from the node definition
      const mcp_info = (fixedNode.data.definition?.mcp_info as MCPSchemaInfo) || {};

      // Create the MCP configuration for the external orchestration service
      const mcpConfig = {
        node_id: fixedNode.id,
        mode: "SSE", // Use SSE mode for MCP Marketplace components
        sse_url: mcp_info.server_path || "",
        selected_tool_name: mcp_info.tool_name || "",
        tool_args: fixedNode.data.config || {}, // Use the config directly as tool_args
      };

      // Send the MCP configuration to the backend API, which will forward to the external service
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
      const response = await fetch(`${API_BASE_URL}/execute_mcp`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(mcpConfig),
      });

      if (!response.ok) {
        throw new Error(`Failed to execute MCP component: ${response.statusText}`);
      }

      const result = await response.json();
      setResult(result);
      onExecutionComplete(result);
    } catch (error) {
      console.error("Error executing MCP Marketplace component:", error);
      setError(error instanceof Error ? error.message : String(error));
      onExecutionComplete({ error: error instanceof Error ? error.message : String(error) });
    } finally {
      setIsExecuting(false);
    }
  };

  return null; // This is a logic component, not a UI component
}

/**
 * Helper function to check if a node is an MCP Marketplace component
 */
export function isMCPMarketplaceExecutable(node: Node<WorkflowNodeData> | null): boolean {
  if (!node) return false;
  return node.data.type === "MCPMarketplaceComponent";
}

/**
 * Helper function to get the MCP configuration for a node
 */
export function getMCPMarketplaceConfig(node: Node<WorkflowNodeData>): any {
  if (!node || !node.data || !node.data.definition) {
    return null;
  }

  // Fix the node to match the expected template
  const fixedNode = fixMCPMarketplaceNode(node);

  // Extract the MCP info from the node definition
  const mcp_info = (fixedNode.data.definition?.mcp_info as MCPSchemaInfo) || {};

  // Create the MCP configuration for the external orchestration service
  return {
    node_id: fixedNode.id,
    mode: "SSE", // Use SSE mode for MCP Marketplace components
    sse_url: mcp_info.server_path || "",
    selected_tool_name: mcp_info.tool_name || "",
    tool_args: fixedNode.data.config || {}, // Use the config directly as tool_args
  };
}
