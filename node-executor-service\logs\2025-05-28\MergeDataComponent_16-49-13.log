2025-05-28 16:49:13 - MergeDataComponent - INFO - [setup_logger:467] Logger MergeDataComponent configured with log file: logs\2025-05-28\MergeDataComponent_16-49-13.log
2025-05-28 16:49:20 - MergeDataComponent - INFO - [__init__:96] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] MergeDataExecutor initialized
2025-05-28 16:49:20 - MergeDataComponent - INFO - [process:241] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Processing merge data request for request_id: 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:49:20 - MergeDataComponent - INFO - [process:243] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-28 16:49:20 - MergeDataComponent - INFO - [process:257] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-28 16:49:20 - MergeDataComponent - INFO - [process:277] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Merging data for request_id 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-28 16:49:20 - MergeDataComponent - INFO - [process:341] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Dictionary input_1 merged with deep merge strategy for request_id 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1. Current keys: ['value', 'marketing']
2025-05-28 16:49:20 - MergeDataComponent - INFO - [process:380] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] All data merged successfully for request_id 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
