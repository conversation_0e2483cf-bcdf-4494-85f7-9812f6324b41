#!/usr/bin/env python3
"""
Integration Testing Examples for AlterMetadataComponent
These examples show how to test the component in realistic workflow scenarios.
"""
import asyncio
import json
import sys
import os

# Add both service paths
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'workflow-service'))

from app.components.processing.alter_metadata import AlterMetadataComponent
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeStatus


async def test_document_processing_workflow():
    """
    Integration Test 1: Document Processing Workflow
    Simulates a real document processing pipeline.
    """
    print("🔄 Integration Test 1: Document Processing Workflow")
    print("=" * 60)
    
    component = AlterMetadataComponent()
    
    # Simulate a document processing workflow
    print("Scenario: Processing a document through multiple stages")
    print("1. Document uploaded (draft status)")
    print("2. Content processed and analyzed")
    print("3. <PERSON>adata updated with processing results")
    print("4. Temporary processing fields removed")
    
    # Stage 1: Initial document metadata (from file upload)
    initial_metadata = {
        "document_id": "DOC-2024-001",
        "filename": "important_report.pdf",
        "upload_date": "2024-01-15T10:30:00Z",
        "uploader": "<EMAIL>",
        "file_size": 2048576,
        "status": "uploaded",
        "processing_stage": "initial",
        "temp_upload_id": "TEMP-UPLOAD-12345",
        "raw_text_length": 0,
        "content_type": "application/pdf"
    }
    
    # Stage 2: After content analysis
    analysis_updates = {
        "status": "analyzed",
        "processing_stage": "content_analysis",
        "raw_text_length": 15420,
        "word_count": 2847,
        "page_count": 12,
        "language": "en",
        "content_summary": "Financial report for Q4 2023",
        "keywords": ["revenue", "profit", "quarterly", "financial"],
        "analysis_date": "2024-01-15T10:35:00Z",
        "confidence_score": 0.95
    }
    
    # Stage 3: Final processing - remove temporary fields
    temp_fields_to_remove = [
        "temp_upload_id",
        "processing_stage",
        "raw_text_length"  # Keep word_count instead
    ]
    
    # Execute the metadata transformation
    context = WorkflowContext(workflow_id="doc_processing_001", execution_id="exec_001")
    context.current_node_id = "alter_metadata_node"
    context.node_outputs["alter_metadata_node"] = {
        "input_metadata": initial_metadata,
        "updates": analysis_updates,
        "keys_to_remove": temp_fields_to_remove
    }
    
    print(f"\nInitial metadata ({len(initial_metadata)} fields):")
    for key, value in initial_metadata.items():
        print(f"  {key}: {value}")
    
    print(f"\nAnalysis updates ({len(analysis_updates)} fields):")
    for key, value in analysis_updates.items():
        print(f"  {key}: {value}")
    
    print(f"\nFields to remove: {temp_fields_to_remove}")
    
    # Execute
    result = await component.execute(context)
    
    print(f"\nExecution Result:")
    print(f"Status: {result.status}")
    print(f"Execution Time: {result.execution_time:.4f}s")
    
    if result.status == NodeStatus.SUCCESS:
        final_metadata = result.outputs.get('output_metadata', {})
        
        print(f"\nFinal metadata ({len(final_metadata)} fields):")
        for key, value in final_metadata.items():
            print(f"  {key}: {value}")
        
        # Verify the workflow
        success_checks = [
            ("Status updated", final_metadata.get('status') == 'analyzed'),
            ("Word count added", 'word_count' in final_metadata),
            ("Temp fields removed", not any(field in final_metadata for field in temp_fields_to_remove)),
            ("Original data preserved", final_metadata.get('document_id') == 'DOC-2024-001'),
            ("Analysis data added", final_metadata.get('confidence_score') == 0.95)
        ]
        
        print(f"\nWorkflow Verification:")
        all_passed = True
        for check_name, passed in success_checks:
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            all_passed = all_passed and passed
        
        if all_passed:
            print("\n🎉 Document processing workflow completed successfully!")
        else:
            print("\n⚠️  Some workflow checks failed.")
        
        return all_passed
    else:
        print(f"❌ Workflow failed: {result.error_message}")
        return False


async def test_user_profile_update_workflow():
    """
    Integration Test 2: User Profile Update Workflow
    Simulates updating user profile with privacy controls.
    """
    print("\n🔄 Integration Test 2: User Profile Update Workflow")
    print("=" * 60)
    
    component = AlterMetadataComponent()
    
    print("Scenario: User profile update with privacy controls")
    print("1. User updates their profile information")
    print("2. System adds audit trail")
    print("3. Remove sensitive temporary data")
    
    # Current user profile
    current_profile = {
        "user_id": "USER-789",
        "username": "jane_smith",
        "email": "<EMAIL>",
        "first_name": "Jane",
        "last_name": "Smith",
        "phone": "******-0123",
        "date_of_birth": "1990-05-15",
        "profile_picture": "https://cdn.example.com/profiles/jane_smith.jpg",
        "privacy_level": "public",
        "account_status": "active",
        "created_date": "2023-06-01T09:00:00Z",
        "last_login": "2024-01-14T15:30:00Z",
        # Temporary session data
        "temp_session_id": "SESSION-ABC123",
        "temp_csrf_token": "CSRF-XYZ789",
        "temp_upload_token": "UPLOAD-DEF456"
    }
    
    # User's profile updates
    profile_updates = {
        "phone": "******-0199",  # Updated phone
        "privacy_level": "friends_only",  # Changed privacy
        "bio": "Software engineer passionate about AI and machine learning",
        "location": "San Francisco, CA",
        "website": "https://janesmith.dev",
        "last_updated": "2024-01-15T11:45:00Z",
        "updated_by": "USER-789",  # Self-update
        "update_reason": "profile_enhancement"
    }
    
    # Remove temporary/sensitive data
    sensitive_fields = [
        "temp_session_id",
        "temp_csrf_token", 
        "temp_upload_token"
    ]
    
    context = WorkflowContext(workflow_id="profile_update_001", execution_id="exec_002")
    context.current_node_id = "profile_alter_node"
    context.node_outputs["profile_alter_node"] = {
        "input_metadata": current_profile,
        "updates": profile_updates,
        "keys_to_remove": sensitive_fields
    }
    
    print(f"\nCurrent profile ({len(current_profile)} fields):")
    for key, value in current_profile.items():
        if "temp_" in key:
            print(f"  {key}: [SENSITIVE DATA]")
        else:
            print(f"  {key}: {value}")
    
    print(f"\nProfile updates ({len(profile_updates)} fields):")
    for key, value in profile_updates.items():
        print(f"  {key}: {value}")
    
    print(f"\nSensitive fields to remove: {sensitive_fields}")
    
    # Execute
    result = await component.execute(context)
    
    print(f"\nExecution Result:")
    print(f"Status: {result.status}")
    print(f"Execution Time: {result.execution_time:.4f}s")
    
    if result.status == NodeStatus.SUCCESS:
        updated_profile = result.outputs.get('output_metadata', {})
        
        print(f"\nUpdated profile ({len(updated_profile)} fields):")
        for key, value in updated_profile.items():
            print(f"  {key}: {value}")
        
        # Verify the profile update
        verification_checks = [
            ("Phone updated", updated_profile.get('phone') == '******-0199'),
            ("Privacy changed", updated_profile.get('privacy_level') == 'friends_only'),
            ("Bio added", 'bio' in updated_profile),
            ("Sensitive data removed", not any(field in updated_profile for field in sensitive_fields)),
            ("Original data preserved", updated_profile.get('user_id') == 'USER-789'),
            ("Audit trail added", 'last_updated' in updated_profile)
        ]
        
        print(f"\nProfile Update Verification:")
        all_passed = True
        for check_name, passed in verification_checks:
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            all_passed = all_passed and passed
        
        if all_passed:
            print("\n🎉 User profile update workflow completed successfully!")
        else:
            print("\n⚠️  Some profile update checks failed.")
        
        return all_passed
    else:
        print(f"❌ Profile update failed: {result.error_message}")
        return False


async def test_api_response_processing_workflow():
    """
    Integration Test 3: API Response Processing Workflow
    Simulates processing API response data.
    """
    print("\n🔄 Integration Test 3: API Response Processing Workflow")
    print("=" * 60)
    
    component = AlterMetadataComponent()
    
    print("Scenario: Processing external API response")
    print("1. Receive data from external API")
    print("2. Add internal tracking fields")
    print("3. Remove API-specific metadata")
    
    # Simulated API response metadata
    api_response_metadata = {
        "data_source": "external_api",
        "api_endpoint": "https://api.example.com/v1/products",
        "api_version": "1.2.3",
        "response_time_ms": 245,
        "rate_limit_remaining": 4750,
        "rate_limit_reset": "2024-01-15T12:00:00Z",
        "request_id": "req_abc123xyz",
        "server_region": "us-west-2",
        "cache_status": "miss",
        # Actual data
        "product_id": "PROD-12345",
        "product_name": "Wireless Headphones",
        "category": "Electronics",
        "price": 199.99,
        "currency": "USD",
        "availability": "in_stock",
        "rating": 4.5,
        "review_count": 1247,
        # API internal fields to remove
        "api_internal_id": "internal_98765",
        "api_debug_info": {"trace_id": "trace_xyz", "debug_level": 2},
        "api_temp_cache_key": "cache_temp_abc123"
    }
    
    # Add internal processing metadata
    internal_updates = {
        "processed_date": "2024-01-15T11:50:00Z",
        "processing_system": "workflow_engine",
        "data_quality_score": 0.98,
        "internal_product_id": "INT-PROD-67890",
        "category_mapping": "consumer_electronics",
        "price_tier": "premium",
        "inventory_status": "available",
        "last_sync": "2024-01-15T11:50:00Z"
    }
    
    # Remove API-specific fields
    api_fields_to_remove = [
        "api_internal_id",
        "api_debug_info",
        "api_temp_cache_key",
        "rate_limit_remaining",
        "rate_limit_reset",
        "request_id",
        "server_region",
        "cache_status"
    ]
    
    context = WorkflowContext(workflow_id="api_processing_001", execution_id="exec_003")
    context.current_node_id = "api_metadata_processor"
    context.node_outputs["api_metadata_processor"] = {
        "input_metadata": api_response_metadata,
        "updates": internal_updates,
        "keys_to_remove": api_fields_to_remove
    }
    
    print(f"\nAPI response metadata ({len(api_response_metadata)} fields):")
    for key, value in api_response_metadata.items():
        if "api_" in key or key in ["rate_limit_remaining", "request_id"]:
            print(f"  {key}: [API INTERNAL]")
        else:
            print(f"  {key}: {value}")
    
    print(f"\nInternal updates ({len(internal_updates)} fields):")
    for key, value in internal_updates.items():
        print(f"  {key}: {value}")
    
    print(f"\nAPI fields to remove: {api_fields_to_remove}")
    
    # Execute
    result = await component.execute(context)
    
    print(f"\nExecution Result:")
    print(f"Status: {result.status}")
    print(f"Execution Time: {result.execution_time:.4f}s")
    
    if result.status == NodeStatus.SUCCESS:
        processed_metadata = result.outputs.get('output_metadata', {})
        
        print(f"\nProcessed metadata ({len(processed_metadata)} fields):")
        for key, value in processed_metadata.items():
            print(f"  {key}: {value}")
        
        # Verify API processing
        processing_checks = [
            ("Product data preserved", processed_metadata.get('product_id') == 'PROD-12345'),
            ("Internal ID added", 'internal_product_id' in processed_metadata),
            ("API fields removed", not any(field in processed_metadata for field in api_fields_to_remove)),
            ("Quality score added", 'data_quality_score' in processed_metadata),
            ("Category mapped", processed_metadata.get('category_mapping') == 'consumer_electronics'),
            ("Useful API data kept", 'api_version' in processed_metadata)  # Keep useful API info
        ]
        
        print(f"\nAPI Processing Verification:")
        all_passed = True
        for check_name, passed in processing_checks:
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            all_passed = all_passed and passed
        
        if all_passed:
            print("\n🎉 API response processing workflow completed successfully!")
        else:
            print("\n⚠️  Some API processing checks failed.")
        
        return all_passed
    else:
        print(f"❌ API processing failed: {result.error_message}")
        return False


async def run_integration_tests():
    """Run all integration tests."""
    print("🚀 Starting Integration Testing Suite for AlterMetadataComponent")
    print("=" * 80)
    
    tests = [
        ("Document Processing Workflow", test_document_processing_workflow),
        ("User Profile Update Workflow", test_user_profile_update_workflow),
        ("API Response Processing Workflow", test_api_response_processing_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ EXCEPTION in {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 INTEGRATION TESTING SUMMARY")
    print(f"{'='*80}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")
    
    print(f"\nOverall Result: {passed}/{total} integration tests passed")
    
    if passed == total:
        print("🎉 All integration tests PASSED! Component works correctly in realistic scenarios.")
    else:
        print("⚠️  Some integration tests failed. Please review the results above.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(run_integration_tests())
