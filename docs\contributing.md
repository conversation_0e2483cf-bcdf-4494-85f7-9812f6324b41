# Contributing to Workflow Builder

Thank you for your interest in contributing to the Workflow Builder! This document provides guidelines and instructions for contributing to the project.

## Code of Conduct

Please read and follow our [Code of Conduct](code_of_conduct.md) to help us maintain a healthy and welcoming community.

## How to Contribute

There are many ways to contribute to the Workflow Builder:

1. **Report Bugs**: If you find a bug, please report it by creating an issue in the issue tracker.
2. **Suggest Features**: If you have an idea for a new feature, please suggest it by creating an issue in the issue tracker.
3. **Improve Documentation**: Help us improve the documentation by fixing typos, adding examples, or clarifying explanations.
4. **Write Code**: Contribute code by fixing bugs, implementing features, or improving existing code.

## Development Setup

To set up the Workflow Builder for development:

1. **Clone the Repository**:
   ```bash
   git clone https://github.com/your-organization/workflow-builder.git
   cd workflow-builder
   ```

2. **Install Dependencies**:
   ```bash
   # For the workflow service
   cd workflow-service
   pip install -r requirements.txt
   
   # For the workflow builder app
   cd ../workflow-builder-app
   npm install
   ```

3. **Run the Development Servers**:
   ```bash
   # For the workflow service
   cd workflow-service
   python -m app.main
   
   # For the workflow builder app
   cd ../workflow-builder-app
   npm run dev
   ```

## Pull Request Process

1. **Fork the Repository**: Fork the repository to your GitHub account.
2. **Create a Branch**: Create a branch for your changes.
3. **Make Changes**: Make your changes to the code or documentation.
4. **Run Tests**: Run the tests to ensure your changes don't break existing functionality.
5. **Submit a Pull Request**: Submit a pull request to the main repository.

## Coding Standards

Please follow these coding standards when contributing code:

1. **Code Style**: Follow the existing code style of the project.
2. **Documentation**: Document your code using docstrings and comments.
3. **Tests**: Write tests for your code.
4. **Commit Messages**: Write clear and descriptive commit messages.

## Component Development Guidelines

When developing new components or modifying existing ones:

1. **Follow the Component API**: Ensure your component follows the Component API.
2. **Use Dual-Purpose Inputs**: Use the `create_dual_purpose_input` helper function for inputs that can be both connected and directly edited.
3. **Provide Clear Documentation**: Document your component with clear descriptions and examples.
4. **Write Tests**: Write tests for your component to ensure it works as expected.
5. **Consider Backward Compatibility**: Consider backward compatibility when making changes to existing components.

## Documentation Guidelines

When contributing to the documentation:

1. **Use Markdown**: Write documentation in Markdown format.
2. **Follow the Documentation Structure**: Follow the existing structure of the documentation.
3. **Provide Examples**: Include examples to illustrate concepts and usage.
4. **Keep It Up-to-Date**: Ensure the documentation reflects the current state of the project.

## License

By contributing to the Workflow Builder, you agree that your contributions will be licensed under the project's [MIT License](license.md).
