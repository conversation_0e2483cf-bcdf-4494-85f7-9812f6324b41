"""
Base Component - Base class for all components.
"""
from abc import ABC, abstractmethod
import logging
import json
from typing import Dict, Any, List, Optional, Type, Union
from pydantic import BaseModel, ValidationError, create_model

# Removed security_config import
# from app.core_.security_config import get_security_settings

logger = logging.getLogger(__name__)


class ValidationResult:
    """
    Result of a validation operation.
    """
    def __init__(self, is_valid: bool, error_message: Optional[str] = None, error_details: Optional[Dict[str, Any]] = None):
        """
        Initialize a validation result.

        Args:
            is_valid: Whether the validation passed
            error_message: Error message if validation failed
            error_details: Detailed error information
        """
        self.is_valid = is_valid
        self.error_message = error_message
        self.error_details = error_details or {}


class BaseComponent(ABC):
    """
    Base class for all components.

    All components must inherit from this class and implement
    the required methods.
    """
    # Class attribute to store component type (set by decorator)
    component_type = None

    def __init__(self):
        """
        Initialize the component.
        """
        # Removed security settings initialization
        # self.security_settings = get_security_settings()
        # Schema models for validation (can be set by subclasses)
        self.request_schema: Optional[Type[BaseModel]] = None
        self.response_schema: Optional[Type[BaseModel]] = None

    @abstractmethod
    async def process(self, payload: Dict[str, Any]) -> Any:
        """
        Process a message.

        Args:
            payload: The message payload

        Returns:
            The processing result

        Raises:
            NotImplementedError: If not implemented by subclass
        """
        raise NotImplementedError("Subclasses must implement process()")

    async def validate(self, payload: Dict[str, Any]) -> Union[bool, ValidationResult]:
        """
        Validate a message payload.

        Args:
            payload: The message payload

        Returns:
            True/ValidationResult with is_valid=True if valid, False/ValidationResult with is_valid=False otherwise
        """
        # If a request schema is defined, use it for validation
        if self.request_schema:
            try:
                # Validate against the schema
                self.request_schema(**payload)
                return ValidationResult(is_valid=True)
            except ValidationError as e:
                # Return detailed validation errors
                error_details = {}
                for error in e.errors():
                    field = '.'.join(str(x) for x in error['loc'])
                    error_details[field] = error['msg']

                error_message = f"Validation failed: {', '.join(f'{k}: {v}' for k, v in error_details.items())}"
                logger.error(f"[{self.component_type}] {error_message}")
                return ValidationResult(is_valid=False, error_message=error_message, error_details=error_details)

        # Default implementation always returns True
        return ValidationResult(is_valid=True)

    # Removed validate_field method
    # def validate_field(self, field_name: str, value: Any, required: bool = True, field_type: Any = None,
    #                   min_length: Optional[int] = None, max_length: Optional[int] = None,
    #                   min_value: Optional[int] = None, max_value: Optional[int] = None,
    #                   allowed_values: Optional[List[Any]] = None) -> ValidationResult:
    #     """
    #     Validate a single field.
    #
    #     Args:
    #         field_name: Name of the field
    #         value: Value to validate
    #         required: Whether the field is required
    #         field_type: Expected type of the field
    #         min_length: Minimum length (for strings/lists)
    #         max_length: Maximum length (for strings/lists)
    #         min_value: Minimum value (for numbers)
    #         max_value: Maximum value (for numbers)
    #         allowed_values: List of allowed values
    #
    #     Returns:
    #         ValidationResult with validation result
    #     """
    #     # Check if field is required but missing or None
    #     if required and (value is None):
    #         return ValidationResult(
    #             is_valid=False,
    #             error_message=f"Field '{field_name}' is required",
    #             error_details={field_name: "required field missing"}
    #         )
    #
    #     # If value is None and not required, it's valid
    #     if value is None:
    #         return ValidationResult(is_valid=True)
    #
    #     # Type validation
    #     if field_type and not isinstance(value, field_type):
    #         return ValidationResult(
    #             is_valid=False,
    #             error_message=f"Field '{field_name}' must be of type {field_type.__name__}",
    #             error_details={field_name: f"expected {field_type.__name__}, got {type(value).__name__}"}
    #         )
    #
    #     # Length validation for strings and lists
    #     if (isinstance(value, (str, list, dict)) and
    #         ((min_length is not None and len(value) < min_length) or
    #          (max_length is not None and len(value) > max_length))):
    #
    #         length_constraints = []
    #         if min_length is not None:
    #             length_constraints.append(f"min {min_length}")
    #         if max_length is not None:
    #             length_constraints.append(f"max {max_length}")
    #
    #         constraints_str = " and ".join(length_constraints)
    #
    #         return ValidationResult(
    #             is_valid=False,
    #             error_message=f"Field '{field_name}' length must be {constraints_str}",
    #             error_details={field_name: f"length must be {constraints_str}, got {len(value)}"}
    #         )
    #
    #     # Value range validation for numbers
    #     if isinstance(value, (int, float)) and (
    #         (min_value is not None and value < min_value) or
    #         (max_value is not None and value > max_value)):
    #
    #         range_constraints = []
    #         if min_value is not None:
    #             range_constraints.append(f">= {min_value}")
    #         if max_value is not None:
    #             range_constraints.append(f"<= {max_value}")
    #
    #         constraints_str = " and ".join(range_constraints)
    #
    #         return ValidationResult(
    #             is_valid=False,
    #             error_message=f"Field '{field_name}' must be {constraints_str}",
    #             error_details={field_name: f"must be {constraints_str}, got {value}"}
    #         )
    #
    #     # Allowed values validation
    #     if allowed_values is not None and value not in allowed_values:
    #         return ValidationResult(
    #             is_valid=False,
    #             error_message=f"Field '{field_name}' must be one of: {', '.join(str(v) for v in allowed_values)}",
    #             error_details={field_name: f"must be one of: {allowed_values}"}
    #         )
    #
    #     # All validations passed
    #     return ValidationResult(is_valid=True)

    def create_schema_model(self, model_name: str, **field_definitions) -> Type[BaseModel]:
        """
        Create a Pydantic model for schema validation.

        Args:
            model_name: Name of the model
            **field_definitions: Field definitions as field_name=(type, default_value)

        Returns:
            A Pydantic model class
        """
        return create_model(model_name, **field_definitions)
