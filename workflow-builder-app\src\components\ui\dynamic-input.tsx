import React, { useState } from "react";
import { PlusCircle, MinusCircle, LinkIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { InputDefinition } from "@/types";

interface DynamicInputProps {
  inputDef: InputDefinition;
  currentValue: any;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  minInputs?: number;
  maxInputs?: number;
  defaultInputs?: number;
}

/**
 * A component for handling dynamic inputs with variable number of fields
 */
export function DynamicInput({
  inputDef,
  currentValue,
  onChange,
  isDisabled = false,
  isConnected = false,
  minInputs = 1,
  maxInputs = 10,
  defaultInputs = 2,
}: DynamicInputProps) {
  // Initialize with default number of inputs or current value length
  const [numInputs, setNumInputs] = useState<number>(
    Array.isArray(currentValue) ? Math.max(currentValue.length, minInputs) : defaultInputs,
  );

  // Initialize values array
  const values = Array.isArray(currentValue) ? currentValue : Array(numInputs).fill("");

  // Add a new input field
  const addInput = () => {
    if (numInputs < maxInputs) {
      setNumInputs(numInputs + 1);
      // Update the values array with a new empty value
      const newValues = [...values, ""];
      onChange(inputDef.name, newValues);
    }
  };

  // Remove an input field
  const removeInput = (index: number) => {
    if (numInputs > minInputs) {
      setNumInputs(numInputs - 1);
      // Remove the value at the specified index
      const newValues = [...values];
      newValues.splice(index, 1);
      onChange(inputDef.name, newValues);
    }
  };

  // Update a specific input value
  const updateValue = (index: number, value: string) => {
    const newValues = [...values];
    newValues[index] = value;
    onChange(inputDef.name, newValues);
  };

  return (
    <div className="space-y-2">
      {/* Input fields */}
      {Array.from({ length: numInputs }).map((_, index) => (
        <div key={index} className="flex items-center gap-2">
          <div className={cn("relative", isConnected && isDisabled && "connected")}>
            <Input
              id={`${inputDef.name}_${index}`}
              type="text"
              value={values[index]}
              onChange={(e) => updateValue(index, e.target.value)}
              disabled={isDisabled}
              className="bg-background/50 mt-1 h-8 text-xs"
            />
            {isConnected && isDisabled && (
              <ConnectedIndicator className="absolute top-1/2 right-2 -translate-y-1/2" />
            )}
          </div>
          {numInputs > minInputs && (
            <Button
              variant="ghost"
              size="icon"
              className="text-destructive/70 hover:text-destructive h-8 w-8"
              onClick={() => removeInput(index)}
              disabled={isDisabled}
            >
              <MinusCircle className="h-4 w-4" />
            </Button>
          )}
        </div>
      ))}

      {/* Add button */}
      {numInputs < maxInputs && (
        <Button
          variant="outline"
          size="sm"
          className="mt-2 h-7 w-full border-dashed text-xs"
          onClick={addInput}
          disabled={isDisabled}
        >
          <PlusCircle className="mr-1 h-3.5 w-3.5" /> Add Input
        </Button>
      )}
    </div>
  );
}
