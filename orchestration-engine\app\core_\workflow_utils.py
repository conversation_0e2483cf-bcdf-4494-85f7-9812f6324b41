import json
import re
from typing import Any, Dict, List, Optional, Union
from jsonschema import Draft7Validator
import warnings
from app.utils.enhanced_logger import get_logger
from app.utils.helper_functions import load_schema, fix_invalid_escapes
from app.utils.agentic_operations import resolve_switch_case, format_schema
from app.config.config import settings
from ..core_.state_manager import WorkflowStateManager

logger = get_logger("WorkflowUtils")


class WorkflowUtils:

    def __init__(self, workflow_id=None):
        self.logger = logger
        self.logger.info("WorkflowUtils initialized")
        self.state_manager = WorkflowStateManager(workflow_id)
        self.ENHANCED_SCHEMA = load_schema(settings.schema_file_path)

    def _validate_schema(self, workflow_json: dict):
        """
        Validate the input workflow against the Enhanced Workflow Schema.
        Raises jsonschema.exceptions.ValidationError on failure.
        """
        validator = Draft7Validator(self.ENHANCED_SCHEMA)
        validator.validate(workflow_json)
        self.logger.info("Workflow JSON is valid against the enhanced schema.")

    async def _format_tool_parameters(
        self,
        node_tool_info: dict,  # Kept for compatibility with existing calls
        input_data_configs: dict,
        transition_id: str,
        current_tool_params: dict,
    ) -> dict:
        """
        Format tool parameters by replacing placeholders with values from previous results.

        Looks for values in the format ${field_name} in current_tool_params and replaces them
        with corresponding values from previous transition results.

        If direct replacement fails or doesn't fully resolve all placeholders, falls back to
        using the format_schema function which uses an AI agent for more complex formatting.

        Always returns a dictionary with direct key-value pairs.
        """
        # Store transition_id for logging purposes
        self.logger.debug(f"Formatting tool parameters for transition: {transition_id}")
        all_previous_results = {}
        current_previous_results = {}  # For format_schema compatibility

        # Collect all results from specified transitions
        if input_data_configs:
            for input_data_config in input_data_configs:
                from_transition_id = input_data_config.get("from_transition_id")
                if from_transition_id:
                    result_from_dependency = self.state_manager.get_transition_result(
                        from_transition_id
                    )
                    if result_from_dependency:
                        all_previous_results[from_transition_id] = (
                            result_from_dependency
                        )
                        current_previous_results[from_transition_id] = (
                            result_from_dependency
                        )
                        self.logger.debug(
                            f"Found results from transition {from_transition_id}: {result_from_dependency}"
                        )

        # If we have no previous results, convert the current params to dictionary format
        if not all_previous_results:
            self.logger.debug(
                "No previous results found, converting current params to dictionary format"
            )
            # Convert the current params to dictionary format if they're in list format
            if isinstance(current_tool_params, list):
                result_dict = {}
                for param_item in current_tool_params:
                    if isinstance(param_item, dict):
                        field_name = param_item.get("field_name")
                        field_value = param_item.get("field_value")
                        if field_name:
                            result_dict[field_name] = field_value
                return result_dict
            elif isinstance(current_tool_params, dict):
                return current_tool_params
            else:
                return {}

        # Create a flattened dictionary of all field values from all previous results
        # This makes it easier to look up values by field name
        flattened_results = {}
        for transition_results in all_previous_results.values():
            if isinstance(transition_results, dict):
                flattened_results.update(transition_results)

        self.logger.debug(f"Flattened previous results: {flattened_results}")

        try:
            # Process the current_tool_params to replace placeholders
            # This now always returns a dictionary with direct key-value pairs
            processed_params, has_unresolved_placeholders = (
                self._process_params_for_placeholders(
                    current_tool_params, flattened_results
                )
            )

            # If we have unresolved placeholders, fall back to format_schema
            if has_unresolved_placeholders:
                self.logger.info(
                    f"Direct placeholder replacement couldn't resolve all placeholders for transition {transition_id}. Falling back to AI-based formatting."
                )
                processed_params = await self._fallback_to_format_schema(
                    node_tool_info, current_previous_results, current_tool_params
                )

                # Convert the result to dictionary format if it's not already
                if isinstance(processed_params, list):
                    result_dict = {}
                    for param_item in processed_params:
                        if isinstance(param_item, dict):
                            field_name = param_item.get("field_name")
                            field_value = param_item.get("field_value")
                            if field_name:
                                result_dict[field_name] = field_value
                    return result_dict
                elif isinstance(processed_params, dict):
                    return processed_params
                else:
                    return {}

            return processed_params

        except Exception as e:
            self.logger.warning(
                f"Error during direct placeholder replacement for transition {transition_id}: {str(e)}. Falling back to AI-based formatting."
            )
            processed_params = await self._fallback_to_format_schema(
                node_tool_info, current_previous_results, current_tool_params
            )

            # Convert the result to dictionary format if it's not already
            if isinstance(processed_params, list):
                result_dict = {}
                for param_item in processed_params:
                    if isinstance(param_item, dict):
                        field_name = param_item.get("field_name")
                        field_value = param_item.get("field_value")
                        if field_name:
                            result_dict[field_name] = field_value
                return result_dict
            elif isinstance(processed_params, dict):
                return processed_params
            else:
                return {}

    def _format_params_according_to_schema(self, node_tool_info, params):
        """
        Format parameters according to the input schema structure.

        Args:
            node_tool_info: Dictionary containing the input schema
            params: Parameters to format (can be a list or dict)

        Returns:
            dict: Parameters formatted according to the input schema
        """
        input_schema = node_tool_info.get("input_schema", {})

        # If there's no input schema, return the params as is
        if not input_schema:
            return params

        # Extract the predefined fields from the input schema
        predefined_fields = input_schema.get("predefined_fields", [])
        if not predefined_fields:
            return params

        # Create a dictionary to store the formatted parameters
        formatted_params = {}

        # Convert params to a dictionary if it's a list
        param_dict = {}
        if isinstance(params, list):
            for param_item in params:
                if isinstance(param_item, dict):
                    field_name = param_item.get("field_name")
                    field_value = param_item.get("field_value")
                    if field_name:
                        param_dict[field_name] = field_value
        else:
            param_dict = params if isinstance(params, dict) else {}

        # Fill in the values for each field in the input schema
        for field in predefined_fields:
            field_name = field.get("field_name")
            if not field_name:
                continue

            # Get the data type for this field
            data_type = field.get("data_type", {})
            field_type = data_type.get("type", "string")

            # Get the value for this field from the params
            field_value = param_dict.get(field_name)

            # Handle nested objects
            if field_type == "object" and isinstance(field_value, dict):
                # For object types, include the nested properties
                formatted_params[field_name] = field_value
            else:
                # For other types, just include the value
                formatted_params[field_name] = field_value

        self.logger.debug(
            f"Formatted parameters according to schema: {formatted_params}"
        )
        return formatted_params

    def _process_params_for_placeholders(self, params, flattened_results):
        """
        Process parameters to replace placeholders with values from flattened_results.
        Always returns a dictionary with direct key-value pairs.

        Args:
            params: The parameters to process (can be a list or dict)
            flattened_results: Dictionary of field values from previous results

        Returns:
            tuple: (processed_params, has_unresolved_placeholders)
        """
        has_unresolved_placeholders = False
        processed_params = {}

        if isinstance(params, list):
            # Convert list format to dictionary format
            for param_item in params:
                if isinstance(param_item, dict):
                    field_name = param_item.get("field_name", "")
                    field_value = param_item.get("field_value", "")

                    if not field_name:
                        continue

                    # Check if field_value contains a placeholder
                    if (
                        isinstance(field_value, str)
                        and "${" in field_value
                        and "}" in field_value
                    ):
                        # Extract the placeholder name
                        placeholder_match = re.search(r"\${(.*?)}", field_value)
                        if placeholder_match:
                            placeholder = placeholder_match.group(1)
                            self.logger.debug(
                                f"Found placeholder: {placeholder} in field: {field_name}"
                            )

                            # Look for the placeholder in flattened results
                            if placeholder in flattened_results:
                                field_value = flattened_results[placeholder]
                                self.logger.debug(
                                    f"Replaced placeholder ${{{placeholder}}} with value: {field_value}"
                                )
                            else:
                                # Mark that we have unresolved placeholders
                                has_unresolved_placeholders = True
                                self.logger.debug(
                                    f"Could not resolve placeholder ${{{placeholder}}} in field: {field_name}"
                                )

                    # Handle nested objects (like keywords)
                    if isinstance(field_value, dict):
                        # Process nested dictionary for placeholders
                        nested_dict, nested_unresolved = self._process_nested_dict(
                            field_value, flattened_results
                        )
                        if nested_unresolved:
                            has_unresolved_placeholders = True
                        processed_params[field_name] = nested_dict
                    else:
                        processed_params[field_name] = field_value

            return processed_params, has_unresolved_placeholders

        elif isinstance(params, dict):
            # Handle case where params is already a dictionary
            for key, value in params.items():
                if isinstance(value, str) and "${" in value and "}" in value:
                    # Extract the placeholder name
                    placeholder_match = re.search(r"\${(.*?)}", value)
                    if placeholder_match:
                        placeholder = placeholder_match.group(1)
                        self.logger.debug(
                            f"Found placeholder: {placeholder} in key: {key}"
                        )

                        # Look for the placeholder in flattened results
                        if placeholder in flattened_results:
                            value = flattened_results[placeholder]
                            self.logger.debug(
                                f"Replaced placeholder ${{{placeholder}}} with value: {value}"
                            )
                        else:
                            # Mark that we have unresolved placeholders
                            has_unresolved_placeholders = True
                            self.logger.debug(
                                f"Could not resolve placeholder ${{{placeholder}}} in key: {key}"
                            )
                elif isinstance(value, dict):
                    # Process nested dictionary for placeholders
                    nested_dict, nested_unresolved = self._process_nested_dict(
                        value, flattened_results
                    )
                    if nested_unresolved:
                        has_unresolved_placeholders = True
                    value = nested_dict

                processed_params[key] = value

            return processed_params, has_unresolved_placeholders

        # If params is neither a list nor a dict, return an empty dict
        return {}, False

    def _process_nested_dict(self, nested_dict, flattened_results):
        """
        Process a nested dictionary for placeholders.

        Args:
            nested_dict: The nested dictionary to process
            flattened_results: Dictionary of field values from previous results

        Returns:
            tuple: (processed_nested_dict, has_unresolved_placeholders)
        """
        has_unresolved_placeholders = False
        processed_nested_dict = {}

        for key, value in nested_dict.items():
            if isinstance(value, str) and "${" in value and "}" in value:
                # Extract the placeholder name
                placeholder_match = re.search(r"\${(.*?)}", value)
                if placeholder_match:
                    placeholder = placeholder_match.group(1)
                    self.logger.debug(
                        f"Found placeholder: {placeholder} in nested key: {key}"
                    )

                    # Look for the placeholder in flattened results
                    if placeholder in flattened_results:
                        value = flattened_results[placeholder]
                        self.logger.debug(
                            f"Replaced placeholder ${{{placeholder}}} with value: {value}"
                        )
                    else:
                        # Mark that we have unresolved placeholders
                        has_unresolved_placeholders = True
                        self.logger.debug(
                            f"Could not resolve placeholder ${{{placeholder}}} in nested key: {key}"
                        )
            elif isinstance(value, dict):
                # Recursively process nested dictionaries
                value, nested_unresolved = self._process_nested_dict(
                    value, flattened_results
                )
                if nested_unresolved:
                    has_unresolved_placeholders = True

            processed_nested_dict[key] = value

        return processed_nested_dict, has_unresolved_placeholders

    async def _fallback_to_format_schema(
        self,
        node_tool_info: dict,
        current_previous_results: dict,
        current_tool_params: dict,
    ) -> dict:
        """
        Fallback method that uses format_schema (AI-based) to format tool parameters.
        Used when direct placeholder replacement fails or doesn't fully resolve all placeholders.
        """
        input_schema = node_tool_info.get("input_schema", {})
        output_schema = node_tool_info.get("output_schema", {})

        self.logger.info(
            "Using AI-based format_schema as fallback for parameter formatting"
        )

        try:
            formatted_params = await format_schema(
                input_schema,
                output_schema,
                current_previous_results,
                current_tool_params,
            )
            return formatted_params
        except Exception as e:
            self.logger.error(f"AI-based formatting also failed: {str(e)}")
            # If AI-based formatting also fails, return the original params
            return current_tool_params

    async def _evaluate_switch_case(
        self,
        transition_routing: Dict[str, Any],
        node_result: Any,
    ) -> List[str]:
        """
        Evaluates conditional routing cases based on node output and global context.
        Global context is expected to follow the schema structure with 'variables' array.
        Returns a list of all matching transitions. If no conditions match, returns a list
        containing only the default transition (if provided).
        """
        cases: List[Dict[str, Any]] = transition_routing.get("cases", [])
        default_transition: Optional[str] = transition_routing.get("default_transition")
        global_context_defs = transition_routing.get("global_context_definitions", {})
        matching_transitions: List[str] = []

        self.logger.info(
            f"Evaluating conditional routing with {len(cases)} cases. Default: '{default_transition}'"
        )
        self.logger.debug(f"Context - Node Result: {node_result}")
        self.logger.debug(
            f"Context - Global Context Definitions: {global_context_defs}"
        )

        if not cases:
            self.logger.warning(
                "No cases defined in transition routing. Returning default."
            )
            return [default_transition] if default_transition else []

        def find_global_variable(var_name: str) -> Optional[Any]:
            """Helper function to find a variable in global_context_definitions"""
            if not global_context_defs:
                return None

            variables = global_context_defs.get("variables", [])
            for var in variables:
                if var.get("name") == var_name:
                    return var.get("value")
            return None

        for i, case in enumerate(cases):
            condition = case.get("condition")
            next_transition = case.get("next_transition")

            if not condition or not next_transition:
                self.logger.warning(
                    f"Skipping invalid case {i+1} (missing condition or next_transition)"
                )
                continue

            source = condition.get("source", "node_output")
            operator = condition.get("operator")
            variable_name = condition.get("variable_name")
            expected_value = condition.get("expected_value")

            actual_value = None
            if source == "node_output":
                actual_value = node_result
            elif source == "global_context":
                if variable_name:
                    actual_value = find_global_variable(variable_name)
                    if actual_value is None:
                        self.logger.warning(
                            f"Case {i+1}: Global context variable '{variable_name}' not found"
                        )
                else:
                    self.logger.warning(
                        f"Case {i+1}: Missing variable_name for global_context source"
                    )

            self.logger.debug(
                f"Case {i+1}: Source='{source}', Variable='{variable_name}', "
                f"Operator='{operator}', Expected='{expected_value}', Actual='{actual_value}'"
            )

            try:
                if operator == "ai_evaluate":
                    match = await resolve_switch_case(actual_value, expected_value)
                elif operator == "exists":
                    if source == "global_context":
                        match = any(
                            var.get("name") == variable_name
                            for var in global_context_defs.get("variables", [])
                        )
                    else:
                        match = actual_value is not None
                elif operator == "is_empty":
                    match = (
                        actual_value is None
                        or actual_value == ""
                        or actual_value == []
                        or actual_value == {}
                    )
                elif operator == "equals":
                    match = actual_value == expected_value
                elif operator == "not_equals":
                    match = actual_value != expected_value
                elif operator == "contains":
                    match = (
                        str(expected_value) in str(actual_value)
                        if actual_value is not None
                        else False
                    )
                elif operator == "starts_with":
                    match = (
                        str(actual_value).startswith(str(expected_value))
                        if actual_value is not None
                        else False
                    )
                elif operator == "ends_with":
                    match = (
                        str(actual_value).endswith(str(expected_value))
                        if actual_value is not None
                        else False
                    )
                elif operator == "greater_than":
                    match = (
                        float(actual_value) > float(expected_value)
                        if actual_value is not None
                        else False
                    )
                elif operator == "less_than":
                    match = (
                        float(actual_value) < float(expected_value)
                        if actual_value is not None
                        else False
                    )
                else:
                    self.logger.warning(
                        f"Case {i+1}: Unsupported operator '{operator}'"
                    )
                    continue

                if match:
                    self.logger.info(
                        f"Case {i+1} matched. Adding transition '{next_transition}' to routing list"
                    )
                    matching_transitions.append(next_transition)

            except Exception as e:
                self.logger.error(f"Case {i+1}: Error evaluating condition: {str(e)}")
                continue

        if not matching_transitions and default_transition:
            self.logger.info(
                f"No conditions matched. Adding default transition: '{default_transition}'"
            )
            matching_transitions.append(default_transition)

        self.logger.info(f"Final routing transitions: {matching_transitions}")
        return matching_transitions
