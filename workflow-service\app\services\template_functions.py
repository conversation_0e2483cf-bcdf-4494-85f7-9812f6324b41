# app/services/workflow_service.py
import grpc
import json
import structlog
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowTemplate
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
)
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from app.utils.json_validator import validate_transition_schema
from app.utils.file_upload import GCSUploadService
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum
from app.core.config import settings

# Initialize structured logger
logger = structlog.get_logger()


class WorkflowTemplateFunctions(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowService with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def createWorkflowFromTemplate(
        self, request: workflow_pb2.CreateWorkflowFromTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CreateWorkflowFromTemplateResponse:
        """
        Creates a new workflow by copying from a template.

        Args:
            request: The request containing template_id and owner information
            context: The gRPC context for handling errors

        Returns:
            Response indicating success or failure
        """
        db = self.get_db()
        logger.info("create_workflow_from_template_request", template_id=request.template_id)
        try:
            # Get the template
            template = (
                db.query(WorkflowTemplate)
                .filter(WorkflowTemplate.id == request.template_id)
                .first()
            )
            if not template:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Template with ID {request.template_id} not found")
                return workflow_pb2.CreateWorkflowFromTemplateResponse(
                    success=False, message="Template not found"
                )

            # Create new workflow from template
            new_workflow = Workflow(
                # Primary Fields
                name=template.name,  # Use template name as default
                description=template.description,
                workflow_url=template.workflow_url,
                builder_url=template.builder_url,
                start_nodes=template.start_nodes if template.start_nodes else [],
                # Access Control
                owner_id=request.owner.id,
                user_ids=[request.owner.id],  # Default to owner's ID for new workflows
                owner_type=workflow_pb2.WorkflowOwnerType.Name(request.owner_type).lower(),
                # Template Reference
                workflow_template_id=template.id,  # Reference to the source template
                template_owner_id=template.owner_id,  # Owner of the source template=None,  # New workflow, no parent
                url=None,  # New workflow, no URL
                is_imported=True,  # Mark as imported from template
                # Metadata
                visibility=WorkflowVisibilityEnum.PRIVATE.value,  # Default to private for new workflows
                category=template.category,
                tags=template.tags if template.tags else [],
                status=WorkflowStatusEnum.ACTIVE.value,  # Default to active for new workflows
                version=template.version,  # Use template version
                is_changes_marketplace=False,  # Not a marketplace change by default
            )

            template.use_count += 1

            db.add(new_workflow)
            db.add(template)
            db.commit()
            db.refresh(new_workflow)

            # Update template execution count
            template.execution_count += 1
            db.commit()

            # # Send Kafka notification
            # self.kafka_producer.send_email_event_unified(
            #     email_type=SendEmailTypeEnum.WORKFLOW_CREATED.value,
            #     data={
            #         "emailId": request.owner.email,
            #         "userName": request.owner.full_name,
            #         "userId": request.owner.id,
            #         "fcmToken": request.owner.fcm_token,
            #         "workflowId": new_workflow.id,
            #         "workflowName": new_workflow.name,
            #         "title": "New Workflow Created from Template",
            #         "body": f"Your workflow '{new_workflow.name}' has been created from template successfully.",
            #         "link": f"{settings.FRONTEND_URL}/workflows/{new_workflow.id}",
            #         "logo": f"{settings.FRONTEND_URL}/assets/logo.png",
            #     },
            #     action=["sendNotification", "sendWorkflowEmail"],
            # )

            return workflow_pb2.CreateWorkflowFromTemplateResponse(
                success=True, message="Workflow created from template successfully"
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            logger.error("workflow_creation_from_template_failed", error=str(e))
            return workflow_pb2.CreateWorkflowFromTemplateResponse(
                success=False, message="Internal server error"
            )
        finally:
            db.close()

    def createWorkflowTemplate(
        self, request: workflow_pb2.CreateTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CreateTemplateResponse:
        db = SessionLocal()
        logger.info("create_workflow_template_request", name=request.name)
        print(f"[DEBUG] Starting workflow template creation for: {request.name}")

        try:
            # Parse workflow_data from string to JSON
            print("[DEBUG] Attempting to parse workflow_data JSON")
            try:
                workflow_data = json.loads(request.workflow_data)
                print("[DEBUG] Successfully parsed workflow_data JSON")
            except json.JSONDecodeError as e:
                print(f"[DEBUG] JSON parsing failed: {str(e)}")
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid JSON format in workflow_data")
                return workflow_pb2.CreateTemplateResponse(
                    success=False, message="Invalid JSON format in workflow_data"
                )

            # # Validate against workflow_capture_schema.json
            # print("[DEBUG] Validating against workflow_capture_schema.json")
            # try:
            #     validate_transition_schema(
            #         workflow_data, "app/services/shared/json_schemas/validate_capture_schema.json"
            #     )
            #     print("[DEBUG] Workflow capture schema validation successful")
            # except Exception as e:
            #     print(f"[DEBUG] Workflow capture schema validation failed: {str(e)}")
            #     context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            #     context.set_details(f"Workflow capture schema validation failed: {str(e)}")
            #     return workflow_pb2.CreateTemplateResponse(
            #         success=False, message=f"Workflow capture schema validation failed: {str(e)}"
            #     )

            # Upload original workflow to GCS
            # print("[DEBUG] Uploading workflow to GCS")
            try:
                file_upload = GCSUploadService()
                gcs_response = file_upload.upload_json_as_file(workflow_data, "template_builders")
                builder_url = gcs_response.get("publicUrl")
                print("[DEBUG] GCS upload successful, got builder_url")
                if not builder_url:
                    print("[DEBUG] Failed to get public URL from GCS response")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details("Failed to obtain public URL from GCS for builder workflow")
                    return workflow_pb2.CreateTemplateResponse(
                        success=False,
                        message="Failed to obtain public URL from GCS for builder workflow",
                    )
            except Exception as e:
                print(f"[DEBUG] GCS upload failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"GCS upload failed for builder workflow: {str(e)}")
                return workflow_pb2.CreateTemplateResponse(
                    success=False, message=f"GCS upload failed for builder workflow: {str(e)}"
                )

            # Convert workflow to transition schema
            print("[DEBUG] Converting workflow to transition schema")
            try:
                converted_workflow = convert_workflow_to_transition_schema(workflow_data)
                print("[DEBUG] Workflow conversion successful")
            except Exception as e:
                print(f"[DEBUG] Workflow conversion failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Workflow schema conversion failed: {str(e)}")
                return workflow_pb2.CreateTemplateResponse(
                    success=False, message=f"Workflow schema conversion failed: {str(e)}"
                )

            # Validate converted workflow against transition_schema.json
            print("[DEBUG] Validating against transition_schema.json")
            try:
                validate_transition_schema(
                    data_input=converted_workflow,
                    schema_path="app/utils/shared/json_schemas/transition_schema.json",
                )
                print("[DEBUG] Transition schema validation successful")
            except Exception as e:
                print(f"[DEBUG] Transition schema validation failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Transition schema validation failed: {str(e)}")
                return workflow_pb2.CreateTemplateResponse(
                    success=False, message=f"Transition schema validation failed: {str(e)}"
                )

            # Upload converted workflow to GCS
            print("[DEBUG] Uploading converted workflow to GCS")
            try:
                gcs_response = file_upload.upload_json_as_file(
                    converted_workflow, "workflow_templates"
                )
                workflow_url = gcs_response.get("publicUrl")
                print("[DEBUG] Converted workflow upload successful")
                if not workflow_url:
                    print("[DEBUG] Failed to get public URL for converted workflow")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(
                        "Failed to obtain public URL from GCS for converted workflow"
                    )
                    return workflow_pb2.CreateTemplateResponse(
                        success=False,
                        message="Failed to obtain public URL from GCS for converted workflow",
                    )
            except Exception as e:
                print(f"[DEBUG] Converted workflow GCS upload failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"GCS upload failed for converted workflow: {str(e)}")
                return workflow_pb2.CreateTemplateResponse(
                    success=False, message=f"GCS upload failed for converted workflow: {str(e)}"
                )

            # Create new workflow template
            print("[DEBUG] Creating new workflow template object")
            template = WorkflowTemplate(
                name=request.name,
                description=request.description,
                workflow_url=workflow_url,
                start_nodes=list(request.start_nodes) if request.start_nodes else [],
                builder_url=builder_url,
                category=workflow_pb2.WorkflowCategory.Name(request.category).lower(),
                tags=list(request.tags) if request.tags else [],
                status=workflow_pb2.WorkflowStatus.Name(request.status).lower(),
                owner_id=request.owner.id,
            )

            print("[DEBUG] Adding workflow template to database")
            db.add(template)
            db.commit()
            db.refresh(template)
            print("[DEBUG] Workflow template saved to database successfully")

            print("[DEBUG] Workflow template creation completed successfully")
            return workflow_pb2.CreateTemplateResponse(
                success=True, message=f"Workflow template {request.name} created successfully"
            )

        except Exception as e:
            print(f"[DEBUG] Unexpected error in createWorkflowTemplate: {str(e)}")
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("workflow_template_creation_failed", error=str(e))
            return workflow_pb2.CreateTemplateResponse(
                success=False, message=f"Workflow template creation failed: {str(e)}"
            )
        finally:
            print("[DEBUG] Closing database connection")
            db.close()

    def getTemplate(
        self, request: workflow_pb2.GetTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetTemplateResponse:
        """
        Retrieve a workflow template by its ID.

        Args:
            request: The request containing the template ID.
            context: The gRPC context for handling the request.

        Returns:
            Response containing the template details if found.
        """
        db = self.get_db()
        logger.info("get_template_request", template_id=request.id)
        try:
            template = db.query(WorkflowTemplate).filter(WorkflowTemplate.id == request.id).first()
            if template is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow template not found")
                return workflow_pb2.GetTemplateResponse(
                    success=False, message="Workflow template not found"
                )

            logger.info("template_retrieved", template_id=template.id)

            # Convert template to protobuf
            template_proto = self._template_to_protobuf(template)

            # Check if user_id is provided and if the user has already added this template
            is_added = False
            if request.HasField("user_id"):
                # Check if there are any workflows created from this template by the user
                user_workflows = (
                    db.query(Workflow)
                    .filter(
                        Workflow.workflow_template_id == template.id,
                        Workflow.owner_id == request.user_id,
                    )
                    .first()
                )
                is_added = user_workflows is not None
                logger.info(
                    "checking_if_user_added_template",
                    user_id=request.user_id,
                    template_id=template.id,
                    is_added=is_added,
                )

            # Set the is_added field
            template_proto.is_added = is_added

            print(f"[DEBUG] Template retrieved: {template_proto}")
            return workflow_pb2.GetTemplateResponse(
                success=True,
                message=f"Workflow template {template.name} retrieved successfully",
                template=template_proto,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.GetTemplateResponse(success=False, message="Internal Server Error")
        finally:
            db.close()

    def listTemplates(
        self, request: workflow_pb2.ListTemplatesRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListTemplatesResponse:
        """
        Retrieve a paginated list of workflow templates.

        Args:
            request: The request object containing filtering and pagination parameters.
            context: The gRPC context for handling errors and metadata.

        Returns:
            A response containing the list of templates and pagination details.
        """
        db = self.get_db()
        page = request.page if request.page > 0 else 1
        page_size = request.page_size if request.page_size > 0 else 10
        logger.info(f"list_templates_request {request}")
        try:
            # Build query with filters
            query = db.query(WorkflowTemplate)

            if request.category:
                query = query.filter(WorkflowTemplate.category == request.category)

            if request.status:
                status = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                query = query.filter(WorkflowTemplate.status == status)

            if request.visibility:
                visibility = workflow_pb2.WorkflowVisibility.Name(request.visibility).lower()
                query = query.filter(WorkflowTemplate.visibility == visibility)

            # Add search filter for name, description, and category
            if request.search:
                search_term = f"%{request.search}%"
                logger.info("filtering_by_search", search=request.search)
                query = query.filter(
                    db.or_(
                        WorkflowTemplate.name.ilike(search_term),
                        WorkflowTemplate.description.ilike(search_term),
                        WorkflowTemplate.category.ilike(search_term),
                    )
                )

            # Add tags filter
            if request.tags:
                logger.info("filtering_by_tags", tags=request.tags)
                # Filter by tags (exact match for any tag in the list)
                query = query.filter(WorkflowTemplate.tags.contains(request.tags))

            # Get total count before pagination
            total = query.count()

            # Apply pagination
            templates = query.offset((page - 1) * page_size).limit(page_size).all()

            total_pages = (total + page_size - 1) // page_size  # Calculate total pages

            template_list = [self._template_to_protobuf(template) for template in templates]

            logger.info("templates_retrieved", total=total, page=page, total_pages=total_pages)

            print(f"[DEBUG] Template list: {template_list}")

            return workflow_pb2.ListTemplatesResponse(
                success=True,
                message="Successfully retrieved templates.",
                templates=template_list,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.ListTemplatesResponse(success=False)
        finally:
            db.close()

    def updateTemplate(
        self, request: workflow_pb2.UpdateTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UpdateTemplateResponse:
        db = self.get_db()
        logger.info(
            "update_template_request (PATCH)",
            template_id=request.id,
            update_mask=request.update_mask.paths,
        )
        print(
            f"[DEBUG] Starting workflow template PATCH for ID: {request.id} with mask: {request.update_mask.paths}"
        )
        try:
            template = db.query(WorkflowTemplate).filter(WorkflowTemplate.id == request.id).first()
            if not template:
                print(f"[DEBUG] Workflow template with ID {request.id} not found")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow template with ID {request.id} not found")
                # Important: return here so the context manager doesn't try to commit
                return workflow_pb2.UpdateTemplateResponse(
                    success=False, message=f"Workflow template with ID {request.id} not found"
                )

            file_upload_service = GCSUploadService()  # Initialize if needed

            # Process workflow_data only if it's in the update_mask
            if "workflow_data" in request.update_mask.paths:
                print("[DEBUG] 'workflow_data' is in update_mask for template. Processing...")
                if not request.workflow_data:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(
                        "workflow_data field is in update_mask but no data provided for template."
                    )
                    return workflow_pb2.UpdateTemplateResponse(
                        success=False,
                        message="workflow_data provided in mask but is empty for template.",
                    )
                try:
                    parsed_workflow_data = json.loads(request.workflow_data)
                except json.JSONDecodeError as e:
                    # (Error handling as before)
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid JSON format in workflow_data for template")
                    return workflow_pb2.UpdateTemplateResponse(
                        success=False, message="Invalid JSON format in workflow_data for template"
                    )

                # Upload original workflow (builder schema) to GCS
                try:
                    gcs_response_builder = file_upload_service.upload_json_as_file(
                        parsed_workflow_data, "template_builders"
                    )
                    builder_url = gcs_response_builder.get("publicUrl")
                    if not builder_url:
                        raise Exception("Failed to get builder URL")
                    template.builder_url = builder_url
                except Exception as e:
                    # (Error handling)
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(
                        f"GCS upload failed for template builder workflow: {str(e)}"
                    )
                    return workflow_pb2.UpdateTemplateResponse(
                        success=False,
                        message=f"GCS upload failed for template builder workflow: {str(e)}",
                    )

                # Convert to transition schema (runtime schema)
                try:
                    converted_workflow = convert_workflow_to_transition_schema(parsed_workflow_data)
                except Exception as e:
                    # (Error handling)
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"Workflow schema conversion failed for template: {str(e)}")
                    return workflow_pb2.UpdateTemplateResponse(
                        success=False,
                        message=f"Workflow schema conversion failed for template: {str(e)}",
                    )

                # Validate converted workflow against transition_schema.json
                try:
                    validate_transition_schema(
                        data_input=converted_workflow,
                        schema_path="app/utils/shared/json_schemas/transition_schema.json",
                    )
                except Exception as e:
                    # (Error handling)
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(
                        f"Transition schema validation failed for template: {str(e)}"
                    )
                    return workflow_pb2.UpdateTemplateResponse(
                        success=False,
                        message=f"Transition schema validation failed for template: {str(e)}",
                    )

                # Upload converted workflow (runtime schema) to GCS
                try:
                    gcs_response_workflow = file_upload_service.upload_json_as_file(
                        converted_workflow, "workflow_templates"
                    )
                    workflow_url = gcs_response_workflow.get("publicUrl")
                    if not workflow_url:
                        raise Exception("Failed to get workflow URL")
                    template.workflow_url = workflow_url
                except Exception as e:
                    # (Error handling)
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(
                        f"GCS upload failed for converted template workflow: {str(e)}"
                    )
                    return workflow_pb2.UpdateTemplateResponse(
                        success=False,
                        message=f"GCS upload failed for converted template workflow: {str(e)}",
                    )

            # Update other fields based on FieldMask
            for field_path in request.update_mask.paths:
                if field_path == "name":
                    template.name = request.name
                elif field_path == "description":
                    template.description = request.description
                elif field_path == "start_nodes":
                    template.start_nodes = list(request.start_nodes)
                elif field_path == "category":
                    template.category = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                elif field_path == "tags":
                    template.tags = list(request.tags) if request.tags else []
                elif field_path == "version":
                    template.version = request.version
                elif field_path == "status":
                    template.status = workflow_pb2.WorkflowStatus.Name(request.status).lower()

            db.commit()

            return workflow_pb2.UpdateTemplateResponse(
                success=True,
                message=f"Workflow template {template.name} updated successfully via PATCH",
            )

        except (
            grpc.RpcError
        ):  # To allow specific gRPC errors set within the try block to pass through
            raise
        except Exception as e:
            print(f"[DEBUG] Unexpected error in updateTemplate (PATCH): {str(e)}")
            # DB rollback/close is handled by the context manager
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error during template PATCH: {str(e)}")
            logger.error("workflow_template_patch_failed", error=str(e), exc_info=True)
            return workflow_pb2.UpdateTemplateResponse(
                success=False, message=f"Workflow template PATCH update failed: {str(e)}"
            )

    def deleteTemplate(
        self, request: workflow_pb2.DeleteTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.DeleteTemplateResponse:
        """
        Delete a workflow template by its ID.

        Args:
            request: The request containing the template ID.
            context: The gRPC context for handling the request.

        Returns:
            Response indicating success or failure of the delete operation.
        """
        db = self.get_db()
        logger.info("delete_template_request", template_id=request.id)
        try:
            template = db.query(WorkflowTemplate).filter(WorkflowTemplate.id == request.id).first()
            if template is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow template not found")
                return workflow_pb2.DeleteTemplateResponse(
                    success=False, message="Workflow template not found"
                )

            db.delete(template)
            db.commit()
            logger.info("template_deleted", template_id=template.id)

            return workflow_pb2.DeleteTemplateResponse(
                success=True, message=f"Workflow template {template.name} deleted successfully"
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.DeleteTemplateResponse(
                success=False, message="Internal server error"
            )
        finally:
            db.close()

    def _template_to_protobuf(self, template: WorkflowTemplate) -> workflow_pb2.WorkflowTemplate:
        """Convert a WorkflowTemplate model instance to a protobuf WorkflowTemplate message."""
        return workflow_pb2.WorkflowTemplate(
            id=str(template.id),
            name=template.name,
            description=template.description,
            workflow_url=template.workflow_url,
            builder_url=template.builder_url,
            start_nodes=(
                [json.dumps(node) for node in template.start_nodes] if template.start_nodes else []
            ),
            use_count=template.use_count,
            owner_id=template.owner_id,
            execution_count=template.execution_count,
            category=template.category,
            tags=template.tags if template.tags else [],
            version=template.version,
            status=template.status,
            created_at=template.created_at.isoformat() if template.created_at else "",
            updated_at=template.updated_at.isoformat() if template.updated_at else "",
        )

    def listTemplatesByUserId(
        self, request: workflow_pb2.ListTemplatesByUserIdRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListTemplatesResponse:
        db = self.get_db()
        logger.info(
            "list_templates_by_user_request",
            owner_id=request.owner_id,
        )
        try:
            # Query templates for the given user ID
            templates = (
                db.query(WorkflowTemplate)
                .filter(WorkflowTemplate.owner_id == request.owner_id)
                .all()
            )

            # Convert to protobuf format
            template_list_proto = [self._template_to_protobuf(template) for template in templates]

            return workflow_pb2.ListTemplatesResponse(
                success=True,
                message=f"Successfully retrieved templates.",
                templates=template_list_proto,
                total=len(template_list_proto),
                page=1,
                total_pages=1,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("list_templates_by_user_failed", error=str(e))
            return workflow_pb2.ListTemplatesResponse(
                success=False, message="Internal server error"
            )
        finally:
            db.close()
