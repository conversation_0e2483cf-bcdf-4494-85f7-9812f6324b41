from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class PromptImprovementRequest(BaseModel):
    """Request model for prompt improvement."""

    original_prompt: str = Field(..., description="The original system prompt to improve")
    agent_context: Optional[Dict[str, Any]] = Field(
        None, description="Optional context about the agent (capabilities, purpose, etc.)"
    )


class PromptImprovementResponse(BaseModel):
    """Response model for prompt improvement."""

    original_prompt: str = Field(..., description="The original system prompt")
    improved_prompt: str = Field(..., description="The improved system prompt")
