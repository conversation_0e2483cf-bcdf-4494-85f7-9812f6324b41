[tool.poetry]
name = "api-gateway"
version = "0.1.0"
description = "API Gateway for RUH AI Core"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.109.0"
uvicorn = "^0.25.0"
python-jose = { extras = ["cryptography"], version = "^3.3.0" }
python-multipart = "^0.0.6"
pydantic = "^2.5.3"
pydantic-settings = "^2.1.0"
grpcio = "^1.60.0"
grpcio-tools = "^1.60.0"
redis = "^5.0.1"
structlog = "^24.1.0"
python-dotenv = "^1.0.0"
httpx = "^0.26.0"
email-validator = "^2.2.0"
requests = "^2.32.3"
kafka-python = "^2.1.1"
aiokafka = "^0.12.0"
bson = "^0.5.10"
livekit = "^1.0.6"
livekit-api = "^1.0.2"
google-cloud-storage = "^3.1.0"
openai = "^1.82.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.2"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.26.0"
black = "^23.12.1"
isort = "^5.13.2"
mypy = "^1.8.0"
pylint = "^3.0.3"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ["py311"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pylint.messages_control]
disable = ["C0111", "C0103", "C0330", "C0326"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=app --cov-report=term-missing"
asyncio_mode = "auto"
