import logging
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    kafka_bootstrap_servers: str = Field(
        default="localhost:9092", alias="KAFKA_BOOTSTRAP_SERVERS"
    )
    kafka_consumer_topic: str = Field(
        default="mcp-execution-request", alias="KAFKA_CONSUMER_TOPIC"
    )
    kafka_consumer_group_id: str = Field(
        default="mcp_executor_service", alias="KAFKA_CONSUMER_GROUP_ID"
    )
    kafka_results_topic: str = Field(default="mcp_results", alias="KAFKA_RESULTS_TOPIC")
    default_mcp_retries: int = Field(default=3, alias="DEFAULT_MCP_RETRIES")
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")
    max_concurrent_tasks: int = Field(default=10, alias="MAX_CONCURRENT_TASKS")

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        populate_by_name=True,
        extra="ignore",
    )


try:
    settings = Settings()
except Exception as e:
    print(f"FATAL: Error loading MCP Executor Service configuration settings: {e}")
    raise

log_level_int = getattr(logging, settings.log_level.upper(), logging.INFO)

logging.basicConfig(
    level=log_level_int,
    format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s] %(message)s",  # Using consistent format
    datefmt="%Y-%m-%d %H:%M:%S",
)
config_logger = logging.getLogger(__name__)
config_logger.info("MCP Executor Service configuration loaded successfully.")
config_logger.debug(f"MCP Executor Service loaded settings: {settings.model_dump()}")
