/**
 * Credentials related types
 */

export interface Credential {
  id: string;
  name: string;
  type: string;
  data: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface CredentialListItem {
  id: string;
  name: string;
  type: string;
  createdAt: string;
  updatedAt: string;
}

export interface CredentialType {
  id: string;
  name: string;
  description: string;
  fields: CredentialField[];
}

export interface CredentialField {
  id: string;
  name: string;
  type: string;
  required: boolean;
  description?: string;
  placeholder?: string;
}

export interface CreateCredentialRequest {
  name: string;
  type: string;
  data: Record<string, any>;
}

export interface UpdateCredentialRequest {
  id: string;
  name?: string;
  data?: Record<string, any>;
}

export interface CredentialState {
  credentials: CredentialListItem[];
  credentialTypes: CredentialType[];
  currentCredential: Credential | null;
  isLoading: boolean;
  error: string | null;
}
