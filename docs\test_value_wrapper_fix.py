#!/usr/bin/env python3
"""
Test the fix for the 'value' wrapper issue in SelectDataComponent.

This test simulates the exact scenario from the production logs where
the input data is wrapped in a 'value' field.
"""

import asyncio
import sys
import os

# Add paths for both services
workflow_service_path = os.path.join(os.path.dirname(__file__), "workflow-service", "app")
node_executor_path = os.path.join(os.path.dirname(__file__), "node-executor-service", "app")

sys.path.insert(0, workflow_service_path)
sys.path.insert(0, node_executor_path)

# Import components from both services
try:
    from components.processing.select_data import SelectDataComponent
    from models.workflow_builder.context import WorkflowContext
    from components.select_data_component import SelectDataExecutor
    print("✓ Successfully imported components from both services")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


async def test_value_wrapper_scenario():
    """Test the exact scenario from production logs."""
    print("\n=== Testing Value Wrapper Scenario ===")
    
    # This is the exact data structure from the production logs
    wrapped_data = {
        "value": '{"data": {"title": "${audio_script} transition-MCP_Audio_Generator_generate_audio-1747991748064", "script": "**[Background Music: Light, professional tone]**\\n\\n**[Male Speaker: Friendly and Professional]**\\n\\n\\"Hey, have you ever thought... what if you could revolutionize your marketing approach in just, well, thirty seconds? Whoa, yeah, it\'s possible! Our mission is to help professionals like you achieve real results. Isn\'t that what your business deserves?\\n\\nImagine... just for a moment... having innovative marketing strategies at your fingertips, strategies that align perfectly with your goals. We know you\'re all about growth, visibility, and success. So, why not let us help elevate your game?\\n\\nJoin the ranks of visionary professionals who are already seeing those remarkable benefits. Your success is the real objective here. Ready to redefine success together?\\n\\n[Pause]\\n\\nReach out today, and let\'s turn those marketing dreams into reality.\\"\\n\\n**[Background Music gently fades out]**", "script_type": "TOPIC", "video_type": "SHORT"}}'
    }
    
    selector = "data.script"
    
    print(f"Input data structure: {type(wrapped_data).__name__} with keys: {list(wrapped_data.keys())}")
    print(f"Original selector: '{selector}'")
    
    # Test workflow-service component
    print("\n--- Testing Workflow-Service Component ---")
    workflow_component = SelectDataComponent()
    context = WorkflowContext()
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_data": wrapped_data,
        "data_type": "Auto-Detect",
        "selector": selector
    }
    
    try:
        workflow_result = await workflow_component.execute(context)
        workflow_output = workflow_result.outputs.get("output_data")
        workflow_error = workflow_result.outputs.get("error")
        
        print(f"Workflow Service Result:")
        print(f"  Output: {workflow_output[:100] if workflow_output else None}{'...' if workflow_output and len(str(workflow_output)) > 100 else ''}")
        print(f"  Error: {workflow_error}")
        print(f"  Success: {workflow_error is None}")
        
    except Exception as e:
        print(f"Workflow Service Exception: {e}")
        workflow_output = None
        workflow_error = str(e)
    
    # Test node-executor-service component
    print("\n--- Testing Node-Executor-Service Component ---")
    executor_component = SelectDataExecutor()
    payload = {
        "request_id": "test_value_wrapper",
        "input_data": wrapped_data,
        "data_type": "Auto-Detect",
        "selector": selector
    }
    
    try:
        executor_result = await executor_component.process(payload)
        executor_output = executor_result.get("output_data")
        executor_error = executor_result.get("error")
        
        print(f"Node Executor Result:")
        print(f"  Output: {executor_output[:100] if executor_output else None}{'...' if executor_output and len(str(executor_output)) > 100 else ''}")
        print(f"  Error: {executor_error}")
        print(f"  Success: {executor_error is None}")
        
    except Exception as e:
        print(f"Node Executor Exception: {e}")
        executor_output = None
        executor_error = str(e)
    
    # Check if both services now work correctly
    print(f"\n--- Results Comparison ---")
    workflow_success = workflow_error is None and workflow_output is not None
    executor_success = executor_error is None and executor_output is not None
    
    if workflow_success and executor_success:
        print("✅ BOTH SERVICES SUCCESSFULLY HANDLED THE VALUE WRAPPER!")
        print("✅ The selector adjustment fix is working correctly")
        return True
    elif workflow_success or executor_success:
        print("⚠️  PARTIAL SUCCESS - One service worked, one didn't")
        return False
    else:
        print("❌ BOTH SERVICES STILL FAILING")
        return False


async def test_normal_data_still_works():
    """Test that normal data (without value wrapper) still works."""
    print("\n=== Testing Normal Data (No Value Wrapper) ===")
    
    # Normal data structure (not wrapped)
    normal_data = {
        "data": {
            "title": "Test Title",
            "script": "Test Script Content",
            "script_type": "TOPIC",
            "video_type": "SHORT"
        }
    }
    
    selector = "data.script"
    
    print(f"Input data structure: {type(normal_data).__name__} with keys: {list(normal_data.keys())}")
    print(f"Selector: '{selector}'")
    
    # Test workflow-service component
    workflow_component = SelectDataComponent()
    context = WorkflowContext()
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_data": normal_data,
        "data_type": "Auto-Detect",
        "selector": selector
    }
    
    workflow_result = await workflow_component.execute(context)
    workflow_output = workflow_result.outputs.get("output_data")
    workflow_error = workflow_result.outputs.get("error")
    
    # Test node-executor-service component
    executor_component = SelectDataExecutor()
    payload = {
        "request_id": "test_normal_data",
        "input_data": normal_data,
        "data_type": "Auto-Detect",
        "selector": selector
    }
    
    executor_result = await executor_component.process(payload)
    executor_output = executor_result.get("output_data")
    executor_error = executor_result.get("error")
    
    print(f"Workflow Service: output='{workflow_output}', error={workflow_error}")
    print(f"Node Executor: output='{executor_output}', error={executor_error}")
    
    # Check if both work and return the same result
    if (workflow_error is None and executor_error is None and 
        workflow_output == executor_output == "Test Script Content"):
        print("✅ NORMAL DATA STILL WORKS CORRECTLY")
        return True
    else:
        print("❌ NORMAL DATA HANDLING BROKEN")
        return False


async def main():
    """Run all tests."""
    print("SelectDataComponent Value Wrapper Fix Test")
    print("=" * 60)
    
    test1_passed = await test_value_wrapper_scenario()
    test2_passed = await test_normal_data_still_works()
    
    print(f"\n=== FINAL RESULTS ===")
    print(f"Value Wrapper Fix: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Normal Data Compatibility: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The value wrapper fix is working correctly")
        print("✅ Backward compatibility is maintained")
        print("✅ Production issue should be resolved")
        return 0
    else:
        print(f"\n❌ SOME TESTS FAILED")
        print("❌ The fix needs more work")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
