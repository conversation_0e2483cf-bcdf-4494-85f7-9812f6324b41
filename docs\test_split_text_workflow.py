#!/usr/bin/env python3
"""
Testing Split Text Component in Workflow Service.
Run this from the workflow-service directory.
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

async def test_workflow_component():
    """Test the Split Text Component in Workflow Service."""
    print("🧪 Testing Split Text Component - Workflow Service")
    print("=" * 60)
    
    try:
        from app.components.processing.split_text import SplitTextComponent
        from app.models.workflow_builder.context import WorkflowContext
        
        component = SplitTextComponent()
        print("✅ Component initialized successfully")
        
        # Print component metadata
        print(f"\n📋 Component Info:")
        print(f"Name: {component.name}")
        print(f"Display Name: {component.display_name}")
        print(f"Description: {component.description}")
        print(f"Category: {component.category}")
        
        print(f"\n📋 Inputs:")
        for input_def in component.inputs:
            print(f"- {input_def.name}: {input_def.display_name}")
        
        print(f"\n📋 Outputs:")
        for output_def in component.outputs:
            print(f"- {output_def.name}: {output_def.display_name}")
        
        # Create test context
        context = WorkflowContext(
            workflow_id="test_workflow_123",
            current_node_id="split_text_node_456",
            global_context={}
        )
        
        # Test cases
        test_cases = [
            {
                "name": "CSV data split",
                "input_text": "John,25,Engineer,New York",
                "delimiter": ",",
                "max_splits": -1,
                "include_delimiter": False,
                "expected": ["John", "25", "Engineer", "New York"]
            },
            {
                "name": "Log parsing with limited splits",
                "input_text": "2024-01-15 10:30:00 INFO Application started successfully",
                "delimiter": " ",
                "max_splits": 2,
                "include_delimiter": False,
                "expected": ["2024-01-15", "10:30:00", "INFO Application started successfully"]
            },
            {
                "name": "Path splitting",
                "input_text": "/home/<USER>/documents/file.txt",
                "delimiter": "/",
                "max_splits": -1,
                "include_delimiter": False,
                "expected": ["", "home", "user", "documents", "file.txt"]
            }
        ]
        
        # Run tests
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test {i}: {test_case['name']}")
            print("-" * 40)
            
            result = await component.execute(
                context=context,
                input_text=test_case["input_text"],
                delimiter=test_case["delimiter"],
                max_splits=test_case["max_splits"],
                include_delimiter=test_case["include_delimiter"]
            )
            
            print(f"Status: {result.status}")
            print(f"Node ID: {result.node_id}")
            
            if result.status == "success":
                actual = result.outputs.get("output_list", [])
                expected = test_case["expected"]
                
                print(f"Expected: {expected}")
                print(f"Actual:   {actual}")
                
                if actual == expected:
                    print("✅ Test PASSED")
                else:
                    print("❌ Test FAILED")
            else:
                print(f"❌ Error: {result.outputs.get('error', 'Unknown error')}")
        
        # Test error case
        print(f"\n🚨 Error Test: None input")
        print("-" * 40)
        
        result = await component.execute(
            context=context,
            input_text=None,
            delimiter=",",
            max_splits=-1,
            include_delimiter=False
        )
        
        print(f"Status: {result.status}")
        if result.status == "error":
            print(f"Error: {result.outputs.get('error', 'No error message')}")
            print("✅ Error handling works correctly")
        else:
            print("❌ Expected error but got success")
        
        print(f"\n🎉 Workflow Service tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_workflow_component())
