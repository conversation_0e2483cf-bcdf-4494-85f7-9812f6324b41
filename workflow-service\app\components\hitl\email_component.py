from typing import Dict, Any, List, ClassVar
import asyncio

from app.components.hitl.base_hitl_component import (
    BaseHITLComponent,
)
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    MultilineInput,
    BoolInput,
)
from app.models.workflow_builder.components import Output


class EmailComponent(BaseHITLComponent):
    """
    Sends email via SMTP or API.

    This component allows workflows to send emails to humans, optionally waiting
    for a reply. It can be used for notifications, approvals, or data collection
    from humans as part of an automated workflow.
    """

    name: ClassVar[str] = "EmailComponent"
    display_name: ClassVar[str] = "Send Email"
    description: ClassVar[str] = (
        "Sends an email to one or more recipients and optionally waits for a reply."
    )

    icon: ClassVar[str] = "Mail"

    inputs: ClassVar[List[InputBase]] = [
        # Inherit input_data and timeout_seconds from base class
        # Recipient - connection handle
        HandleInput(
            name="recipient_handle",
            display_name="Recipient Email(s)",
            is_handle=True,
            input_types=["string"],
            info="Connect the recipient email address(es). Multiple addresses can be separated by commas.",
        ),
        # Recipient - direct input in inspector
        StringInput(
            name="recipient",
            display_name="Recipient Email(s) (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The recipient email address(es). Multiple addresses can be separated by commas. Used if no connection is provided.",
        ),
        # Subject - connection handle
        HandleInput(
            name="subject_handle",
            display_name="Subject",
            is_handle=True,
            input_types=["string"],
            info="Connect the email subject.",
        ),
        # Subject - direct input in inspector
        StringInput(
            name="subject",
            display_name="Subject (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The email subject. Used if no connection is provided.",
        ),
        # Message - connection handle (inherited from base but overridden for clarity)
        HandleInput(
            name="message_handle",
            display_name="Email Body",
            is_handle=True,
            input_types=["string"],
            info="Connect the email body content.",
        ),
        # Message - direct input in inspector (overridden from base)
        MultilineInput(
            name="message",
            display_name="Email Body (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The email body content. Used if no connection is provided.",
        ),
        # Wait for reply
        BoolInput(
            name="wait_for_reply",
            display_name="Wait for Reply",
            required=False,
            is_handle=False,
            value=False,
            info="If enabled, the component will wait for a reply email before continuing.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        # Inherit response_data, timed_out, and error from base class
        Output(
            name="message_id",
            display_name="Message ID",
            output_type="string",
            # "The unique identifier of the sent email, if available from the email service."
        ),
        Output(
            name="sent_status",
            display_name="Sent Status",
            output_type="bool",
            # "True if the email was sent successfully, False otherwise."
        ),
        # Override action_taken from base class for clarity
        Output(
            name="action_taken",
            display_name="Action Taken",
            output_type="string",
            # "The action taken by the component, e.g., 'SENT' or 'FAILED'."
        ),
    ]

    # Legacy method for backward compatibility
    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the EmailComponent.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Args:
            **kwargs: Contains the input values.

        Returns:
            A dictionary with the component's outputs.
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )

        # Initialize output variables
        message_id = None
        sent_status = False
        response_data = None
        timed_out = False
        error_msg = None
        action_taken = "PENDING"

        # Get inputs - prioritize handle inputs over direct inputs
        recipient_handle = kwargs.get("recipient_handle")
        recipient_direct = kwargs.get("recipient")
        subject_handle = kwargs.get("subject_handle")
        subject_direct = kwargs.get("subject")
        message_handle = kwargs.get("message_handle")
        message_direct = kwargs.get("message")
        wait_for_reply = kwargs.get("wait_for_reply", False)
        timeout_seconds = kwargs.get("timeout_seconds", 3600)

        # Process inputs - prioritize handle inputs over direct inputs
        recipient = recipient_handle if recipient_handle is not None else recipient_direct
        subject = subject_handle if subject_handle is not None else subject_direct
        message_body = message_handle if message_handle is not None else message_direct

        # Validate inputs
        if not recipient:
            error_msg = "Recipient email address is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        if not subject:
            error_msg = "Email subject is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        if not message_body:
            error_msg = "Email body is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        try:
            # Log attempt
            self.log("info", f"Attempting to send email to {recipient}")

            # Placeholder: Get SMTP details or Email API credentials
            # smtp_host = self.get_config("SMTP_HOST")
            # smtp_port = int(self.get_config("SMTP_PORT"))
            # smtp_user = self.get_config("SMTP_USER")
            # smtp_pass = self.get_config("SMTP_PASSWORD")
            # sender_email = self.get_config("SENDER_EMAIL")

            # Placeholder: Construct email message
            # from email.message import EmailMessage
            # msg = EmailMessage()
            # msg['Subject'] = subject
            # msg['From'] = sender_email
            # msg['To'] = recipient
            # msg.set_content(message_body)

            # Placeholder: Connect to SMTP server and send email
            # import smtplib
            # with smtplib.SMTP(smtp_host, smtp_port) as server:
            #     server.starttls()
            #     server.login(smtp_user, smtp_pass)
            #     server.send_message(msg)
            #     message_id = f"placeholder_email_id_{int(time.time())}"

            # For demonstration, simulate successful sending
            message_id = "placeholder_email_id_12345"
            sent_status = True
            action_taken = "SENT"
            self.log("info", "Email sent successfully")

            # Placeholder: If wait_for_reply is True, implement waiting logic
            if wait_for_reply:
                self.log("info", f"Waiting for reply (timeout: {timeout_seconds}s)")

                # Placeholder: Complex monitoring logic would be needed here
                # This could involve:
                # 1. Polling an IMAP server for new emails
                # 2. Using a webhook if the email service provides one
                # 3. Using a dedicated email processing service

                # For demonstration, simulate no reply received
                response_data = None
                timed_out = True
                action_taken = "TIMED_OUT"
                self.log("info", "Waiting for reply timed out")

        except Exception as e:
            error_msg = f"Failed to send email: {str(e)}"
            self.log("error", error_msg)
            sent_status = False
            action_taken = "FAILED"

        # Return all outputs
        return {
            "message_id": message_id,
            "sent_status": sent_status,
            "response_data": response_data,
            "timed_out": timed_out,
            "error": error_msg,
            "action_taken": action_taken,
        }
