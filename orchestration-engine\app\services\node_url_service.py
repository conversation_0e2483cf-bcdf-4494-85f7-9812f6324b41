import requests
import json
from typing import Dict, Any, List, Optional
from app.config.config import settings
from app.utils.enhanced_logger import get_logger

logger = get_logger("NodeURLService")


def fetch_node_urls(workflow: Dict[str, Any]) -> Dict[str, str]:
    """
    Fetch node URLs from the API Gateway using a POST request to the /mcps/orchestration/urls endpoint.

    Args:
        workflow (Dict[str, Any]): The workflow definition to retrieve node URLs for.

    Returns:
        Dict[str, str]: A dictionary mapping mcp_id to their url.

    Raises:
        requests.HTTPError: If the request fails or returns an error response.
        ValueError: If the response is not valid JSON or doesn't contain the expected data.
        KeyError: If required keys are missing in the API response.
    """
    updated_workflow = workflow.copy()
    node_ids = []
    # Update server_script_path for each node if it exists in node_urls
    for node in updated_workflow.get("nodes", []):
        node_id = node.get("id")
        if node_id:
            node_ids.append(node_id)

    if not node_ids:
        logger.warning("No node IDs found to fetch URLs for")
        raise ValueError("No node IDs found in workflow")

    mcp_ids = list(set(node_ids))
    api_gateway_url = settings.api_gateway_url
    url = f"{api_gateway_url}/api/v1/mcps/orchestration/urls"
    headers = {
        "X-Server-Auth-Key": settings.orchestration_server_auth_key.get_secret_value(),
    }
    payload = {"ids": mcp_ids}
    logger.debug(f"Sending POST request to: {url} with payload: {payload}")
    response = requests.post(url, json=payload, headers=headers)
    logger.debug(f"Received response with status code: {response.status_code}")

    if response.status_code != 200:
        logger.error(f"Request failed: {response.text}")
        raise requests.HTTPError(f"Error {response.status_code}: {response.text}")

    try:
        response_json = response.json()
        logger.debug(f"Parsed JSON response: {json.dumps(response_json, indent=2)}")
    except json.JSONDecodeError:
        logger.error(f"Failed to parse response JSON: {response.text}")
        raise ValueError("Invalid JSON response received")

    if "urls" not in response_json:
        logger.error("'urls' not found in response JSON")
        raise KeyError("'urls' is missing in API response")

    urls = response_json["urls"]
    if not urls:
        logger.warning("No MCP urls found in response")

    # Convert list of MCPUrlItem to dict {mcp_id: url}
    mcp_url_map = {item["mcp_id"]: item["url"] for item in urls}
    return mcp_url_map


def update_workflow_node_urls(
    workflow: Dict[str, Any], node_urls: Dict[str, str]
) -> Dict[str, Any]:
    """
    Update the server_script_path in workflow nodes based on the provided node URLs.

    Args:
        workflow (Dict[str, Any]): The workflow definition to update.
        node_urls (Dict[str, str]): A dictionary mapping node_ids to their server_script_paths.

    Returns:
        Dict[str, Any]: The updated workflow with server_script_paths filled in.
    """
    if not workflow or not isinstance(workflow, dict):
        logger.error("Invalid workflow provided")
        raise ValueError("Invalid workflow provided")

    if "nodes" not in workflow:
        logger.error("Workflow does not contain 'nodes' key")
        raise KeyError("Workflow does not contain 'nodes' key")

    # Create a copy of the workflow to avoid modifying the original
    updated_workflow = workflow.copy()

    # Update server_script_path for each node if it exists in node_urls
    for node in updated_workflow.get("nodes", []):
        node_id = node.get("id")
        if node_id and node_id in node_urls:
            node["server_script_path"] = node_urls[node_id]
            logger.debug(
                f"Updated server_script_path for node {node_id}: {node_urls[node_id]}"
            )
        else:
            logger.debug(
                f"No URL found for node {node_id}, keeping existing server_script_path"
            )

    return updated_workflow


def process_workflow_urls(workflow: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a workflow by fetching node URLs and updating the workflow with those URLs.

    This is the main function to be called from executor_server_kafka.py.

    Args:
        workflow (Dict[str, Any]): The workflow definition to update.

    Returns:
        Dict[str, Any]: The updated workflow with server_script_paths filled in.

    Raises:
        requests.HTTPError: If the request to fetch node URLs fails.
        ValueError: If the workflow or response is invalid.
        KeyError: If required keys are missing.
    """
    try:
        # First, fetch node URLs from the API
        node_urls = fetch_node_urls(workflow)

        # Update the workflow with the fetched URLs
        updated_workflow = update_workflow_node_urls(workflow, node_urls)

        # Ensure all nodes referenced in transitions have server_script_paths
        # final_workflow = update_workflow_from_transition_schema(updated_workflow)

        return updated_workflow

    except Exception as e:
        logger.error(f"Error processing workflow URLs: {str(e)}")
        # Re-raise the exception to be handled by the caller
        raise
