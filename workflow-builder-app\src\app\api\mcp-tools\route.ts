/**
 * MCP Tools API Route Handler
 * 
 * This file provides API routes for MCP tools operations, acting as a server-side
 * proxy between the client and the external MCP tools API.
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getAccessToken } from '@/lib/cookies';
import { fetchMCPTools, debugMCPTools } from '@/lib/api';

/**
 * POST handler for MCP tools operations
 * Supports:
 * - /api/mcp-tools/fetch - Fetch MCP tools
 * - /api/mcp-tools/debug - Debug MCP tools
 */
export async function POST(request: NextRequest) {
  try {
    const { action, ...data } = await request.json();
    
    switch (action) {
      case 'fetch': {
        const { nodeConfig, buttonName } = data;
        
        if (!nodeConfig) {
          return NextResponse.json(
            { error: 'Missing nodeConfig' },
            { status: 400 }
          );
        }
        
        const result = await fetchMCPTools(nodeConfig, buttonName || 'fetch_tools');
        return NextResponse.json(result);
      }
      
      case 'debug': {
        const { nodeType, buttonName, nodeConfig } = data;
        
        if (!nodeConfig) {
          return NextResponse.json(
            { error: 'Missing nodeConfig' },
            { status: 400 }
          );
        }
        
        const result = await debugMCPTools({
          node_type: nodeType || 'MCPNode',
          button_name: buttonName || 'debug',
          node_config: nodeConfig
        });
        
        return NextResponse.json(result);
      }
      
      case 'execute': {
        // For now, we'll just return a mock response
        // In the future, we could implement actual execution
        return NextResponse.json({
          success: true,
          result: {
            message: 'MCP tool executed successfully',
            data: data
          }
        });
      }
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('Error in MCP tools API route:', error);
    return NextResponse.json(
      { 
        error: error.message || 'Failed to process MCP tools request',
        details: error.response?.data || null
      }, 
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
