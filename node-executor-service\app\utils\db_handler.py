"""
Database abstraction layer for email components.
Supports SQLite and MongoDB backends.
"""

import os
import sqlite3
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Tuple, Type
import logging
from enum import Enum

# Setup logger
logger = logging.getLogger(__name__)

class DatabaseType(Enum):
    """Enum for database types."""
    SQLITE = "sqlite"
    MONGODB = "mongodb"

class DatabaseHandler(ABC):
    """Abstract base class for database handlers."""

    @abstractmethod
    def initialize(self) -> None:
        """Initialize the database and create necessary tables/collections."""
        pass

    @abstractmethod
    def save_email(self, message_id: str, recipient: str, industry: str,
                  forwarded: int = 0, **kwargs) -> bool:
        """Save email information to the database."""
        pass

    @abstractmethod
    def get_email(self, message_id: str) -> Optional[Dict[str, Any]]:
        """Get email information by message ID."""
        pass

    @abstractmethod
    def update_email_status(self, message_id: str, forwarded: int = 1) -> bool:
        """Update email forwarded status."""
        pass

    @abstractmethod
    def get_unforwarded_emails(self) -> List[Dict[str, Any]]:
        """Get all emails that have not been forwarded yet."""
        pass

    @abstractmethod
    def check_domain_sent(self, domain: str) -> bool:
        """Check if an email has been sent to a specific domain."""
        pass

    @abstractmethod
    def close(self) -> None:
        """Close the database connection."""
        pass


class SQLiteHandler(DatabaseHandler):
    """SQLite implementation of the database handler."""

    def __init__(self, db_path: str = "Database/email_tracker.db"):
        """Initialize the SQLite handler with the database path."""
        self.db_path = db_path
        # Ensure the directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

    def _get_connection(self) -> sqlite3.Connection:
        """Get a database connection."""
        return sqlite3.connect(self.db_path)

    def initialize(self) -> None:
        """Initialize the database and create necessary tables."""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('''CREATE TABLE IF NOT EXISTS sent_emails (
                            id INTEGER PRIMARY KEY,
                            message_id TEXT UNIQUE,
                            recipient TEXT,
                            industry TEXT,
                            forwarded INTEGER DEFAULT 0,
                            sent_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            organization TEXT,
                            user_id TEXT,
                            additional_data TEXT
                        )''')
            conn.commit()
            logger.info(f"SQLite database initialized at {self.db_path}")
        except Exception as e:
            logger.error(f"Error initializing SQLite database: {str(e)}")
            raise
        finally:
            conn.close()

    def save_email(self, message_id: str, recipient: str, industry: str,
                  forwarded: int = 0, **kwargs) -> bool:
        """Save email information to the database."""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Extract optional parameters
            organization = kwargs.get('organization', '')
            user_id = kwargs.get('user_id', '')
            additional_data = kwargs.get('additional_data', '')

            cursor.execute(
                'INSERT INTO sent_emails (message_id, recipient, industry, forwarded, organization, user_id, additional_data) VALUES (?, ?, ?, ?, ?, ?, ?)',
                (message_id, recipient, industry, forwarded, organization, user_id, additional_data)
            )
            conn.commit()
            return True
        except Exception as e:
            logger.error(f"Error saving email to SQLite: {str(e)}")
            return False
        finally:
            conn.close()

    def get_email(self, message_id: str) -> Optional[Dict[str, Any]]:
        """Get email information by message ID."""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM sent_emails WHERE message_id = ?', (message_id,))
            row = cursor.fetchone()

            if row:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, row))
            return None
        except Exception as e:
            logger.error(f"Error getting email from SQLite: {str(e)}")
            return None
        finally:
            conn.close()

    def update_email_status(self, message_id: str, forwarded: int = 1) -> bool:
        """Update email forwarded status."""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('UPDATE sent_emails SET forwarded = ? WHERE message_id = ?', (forwarded, message_id))
            conn.commit()
            return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating email status in SQLite: {str(e)}")
            return False
        finally:
            conn.close()

    def get_unforwarded_emails(self) -> List[Dict[str, Any]]:
        """Get all emails that have not been forwarded yet."""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM sent_emails WHERE forwarded = 0')
            rows = cursor.fetchall()

            if rows:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in rows]
            return []
        except Exception as e:
            logger.error(f"Error getting unforwarded emails from SQLite: {str(e)}")
            return []
        finally:
            conn.close()

    def check_domain_sent(self, domain: str) -> bool:
        """Check if an email has been sent to a specific domain."""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sent_emails WHERE recipient LIKE ?", (f"%@{domain}",))
            result = cursor.fetchone()
            return result and result[0] > 0
        except Exception as e:
            logger.error(f"Error checking domain in SQLite: {str(e)}")
            return False
        finally:
            conn.close()

    def close(self) -> None:
        """Close the database connection (no-op for SQLite)."""
        pass  # SQLite connections are closed after each operation


class MongoDBHandler(DatabaseHandler):
    """MongoDB implementation of the database handler."""

    def __init__(self, connection_string: str = None, db_name: str = "email_tracker", collection_name: str = "sent_emails"):
        """Initialize the MongoDB handler."""
        self.connection_string = connection_string
        self.db_name = db_name
        self.collection_name = collection_name
        self.client = None
        self.db = None
        self.collection = None

    def initialize(self) -> None:
        """Initialize the MongoDB connection and create necessary collections."""
        try:
            # Import pymongo here to make it an optional dependency
            import pymongo
            from pymongo import MongoClient

            # Connect to MongoDB
            self.client = MongoClient(self.connection_string)
            self.db = self.client[self.db_name]
            self.collection = self.db[self.collection_name]

            # Create indexes
            self.collection.create_index("message_id", unique=True)
            self.collection.create_index("recipient")
            self.collection.create_index("forwarded")

            logger.info(f"MongoDB initialized: {self.db_name}.{self.collection_name}")
        except ImportError:
            logger.error("pymongo not installed. Please install it to use MongoDB.")
            raise
        except Exception as e:
            logger.error(f"Error initializing MongoDB: {str(e)}")
            raise

    def save_email(self, message_id: str, recipient: str, industry: str,
                  forwarded: int = 0, **kwargs) -> bool:
        """Save email information to the database."""
        try:
            # Create document
            document = {
                "message_id": message_id,
                "recipient": recipient,
                "industry": industry,
                "forwarded": forwarded,
                "sent_time": kwargs.get('sent_time', None),
                "organization": kwargs.get('organization', ''),
                "user_id": kwargs.get('user_id', ''),
                "additional_data": kwargs.get('additional_data', '')
            }

            # Insert document
            self.collection.insert_one(document)
            return True
        except Exception as e:
            logger.error(f"Error saving email to MongoDB: {str(e)}")
            return False

    def get_email(self, message_id: str) -> Optional[Dict[str, Any]]:
        """Get email information by message ID."""
        try:
            document = self.collection.find_one({"message_id": message_id})
            return document
        except Exception as e:
            logger.error(f"Error getting email from MongoDB: {str(e)}")
            return None

    def update_email_status(self, message_id: str, forwarded: int = 1) -> bool:
        """Update email forwarded status."""
        try:
            result = self.collection.update_one(
                {"message_id": message_id},
                {"$set": {"forwarded": forwarded}}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating email status in MongoDB: {str(e)}")
            return False

    def get_unforwarded_emails(self) -> List[Dict[str, Any]]:
        """Get all emails that have not been forwarded yet."""
        try:
            cursor = self.collection.find({"forwarded": 0})
            return list(cursor)
        except Exception as e:
            logger.error(f"Error getting unforwarded emails from MongoDB: {str(e)}")
            return []

    def check_domain_sent(self, domain: str) -> bool:
        """Check if an email has been sent to a specific domain."""
        try:
            # Use regex to match domain in recipient field
            count = self.collection.count_documents({"recipient": {"$regex": f"@{domain}$"}})
            return count > 0
        except Exception as e:
            logger.error(f"Error checking domain in MongoDB: {str(e)}")
            return False

    def close(self) -> None:
        """Close the MongoDB connection."""
        if self.client:
            self.client.close()
            self.client = None
            self.db = None
            self.collection = None


def create_db_handler(db_type: Union[str, DatabaseType], **kwargs) -> DatabaseHandler:
    """
    Factory method to create a database handler.

    Args:
        db_type: Type of database to use (sqlite or mongodb)
        **kwargs: Additional arguments to pass to the handler constructor

    Returns:
        An initialized database handler

    Raises:
        ValueError: If the database type is not supported
    """
    # Convert string to enum if needed
    if isinstance(db_type, str):
        try:
            db_type = DatabaseType(db_type.lower())
        except ValueError:
            raise ValueError(f"Unsupported database type: {db_type}")

    # Create the appropriate handler
    if db_type == DatabaseType.SQLITE:
        db_path = kwargs.get('db_path', "Database/email_tracker.db")
        handler = SQLiteHandler(db_path=db_path)
    elif db_type == DatabaseType.MONGODB:
        connection_string = kwargs.get('connection_string')
        db_name = kwargs.get('db_name', "email_tracker")
        collection_name = kwargs.get('collection_name', "sent_emails")
        handler = MongoDBHandler(
            connection_string=connection_string,
            db_name=db_name,
            collection_name=collection_name
        )
    else:
        raise ValueError(f"Unsupported database type: {db_type}")

    # Initialize the handler
    handler.initialize()
    return handler
