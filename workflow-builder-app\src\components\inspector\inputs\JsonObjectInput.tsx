import React, { useState, useEffect, useCallback } from "react";
import { InputDefinition } from "@/types";
import { ValidationWrapper } from "../ValidationWrapper";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { cn, debounce } from "@/lib/utils";
import { formatValueForDisplay } from "@/utils/valueFormatting";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ChevronDown, ChevronUp, Plus, Trash } from "lucide-react";

interface JsonObjectInputProps {
  inputDef: InputDefinition;
  value: any;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId?: string;
}

/**
 * Component for rendering JSON object inputs with nested properties
 */
export function JsonObjectInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: JsonObjectInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;

  // Initialize with current value or empty object
  const [objectValue, setObjectValue] = useState<Record<string, any>>(
    typeof value === "object" && value !== null ? value : {}
  );

  // State to track if we're in JSON edit mode
  const [jsonEditMode, setJsonEditMode] = useState(false);

  // State for the JSON text input
  const [jsonText, setJsonText] = useState(() => formatValueForDisplay(objectValue, "object"));

  // State to track detected properties
  const [detectedProperties, setDetectedProperties] = useState<string[]>([]);

  // State to track schema properties if available
  const [schemaProperties, setSchemaProperties] = useState<Record<string, any>>({});

  // State to track if the object has a predefined schema
  const [hasSchema, setHasSchema] = useState(false);

  // Update local state when value changes
  useEffect(() => {
    if (typeof value === "object" && value !== null) {
      setObjectValue(value);
    } else if (typeof value === "string") {
      try {
        const parsed = JSON.parse(value);
        setObjectValue(parsed);
      } catch (e) {
        // If parsing fails, keep the current object value
      }
    }
  }, [value]);

  // Detect properties from the input schema or from the current value
  useEffect(() => {
    // Check if we have schema information from MCP components
    if (inputDef.mcp_info?.input_schema?.properties?.[inputDef.name]) {
      const propSchema = inputDef.mcp_info.input_schema.properties[inputDef.name];
      if (propSchema.type === "object" && propSchema.properties) {
        setSchemaProperties(propSchema.properties);
        setDetectedProperties(Object.keys(propSchema.properties));
        setHasSchema(true);
        return;
      }
    }

    // If we have properties defined in the input definition
    if (inputDef.properties && Object.keys(inputDef.properties).length > 0) {
      setSchemaProperties(inputDef.properties);
      setDetectedProperties(Object.keys(inputDef.properties));
      setHasSchema(true);
      return;
    }

    // Check if this is a schema from the input definition's schema property
    if (inputDef.schema?.properties) {
      setSchemaProperties(inputDef.schema.properties);
      setDetectedProperties(Object.keys(inputDef.schema.properties));
      setHasSchema(true);
      return;
    }

    // Special case for keywords field
    if (inputDef.name === "keywords") {
      const keywordsSchema = {
        time: { type: 'string', description: 'Time for the script' },
        objective: { type: 'string', description: 'Objective of the script' },
        audience: { type: 'string', description: 'Audience for the script' },
        gender: { type: 'string', description: 'Gender for the script' },
        tone: { type: 'string', description: 'Tone of the script' },
        speakers: { type: 'string', description: 'Speaker in the script' }
      };
      setSchemaProperties(keywordsSchema);
      setDetectedProperties(Object.keys(keywordsSchema));
      setHasSchema(true);
      return;
    }

    // Otherwise, detect properties from the current value
    if (typeof value === "object" && value !== null) {
      setDetectedProperties(Object.keys(value));
      setHasSchema(false);
    } else {
      // Default properties if none detected
      setDetectedProperties(["value"]);
      setHasSchema(false);
    }
  }, [inputDef, value]);

  // Update a specific property value
  const updatePropertyValue = (propertyName: string, propertyValue: any) => {
    const newValue = {
      ...objectValue,
      [propertyName]: propertyValue,
    };
    setObjectValue(newValue);
    onChange(inputDef.name, newValue);

    // Immediately update window.currentWorkflowNodes if available
    if (typeof window !== 'undefined' && window.currentWorkflowNodes && nodeId) {
      const nodeIndex = window.currentWorkflowNodes.findIndex((n: any) => n.id === nodeId);
      if (nodeIndex !== -1) {
        // Create a deep copy to avoid reference issues
        const updatedNodes = [...window.currentWorkflowNodes];
        if (!updatedNodes[nodeIndex].data.config) {
          updatedNodes[nodeIndex].data.config = {};
        }
        updatedNodes[nodeIndex].data.config[inputDef.name] = newValue;
        window.currentWorkflowNodes = updatedNodes;

        if (process.env.NODE_ENV === 'development') {
          console.log(`Updated window.currentWorkflowNodes with new property value for node ${nodeId}, input ${inputDef.name}, property ${propertyName}`);
        }
      }
    }
  };

  // Add a new property
  const addProperty = () => {
    // Generate a unique property name
    let newPropName = "newProperty";
    let counter = 1;
    while (detectedProperties.includes(newPropName)) {
      newPropName = `newProperty${counter}`;
      counter++;
    }

    // Add the new property to the list
    setDetectedProperties([...detectedProperties, newPropName]);

    // Add the property to the object value
    updatePropertyValue(newPropName, "");
  };

  // Remove a property
  const removeProperty = (propertyName: string) => {
    // Remove the property from the list
    setDetectedProperties(detectedProperties.filter(prop => prop !== propertyName));

    // Remove the property from the object value
    const newValue = { ...objectValue };
    delete newValue[propertyName];
    setObjectValue(newValue);
    onChange(inputDef.name, newValue);

    // Immediately update window.currentWorkflowNodes if available
    if (typeof window !== 'undefined' && window.currentWorkflowNodes && nodeId) {
      const nodeIndex = window.currentWorkflowNodes.findIndex((n: any) => n.id === nodeId);
      if (nodeIndex !== -1) {
        // Create a deep copy to avoid reference issues
        const updatedNodes = [...window.currentWorkflowNodes];
        if (!updatedNodes[nodeIndex].data.config) {
          updatedNodes[nodeIndex].data.config = {};
        }
        updatedNodes[nodeIndex].data.config[inputDef.name] = newValue;
        window.currentWorkflowNodes = updatedNodes;

        if (process.env.NODE_ENV === 'development') {
          console.log(`Updated window.currentWorkflowNodes after removing property ${propertyName} for node ${nodeId}, input ${inputDef.name}`);
        }
      }
    }
  };

  // Create a debounced function for JSON text parsing
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedJsonParse = useCallback(
    debounce((jsonText: string) => {
      try {
        const parsed = JSON.parse(jsonText);
        let finalValue;

        // If this is a schema-defined object, ensure we maintain the schema structure
        if (hasSchema) {
          // Create a new object with only the properties defined in the schema
          const validatedObject: Record<string, any> = {};

          // Only keep properties that are in the schema
          detectedProperties.forEach(propName => {
            // If the property exists in the parsed object, use that value
            // Otherwise, keep the existing value or empty string
            validatedObject[propName] = parsed[propName] !== undefined
              ? parsed[propName]
              : (objectValue[propName] || "");
          });

          setObjectValue(validatedObject);
          onChange(inputDef.name, validatedObject);
          finalValue = validatedObject;
        } else {
          // For non-schema objects, allow any structure
          setObjectValue(parsed);
          onChange(inputDef.name, parsed);
          finalValue = parsed;

          // Update detected properties
          setDetectedProperties(Object.keys(parsed));
        }

        // Immediately update window.currentWorkflowNodes if available
        if (typeof window !== 'undefined' && window.currentWorkflowNodes && nodeId) {
          const nodeIndex = window.currentWorkflowNodes.findIndex((n: any) => n.id === nodeId);
          if (nodeIndex !== -1) {
            // Create a deep copy to avoid reference issues
            const updatedNodes = [...window.currentWorkflowNodes];
            if (!updatedNodes[nodeIndex].data.config) {
              updatedNodes[nodeIndex].data.config = {};
            }
            updatedNodes[nodeIndex].data.config[inputDef.name] = finalValue;
            window.currentWorkflowNodes = updatedNodes;

            if (process.env.NODE_ENV === 'development') {
              console.log(`Updated window.currentWorkflowNodes with new JSON value for node ${nodeId}, input ${inputDef.name}`);
            }
          }
        }
      } catch (e) {
        // If parsing fails, just update the text but don't update the object value
      }
    }, 50), // Reduced from 500ms to 50ms for immediate updates
    [hasSchema, detectedProperties, objectValue, onChange, inputDef.name]
  );

  // Handle JSON text edit - just pass to the debounced function
  const handleJsonTextEdit = (jsonText: string) => {
    debouncedJsonParse(jsonText);
  };

  // Toggle between JSON edit mode and property edit mode
  const toggleEditMode = () => {
    setJsonEditMode(!jsonEditMode);
  };

  // If the input is connected and disabled, show a simplified view
  if (isConnected && isDisabled) {
    return (
      <ValidationWrapper inputDef={inputDef} value={value}>
        <div className="relative">
          <div className="bg-background/30 border-border/50 rounded-md border p-3 opacity-70">
            <div className="text-muted-foreground text-xs">
              Connected to another node. Values will be provided at runtime.
            </div>
            {typeof value === "object" && value !== null && Object.keys(value).length > 0 && (
              <div className="bg-background/50 mt-2 max-h-32 overflow-auto rounded p-2 font-mono text-xs">
                {JSON.stringify(value, null, 2)}
              </div>
            )}
          </div>
          <ConnectedIndicator />
        </div>
      </ValidationWrapper>
    );
  }

  // Update jsonText when objectValue changes
  useEffect(() => {
    if (jsonEditMode) {
      setJsonText(formatValueForDisplay(objectValue, "object"));
    }
  }, [objectValue, jsonEditMode]);

  // If in JSON edit mode, show a text area for editing the JSON directly
  if (jsonEditMode) {
    return (
      <ValidationWrapper inputDef={inputDef} value={value}>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">{inputDef.display_name || inputDef.name}</Label>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleEditMode}
              className="h-6 px-2 text-xs"
            >
              Switch to Form View
            </Button>
          </div>
          {hasSchema && (
            <div className="text-muted-foreground text-xs mb-2">
              This object has a predefined structure. Changes that don't match the structure will be ignored.
            </div>
          )}
          <Textarea
            id={inputId}
            value={jsonText}
            onChange={(e) => {
              const newText = e.target.value;
              setJsonText(newText);
              handleJsonTextEdit(newText);
            }}
            placeholder={`Enter ${inputDef.display_name} (JSON format)`}
            className={cn(
              "bg-background/50 font-mono text-xs",
              isDisabled && "opacity-50"
            )}
            rows={5}
            disabled={isDisabled}
          />
        </div>
      </ValidationWrapper>
    );
  }

  // Regular form view for editing properties
  return (
    <ValidationWrapper inputDef={inputDef} value={value}>
      <div className="space-y-2">
        <Accordion type="single" collapsible defaultValue="item-1" className="w-full">
          <AccordionItem value="item-1" className="border-none">
            <div className="flex items-center justify-between">
              <AccordionTrigger className="py-2 text-xs font-medium">
                {inputDef.display_name || inputDef.name}
              </AccordionTrigger>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleEditMode}
                className="h-6 px-2 text-xs"
              >
                Edit as JSON
              </Button>
            </div>
            <AccordionContent>
              <div className="bg-background/30 border-border/50 space-y-3 rounded-md border p-3 pt-2">
                {hasSchema && (
                  <div className="text-muted-foreground text-xs mb-2">
                    This object has a predefined structure. You can only edit the values of existing properties.
                  </div>
                )}
                {detectedProperties.map((propertyName) => (
                  <div key={propertyName} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs font-medium">
                        {propertyName.charAt(0).toUpperCase() + propertyName.slice(1)}
                      </Label>
                      {!hasSchema && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeProperty(propertyName)}
                          className="h-6 w-6 p-0"
                        >
                          <Trash className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                    <Input
                      type="text"
                      value={objectValue[propertyName] || ""}
                      onChange={(e) => updatePropertyValue(propertyName, e.target.value)}
                      placeholder={`Enter ${propertyName}...`}
                      className="bg-background/50 h-8 text-xs"
                      disabled={isDisabled}
                    />
                  </div>
                ))}

                {!hasSchema && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={addProperty}
                    className="mt-2 w-full text-xs"
                  >
                    <Plus className="mr-1 h-3 w-3" /> Add Property
                  </Button>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </ValidationWrapper>
  );
}
