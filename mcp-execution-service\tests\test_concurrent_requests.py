# test_concurrent_requests.py
import asyncio
import json
import uuid
import logging
import time
from app.config.config import settings
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from aiokafka.errors import KafkaError

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# --- Kafka Producer Function ---
async def send_mcp_request(payload: dict):
    """Sends a single request payload to the configured Kafka topic."""
    producer = None
    try:
        producer = AIOKafkaProducer(
            bootstrap_servers=settings.kafka_bootstrap_servers,
        )
        await producer.start()
        logger.info(f"Kafka producer started for sending request.")

        request_topic = settings.kafka_consumer_topic
        logger.info(f"Attempting to send payload to topic '{request_topic}':")

        value_bytes = json.dumps(payload).encode("utf-8")

        logger.info("Sending request message...")
        await producer.send_and_wait(request_topic, value=value_bytes)
        logger.info(
            f"Request message sent successfully (request_id: {payload.get('request_id')})!"
        )
        return payload.get("request_id")
    except KafkaError as e:
        logger.error(f"Kafka error sending request: {e}", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"Unexpected error sending request: {e}", exc_info=True)
        raise
    finally:
        if producer:
            await producer.stop()
            logger.info("Kafka producer stopped.")

# --- Kafka Consumer Function ---
async def consume_results(request_ids, results_topic, timeout_seconds=30):
    """Consumes messages from the results topic, looking for matching request IDs."""
    consumer = None
    try:
        consumer = AIOKafkaConsumer(
            results_topic,
            bootstrap_servers=settings.kafka_bootstrap_servers,
            group_id=f"test-consumer-{uuid.uuid4()}",
            auto_offset_reset="latest",
            enable_auto_commit=True,
        )
        await consumer.start()
        logger.info(f"Started consumer for results topic '{results_topic}'")

        # Track which request IDs we've received responses for
        received_responses = set()
        start_time = time.time()

        while time.time() - start_time < timeout_seconds and len(received_responses) < len(request_ids):
            try:
                # Poll for messages with a 1-second timeout
                messages = await consumer.getmany(timeout_ms=1000)
                
                for tp, msgs in messages.items():
                    for msg in msgs:
                        try:
                            value = json.loads(msg.value.decode("utf-8"))
                            msg_request_id = value.get("request_id")
                            
                            if msg_request_id in request_ids:
                                logger.info(f"Received result for request_id: {msg_request_id}")
                                logger.info(f"Result: {value}")
                                received_responses.add(msg_request_id)
                                
                                if len(received_responses) == len(request_ids):
                                    logger.info("All expected responses received!")
                                    return True
                        except json.JSONDecodeError:
                            logger.warning(f"Failed to decode message: {msg.value}")
                            continue
            except Exception as e:
                logger.error(f"Error consuming messages: {e}", exc_info=True)
                
            # Short sleep to prevent CPU spinning
            await asyncio.sleep(0.1)
            
        if len(received_responses) < len(request_ids):
            logger.warning(f"Timeout reached. Received {len(received_responses)}/{len(request_ids)} responses.")
            return False
        
        return True
    finally:
        if consumer:
            await consumer.stop()
            logger.info("Kafka consumer stopped.")

# --- Main Test Function ---
async def test_concurrent_requests(num_requests=5):
    """Test sending multiple concurrent requests and receiving responses."""
    
    # Generate request payloads
    request_ids = []
    
    for i in range(num_requests):
        request_id = str(uuid.uuid4())
        request_ids.append(request_id)
        
        # Create a test payload with a unique request ID
        tool_parameters = {
            "topic": f"Test Topic {i}",
            "video_type": "explainer",
            "script_type": "TOPIC",
            "keywords": {
                "time": "30seconds",
                "objective": "educational",
                "audience": "general",
                "tone": "Technical",
            },
        }
        
        payload = {
            "server_script_path": "https://script-api.ruh.ai/sse",
            "tool_name": "script_generate",
            "tool_parameters": tool_parameters,
            "request_id": request_id,
        }
        
        # Send the request asynchronously
        asyncio.create_task(send_mcp_request(payload))
        logger.info(f"Created task to send request {i+1}/{num_requests}")
    
    # Wait a moment for all requests to be sent
    await asyncio.sleep(2)
    
    # Start consuming results
    logger.info(f"Waiting for responses for {len(request_ids)} requests...")
    success = await consume_results(request_ids, settings.kafka_results_topic, timeout_seconds=60)
    
    if success:
        logger.info("✅ Test passed! All concurrent requests were processed successfully.")
    else:
        logger.error("❌ Test failed! Not all requests received responses within the timeout period.")
    
    return success

if __name__ == "__main__":
    logger.info("Starting concurrent requests test...")
    asyncio.run(test_concurrent_requests(5))
