import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Float
from sqlalchemy.orm import declarative_base
from app.utils.constants.table_names import WORKFLOW_RATING_TABLE

Base = declarative_base()


class WorkflowRating(Base):
    __tablename__ = WORKFLOW_RATING_TABLE

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Workflow ID (can be from either test-workflow-templates or test-workflows)
    # Removed the foreign key constraint to allow ratings for both templates and instances
    workflow_id = Column(String, nullable=False)

    # User who provided the rating
    user_id = Column(String, nullable=False)

    # Rating value (1.0 to 5.0)
    rating = Column(Float, nullable=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<WorkflowRating(id={self.id}, workflow_id='{self.workflow_id}', user_id='{self.user_id}', rating={self.rating})>"
