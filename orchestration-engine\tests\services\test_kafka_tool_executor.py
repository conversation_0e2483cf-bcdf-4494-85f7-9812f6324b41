import pytest
import asyncio
import json
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from aiokafka.errors import KafkaError
from app.services.kafka_tool_executor import KafkaToolExecutor, KafkaToolExecutionError


class TestKafkaToolExecutor:
    """
    Test suite for KafkaToolExecutor class.
    Tests Kafka producer/consumer initialization, tool execution, and error handling.
    """

    @pytest.fixture
    async def mock_producer(self):
        """
        Provides a mock AIOKafkaProducer instance.
        """
        producer = AsyncMock(spec=AIOKafkaProducer)
        producer._sender = Mock(_running=True)
        return producer

    @pytest.fixture
    async def executor(self, mock_producer):
        """
        Provides a KafkaToolExecutor instance with mocked components.
        """
        executor = KafkaToolExecutor(producer=mock_producer)
        return executor

    @pytest.mark.asyncio
    async def test_initialization(self, mock_producer):
        """
        Test successful initialization of KafkaToolExecutor.
        """
        executor = KafkaToolExecutor(producer=mock_producer)
        
        assert executor.producer == mock_producer
        assert executor._consumer is None
        assert executor._consumer_task is None
        assert executor._pending_requests == {}

    @pytest.mark.asyncio
    async def test_initialization_with_none_producer(self):
        """
        Test initialization with None producer raises ValueError.
        """
        with pytest.raises(ValueError) as exc_info:
            KafkaToolExecutor(producer=None)
        
        assert "A running AIOKafkaProducer instance must be provided" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_initialization_with_stopped_producer(self):
        """
        Test initialization with stopped producer logs warning.
        """
        mock_producer = AsyncMock(spec=AIOKafkaProducer)
        mock_producer._sender = Mock(_running=False)

        with patch('app.services.kafka_tool_executor.logger') as mock_logger:
            executor = KafkaToolExecutor(producer=mock_producer)
            mock_logger.warning.assert_called_once_with(
                "The provided Kafka Producer may not be running."
            )

    @pytest.mark.asyncio
    async def test_start_internal_consumer(self, executor):
        """
        Test starting internal consumer successfully.
        """
        mock_consumer = AsyncMock(spec=AIOKafkaConsumer)
        
        with patch('app.services.kafka_tool_executor.AIOKafkaConsumer', return_value=mock_consumer):
            await executor.start()
            
            assert executor._consumer == mock_consumer
            mock_consumer.start.assert_called_once()
            assert executor._consumer_task is not None

    @pytest.mark.asyncio
    async def test_start_internal_consumer_already_running(self, executor):
        """
        Test starting internal consumer when it's already running.
        """
        executor._consumer = AsyncMock(spec=AIOKafkaConsumer)
        
        with patch('app.services.kafka_tool_executor.logger') as mock_logger:
            await executor._start_internal_consumer()
            mock_logger.warning.assert_called_once_with("Internal consumer already started.")

    @pytest.mark.asyncio
    async def test_start_internal_consumer_kafka_error(self, executor):
        """
        Test handling Kafka error during consumer start.
        """
        mock_consumer = AsyncMock(spec=AIOKafkaConsumer)
        mock_consumer.start.side_effect = KafkaError("Connection failed")
        
        with patch('app.services.kafka_tool_executor.AIOKafkaConsumer', return_value=mock_consumer):
            with pytest.raises(KafkaError) as exc_info:
                await executor.start()
            
            assert "Connection failed" in str(exc_info.value)
            assert executor._consumer is None
            assert executor._consumer_task is None

    @pytest.mark.asyncio
    async def test_stop_internal_consumer(self, executor):
        """
        Test stopping internal consumer successfully.
        """
        executor._consumer = AsyncMock(spec=AIOKafkaConsumer)
        executor._consumer_task = asyncio.create_task(asyncio.sleep(1))
        
        await executor.stop()
        
        assert executor._consumer is None
        assert executor._consumer_task is None
        executor._consumer.stop.assert_called_once()

    @pytest.mark.asyncio
    async def test_stop_internal_consumer_with_pending_requests(self, executor):
        """
        Test stopping internal consumer with pending requests.
        """
        executor._consumer = AsyncMock(spec=AIOKafkaConsumer)
        executor._consumer_task = asyncio.create_task(asyncio.sleep(1))
        
        # Add a pending request
        future = asyncio.Future()
        request_id = "test-request-1"
        executor._pending_requests[request_id] = future
        
        await executor.stop()
        
        assert executor._pending_requests == {}
        assert future.exception() is not None
        assert isinstance(future.exception(), KafkaToolExecutionError)

    @pytest.mark.asyncio
    async def test_execute_tool_success(self, executor):
        """
        Test successful tool execution.
        """
        # Setup mock consumer and response
        mock_consumer = AsyncMock(spec=AIOKafkaConsumer)
        executor._consumer = mock_consumer
        
        # Mock the consumer to return a successful result
        async def mock_consume():
            await asyncio.sleep(0.1)
            return {"result": "success"}

        with patch.object(executor, '_consume_loop', side_effect=mock_consume):
            result = await executor.execute_tool(
                server_script_path="test/path",
                tool_name="test_tool",
                tool_parameters={"param": "value"}
            )
            
            assert result == "success"
            executor.producer.send.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_tool_error_response(self, executor):
        """
        Test tool execution with error response.
        """
        mock_consumer = AsyncMock(spec=AIOKafkaConsumer)
        executor._consumer = mock_consumer
        
        # Mock the consumer to return an error result
        async def mock_consume():
            await asyncio.sleep(0.1)
            return {"error": "Tool execution failed"}

        with patch.object(executor, '_consume_loop', side_effect=mock_consume):
            with pytest.raises(KafkaToolExecutionError) as exc_info:
                await executor.execute_tool(
                    server_script_path="test/path",
                    tool_name="test_tool",
                    tool_parameters={"param": "value"}
                )
            
            assert "Tool execution failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_tool_consumer_not_running(self, executor):
        """
        Test tool execution when consumer is not running.
        """
        executor._consumer = None
        
        with pytest.raises(RuntimeError) as exc_info:
            await executor.execute_tool(
                server_script_path="test/path",
                tool_name="test_tool",
                tool_parameters={"param": "value"}
            )
        
        assert "KafkaToolExecutor's internal consumer is not running" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_tool_producer_not_running(self, executor):
        """
        Test tool execution when producer is not running.
        """
        executor._consumer = AsyncMock(spec=AIOKafkaConsumer)
        executor.producer._sender._running = False
        
        with pytest.raises(RuntimeError) as exc_info:
            await executor.execute_tool(
                server_script_path="test/path",
                tool_name="test_tool",
                tool_parameters={"param": "value"}
            )
        
        assert "The provided Kafka Producer is not running" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_context_manager(self, executor):
        """
        Test using KafkaToolExecutor as a context manager.
        """
        async with executor as exec_instance:
            assert exec_instance == executor
            assert exec_instance._consumer is not None
        
        assert executor._consumer is None
        assert executor._consumer_task is None

    @pytest.mark.asyncio
    async def test_consume_loop_json_decode_error(self, executor):
        """
        Test consumer loop handling of invalid JSON messages.
        """
        mock_consumer = AsyncMock(spec=AIOKafkaConsumer)
        mock_message = Mock()
        mock_message.value = b"invalid json"
        mock_consumer.__aiter__.return_value = [mock_message]
        
        executor._consumer = mock_consumer
        
        with patch('app.services.kafka_tool_executor.logger') as mock_logger:
            await executor._consume_loop()
            mock_logger.warning.assert_called_with(
                "Could not decode JSON from results topic: invalid json"
            )

    @pytest.mark.asyncio
    async def test_consume_loop_missing_request_id(self, executor):
        """
        Test consumer loop handling of messages without request_id.
        """
        mock_consumer = AsyncMock(spec=AIOKafkaConsumer)
        mock_message = Mock()
        mock_message.value = json.dumps({"result": "test"}).encode()
        mock_consumer.__aiter__.return_value = [mock_message]
        
        executor._consumer = mock_consumer
        
        with patch('app.services.kafka_tool_executor.logger') as mock_logger:
            await executor._consume_loop()
            mock_logger.warning.assert_called_with(
                "Received result message without 'request_id': {'result': 'test'}"
            )