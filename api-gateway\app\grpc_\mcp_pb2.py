# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: mcp.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'mcp.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import field_mask_pb2 as google_dot_protobuf_dot_field__mask__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\tmcp.proto\x12\x03mcp\x1a google/protobuf/field_mask.proto\"H\n\x05Owner\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x11\n\tfull_name\x18\x03 \x01(\t\x12\x11\n\tfcm_token\x18\x04 \x01(\t\"#\n\x06MCPUrl\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\"\xe9\x02\n\x10\x43reateMCPRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x19\n\x05owner\x18\x03 \x01(\x0b\x32\n.mcp.Owner\x12\"\n\nowner_type\x18\x04 \x01(\x0e\x32\x0e.mcp.OwnerType\x12\x0f\n\x07git_url\x18\x05 \x01(\t\x12&\n\ndepartment\x18\x06 \x01(\x0e\x32\x12.mcp.MCPDepartment\x12#\n\nvisibility\x18\x07 \x01(\x0e\x32\x0f.mcp.Visibility\x12\x0c\n\x04tags\x18\x08 \x03(\t\x12\x1b\n\x06status\x18\t \x01(\x0e\x32\x0b.mcp.Status\x12\x19\n\x04urls\x18\n \x03(\x0b\x32\x0b.mcp.MCPUrl\x12\x12\n\ngit_branch\x18\x0b \x01(\t\x12\x10\n\x08user_ids\x18\x0c \x03(\t\x12\x0c\n\x04logo\x18\r \x01(\t\x12\x1b\n\x13github_access_token\x18\x0e \x01(\t\"\xad\x04\n\x10UpdateMCPRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12/\n\x0bupdate_mask\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\x19\n\x05owner\x18\x03 \x01(\x0b\x32\n.mcp.Owner\x12\x11\n\x04name\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\x18\n\x0b\x64\x65scription\x18\x05 \x01(\tH\x01\x88\x01\x01\x12 \n\x13github_access_token\x18\x06 \x01(\tH\x02\x88\x01\x01\x12(\n\nvisibility\x18\x07 \x01(\x0e\x32\x0f.mcp.VisibilityH\x03\x88\x01\x01\x12\x10\n\x08user_ids\x18\x08 \x03(\t\x12+\n\ndepartment\x18\t \x01(\x0e\x32\x12.mcp.MCPDepartmentH\x04\x88\x01\x01\x12\x0c\n\x04tags\x18\n \x03(\t\x12 \n\x06status\x18\x0b \x01(\x0e\x32\x0b.mcp.StatusH\x05\x88\x01\x01\x12\x19\n\x04urls\x18\x0c \x03(\x0b\x32\x0b.mcp.MCPUrl\x12\x14\n\x07git_url\x18\r \x01(\tH\x06\x88\x01\x01\x12\x11\n\x04logo\x18\x0e \x01(\tH\x07\x88\x01\x01\x12\x17\n\ngit_branch\x18\x0f \x01(\tH\x08\x88\x01\x01\x42\x07\n\x05_nameB\x0e\n\x0c_descriptionB\x16\n\x14_github_access_tokenB\r\n\x0b_visibilityB\r\n\x0b_departmentB\t\n\x07_statusB\n\n\x08_git_urlB\x07\n\x05_logoB\r\n\x0b_git_branch\"\xb1\x03\n\x03MCP\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x10\n\x08owner_id\x18\x04 \x01(\t\x12\x10\n\x08user_ids\x18\x05 \x03(\t\x12\x12\n\nowner_type\x18\x06 \x01(\t\x12\x11\n\x04urls\x18\x07 \x01(\tH\x00\x88\x01\x01\x12\x17\n\ngit_branch\x18\x08 \x01(\tH\x01\x88\x01\x01\x12\x0f\n\x07git_url\x18\t \x01(\t\x12\x12\n\nvisibility\x18\n \x01(\t\x12\x0c\n\x04tags\x18\x0b \x03(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\x12\n\ndepartment\x18\r \x01(\t\x12\x12\n\ncreated_at\x18\x0e \x01(\t\x12\x12\n\nupdated_at\x18\x0f \x01(\t\x12\x18\n\x10mcp_tools_config\x18\x10 \x01(\t\x12\x0c\n\x04logo\x18\x11 \x01(\t\x12\x15\n\x08is_added\x18\x12 \x01(\x08H\x02\x88\x01\x01\x12\x1e\n\x11\x64\x65ployment_status\x18\x13 \x01(\tH\x03\x88\x01\x01\x42\x07\n\x05_urlsB\r\n\x0b_git_branchB\x0b\n\t_is_addedB\x14\n\x12_deployment_status\"F\n\x0bMCPResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x15\n\x03mcp\x18\x03 \x01(\x0b\x32\x08.mcp.MCP\"=\n\rGetMCPRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x14\n\x07user_id\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\n\n\x08_user_id\"9\n\x10\x44\x65leteMCPRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x19\n\x05owner\x18\x02 \x01(\x0b\x32\n.mcp.Owner\"5\n\x11\x44\x65leteMCPResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\xda\x02\n\x0fListMCPsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x15\n\x08owner_id\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x17\n\ndepartment\x18\x04 \x01(\tH\x01\x88\x01\x01\x12(\n\nvisibility\x18\x05 \x01(\x0e\x32\x0f.mcp.VisibilityH\x02\x88\x01\x01\x12 \n\x06status\x18\x06 \x01(\x0e\x32\x0b.mcp.StatusH\x03\x88\x01\x01\x12#\n\x08url_type\x18\x07 \x01(\x0e\x32\x0c.mcp.UrlTypeH\x04\x88\x01\x01\x12\x0c\n\x04tags\x18\x08 \x03(\t\x12\x1e\n\x11\x64\x65ployment_status\x18\t \x01(\tH\x05\x88\x01\x01\x42\x0b\n\t_owner_idB\r\n\x0b_departmentB\r\n\x0b_visibilityB\t\n\x07_statusB\x0b\n\t_url_typeB\x14\n\x12_deployment_status\"L\n\x17ListMCPsByUserIdRequest\x12\x10\n\x08owner_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\"\\\n\x10ListMCPsResponse\x12\x16\n\x04mcps\x18\x01 \x03(\x0b\x32\x08.mcp.MCP\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x04 \x01(\x05\"\"\n\x13GetMCPsByIdsRequest\x12\x0b\n\x03ids\x18\x01 \x03(\t\"\xba\x03\n\x0eMarketplaceMCP\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0c\n\x04logo\x18\x04 \x01(\t\x12\x18\n\x10mcp_tools_config\x18\x05 \x01(\t\x12\x12\n\nvisibility\x18\x06 \x01(\t\x12\x10\n\x08owner_id\x18\x07 \x01(\t\x12\x12\n\nowner_type\x18\x08 \x01(\t\x12\x10\n\x08user_ids\x18\t \x03(\t\x12\x12\n\ndepartment\x18\n \x01(\t\x12\x0c\n\x04tags\x18\x0b \x03(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\x11\n\x04urls\x18\r \x01(\tH\x00\x88\x01\x01\x12\x0f\n\x07git_url\x18\x0e \x01(\t\x12\x12\n\ncreated_at\x18\x0f \x01(\t\x12\x12\n\nupdated_at\x18\x10 \x01(\t\x12\x16\n\x0e\x61verage_rating\x18\x11 \x01(\x02\x12\x11\n\tuse_count\x18\x12 \x01(\x05\x12\x19\n\x11\x61pi_documentation\x18\x1d \x01(\t\x12\x14\n\x0c\x63\x61pabilities\x18\x14 \x03(\t\x12\x15\n\x08is_added\x18\x15 \x01(\x08H\x01\x88\x01\x01\x42\x07\n\x05_urlsB\x0b\n\t_is_added\"\x80\x02\n\x19GetMarketplaceMCPsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x13\n\x06search\x18\x03 \x01(\tH\x00\x88\x01\x01\x12+\n\ndepartment\x18\x04 \x01(\x0e\x32\x12.mcp.MCPDepartmentH\x01\x88\x01\x01\x12\x15\n\x08\x63\x61tegory\x18\x05 \x01(\tH\x02\x88\x01\x01\x12\x0c\n\x04tags\x18\x06 \x03(\t\x12\x14\n\x07sort_by\x18\x07 \x01(\tH\x03\x88\x01\x01\x12\x12\n\nvisibility\x18\x08 \x01(\tB\t\n\x07_searchB\r\n\x0b_departmentB\x0b\n\t_categoryB\n\n\x08_sort_by\"\x96\x02\n\x1aGetMarketplaceMCPsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12!\n\x04mcps\x18\x03 \x03(\x0b\x32\x13.mcp.MarketplaceMCP\x12\r\n\x05total\x18\x04 \x01(\x05\x12\x0c\n\x04page\x18\x05 \x01(\x05\x12\x11\n\tpage_size\x18\x06 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x07 \x01(\x05\x12\x10\n\x08has_next\x18\x08 \x01(\x08\x12\x10\n\x08has_prev\x18\t \x01(\x08\x12\x16\n\tnext_page\x18\n \x01(\x05H\x00\x88\x01\x01\x12\x16\n\tprev_page\x18\x0b \x01(\x05H\x01\x88\x01\x01\x42\x0c\n\n_next_pageB\x0c\n\n_prev_page\"N\n\x1eGetMarketplaceMCPDetailRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x14\n\x07user_id\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\n\n\x08_user_id\"e\n\x1fGetMarketplaceMCPDetailResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12 \n\x03mcp\x18\x03 \x01(\x0b\x32\x13.mcp.MarketplaceMCP\"A\n\x0eRateMCPRequest\x12\x0e\n\x06mcp_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x0e\n\x06rating\x18\x03 \x01(\x02\"K\n\x0fRateMCPResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x16\n\x0e\x61verage_rating\x18\x03 \x01(\x02\"0\n\rUseMCPRequest\x12\x0e\n\x06mcp_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"E\n\x0eUseMCPResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x11\n\tuse_count\x18\x03 \x01(\x05\"a\n\x1dUpdateDeploymentStatusRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x19\n\x11\x64\x65ployment_status\x18\x02 \x01(\t\x12\x19\n\x04urls\x18\x03 \x03(\x0b\x32\x0b.mcp.MCPUrl*\\\n\rMCPDepartment\x12\x0b\n\x07GENERAL\x10\x00\x12\t\n\x05SALES\x10\x01\x12\r\n\tMARKETING\x10\x02\x12\x0f\n\x0b\x45NGINEERING\x10\x03\x12\x0b\n\x07\x46INANCE\x10\x04\x12\x06\n\x02HR\x10\x05*%\n\nVisibility\x12\x0b\n\x07PRIVATE\x10\x00\x12\n\n\x06PUBLIC\x10\x01*\"\n\x06Status\x12\n\n\x06\x41\x43TIVE\x10\x00\x12\x0c\n\x08INACTIVE\x10\x01*3\n\tOwnerType\x12\x08\n\x04USER\x10\x00\x12\x0e\n\nENTERPRISE\x10\x01\x12\x0c\n\x08PLATFORM\x10\x02*\x1c\n\x07UrlType\x12\x07\n\x03SSE\x10\x00\x12\x08\n\x04HTTP\x10\x01*V\n\x17MarketplaceItemSortEnum\x12\n\n\x06NEWEST\x10\x00\x12\n\n\x06OLDEST\x10\x01\x12\x10\n\x0cMOST_POPULAR\x10\x02\x12\x11\n\rHIGHEST_RATED\x10\x03\x32\xd4\x05\n\nMCPService\x12\x34\n\tcreateMCP\x12\x15.mcp.CreateMCPRequest\x1a\x10.mcp.MCPResponse\x12.\n\x06getMCP\x12\x12.mcp.GetMCPRequest\x1a\x10.mcp.MCPResponse\x12\x34\n\tupdateMCP\x12\x15.mcp.UpdateMCPRequest\x1a\x10.mcp.MCPResponse\x12:\n\tdeleteMCP\x12\x15.mcp.DeleteMCPRequest\x1a\x16.mcp.DeleteMCPResponse\x12\x37\n\x08listMCPs\x12\x14.mcp.ListMCPsRequest\x1a\x15.mcp.ListMCPsResponse\x12?\n\x0cgetMCPsByIds\x12\x18.mcp.GetMCPsByIdsRequest\x1a\x15.mcp.ListMCPsResponse\x12N\n\x16UpdateDeploymentStatus\x12\".mcp.UpdateDeploymentStatusRequest\x1a\x10.mcp.MCPResponse\x12U\n\x12getMarketplaceMCPs\x12\x1e.mcp.GetMarketplaceMCPsRequest\x1a\x1f.mcp.GetMarketplaceMCPsResponse\x12\x64\n\x17getMarketplaceMCPDetail\x12#.mcp.GetMarketplaceMCPDetailRequest\x1a$.mcp.GetMarketplaceMCPDetailResponse\x12\x34\n\x07rateMCP\x12\x13.mcp.RateMCPRequest\x1a\x14.mcp.RateMCPResponse\x12\x31\n\x06useMCP\x12\x12.mcp.UseMCPRequest\x1a\x13.mcp.UseMCPResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mcp_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_MCPDEPARTMENT']._serialized_start=3861
  _globals['_MCPDEPARTMENT']._serialized_end=3953
  _globals['_VISIBILITY']._serialized_start=3955
  _globals['_VISIBILITY']._serialized_end=3992
  _globals['_STATUS']._serialized_start=3994
  _globals['_STATUS']._serialized_end=4028
  _globals['_OWNERTYPE']._serialized_start=4030
  _globals['_OWNERTYPE']._serialized_end=4081
  _globals['_URLTYPE']._serialized_start=4083
  _globals['_URLTYPE']._serialized_end=4111
  _globals['_MARKETPLACEITEMSORTENUM']._serialized_start=4113
  _globals['_MARKETPLACEITEMSORTENUM']._serialized_end=4199
  _globals['_OWNER']._serialized_start=52
  _globals['_OWNER']._serialized_end=124
  _globals['_MCPURL']._serialized_start=126
  _globals['_MCPURL']._serialized_end=161
  _globals['_CREATEMCPREQUEST']._serialized_start=164
  _globals['_CREATEMCPREQUEST']._serialized_end=525
  _globals['_UPDATEMCPREQUEST']._serialized_start=528
  _globals['_UPDATEMCPREQUEST']._serialized_end=1085
  _globals['_MCP']._serialized_start=1088
  _globals['_MCP']._serialized_end=1521
  _globals['_MCPRESPONSE']._serialized_start=1523
  _globals['_MCPRESPONSE']._serialized_end=1593
  _globals['_GETMCPREQUEST']._serialized_start=1595
  _globals['_GETMCPREQUEST']._serialized_end=1656
  _globals['_DELETEMCPREQUEST']._serialized_start=1658
  _globals['_DELETEMCPREQUEST']._serialized_end=1715
  _globals['_DELETEMCPRESPONSE']._serialized_start=1717
  _globals['_DELETEMCPRESPONSE']._serialized_end=1770
  _globals['_LISTMCPSREQUEST']._serialized_start=1773
  _globals['_LISTMCPSREQUEST']._serialized_end=2119
  _globals['_LISTMCPSBYUSERIDREQUEST']._serialized_start=2121
  _globals['_LISTMCPSBYUSERIDREQUEST']._serialized_end=2197
  _globals['_LISTMCPSRESPONSE']._serialized_start=2199
  _globals['_LISTMCPSRESPONSE']._serialized_end=2291
  _globals['_GETMCPSBYIDSREQUEST']._serialized_start=2293
  _globals['_GETMCPSBYIDSREQUEST']._serialized_end=2327
  _globals['_MARKETPLACEMCP']._serialized_start=2330
  _globals['_MARKETPLACEMCP']._serialized_end=2772
  _globals['_GETMARKETPLACEMCPSREQUEST']._serialized_start=2775
  _globals['_GETMARKETPLACEMCPSREQUEST']._serialized_end=3031
  _globals['_GETMARKETPLACEMCPSRESPONSE']._serialized_start=3034
  _globals['_GETMARKETPLACEMCPSRESPONSE']._serialized_end=3312
  _globals['_GETMARKETPLACEMCPDETAILREQUEST']._serialized_start=3314
  _globals['_GETMARKETPLACEMCPDETAILREQUEST']._serialized_end=3392
  _globals['_GETMARKETPLACEMCPDETAILRESPONSE']._serialized_start=3394
  _globals['_GETMARKETPLACEMCPDETAILRESPONSE']._serialized_end=3495
  _globals['_RATEMCPREQUEST']._serialized_start=3497
  _globals['_RATEMCPREQUEST']._serialized_end=3562
  _globals['_RATEMCPRESPONSE']._serialized_start=3564
  _globals['_RATEMCPRESPONSE']._serialized_end=3639
  _globals['_USEMCPREQUEST']._serialized_start=3641
  _globals['_USEMCPREQUEST']._serialized_end=3689
  _globals['_USEMCPRESPONSE']._serialized_start=3691
  _globals['_USEMCPRESPONSE']._serialized_end=3760
  _globals['_UPDATEDEPLOYMENTSTATUSREQUEST']._serialized_start=3762
  _globals['_UPDATEDEPLOYMENTSTATUSREQUEST']._serialized_end=3859
  _globals['_MCPSERVICE']._serialized_start=4202
  _globals['_MCPSERVICE']._serialized_end=4926
# @@protoc_insertion_point(module_scope)
