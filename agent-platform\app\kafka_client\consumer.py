import asyncio
from typing import Dict, Any, Set
from aiokafka import AIOKafkaConsumer, ConsumerRecord  # type: ignore
import json
from ..shared.config.base import get_settings
from ..schemas.kafka import AgentCreationRequest, AgentChatRequest, AgentChatResponse
from .producer import kafka_producer
from ..services.agent_fetch import AgentFetchService
from ..helper.session_manager import SessionManager
from ..helper.redis_client import RedisClient
from ..autogen_service.chat_processor import ChatProcessor
from ..autogen_service.agent_factory import AgentFactory
from ..shared.config.logging_config import get_logger

# Get logger for this module
logger = get_logger(__name__)


class KafkaConsumer:

    def __init__(self) -> None:
        self.settings = get_settings()
        self.logger = logger

        self.kafka_agent_response_topic = self.settings.kafka.kafka_agent_response_topic

        # Initialize Kafka consumer
        self.consumer = AIOKafkaConsumer(
            self.settings.kafka.kafka_agent_creation_topic,
            self.settings.kafka.kafka_agent_chat_topic,
            bootstrap_servers=self.settings.kafka.kafka_bootstrap_servers,
            group_id=self.settings.kafka.kafka_consumer_group,
            auto_offset_reset="earliest",
            enable_auto_commit=True,
        )

        # Initialize services
        self.agent_fetch_service = AgentFetchService()
        self.redis_client = RedisClient()
        self.session_manager = None  # Will be initialized in start_consumer
        self.agent_factory = AgentFactory()
        self.chat_processor = None  # Will be initialized in start_consumer

        # Track active tasks to manage concurrency
        self.active_tasks: Set[asyncio.Task] = set()
        # Maximum number of concurrent message processing tasks
        self.max_concurrent_tasks: int = 10
        # Semaphore to limit concurrent processing
        self.semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        self.logger.info("Initialized successfully")

    async def start_consumer(self) -> None:
        """Start the Kafka consumer and process messages"""
        # Initialize Redis client
        await self.redis_client.initialize()

        # Initialize session manager with optimizations
        self.session_manager = SessionManager(
            self.redis_client,
            session_ttl=3600,  # 1 hour TTL
            max_messages=50,  # Keep only 50 messages per session
            compression_level=6,  # Medium compression level
        )
        self.chat_processor = ChatProcessor(self.session_manager, self.agent_factory)

        # Start Kafka consumer
        await self.consumer.start()
        self.logger.info("Kafka consumer started")

        try:
            while True:
                try:
                    # Fetch batch of messages
                    batch = await self.consumer.getmany(timeout_ms=1000)

                    for tp, messages in batch.items():
                        for msg in messages:
                            # Process each message in a separate task
                            # but limit the number of concurrent tasks
                            if len(self.active_tasks) >= self.max_concurrent_tasks:
                                # Wait for at least one task to complete if we're at the limit
                                done, pending = await asyncio.wait(
                                    self.active_tasks,
                                    return_when=asyncio.FIRST_COMPLETED,
                                )
                                self.active_tasks = pending

                            # Create and start a new task for this message
                            task = asyncio.create_task(self.process_message_task(msg))
                            self.active_tasks.add(task)
                            # Remove the task from active_tasks when it completes
                            task.add_done_callback(self.active_tasks.discard)

                    # If no messages were received, wait a bit to avoid tight polling
                    if not batch:
                        await asyncio.sleep(0.1)

                except Exception as e:
                    self.logger.error(
                        "Error processing Kafka messages",
                        exc_info=True,
                        extra={"error": str(e)},
                    )
                    await asyncio.sleep(1)  # Wait before retrying

        finally:
            # Clean up resources
            await self.consumer.stop()
            await self.redis_client.close()
            self.logger.info("Kafka consumer stopped")

    async def process_message_task(self, msg: ConsumerRecord) -> None:
        """Process a Kafka message in a separate task"""
        async with self.semaphore:
            try:
                # Decode message
                message_value = json.loads(msg.value.decode("utf-8"))
                topic = msg.topic

                self.logger.info(f"Processing message from topic {topic}")

                if topic == self.settings.kafka.kafka_agent_creation_topic:
                    await self.process_agent_creation(message_value)
                elif topic == self.settings.kafka.kafka_agent_chat_topic:
                    await self.process_agent_chat(message_value)
                else:
                    self.logger.warning(f"Unknown topic: {topic}")

            except Exception as e:
                self.logger.error(f"Error processing message: {e}")

    async def process_agent_creation(self, message: Dict[str, Any]) -> None:
        """Process agent creation request"""

        print(f"Processing agent creation message: {message}")

        await kafka_producer.init_kafka_producer()

        headers = [
            ("correlationId", message["run_id"].encode("utf-8")),
            (
                "reply-topic",
                self.kafka_agent_response_topic.encode("utf-8"),
            ),
        ]
        try:
            # Parse request
            request = AgentCreationRequest(**message)

            # # Fetch agent configuration
            # agent_config = await self.agent_fetch_service.fetch_agent_config_hardcoded(
            #     request.agent_id,
            # )

            # Fetch agent configuration
            agent_config = await self.agent_fetch_service.fetch_agent_config(
                request.agent_id
            )

            print(f"Agent config: {agent_config}")

            if not agent_config:
                raise ValueError(
                    f"Agent configuration not found for ID: {request.agent_id}"
                )

            # Create session
            session_id = await self.session_manager.create_session(
                agent_config=agent_config,
                user_id=request.user_id,
                communication_type=request.communication_type,
            )

            # Send response
            response = {
                "run_id": request.run_id,
                "session_id": session_id,
                "success": True,
                "message": "Agent session created successfully",
            }

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                message=response,
                headers=headers,
            )

            self.logger.info(
                "Agent session created successfully",
                extra={
                    "session_id": session_id,
                    "agent_id": request.agent_id,
                    "user_id": request.user_id,
                    "run_id": request.run_id,
                },
            )

        except Exception as e:
            self.logger.error(
                "Failed to create agent session",
                exc_info=True,
                extra={
                    "agent_id": message.get("agent_id", "unknown"),
                    "user_id": message.get("user_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "error": str(e),
                },
            )

            # Send error response
            response = {
                "run_id": message.get("run_id", "unknown"),
                "success": False,
                "message": f"Failed to create agent session: {str(e)}",
            }

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                message=response,
                headers=headers,
            )

    async def process_agent_chat(self, message: Dict[str, Any]) -> None:
        """Process agent chat request"""

        print(f"Processing agent chat message: {message}")

        # Initialize Kafka producer
        await kafka_producer.init_kafka_producer()

        headers = [
            ("correlationId", message["run_id"].encode("utf-8")),
            (
                "reply-topic",
                self.kafka_agent_response_topic.encode("utf-8"),
            ),
        ]
        try:
            # Parse request
            request = AgentChatRequest(**message)

            # Check if session exists
            if not await self.session_manager.session_exists(request.session_id):
                raise ValueError(f"Session not found: {request.session_id}")

            # Get session data
            agent_config, communication_type, memory, cancellation_token = (
                await self.session_manager.get_session_data(request.session_id)
            )

            # Initialize agents and team
            agents, team, _ = await self.agent_factory.initialize_chat_session(
                run_id=request.run_id,
                agent_configs=[agent_config],
                chat_context=[],  # We'll use the session memory instead
                communication_type=communication_type,
                session_memory=memory,
            )

            self.logger.info("Chat session initialized successfully")

            user_message = (
                request.chat_context[-1]["content"] if request.chat_context else ""
            )

            # Example usage (inside an async context):
            result = await self.chat_processor.chat_with_agent_once(
                request.session_id,
                user_message,
                agents=agents,
                run_id=request.run_id,
                cancellation_token=cancellation_token,
            )

            print(f"Result: {result}")

            resp = AgentChatResponse(
                run_id=request.run_id,
                session_id=result["session_id"],
                agent_response=result["agent_response"],
                success=result.get("success", True),
                final=True,  # Mark as final since we're sending one response
                message="Chat processed successfully",
            )

            await kafka_producer.send_message(
                self.kafka_agent_response_topic,
                resp.dict(),  # Use model_dump instead of dict
                headers,
            )

            # # Process chat and stream responses
            # async for chunk in self.chat_processor.process_chat(
            #     req.session_id, user_message, agents=agents, team=team
            # ):
            #     print(f"Chunk: {chunk}")
            #     # Create response with all necessary fields
            #     resp = AgentChatResponse(
            #         run_id=req.run_id,
            #         session_id=chunk["session_id"],
            #         request_id=chunk["request_id"],
            #         agent_response=chunk["agent_response"],
            #         success=True,
            #         final=True,  # Mark as final since we're sending one response
            #     )

            #     await kafka_producer.send_message(
            #         self.kafka_agent_response_topic,
            #         resp.dict(),
            #         headers,
            #     )

            # # Process chat and stream responses
            # async for response_chunk in self.chat_processor.process_chat(
            #     session_id=request.session_id,
            #     user_message=user_message,
            #     agents=agents,
            #     team=team,
            #     run_id=request.run_id,
            # ):
            #     # Send response chunk
            #     response = AgentChatResponse(
            #         run_id=request.run_id,
            #         session_id=request.session_id,
            #         agent_response=response_chunk.get("agent_response", ""),
            #         success=response_chunk.get("success", False),
            #         final=response_chunk.get("final", False),
            #     )

            #     await kafka_producer.send_message(
            #         self.settings.kafka.kafka_agent_response_topic,
            #         response.dict(),
            #         headers=headers,
            #     )

            self.logger.info(
                "Chat processing completed",
                extra={"session_id": request.session_id, "run_id": request.run_id},
            )

        except Exception as e:
            self.logger.error(
                "Failed to process chat request",
                exc_info=True,
                extra={
                    "session_id": message.get("session_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "error": str(e),
                },
            )

            # Send error response
            response = AgentChatResponse(
                run_id=message.get("run_id", "unknown"),
                session_id=message.get("session_id", "unknown"),
                message=f"Error: {str(e)}",
                success=False,
                final=True,
                agent_response={}
            )

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                response.dict(),  # Use model_dump_json instead of json
                headers=headers,
            )


async def consume() -> None:
    """Start the Kafka consumer"""
    consumer_instance = KafkaConsumer()
    return await consumer_instance.start_consumer()
