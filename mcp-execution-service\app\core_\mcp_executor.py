# mcp_executor.py
import asyncio
import json
import logging
from typing import Any, Optional, List, Dict, Union  # type: ignore
from werkzeug.exceptions import InternalServerError  # type: ignore
from app.core_.client import MCPClient  # type: ignore


class MCPExecutor:
    """
    Executes MCP tools based on requests, encapsulating the execution logic.
    Uses the provided _execute_tool method for the core functionality.
    """

    def __init__(self, producer, logger: Optional[logging.Logger] = None):
        """
        Initializes the MCPExecutor.

        Args:
            logger: An optional logger instance. If None, a default logger is created.
        """
        self.logger = logger or logging.getLogger(__name__)
        self.logger.info("MCPExecutor initialized.")
        self.producer = producer
        self.logging_topic = "workflow-responses"

    async def _execute_tool(
        self,
        server_script_path: str,
        tool_name: str,
        tool_parameters: dict,
        retries: int = 3,
        correlation_id: Optional[str] = None,
    ) -> List[Union[Dict[str, Any], str]]:
        """Execute tool with retry logic and raise exceptions for errors after all retries."""
        results = []
        last_error = None
        if correlation_id:
            headers: List[tuple[str, bytes]] = [
                ("correlationId", correlation_id.encode("utf-8"))
            ]
        else:
            headers = None
        try:
            for attempt in range(retries):
                try:
                    async with MCPClient(server_script_path) as client:
                        self.logger.info(
                            f"Connected to MCP server at {server_script_path} (Attempt {attempt + 1}/{retries})"
                        )
                        result_info = {
                            "result": f"Connected to MCP server",
                            "status": "connected",
                            "workflow_status": "running",
                        }
                        self.logger.info(
                            f"Sending result to topic '{self.logging_topic}': {result_info}"
                        )
                        await self.producer.send(
                            self.logging_topic, result_info, headers=headers
                        )
                        result_info["result"] = "Processing request..."
                        await self.producer.send(
                            self.logging_topic, result_info, headers=headers
                        )
                        result = await client.call_tool(tool_name, tool_parameters)

                        self.logger.debug(f"MCP result raw: {result}")

                        if (
                            result
                            and hasattr(result, "isError")
                            and isinstance(result.isError, list)
                        ):
                            if result.isError:
                                error_content = getattr(
                                    result, "content", "No error details provided."
                                )
                                self.logger.error(
                                    f"MCP server returned explicit error list: {error_content}"
                                )
                                last_error = {"error": f"MCP Error: {error_content}"}
                                continue

                        if (
                            result
                            and hasattr(result, "content")
                            and isinstance(result.content, list)
                        ):
                            text_contents = [
                                content.text
                                for content in result.content
                                if hasattr(content, "text")
                                and isinstance(content.text, str)
                            ]

                            if not text_contents:
                                self.logger.warning(
                                    f"MCP result for {tool_name} had content, but no text fields."
                                )
                                results.append("[WARNING] No text results returned")
                                return results

                            extracted_data = []
                            for text in text_contents:
                                try:
                                    parsed_json = json.loads(text)

                                    if isinstance(
                                        parsed_json, dict
                                    ) and parsed_json.get("is_error"):
                                        error_msg = parsed_json.get(
                                            "message",
                                            "Unknown error from tool execution.",
                                        )
                                        self.logger.error(
                                            f"Tool execution indicated error in JSON payload: {error_msg}"
                                        )
                                        last_error = {
                                            "error": f"Tool Error: {error_msg}"
                                        }
                                        continue

                                    extracted_data.append(parsed_json)
                                except json.JSONDecodeError:
                                    self.logger.warning(
                                        f"Content item is not valid JSON, returning as raw text: {text[:100]}..."
                                    )
                                    extracted_data.append(text)
                                except Exception as parse_exc:
                                    self.logger.error(
                                        f"Unexpected error processing text content: {parse_exc}",
                                        exc_info=True,
                                    )
                                    extracted_data.append(
                                        f"[ERROR] Failed to process content: {text[:100]}..."
                                    )

                            if extracted_data:
                                results.extend(extracted_data)
                                return results
                            else:
                                self.logger.warning(
                                    "No valid data extracted from any text content, though text fields were present."
                                )
                                results.extend(text_contents)
                                return results
                        else:
                            self.logger.warning(
                                f"MCP result for {tool_name} has invalid or empty structure: {result}"
                            )
                            last_error = {
                                "error": "[ERROR] Invalid or empty result structure"
                            }
                            continue

                except InternalServerError as ise:
                    self.logger.error(
                        f"Tool call failed on attempt {attempt+1}/{retries} due to InternalServerError: {str(ise)}"
                    )
                    last_error = {"error": str(ise)}
                    if attempt < retries - 1:
                        self.logger.info(
                            f"Retrying {tool_name} after InternalServerError..."
                        )
                        await asyncio.sleep(2 ** (attempt + 1))

                except Exception as e:
                    self.logger.error(
                        f"Tool call failed on attempt {attempt+1}/{retries} for {tool_name} on {server_script_path} with unexpected error: {str(e)}",
                        exc_info=True,
                    )
                    result_info = {
                        "result": f"Tool call failed with unexpected error: {str(e)}, Retrying...",
                        "status": "connected",
                        "workflow_status": "running",
                    }
                    await self.producer.send(
                        self.logging_topic, result_info, headers=headers
                    )

                    last_error = {"error": str(e)}
                    if attempt < retries - 1:
                        self.logger.info(
                            f"Retrying {tool_name} after unexpected error..."
                        )
                        await asyncio.sleep(2 ** (attempt + 1))

            if last_error:
                self.logger.error(
                    f"Execution failed for {tool_name} after {retries} attempts with error: {last_error}"
                )
                result_info = {
                    "result": f"Execution failed after {retries} attempts with error: {last_error}",
                    "status": "disconnected",
                    "workflow_status": "running",
                }
                await self.producer.send(
                    self.logging_topic, result_info, headers=headers
                )
                raise InternalServerError(
                    f"Failed to execute tool {tool_name} after {retries} attempts: {last_error}"
                )

            self.logger.error(
                f"Execution loop for {tool_name} completed without returning or raising an exception after {retries} retries."
            )
            result_info = {
                "result": f"Execution returned no results after {retries} attempts.",
                "status": "disconnected",
                "workflow_status": "running",
            }
            await self.producer.send(self.logging_topic, result_info, headers=headers)
            raise InternalServerError(
                f"Failed to execute tool {tool_name} after {retries} attempts."
            )
        except Exception as e:
            self.logger.error(
                f"Unexpected error executing MCP tool: {e}", exc_info=True
            )
            raise

    async def execute_tool(
        self,
        server_script_path: str,
        tool_name: str,
        tool_parameters: dict,
        retries: int = 3,
        correlation_id: Optional[str] = None,
    ) -> List[Union[Dict[str, Any], str]]:
        """
        Public interface to execute an MCP tool.

        This method delegates the actual execution, result parsing, and retry
        logic to the internal `_execute_tool` method.

        Args:
            server_script_path: The URL/path to the MCP server script.
            tool_name: The name of the tool to execute.
            tool_parameters: A dictionary of parameters for the tool.
            retries: The number of times to retry the execution on failure.

        Returns:
            A list containing either dictionaries (parsed JSON results) or
            strings (raw text results or error/warning messages).

        Raises:
            InternalServerError: If the MCP tool reports a specific error after retries.
            Exception: If an unexpected error occurs after all retries are exhausted.
        """
        self.logger.info(
            f"Received request to execute tool '{tool_name}' on {server_script_path}"
        )

        result = await self._execute_tool(
            server_script_path=server_script_path,
            tool_name=tool_name,
            tool_parameters=tool_parameters,
            retries=retries,
            correlation_id=correlation_id,
        )
        return result
