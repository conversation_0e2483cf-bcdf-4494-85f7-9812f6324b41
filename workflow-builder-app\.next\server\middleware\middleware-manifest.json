{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\.[\\w]+$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "b934cd1fc0f57031bf0c5377eb73dafc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a6ceab093ba295fd615a4f39c1307171fe6fe1e5d91511ee356044f392c3b53", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8e74d7019d29564bd311230da1d8a70314b2cbb42de2f8b796cf5cce1b10a6c5"}}}, "instrumentation": null, "functions": {}}