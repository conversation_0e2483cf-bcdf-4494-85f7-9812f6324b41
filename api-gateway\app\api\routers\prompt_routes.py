import logging

from fastapi import APIRouter, Depends, HTTPException, status

from app.core.auth_guard import role_required
from app.core.config import settings
from app.schemas.prompt import PromptImprovementRequest, PromptImprovementResponse
from app.services.openai_service import OpenAIService

logger = logging.getLogger(__name__)

prompt_router = APIRouter(prefix="/prompts", tags=["prompts"])

# Initialize OpenAI service
openai_service = OpenAIService()


@prompt_router.post(
    "/improve",
    response_model=PromptImprovementResponse,
    summary="Improve an agent system prompt",
    description="Uses OpenAI to improve an agent's system prompt for better performance",
)
async def improve_system_prompt(
    request: PromptImprovementRequest,
    current_user: dict = Depends(role_required(["user", "admin"])),
) -> PromptImprovementResponse:
    """
    Improve an agent's system prompt using OpenAI.

    Args:
        request: The prompt improvement request containing the original prompt and optional context

    Returns:
        PromptImprovementResponse: The original and improved prompts
    """
    try:
        logger.info("Improving system prompt with OpenAI")

        # Check if OpenAI API key is set
        if not settings.OPENAI_API_KEY:
            logger.error("OpenAI API key is not set")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="OpenAI service is not configured. Please set OPENAI_API_KEY in the environment variables.",
            )

        # Call OpenAI service to improve the prompt
        improved_prompt = openai_service.improve_system_prompt(
            request.original_prompt, request.agent_context
        )

        return PromptImprovementResponse(
            original_prompt=request.original_prompt, improved_prompt=improved_prompt
        )

    except Exception as e:
        logger.error(f"Error improving system prompt: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error improving system prompt: {str(e)}",
        )
