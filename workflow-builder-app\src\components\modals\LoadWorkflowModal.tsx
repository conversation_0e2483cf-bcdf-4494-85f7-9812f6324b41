import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Ta<PERSON>, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
// import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, FileUp, Upload } from "lucide-react";
import { validateWorkflowJson } from "@/lib/workflowUtils";

interface LoadWorkflowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoadWorkflow: (workflowData: any, showSuccessMessage?: boolean) => void;
}

export function LoadWorkflowModal({ isOpen, onClose, onLoadWorkflow }: LoadWorkflowModalProps) {
  const [activeTab, setActiveTab] = useState("upload");
  const [jsonInput, setJsonInput] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const resetState = () => {
    setJsonInput("");
    setFile(null);
    setError(null);
    setIsLoading(false);
    setActiveTab("upload");
  };

  const handleClose = () => {
    resetState();
    onClose();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setError(null);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      setFile(e.dataTransfer.files[0]);
      setError(null);
    }
  };

  const handleJsonInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setJsonInput(e.target.value);
    setError(null);
  };

  const processWorkflowData = async (data: any) => {
    setIsLoading(true);
    try {
      // Validate the workflow data
      const validationResult = validateWorkflowJson(data);

      if (!validationResult.isValid) {
        setError(validationResult.error || "Invalid workflow data");
        setIsLoading(false);
        return;
      }

      // If valid, pass the data to the parent component
      // When loading from the modal, we want to show the success message
      onLoadWorkflow(data, true);
      handleClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadFromFile = async () => {
    if (!file) {
      setError("Please select a file");
      return;
    }

    if (!file.name.endsWith(".json")) {
      setError("Please select a JSON file");
      return;
    }

    setIsLoading(true);
    try {
      const fileContent = await file.text();
      console.log("File content:", fileContent);
      let workflowData;

      try {
        workflowData = JSON.parse(fileContent);
        console.log("Parsed file data:", workflowData);
      } catch (err) {
        console.error("JSON parse error:", err);
        setError("Invalid JSON format");
        setIsLoading(false);
        return;
      }

      // Ensure the workflow data has the expected structure
      // Check for both edges and connections for backward compatibility
      if (!workflowData.nodes || (!workflowData.edges && !workflowData.connections)) {
        console.error("Missing nodes or edges in workflow data");
        setError("Invalid workflow format: missing nodes or edges");
        setIsLoading(false);
        return;
      }

      // For backward compatibility, if only connections is present, copy it to edges
      if (!workflowData.edges && workflowData.connections) {
        console.log("Converting connections to edges for backward compatibility");
        workflowData.edges = workflowData.connections;
      }

      await processWorkflowData(workflowData);
    } catch (err) {
      console.error("Error in handleLoadFromFile:", err);
      setError(err instanceof Error ? err.message : "Failed to read file");
      setIsLoading(false);
    }
  };

  const handleLoadFromJson = async () => {
    if (!jsonInput.trim()) {
      setError("Please enter JSON data");
      return;
    }

    setIsLoading(true);
    try {
      let workflowData;

      try {
        workflowData = JSON.parse(jsonInput);
        console.log("Parsed JSON data:", workflowData);
      } catch (err) {
        console.error("JSON parse error:", err);
        setError("Invalid JSON format");
        setIsLoading(false);
        return;
      }

      // Ensure the workflow data has the expected structure
      // Check for both edges and connections for backward compatibility
      if (!workflowData.nodes || (!workflowData.edges && !workflowData.connections)) {
        console.error("Missing nodes or edges in workflow data");
        setError("Invalid workflow format: missing nodes or edges");
        setIsLoading(false);
        return;
      }

      // For backward compatibility, if only connections is present, copy it to edges
      if (!workflowData.edges && workflowData.connections) {
        console.log("Converting connections to edges for backward compatibility");
        workflowData.edges = workflowData.connections;
      }

      await processWorkflowData(workflowData);
    } catch (err) {
      console.error("Error in handleLoadFromJson:", err);
      setError(err instanceof Error ? err.message : "Failed to parse JSON");
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open: boolean) => !open && handleClose()}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Load Workflow</DialogTitle>
          <DialogDescription>
            Load a workflow from a JSON file or paste JSON directly.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload File</TabsTrigger>
            <TabsTrigger value="paste">Paste JSON</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="mt-4">
            <div
              className="hover:bg-muted/50 cursor-pointer rounded-lg border-2 border-dashed p-8 text-center transition-colors"
              onClick={() => fileInputRef.current?.click()}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <FileUp className="text-muted-foreground mx-auto mb-4 h-10 w-10" />
              <p className="mb-2 font-medium">Drag and drop a JSON file here</p>
              <p className="text-muted-foreground mb-4 text-sm">or click to browse</p>
              <Input
                ref={fileInputRef}
                type="file"
                accept=".json"
                className="hidden"
                onChange={handleFileChange}
              />
              {file && (
                <div className="bg-muted mt-2 rounded p-2 text-sm">
                  <p className="font-medium">Selected file:</p>
                  <p className="truncate">{file.name}</p>
                </div>
              )}
            </div>
            <button
              type="button"
              className="bg-primary text-primary-foreground hover:bg-primary/90 mt-4 inline-flex h-9 w-full items-center justify-center rounded-md px-4 py-2 text-sm font-medium disabled:pointer-events-none disabled:opacity-50"
              onClick={handleLoadFromFile}
              disabled={!file || isLoading}
            >
              {isLoading ? "Loading..." : "Load Workflow"}
            </button>
          </TabsContent>

          <TabsContent value="paste" className="mt-4">
            <Textarea
              placeholder="Paste your workflow JSON here..."
              className="min-h-[200px] font-mono text-sm"
              value={jsonInput}
              onChange={handleJsonInputChange}
            />
            <button
              type="button"
              className="bg-primary text-primary-foreground hover:bg-primary/90 mt-4 inline-flex h-9 w-full items-center justify-center rounded-md px-4 py-2 text-sm font-medium disabled:pointer-events-none disabled:opacity-50"
              onClick={handleLoadFromJson}
              disabled={!jsonInput.trim() || isLoading}
            >
              {isLoading ? "Loading..." : "Load Workflow"}
            </button>
          </TabsContent>
        </Tabs>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <DialogFooter>
          <button
            type="button"
            className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium"
            onClick={handleClose}
          >
            Cancel
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
