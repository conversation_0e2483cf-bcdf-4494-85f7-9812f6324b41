#!/usr/bin/env python3
"""
Demonstration script showing the complete AlterMetadataComponent workflow.
This script simulates the entire flow from workflow definition to execution.
"""
import asyncio
import json
import sys
import os
import time

# Add both service paths
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'workflow-service'))

# Import from workflow service
from app.components.processing.alter_metadata import AlterMetadataComponent
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeStatus


async def demo_workflow_execution():
    """Demonstrate the complete workflow execution process."""
    print("🚀 AlterMetadataComponent Workflow Demonstration")
    print("=" * 70)

    # Step 1: Component Discovery (what the frontend would see)
    print("\n📋 Step 1: Component Discovery")
    print("-" * 50)

    component = AlterMetadataComponent()
    definition = component.get_definition()

    print(f"Component discovered: {definition['display_name']}")
    print(f"Category: {definition['category']}")
    print(f"Description: {definition['description']}")
    print(f"Icon: {definition['icon']}")

    print("\nAvailable Inputs:")
    for inp in definition['inputs']:
        required = "Required" if inp['required'] else "Optional"
        print(f"  • {inp['display_name']} ({inp['input_type']}) - {required}")
        print(f"    {inp['info']}")

    print("\nAvailable Outputs:")
    for out in definition['outputs']:
        print(f"  • {out['display_name']} ({out['output_type']})")

    # Step 2: Workflow Configuration (user configures the component)
    print("\n📋 Step 2: Workflow Configuration")
    print("-" * 50)

    # Simulate user configuration
    user_config = {
        "input_metadata": {
            "document_id": "DOC-12345",
            "title": "Important Document",
            "version": "1.0",
            "status": "draft",
            "author": "John Doe",
            "created_date": "2024-01-01",
            "tags": ["important", "draft"],
            "deprecated_field": "old_value",
            "temp_processing_id": "TEMP-98765"
        },
        "updates": {
            "version": "2.0",
            "status": "published",
            "last_modified": "2024-01-15",
            "reviewer": "Jane Smith",
            "publication_date": "2024-01-15"
        },
        "keys_to_remove": ["deprecated_field", "temp_processing_id"]
    }

    print("User Configuration:")
    print(f"Input Metadata: {len(user_config['input_metadata'])} keys")
    print(f"Updates: {len(user_config['updates'])} keys")
    print(f"Keys to Remove: {user_config['keys_to_remove']}")

    # Step 3: Workflow Execution (orchestration engine executes)
    print("\n📋 Step 3: Workflow Execution")
    print("-" * 50)

    # Create workflow context
    context = WorkflowContext(
        workflow_id="demo_workflow_001",
        execution_id="exec_" + str(int(time.time()))
    )
    context.current_node_id = "alter_metadata_node_1"
    context.node_outputs["alter_metadata_node_1"] = user_config

    print(f"Workflow ID: {context.workflow_id}")
    print(f"Execution ID: {context.execution_id}")
    print(f"Node ID: {context.current_node_id}")

    # Execute the component
    print("\nExecuting component...")
    start_time = time.time()
    result = await component.execute(context)
    total_time = time.time() - start_time

    # Step 4: Results Processing
    print("\n📋 Step 4: Results Processing")
    print("-" * 50)

    print(f"Execution Status: {result.status}")
    print(f"Execution Time: {result.execution_time:.4f}s")
    print(f"Total Time: {total_time:.4f}s")

    if result.status == NodeStatus.SUCCESS:
        output_metadata = result.outputs.get('output_metadata', {})

        print(f"\n✅ Execution Successful!")
        print(f"Output Metadata ({len(output_metadata)} keys):")

        # Show the transformed metadata
        for key, value in output_metadata.items():
            print(f"  • {key}: {value}")

        # Verify the transformations
        print(f"\n🔍 Transformation Verification:")

        # Check updates were applied
        updates_applied = all(
            output_metadata.get(key) == value
            for key, value in user_config['updates'].items()
        )
        print(f"  Updates Applied: {'✅' if updates_applied else '❌'}")

        # Check keys were removed
        keys_removed = all(
            key not in output_metadata
            for key in user_config['keys_to_remove']
        )
        print(f"  Keys Removed: {'✅' if keys_removed else '❌'}")

        # Check original data preserved
        original_keys = set(user_config['input_metadata'].keys()) - set(user_config['keys_to_remove'])
        preserved_keys = set(output_metadata.keys()) & original_keys
        preservation_rate = len(preserved_keys) / len(original_keys) * 100
        print(f"  Data Preservation: {preservation_rate:.1f}%")

    else:
        print(f"❌ Execution Failed: {result.error_message}")
        return

    # Step 5: Workflow Context Logs
    print("\n📋 Step 5: Execution Logs")
    print("-" * 50)

    if context.logs:
        for log_entry in context.logs:
            print(f"  📝 {log_entry}")
    else:
        print("  No logs generated")

    # Step 6: Legacy Compatibility Test
    print("\n📋 Step 6: Legacy Compatibility Test")
    print("-" * 50)

    print("Testing legacy build method...")
    legacy_result = component.build(**user_config)

    if "output_metadata" in legacy_result:
        legacy_output = legacy_result["output_metadata"]

        # Compare with modern execution result
        outputs_match = legacy_output == output_metadata
        print(f"Legacy/Modern Consistency: {'✅' if outputs_match else '❌'}")

        if not outputs_match:
            print("  Differences detected:")
            for key in set(legacy_output.keys()) | set(output_metadata.keys()):
                legacy_val = legacy_output.get(key, "<missing>")
                modern_val = output_metadata.get(key, "<missing>")
                if legacy_val != modern_val:
                    print(f"    {key}: legacy={legacy_val}, modern={modern_val}")
    else:
        print(f"❌ Legacy execution failed: {legacy_result.get('error', 'Unknown error')}")


async def demo_error_scenarios():
    """Demonstrate error handling scenarios."""
    print("\n🧪 Error Handling Demonstration")
    print("=" * 70)

    component = AlterMetadataComponent()

    error_scenarios = [
        {
            "name": "Missing Input Metadata",
            "config": {
                "updates": {"version": "2.0"},
                "keys_to_remove": []
            }
        },
        {
            "name": "Invalid Input Type",
            "config": {
                "input_metadata": "not_a_dict",
                "updates": {},
                "keys_to_remove": []
            }
        },
        {
            "name": "Invalid Updates Type",
            "config": {
                "input_metadata": {"name": "test"},
                "updates": "not_a_dict",
                "keys_to_remove": []
            }
        },
        {
            "name": "Invalid Keys Type",
            "config": {
                "input_metadata": {"name": "test"},
                "updates": {},
                "keys_to_remove": "not_a_list"
            }
        }
    ]

    for i, scenario in enumerate(error_scenarios, 1):
        print(f"\n📋 Error Scenario {i}: {scenario['name']}")
        print("-" * 50)

        context = WorkflowContext(
            workflow_id=f"error_test_{i}",
            execution_id=f"error_exec_{i}"
        )
        context.current_node_id = f"error_node_{i}"
        context.node_outputs[f"error_node_{i}"] = scenario['config']

        result = await component.execute(context)

        if result.status == NodeStatus.ERROR:
            print(f"✅ Error correctly detected: {result.error_message}")
        else:
            print(f"❌ Error not detected (unexpected success)")


async def demo_performance_test():
    """Demonstrate performance characteristics."""
    print("\n⚡ Performance Demonstration")
    print("=" * 70)

    component = AlterMetadataComponent()

    # Test with different data sizes
    test_cases = [
        {"name": "Small (10 keys)", "size": 10},
        {"name": "Medium (100 keys)", "size": 100},
        {"name": "Large (1000 keys)", "size": 1000},
    ]

    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}")
        print("-" * 30)

        # Generate test data
        size = test_case['size']
        large_metadata = {f"field_{i}": f"value_{i}" for i in range(size)}
        large_metadata.update({
            "name": "performance_test",
            "version": "1.0",
            "remove_me": "temp"
        })

        config = {
            "input_metadata": large_metadata,
            "updates": {"version": "2.0", "processed": True},
            "keys_to_remove": ["remove_me"]
        }

        context = WorkflowContext(
            workflow_id=f"perf_test_{size}",
            execution_id=f"perf_exec_{size}"
        )
        context.current_node_id = f"perf_node_{size}"
        context.node_outputs[f"perf_node_{size}"] = config

        # Run multiple iterations
        iterations = 10
        total_time = 0

        for _ in range(iterations):
            start_time = time.time()
            result = await component.execute(context)
            total_time += time.time() - start_time

            if result.status != NodeStatus.SUCCESS:
                print(f"❌ Performance test failed: {result.error_message}")
                break
        else:
            avg_time = total_time / iterations
            print(f"Average execution time: {avg_time:.4f}s")
            if avg_time > 0:
                print(f"Throughput: {1/avg_time:.1f} executions/second")
            else:
                print("Throughput: >10,000 executions/second (too fast to measure accurately)")

            # Verify result correctness
            output = result.outputs.get('output_metadata', {})
            expected_size = size + 1  # original + 1 update - 1 removal
            if len(output) == expected_size:
                print(f"✅ Result correctness verified ({expected_size} keys)")
            else:
                print(f"❌ Result size mismatch: expected {expected_size}, got {len(output)}")


async def main():
    """Run the complete demonstration."""
    await demo_workflow_execution()
    await demo_error_scenarios()
    await demo_performance_test()

    print("\n🎉 Demonstration Complete!")
    print("=" * 70)
    print("The AlterMetadataComponent is fully functional and ready for production use.")
    print("Key highlights:")
    print("  ✅ Modern dual-purpose input patterns")
    print("  ✅ Comprehensive error handling")
    print("  ✅ Excellent performance characteristics")
    print("  ✅ Full backward compatibility")
    print("  ✅ Consistent behavior across execution methods")


if __name__ == "__main__":
    asyncio.run(main())
