import { InputDefinition } from "@/types";
import { z } from "zod";

/**
 * Validates an input value based on its definition
 * @param inputDef The input definition
 * @param value The value to validate
 * @returns Validation result with isValid flag and message
 */
export function validateInput(
  inputDef: InputDefinition,
  value: any
): { isValid: boolean; message: string } {
  // Skip validation for empty optional inputs
  if (!inputDef.required && (value === undefined || value === null || value === "")) {
    return { isValid: true, message: "" };
  }

  // Validate based on input type
  switch (inputDef.input_type) {
    case "string":
      return validateString(inputDef, value);
    case "int":
    case "float":
    case "number":
      return validateNumber(inputDef, value);
    case "list":
    case "array":
      return validateArray(inputDef, value);
    case "dict":
    case "json":
    case "object":
      return validateObject(inputDef, value);
    default:
      // For other types, assume valid
      return { isValid: true, message: "" };
  }
}

/**
 * Validates a string input
 */
function validateString(
  inputDef: InputDefinition,
  value: any
): { isValid: boolean; message: string } {
  if (typeof value !== "string") {
    return { isValid: false, message: "Must be a string" };
  }

  const minLength = inputDef.min_length || 0;
  const maxLength = inputDef.max_length || Number.MAX_SAFE_INTEGER;

  if (value.length < minLength) {
    return { isValid: false, message: `Must be at least ${minLength} characters` };
  }
  if (value.length > maxLength) {
    return { isValid: false, message: `Must be at most ${maxLength} characters` };
  }

  // Check pattern if specified
  if (inputDef.pattern) {
    try {
      const regex = new RegExp(inputDef.pattern);
      if (!regex.test(value)) {
        return { isValid: false, message: inputDef.pattern_error || "Invalid format" };
      }
    } catch (e) {
      console.error("Invalid regex pattern:", inputDef.pattern);
    }
  }

  return { isValid: true, message: "Valid input" };
}

/**
 * Validates a number input
 */
function validateNumber(
  inputDef: InputDefinition,
  value: any
): { isValid: boolean; message: string } {
  const numValue = Number(value);
  if (isNaN(numValue)) {
    return { isValid: false, message: "Must be a number" };
  }

  const minValue =
    inputDef.min_value !== undefined ? Number(inputDef.min_value) : Number.MIN_SAFE_INTEGER;
  const maxValue =
    inputDef.max_value !== undefined ? Number(inputDef.max_value) : Number.MAX_SAFE_INTEGER;

  if (numValue < minValue) {
    return { isValid: false, message: `Must be at least ${minValue}` };
  }
  if (numValue > maxValue) {
    return { isValid: false, message: `Must be at most ${maxValue}` };
  }
  
  return { isValid: true, message: "Valid number" };
}

/**
 * Validates an array input
 */
function validateArray(
  inputDef: InputDefinition,
  value: any
): { isValid: boolean; message: string } {
  // Check if it's a list
  let listValue = value;
  if (typeof value === "string") {
    try {
      listValue = JSON.parse(value);
    } catch (e) {
      return { isValid: false, message: "Invalid JSON format" };
    }
  }

  if (!Array.isArray(listValue)) {
    return { isValid: false, message: "Must be an array" };
  }

  // Check min/max items
  const minItems = inputDef.min_items || 0;
  const maxItems = inputDef.max_items || Number.MAX_SAFE_INTEGER;

  if (listValue.length < minItems) {
    return { isValid: false, message: `Must have at least ${minItems} items` };
  }
  if (listValue.length > maxItems) {
    return { isValid: false, message: `Must have at most ${maxItems} items` };
  }
  
  return { isValid: true, message: "Valid list" };
}

/**
 * Validates an object input
 */
function validateObject(
  inputDef: InputDefinition,
  value: any
): { isValid: boolean; message: string } {
  // Check if it's a valid JSON object
  let dictValue = value;
  if (typeof value === "string") {
    try {
      dictValue = JSON.parse(value);
    } catch (e) {
      return { isValid: false, message: "Invalid JSON format" };
    }
  }

  if (typeof dictValue !== "object" || dictValue === null || Array.isArray(dictValue)) {
    return { isValid: false, message: "Must be an object" };
  }

  // Check required keys
  const requiredKeys = inputDef.required_keys || [];
  for (const key of requiredKeys) {
    if (!(key in dictValue)) {
      return { isValid: false, message: `Missing required key: ${key}` };
    }
  }
  
  return { isValid: true, message: "Valid object" };
}

/**
 * Validates all inputs in a node configuration
 * @param inputs Array of input definitions
 * @param config The current configuration values
 * @returns Object with validation results for each input
 */
export function validateAllInputs(
  inputs: InputDefinition[],
  config: Record<string, any>
): Record<string, { isValid: boolean; message: string }> {
  const errors: Record<string, { isValid: boolean; message: string }> = {};

  // Validate each input
  inputs.forEach((inputDef) => {
    // Skip handle inputs
    if (inputDef.is_handle) return;

    // Get current value
    const value = config[inputDef.name];

    // Validate
    errors[inputDef.name] = validateInput(inputDef, value);
  });

  return errors;
}

/**
 * Creates a Zod schema for an input definition
 * @param inputDef The input definition
 * @returns A Zod schema for the input
 */
export function createSchemaForInput(inputDef: InputDefinition): z.ZodTypeAny {
  switch (inputDef.input_type) {
    case "string":
      let schema = z.string();
      
      if (inputDef.required) {
        schema = schema.min(1, { message: "This field is required" });
      } else {
        schema = schema.optional();
      }
      
      if (inputDef.min_length) {
        schema = schema.min(inputDef.min_length, { 
          message: `Must be at least ${inputDef.min_length} characters` 
        });
      }
      
      if (inputDef.max_length) {
        schema = schema.max(inputDef.max_length, { 
          message: `Must be at most ${inputDef.max_length} characters` 
        });
      }
      
      if (inputDef.pattern) {
        schema = schema.regex(new RegExp(inputDef.pattern), { 
          message: inputDef.pattern_error || "Invalid format" 
        });
      }
      
      return schema;
      
    case "int":
      let intSchema = z.coerce.number().int();
      
      if (!inputDef.required) {
        intSchema = intSchema.optional();
      }
      
      if (inputDef.min_value !== undefined) {
        intSchema = intSchema.min(Number(inputDef.min_value), { 
          message: `Must be at least ${inputDef.min_value}` 
        });
      }
      
      if (inputDef.max_value !== undefined) {
        intSchema = intSchema.max(Number(inputDef.max_value), { 
          message: `Must be at most ${inputDef.max_value}` 
        });
      }
      
      return intSchema;
      
    case "float":
    case "number":
      let numSchema = z.coerce.number();
      
      if (!inputDef.required) {
        numSchema = numSchema.optional();
      }
      
      if (inputDef.min_value !== undefined) {
        numSchema = numSchema.min(Number(inputDef.min_value), { 
          message: `Must be at least ${inputDef.min_value}` 
        });
      }
      
      if (inputDef.max_value !== undefined) {
        numSchema = numSchema.max(Number(inputDef.max_value), { 
          message: `Must be at most ${inputDef.max_value}` 
        });
      }
      
      return numSchema;
      
    case "bool":
      return inputDef.required ? z.boolean() : z.boolean().optional();
      
    case "list":
    case "array":
      let arraySchema = z.array(z.any());
      
      if (!inputDef.required) {
        arraySchema = arraySchema.optional();
      }
      
      if (inputDef.min_items !== undefined) {
        arraySchema = arraySchema.min(inputDef.min_items, { 
          message: `Must have at least ${inputDef.min_items} items` 
        });
      }
      
      if (inputDef.max_items !== undefined) {
        arraySchema = arraySchema.max(inputDef.max_items, { 
          message: `Must have at most ${inputDef.max_items} items` 
        });
      }
      
      return arraySchema;
      
    case "dict":
    case "json":
    case "object":
      let objSchema = z.record(z.any());
      
      if (!inputDef.required) {
        objSchema = objSchema.optional();
      }
      
      // Add validation for required keys if specified
      if (inputDef.required_keys && inputDef.required_keys.length > 0) {
        // This is a simplified approach - for more complex validation,
        // we would need to create a proper object schema with all properties
        objSchema = objSchema.refine(
          (obj) => {
            if (!obj) return false;
            return inputDef.required_keys!.every((key) => key in obj);
          },
          {
            message: `Missing required keys: ${inputDef.required_keys.join(", ")}`,
          }
        );
      }
      
      return objSchema;
      
    default:
      // For other types, use any schema
      return inputDef.required ? z.any() : z.any().optional();
  }
}

/**
 * Creates a Zod schema for a complete node configuration
 * @param inputs Array of input definitions
 * @returns A Zod schema for the node configuration
 */
export function createNodeConfigSchema(inputs: InputDefinition[]): z.ZodObject<any> {
  const shape: Record<string, z.ZodTypeAny> = {};
  
  inputs.forEach((input) => {
    // Skip handle inputs
    if (input.is_handle) return;
    
    shape[input.name] = createSchemaForInput(input);
  });
  
  return z.object(shape);
}
