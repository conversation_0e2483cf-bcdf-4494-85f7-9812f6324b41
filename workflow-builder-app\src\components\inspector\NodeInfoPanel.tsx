import React from "react";
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { ScrollArea } from "@/components/ui/scroll-area";

interface NodeInfoPanelProps {
  node: Node<WorkflowNodeData>;
}

/**
 * Component for displaying node information in the inspector
 */
export function NodeInfoPanel({ node }: NodeInfoPanelProps) {
  return (
    <ScrollArea className="h-full flex-grow overflow-auto p-4">
      <div className="space-y-4">
        {/* Node Information */}
        <div>
          <h3 className="mb-3 text-sm font-medium">Node Information</h3>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between border-b py-1.5">
              <span className="text-muted-foreground">Type</span>
              <span className="font-medium">{node.data.type}</span>
            </div>
            <div className="flex justify-between border-b py-1.5">
              <span className="text-muted-foreground">ID</span>
              <span className="font-mono text-[10px] font-medium">
                {node.id}
              </span>
            </div>
            <div className="flex justify-between border-b py-1.5">
              <span className="text-muted-foreground">Category</span>
              <span className="font-medium">
                {node.data.definition?.category || "Unknown"}
              </span>
            </div>
          </div>
        </div>

        {/* Input Handles */}
        {node.data.definition?.inputs?.filter((i: any) => i.is_handle)?.length > 0 && (
          <div>
            <h3 className="mb-2 text-sm font-medium">Input Handles</h3>
            <div className="space-y-1.5 text-xs">
              {node.data.definition.inputs
                .filter((i: any) => i.is_handle)
                .map((input: any) => (
                  <div
                    key={input.name}
                    className="flex justify-between border-b py-1.5"
                  >
                    <span className="text-muted-foreground">
                      {input.display_name}
                    </span>
                    <span className="font-medium">
                      {input.input_types?.join(", ") || "any"}
                    </span>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Output Handles */}
        {node.data.definition?.outputs?.length > 0 && (
          <div>
            <h3 className="mb-2 text-sm font-medium">Output Handles</h3>
            <div className="space-y-1.5 text-xs">
              {node.data.definition.outputs.map((output: any) => (
                <div
                  key={output.name}
                  className="flex justify-between border-b py-1.5"
                >
                  <span className="text-muted-foreground">{output.display_name}</span>
                  <span className="font-medium">{output.output_type}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </ScrollArea>
  );
}
