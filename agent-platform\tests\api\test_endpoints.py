import pytest
from fastapi import status
from app.api.models import ChatRequest, AgentResponse

def test_health_check(test_client):
    response = test_client.get("/health")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {"status": "healthy"}

def test_chat_endpoint(test_client):
    request_data = {
        "message": "Hello",
        "session_id": "test-session",
        "context": {}
    }
    response = test_client.post("/chat", json=request_data)
    assert response.status_code == status.HTTP_200_OK
    assert "response" in response.json()

def test_invalid_chat_request(test_client):
    request_data = {
        "message": "",  # Invalid empty message
        "session_id": "test-session"
    }
    response = test_client.post("/chat", json=request_data)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

@pytest.mark.asyncio
async def test_stream_chat(test_client):
    request_data = {
        "message": "Hello",
        "session_id": "test-session",
        "stream": True
    }
    response = test_client.post("/chat/stream", json=request_data)
    assert response.status_code == status.HTTP_200_OK
    assert "text/event-stream" in response.headers["content-type"]