from typing import Optional, List
from pydantic import BaseModel, EmailStr
from datetime import datetime

class RoleInfo(BaseModel):
    role_id: str
    name: str
    description: str
    permissions: List[str]
    created_at: str
    updated_at: str

class AdminInfo(BaseModel):
    admin_id: str
    email: str
    full_name: str
    roles: List[RoleInfo]
    created_at: str
    updated_at: str

class AdminCreate(BaseModel):
    email: EmailStr
    password: str
    full_name: str
    role_ids: Optional[List[str]] = None

class AdminUpdate(BaseModel):
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    role_ids: Optional[List[str]] = None

class RoleCreate(BaseModel):
    name: str
    description: str
    permissions: List[str]

class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    permissions: Optional[List[str]] = None

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class LoginResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    roles: List[RoleInfo]

class AdminResponse(BaseModel):
    success: bool
    message: str
    admin: Optional[AdminInfo] = None

class RoleResponse(BaseModel):
    success: bool
    message: str
    role: Optional[RoleInfo] = None

class AdminList(BaseModel):
    admins: List[AdminInfo]
    total: int
    page: int
    total_pages: int

class RoleList(BaseModel):
    roles: List[RoleInfo]
    total: int
    page: int
    total_pages: int 
