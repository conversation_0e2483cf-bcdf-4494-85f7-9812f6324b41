# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import notification_pb2 as notification__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in notification_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class NotificationServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetUserNotifications = channel.unary_unary(
                '/notifications.NotificationService/GetUserNotifications',
                request_serializer=notification__pb2.GetUserNotificationsRequest.SerializeToString,
                response_deserializer=notification__pb2.GetUserNotificationsResponse.FromString,
                _registered_method=True)
        self.MarkNotificationAsSeen = channel.unary_unary(
                '/notifications.NotificationService/MarkNotificationAsSeen',
                request_serializer=notification__pb2.MarkNotificationAsSeenRequest.SerializeToString,
                response_deserializer=notification__pb2.MarkNotificationAsSeenResponse.FromString,
                _registered_method=True)
        self.GetNotificationById = channel.unary_unary(
                '/notifications.NotificationService/GetNotificationById',
                request_serializer=notification__pb2.GetNotificationByIdRequest.SerializeToString,
                response_deserializer=notification__pb2.GetNotificationByIdResponse.FromString,
                _registered_method=True)


class NotificationServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetUserNotifications(self, request, context):
        """Get paginated notifications for a user
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MarkNotificationAsSeen(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNotificationById(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_NotificationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetUserNotifications': grpc.unary_unary_rpc_method_handler(
                    servicer.GetUserNotifications,
                    request_deserializer=notification__pb2.GetUserNotificationsRequest.FromString,
                    response_serializer=notification__pb2.GetUserNotificationsResponse.SerializeToString,
            ),
            'MarkNotificationAsSeen': grpc.unary_unary_rpc_method_handler(
                    servicer.MarkNotificationAsSeen,
                    request_deserializer=notification__pb2.MarkNotificationAsSeenRequest.FromString,
                    response_serializer=notification__pb2.MarkNotificationAsSeenResponse.SerializeToString,
            ),
            'GetNotificationById': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNotificationById,
                    request_deserializer=notification__pb2.GetNotificationByIdRequest.FromString,
                    response_serializer=notification__pb2.GetNotificationByIdResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'notifications.NotificationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('notifications.NotificationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class NotificationService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetUserNotifications(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/notifications.NotificationService/GetUserNotifications',
            notification__pb2.GetUserNotificationsRequest.SerializeToString,
            notification__pb2.GetUserNotificationsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MarkNotificationAsSeen(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/notifications.NotificationService/MarkNotificationAsSeen',
            notification__pb2.MarkNotificationAsSeenRequest.SerializeToString,
            notification__pb2.MarkNotificationAsSeenResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNotificationById(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/notifications.NotificationService/GetNotificationById',
            notification__pb2.GetNotificationByIdRequest.SerializeToString,
            notification__pb2.GetNotificationByIdResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
