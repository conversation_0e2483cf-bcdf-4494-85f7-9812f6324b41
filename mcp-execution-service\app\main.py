# main.py
import asyncio
import signal
import logging
import os

from app.core_.kafka_service import KafkaMCPService

logger = logging.getLogger(__name__)


async def main():
    """
    Main entry point for the Kafka MCP Executor Service.
    Initializes the service, starts the Kafka consumer in the background,
    and runs indefinitely until a shutdown signal is received.
    """
    logger.info(f"Initializing MCP Executor Service in process {os.getpid()}...")
    service = KafkaMCPService()
    loop = asyncio.get_running_loop()

    shutdown_future = loop.create_future()

    def signal_handler(signame):
        """Handles SIGINT and SIGTERM signals for graceful shutdown."""
        logger.warning(f"Received signal {signame}. Initiating graceful shutdown...")
        if not shutdown_future.done():
            shutdown_future.set_result(True)

    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(sig, signal_handler, sig.name)
        except NotImplementedError:

            logger.warning(
                f"Cannot add signal handler for {sig.name} on this platform."
            )

    consumer_task = None
    try:
        logger.info("Starting Kafka consumer task in the background...")

        consumer_task = asyncio.create_task(
            service.start_consumer(), name="KafkaConsumerTask"
        )
        service._consumer_task = consumer_task

        logger.info("Service is now running and listening for Kafka messages.")
        logger.info("Press Ctrl+C or send SIGTERM to initiate graceful shutdown.")

        await shutdown_future

    except asyncio.CancelledError:
        logger.info("Main task cancelled.")
    except Exception as e:
        logger.error(f"Unexpected error in main execution: {e}", exc_info=True)
    finally:
        logger.info("Shutdown sequence started...")

        for sig in (signal.SIGINT, signal.SIGTERM):
            try:
                loop.remove_signal_handler(sig)
            except (NotImplementedError, ValueError):
                pass

        if consumer_task and not consumer_task.done():
            logger.info("Cancelling the Kafka consumer task...")
            consumer_task.cancel()
            try:
                await consumer_task
            except asyncio.CancelledError:
                logger.info("Consumer task successfully cancelled and cleaned up.")
            except Exception as e:

                logger.error(f"Error during consumer task cleanup: {e}", exc_info=True)
        else:
            logger.info("Consumer task was already completed or not started.")

        logger.info("Service shutdown complete.")


if __name__ == "__main__":
    print("Starting service...")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Process interrupted by KeyboardInterrupt (Ctrl+C).")
    except Exception as main_err:
        logger.critical(f"Service failed to run: {main_err}", exc_info=True)
        exit(1)
    print("Service finished.")
