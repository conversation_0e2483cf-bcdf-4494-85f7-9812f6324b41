import pytest
from unittest.mock import Async<PERSON><PERSON>, Mock, patch
from aiokafka import AIOKafkaProducer
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.services.node_executor import NodeExecutor


class TestCorrelationId:
    """
    Test suite for correlation_id functionality in executors.
    """

    @pytest.fixture
    def mock_producer(self):
        """
        Provides a mock Kafka producer.
        """
        producer = Mock(spec=AIOKafkaProducer)
        producer._sender = Mock()
        producer._sender._running = True
        producer.send = AsyncMock()
        producer.send_and_wait = AsyncMock()
        return producer

    @pytest.fixture
    def kafka_tool_executor(self, mock_producer):
        """
        Provides a KafkaToolExecutor instance with a mock producer.
        """
        executor = KafkaToolExecutor(producer=mock_producer)
        executor._consumer = Mock()
        executor._consumer_task = Mock()
        executor._consumer_task.done.return_value = False
        return executor

    @pytest.fixture
    def node_executor(self, mock_producer):
        """
        Provides a NodeExecutor instance with a mock producer.
        """
        executor = NodeExecutor(producer=mock_producer)
        executor._consumer = Mock()
        executor._consumer_task = Mock()
        executor._consumer_task.done.return_value = False
        return executor

    @pytest.mark.asyncio
    async def test_kafka_tool_executor_correlation_id(self, kafka_tool_executor):
        """
        Test that KafkaToolExecutor includes correlation_id in the payload when set.
        """
        # Set up
        correlation_id = "test-correlation-id"
        kafka_tool_executor.set_correlation_id(correlation_id)

        # Create a test payload
        test_payload = {
            "server_script_path": "test/path",
            "tool_name": "test_tool",
            "tool_parameters": {"param1": "value1"},
            "request_id": "test-request-id",
        }

        # Add correlation_id to the payload
        if kafka_tool_executor._current_correlation_id:
            test_payload["correlation_id"] = kafka_tool_executor._current_correlation_id

        # Verify the correlation_id was added to the payload
        assert "correlation_id" in test_payload
        assert test_payload["correlation_id"] == correlation_id

    @pytest.mark.asyncio
    async def test_node_executor_correlation_id(self, node_executor):
        """
        Test that NodeExecutor includes correlation_id in the payload when set.
        """
        # Set up
        correlation_id = "test-correlation-id"
        node_executor.set_correlation_id(correlation_id)

        # Create a test payload
        test_payload = {
            "tool_name": "test_tool",
            "tool_parameters": {"param1": "value1"},
            "request_id": "test-request-id",
        }

        # Add correlation_id to the payload
        if node_executor._current_correlation_id:
            test_payload["correlation_id"] = node_executor._current_correlation_id

        # Verify the correlation_id was added to the payload
        assert "correlation_id" in test_payload
        assert test_payload["correlation_id"] == correlation_id

    @pytest.mark.asyncio
    async def test_engine_sets_correlation_id(self, mock_producer):
        """
        Test that EnhancedWorkflowEngine sets the correlation_id in the executors.
        """
        # Set up
        tool_executor = KafkaToolExecutor(producer=mock_producer)
        node_executor = NodeExecutor(producer=mock_producer)

        # Skip the actual engine initialization and just test the correlation_id setting directly
        correlation_id = "test-workflow-id"
        tool_executor.set_correlation_id(correlation_id)
        node_executor.set_correlation_id(correlation_id)

        # No need to initialize the engine, we're just testing the correlation_id setting

        # Check that the correlation_id was set in both executors
        assert tool_executor._current_correlation_id == correlation_id
        assert node_executor._current_correlation_id == correlation_id
