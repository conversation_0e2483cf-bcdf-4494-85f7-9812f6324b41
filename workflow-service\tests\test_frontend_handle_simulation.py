"""
Frontend Handle Simulation Test

This test simulates the exact frontend logic from WorkflowNode.tsx
to verify that the handle filtering works correctly.
"""

import pytest
from app.components.control_flow.conditionalNode import ConditionalNode


def evaluate_visibility_rule(rule, config):
    """Evaluate a single visibility rule against configuration."""
    target_value = config.get(rule.field_name)

    # Simple equality check (default)
    if not hasattr(rule, 'operator') or rule.operator is None or rule.operator == "equals":
        return target_value == rule.field_value

    # Not equals
    if rule.operator == "not_equals":
        return target_value != rule.field_value

    # Add other operators as needed
    return False


def evaluate_visibility_rules(rules, config, logic_operator="OR"):
    """Evaluate a set of visibility rules against configuration."""
    if not rules or len(rules) == 0:
        return True

    # Evaluate each rule
    results = [evaluate_visibility_rule(rule, config) for rule in rules]

    # Combine results based on logic operator
    if logic_operator == "AND":
        return all(results)
    else:  # OR
        return any(results)


def get_visible_handles_using_backend_logic(node, node_config):
    """
    Use the backend's visibility rules to determine which handles should be visible.
    This properly evaluates the visibility rules defined in the component.

    Args:
        node: The ConditionalNode instance
        node_config: Configuration object (data.config)

    Returns:
        List of visible handle input names
    """
    visible_handles = []

    for inp in node.inputs:
        # Only check handle inputs
        if hasattr(inp, 'is_handle') and inp.is_handle:
            # Check if this input should be visible based on its visibility rules
            is_visible = True

            if hasattr(inp, 'visibility_rules') and inp.visibility_rules:
                # Use our visibility evaluation logic
                logic_operator = getattr(inp, 'visibility_logic', 'OR')
                is_visible = evaluate_visibility_rules(inp.visibility_rules, node_config, logic_operator)

                # Debug: Print visibility evaluation for condition input handles
                if inp.name.endswith("_input_handle"):
                    print(f"DEBUG: {inp.name} visibility_rules: {[(rule.field_name, rule.field_value, getattr(rule, 'operator', 'equals')) for rule in inp.visibility_rules]}")
                    print(f"DEBUG: {inp.name} config values: {[(rule.field_name, node_config.get(rule.field_name)) for rule in inp.visibility_rules]}")
                    print(f"DEBUG: {inp.name} is_visible: {is_visible}")

            if is_visible:
                visible_handles.append(inp.name)

    return visible_handles


class TestFrontendHandleSimulation:
    """Test frontend handle filtering simulation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.node = ConditionalNode()
        self.original_type = "ConditionalNode"

    def test_scenario_1_initial_state(self):
        """
        Scenario 1: Initial State (Default Configuration)
        - Number of Additional Conditions: 0 (default)
        - Expected: 3 input handles (Primary + Condition 1 + Condition 2)
        """
        config = {
            "num_additional_conditions": 0
            # Sources default to "node_output" if not specified
        }

        handle_inputs = get_visible_handles_using_backend_logic(self.node, config)

        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            "condition_2_input_handle"
        ]

        print(f"Scenario 1 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 3, f"Expected 3 handles, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)

    def test_scenario_2_adding_additional_conditions(self):
        """
        Scenario 2: Adding Additional Conditions
        - Number of Additional Conditions: 2 (4 total conditions)
        - Expected: 5 input handles (Primary + 4 Conditions)
        """
        config = {
            "num_additional_conditions": 2
            # All sources default to "node_output"
        }

        handle_inputs = get_visible_handles_using_backend_logic(self.node, config)

        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            "condition_2_input_handle",
            "condition_3_input_handle",
            "condition_4_input_handle"
        ]

        print(f"Scenario 2 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 5, f"Expected 5 handles, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)

    def test_scenario_3_source_selection_single_change(self):
        """
        Scenario 3: Source Selection Behavior - Single Change
        - Number of Additional Conditions: 2
        - Condition 2 source changed to "Global Context"
        - Expected: 4 input handles (Primary + Condition 1, 3, 4)
        """
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "node_output",
            "condition_2_source": "global_context",  # Changed to global_context
            "condition_3_source": "node_output",
            "condition_4_source": "node_output"
        }

        handle_inputs = get_visible_handles_using_backend_logic(self.node, config)

        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            # condition_2_input_handle should be missing
            "condition_3_input_handle",
            "condition_4_input_handle"
        ]

        print(f"Scenario 3 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 4, f"Expected 4 handles, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)
        assert "condition_2_input_handle" not in handle_inputs

    def test_scenario_4_source_selection_multiple_changes(self):
        """
        Scenario 4: Source Selection Behavior - Multiple Changes
        - Number of Additional Conditions: 2
        - Conditions 2 and 3 changed to "Global Context"
        - Expected: 3 input handles (Primary + Condition 1, 4)
        """
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "node_output",
            "condition_2_source": "global_context",
            "condition_3_source": "global_context",
            "condition_4_source": "node_output"
        }

        handle_inputs = get_visible_handles_using_backend_logic(self.node, config)

        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            # condition_2_input_handle should be missing
            # condition_3_input_handle should be missing
            "condition_4_input_handle"
        ]

        print(f"Scenario 4 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 3, f"Expected 3 handles, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)
        assert "condition_2_input_handle" not in handle_inputs
        assert "condition_3_input_handle" not in handle_inputs

    def test_scenario_5_change_back_to_node_output(self):
        """
        Scenario 5: Source Selection Behavior - Change Back
        - Number of Additional Conditions: 2
        - All conditions changed back to "Node Output"
        - Expected: 5 input handles (Primary + 4 Conditions)
        """
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "node_output",
            "condition_2_source": "node_output",  # Changed back
            "condition_3_source": "node_output",
            "condition_4_source": "node_output"
        }

        handle_inputs = get_visible_handles_using_backend_logic(self.node, config)

        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            "condition_2_input_handle",  # Should reappear
            "condition_3_input_handle",
            "condition_4_input_handle"
        ]

        print(f"Scenario 5 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 5, f"Expected 5 handles, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)

    def test_scenario_6_all_global_context(self):
        """
        Scenario 6: Edge Case - All Global Context
        - Number of Additional Conditions: 2
        - All conditions set to "Global Context"
        - Expected: 1 input handle (Primary only)
        """
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "global_context",
            "condition_2_source": "global_context",
            "condition_3_source": "global_context",
            "condition_4_source": "global_context"
        }

        handle_inputs = get_visible_handles_using_backend_logic(self.node, config)

        expected_handles = [
            "primary_input_data"
        ]

        print(f"Scenario 6 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 1, f"Expected 1 handle, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
