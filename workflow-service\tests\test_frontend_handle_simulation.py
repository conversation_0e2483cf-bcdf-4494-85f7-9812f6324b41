"""
Frontend Handle Simulation Test

This test simulates the exact frontend logic from WorkflowNode.tsx
to verify that the handle filtering works correctly.
"""

import pytest
from app.components.control_flow.conditionalNode import ConditionalNode


def simulate_frontend_handle_filtering(node_inputs, node_config, original_type):
    """
    Simulate the exact frontend handle filtering logic from WorkflowNode.tsx

    Args:
        node_inputs: List of input definitions from the component
        node_config: Configuration object (data.config)
        original_type: The original component type

    Returns:
        List of visible input names
    """
    visible_inputs = []

    for inp in node_inputs:
        should_show = True

        # Debug: Print input details
        print(f"DEBUG: Input {inp.name}, is_handle: {getattr(inp, 'is_handle', False)}, type: {type(inp).__name__}")

        # Check if this is a handle input
        if hasattr(inp, 'is_handle') and inp.is_handle:
            # Special logic for ConditionalNode condition input handles
            if (original_type == "ConditionalNode" and
                inp.name.startswith("condition_") and
                inp.name.endswith("_input_handle")):

                # Extract the condition number from the handle name
                import re
                match = re.match(r"condition_(\d+)_input_handle", inp.name)
                if match and match.group(1):
                    condition_index = int(match.group(1))
                    num_additional_conditions = int(node_config.get("num_additional_conditions", 0))
                    total_conditions = 2 + num_additional_conditions  # Base 2 + additional

                    # Show the handle if the condition index is within the total conditions
                    # AND the source for this condition is "node_output"
                    if condition_index <= total_conditions:
                        source_value = node_config.get(f"condition_{condition_index}_source", "node_output")
                        should_show = (source_value == "node_output")
                    else:
                        should_show = False
                else:
                    should_show = False
            else:
                # For all other handles (like primary_input_data), show them
                should_show = True
        else:
            should_show = False  # Only show handles

        if should_show:
            visible_inputs.append(inp.name)

    return visible_inputs


class TestFrontendHandleSimulation:
    """Test frontend handle filtering simulation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.node = ConditionalNode()
        self.original_type = "ConditionalNode"

    def test_scenario_1_initial_state(self):
        """
        Scenario 1: Initial State (Default Configuration)
        - Number of Additional Conditions: 0 (default)
        - Expected: 3 input handles (Primary + Condition 1 + Condition 2)
        """
        config = {
            "num_additional_conditions": 0
            # Sources default to "node_output" if not specified
        }

        visible_inputs = simulate_frontend_handle_filtering(
            self.node.inputs, config, self.original_type
        )

        # Filter to only handle inputs
        handle_inputs = [name for name in visible_inputs if "handle" in name]

        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            "condition_2_input_handle"
        ]

        print(f"Scenario 1 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 3, f"Expected 3 handles, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)

    def test_scenario_2_adding_additional_conditions(self):
        """
        Scenario 2: Adding Additional Conditions
        - Number of Additional Conditions: 2 (4 total conditions)
        - Expected: 5 input handles (Primary + 4 Conditions)
        """
        config = {
            "num_additional_conditions": 2
            # All sources default to "node_output"
        }

        visible_inputs = simulate_frontend_handle_filtering(
            self.node.inputs, config, self.original_type
        )

        handle_inputs = [name for name in visible_inputs if "handle" in name]

        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            "condition_2_input_handle",
            "condition_3_input_handle",
            "condition_4_input_handle"
        ]

        print(f"Scenario 2 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 5, f"Expected 5 handles, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)

    def test_scenario_3_source_selection_single_change(self):
        """
        Scenario 3: Source Selection Behavior - Single Change
        - Number of Additional Conditions: 2
        - Condition 2 source changed to "Global Context"
        - Expected: 4 input handles (Primary + Condition 1, 3, 4)
        """
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "node_output",
            "condition_2_source": "global_context",  # Changed to global_context
            "condition_3_source": "node_output",
            "condition_4_source": "node_output"
        }

        visible_inputs = simulate_frontend_handle_filtering(
            self.node.inputs, config, self.original_type
        )

        handle_inputs = [name for name in visible_inputs if "handle" in name]

        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            # condition_2_input_handle should be missing
            "condition_3_input_handle",
            "condition_4_input_handle"
        ]

        print(f"Scenario 3 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 4, f"Expected 4 handles, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)
        assert "condition_2_input_handle" not in handle_inputs

    def test_scenario_4_source_selection_multiple_changes(self):
        """
        Scenario 4: Source Selection Behavior - Multiple Changes
        - Number of Additional Conditions: 2
        - Conditions 2 and 3 changed to "Global Context"
        - Expected: 3 input handles (Primary + Condition 1, 4)
        """
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "node_output",
            "condition_2_source": "global_context",
            "condition_3_source": "global_context",
            "condition_4_source": "node_output"
        }

        visible_inputs = simulate_frontend_handle_filtering(
            self.node.inputs, config, self.original_type
        )

        handle_inputs = [name for name in visible_inputs if "handle" in name]

        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            # condition_2_input_handle should be missing
            # condition_3_input_handle should be missing
            "condition_4_input_handle"
        ]

        print(f"Scenario 4 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 3, f"Expected 3 handles, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)
        assert "condition_2_input_handle" not in handle_inputs
        assert "condition_3_input_handle" not in handle_inputs

    def test_scenario_5_change_back_to_node_output(self):
        """
        Scenario 5: Source Selection Behavior - Change Back
        - Number of Additional Conditions: 2
        - All conditions changed back to "Node Output"
        - Expected: 5 input handles (Primary + 4 Conditions)
        """
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "node_output",
            "condition_2_source": "node_output",  # Changed back
            "condition_3_source": "node_output",
            "condition_4_source": "node_output"
        }

        visible_inputs = simulate_frontend_handle_filtering(
            self.node.inputs, config, self.original_type
        )

        handle_inputs = [name for name in visible_inputs if "handle" in name]

        expected_handles = [
            "primary_input_data",
            "condition_1_input_handle",
            "condition_2_input_handle",  # Should reappear
            "condition_3_input_handle",
            "condition_4_input_handle"
        ]

        print(f"Scenario 5 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 5, f"Expected 5 handles, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)

    def test_scenario_6_all_global_context(self):
        """
        Scenario 6: Edge Case - All Global Context
        - Number of Additional Conditions: 2
        - All conditions set to "Global Context"
        - Expected: 1 input handle (Primary only)
        """
        config = {
            "num_additional_conditions": 2,
            "condition_1_source": "global_context",
            "condition_2_source": "global_context",
            "condition_3_source": "global_context",
            "condition_4_source": "global_context"
        }

        visible_inputs = simulate_frontend_handle_filtering(
            self.node.inputs, config, self.original_type
        )

        handle_inputs = [name for name in visible_inputs if "handle" in name]

        expected_handles = [
            "primary_input_data"
        ]

        print(f"Scenario 6 - Visible handles: {handle_inputs}")
        assert len(handle_inputs) == 1, f"Expected 1 handle, got {len(handle_inputs)}: {handle_inputs}"
        assert set(handle_inputs) == set(expected_handles)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
