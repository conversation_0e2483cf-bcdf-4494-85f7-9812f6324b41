from fastapi import APIRout<PERSON>, Depends, HTTPException, Query
from fastapi.security import OAuth2PasswordRequestForm
from app.services.admin_service import AdminServiceClient
from app.core.security import get_current_user
from app.schemas.admin import (
    AdminCreate,
    AdminUpdate,
    AdminResponse,
    LoginResponse,
    TokenResponse,
    AdminList,
    RoleCreate,
    RoleUpdate,
    RoleResponse,
    RoleList,
)
from app.utils.parse_error import parse_error

# Create separate routers for admin auth, admin management, and role management
admin_auth_router = APIRouter(prefix="/admin/auth", tags=["admin auth"])
admin_router = APIRouter(prefix="/admin", tags=["admin management"])
role_router = APIRouter(prefix="/roles", tags=["role management"])

admin_service = AdminServiceClient()

# Admin Authentication Routes


@admin_auth_router.post(
    "/login",
    summary="Admin Login",
    description="""
    This endpoint allows an admin to log in using their email and password.

    - If the admin does not exist, a 404 error is returned.
    - If the credentials are incorrect, a 401 error is returned.
    - On success, an access token, refresh token, and admin info are returned.
    """,
    responses={
        200: {
            "description": "Login successful",
            "content": {
                "application/json": {
                    "example": {
                        "access_token": "your_access_token",
                        "refresh_token": "your_refresh_token",
                        "token_type": "bearer",
                        "roles": [
                            {
                                "roleId": "1",
                                "name": "Super Admin",
                                "description": "Has full access",
                                "permissions": ["manage_users", "manage_roles"],
                                "createdAt": "2024-01-01T00:00:00Z",
                                "updatedAt": "2024-01-01T00:00:00Z",
                            }
                        ],
                    }
                }
            },
        },
        401: {
            "description": "Invalid credentials",
            "content": {"application/json": {"example": {"detail": "Invalid credentials"}}},
        },
        404: {
            "description": "Admin not found",
            "content": {"application/json": {"example": {"detail": "Admin does not exist"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
    response_model=LoginResponse,
)
@admin_auth_router.post("/login", response_model=LoginResponse)
async def admin_login(form_data: OAuth2PasswordRequestForm = Depends()):
    try:
        response = await admin_service.login(email=form_data.username, password=form_data.password)
        if not response.success:
            raise HTTPException(status_code=401, detail=response.message)

        # Extract the roles information correctly
        roles_data = []
        for role in response.admin.roles:
            roles_data.append(
                {
                    "role_id": role.roleId,
                    "name": role.name,
                    "description": role.description,
                    "permissions": role.permissions,
                    "created_at": role.createdAt,
                    "updated_at": role.updatedAt,
                }
            )

        return {
            "access_token": response.accessToken,
            "refresh_token": response.refreshToken,
            "token_type": "bearer",
            "roles": roles_data,  # Ensure the `admin` object is returned
        }
    except Exception as e:
        print(str(e))
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@admin_auth_router.post(
    "/access-token",
    summary="Refresh Admin Access Token",
    description="""
    This endpoint allows refreshing an admin's access token using their refresh token.

    - If the refresh token is invalid or expired, a 401 error is returned.
    - If the admin does not exist, a 404 error is returned.
    - On success, a new access token and refresh token are returned.

    """,
    responses={
        200: {
            "description": "Token refresh successful",
            "content": {
                "application/json": {
                    "example": {
                        "access_token": "new_access_token",
                        "refresh_token": "new_refresh_token",
                        "token_type": "bearer",
                    }
                }
            },
        },
        401: {
            "description": "Authentication failed",
            "content": {"application/json": {"example": {"detail": "Refresh token expired"}}},
        },
        404: {
            "description": "Admin not found",
            "content": {"application/json": {"example": {"detail": "Admin not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
    response_model=TokenResponse,
)
@admin_auth_router.post("/access-token", response_model=TokenResponse)
async def admin_refresh_token(refresh_token: str):
    try:
        response = await admin_service.access_token(refresh_token)
        if not response.success:
            raise HTTPException(status_code=401, detail=response.message)
        return {
            "access_token": response.accessToken,
            "refresh_token": response.refreshToken,
            "token_type": "bearer",
        }
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# Admin Management Routes
@admin_router.post("", response_model=AdminResponse)
async def create_admin(admin_data: AdminCreate, current_user: dict = Depends(get_current_user)):
    response = await admin_service.create_admin(
        email=admin_data.email,
        password=admin_data.password,
        full_name=admin_data.full_name,
        role_ids=admin_data.role_ids,
    )
    return response


@admin_router.get("/{admin_id}", response_model=AdminResponse)
async def get_admin(admin_id: str, current_user: dict = Depends(get_current_user)):
    response = await admin_service.get_admin(admin_id)
    return response


@admin_router.put("/{admin_id}", response_model=AdminResponse)
async def update_admin(
    admin_id: str, admin_data: AdminUpdate, current_user: dict = Depends(get_current_user)
):
    response = await admin_service.update_admin(
        admin_id=admin_id,
        full_name=admin_data.full_name,
        email=admin_data.email,
        password=admin_data.password,
        role_ids=admin_data.role_ids,
    )
    return response


@admin_router.delete("/{admin_id}")
async def delete_admin(admin_id: str, current_user: dict = Depends(get_current_user)):
    response = await admin_service.delete_admin(admin_id)
    if not response.success:
        raise HTTPException(status_code=400, detail=response.message)
    return {"message": "Admin deleted successfully"}


@admin_router.get("", response_model=AdminList)
async def list_admins(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    current_user: dict = Depends(get_current_user),
):
    response = await admin_service.list_admins(page=page, page_size=page_size)
    return {
        "admins": response.admins,
        "total": response.total,
        "page": response.page,
        "total_pages": response.totalPages,
    }


@admin_router.post("/{admin_id}/roles", response_model=AdminResponse)
async def assign_roles(
    admin_id: str, role_ids: list[str], current_user: dict = Depends(get_current_user)
):
    response = await admin_service.assign_role(admin_id=admin_id, role_ids=role_ids)
    return response


# Role Management Routes
@role_router.post("", response_model=RoleResponse)
async def create_role(role_data: RoleCreate, current_user: dict = Depends(get_current_user)):
    response = await admin_service.create_role(
        name=role_data.name, description=role_data.description, permissions=role_data.permissions
    )
    return response


@role_router.get("/{role_id}", response_model=RoleResponse)
async def get_role(role_id: str, current_user: dict = Depends(get_current_user)):
    response = await admin_service.get_role(role_id)
    return response


@role_router.put("/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: str, role_data: RoleUpdate, current_user: dict = Depends(get_current_user)
):
    response = await admin_service.update_role(
        role_id=role_id,
        name=role_data.name,
        description=role_data.description,
        permissions=role_data.permissions,
    )
    return response


@role_router.delete("/{role_id}")
async def delete_role(role_id: str, current_user: dict = Depends(get_current_user)):
    response = await admin_service.delete_role(role_id)
    if not response.success:
        raise HTTPException(status_code=400, detail=response.message)
    return {"message": "Role deleted successfully"}


@role_router.get("", response_model=RoleList)
async def list_roles(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    current_user: dict = Depends(get_current_user),
):
    response = await admin_service.list_roles(page=page, page_size=page_size)
    return {
        "roles": response.roles,
        "total": response.total,
        "page": response.page,
        "total_pages": response.totalPages,
    }
