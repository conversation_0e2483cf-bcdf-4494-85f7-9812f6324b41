"use client";

import React from "react";
import { ResetPasswordForm } from "@/components/auth/ResetPasswordForm";
import Link from "next/link";
import { Workflow } from "lucide-react";

export default function ResetPasswordPage() {
  return (
    <div className="bg-background flex min-h-screen flex-col items-center justify-center p-4">
      <div className="bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg">
        <div className="flex flex-col items-center space-y-2">
          <div className="rounded-md bg-blue-600 p-2 shadow-md">
            <Workflow className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-center text-2xl font-bold">Reset Password</h1>
          <p className="text-muted-foreground text-center text-sm">
            Enter your email to receive a password reset link
          </p>
        </div>

        <ResetPasswordForm />

        <div className="text-center text-sm">
          <p className="text-muted-foreground">
            Remember your password?{" "}
            <Link href="/login" className="text-primary font-medium hover:underline">
              Log in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
