import logging
import asyncio
from typing import Any, AsyncGenerator, Dict, List, Optional, Tuple
from autogen_agentchat.messages import (
    TextMessage,
    UserInputRequestedEvent,
    BaseChatMessage,
)
from ..schemas.api import AgentConfig
from autogen_agentchat.base import TaskResult
from ..helper.session_manager import Session<PERSON><PERSON><PERSON>
from autogen_agentchat.agents import User<PERSON>roxy<PERSON>gent, AssistantAgent
from .agent_factory import AgentFactory

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ChatProcessor:
    def __init__(self, session_manager: SessionManager, agent_factory: AgentFactory):
        self.session_manager = session_manager
        self.agent_factory = agent_factory
        self.logger = logger
        self._active_chats = set()
        self._chat_lock = asyncio.Lock()

    async def process_chat(
        self,
        session_id: str,
        user_message: str,
        agents,
        team,
        run_id: str = None,
    ) -> AsyncGenerator[dict, None]:
        """
        Process chat with persistent memory across interactions and stream
        responses from team agent.

        Args:
            session_id: Unique session identifier
            user_message: Message from the user
            agents: List of agent instances
            team: Team instance (RoundRobinGroupChat or SelectorGroupChat)
            run_id: Optional run identifier

        Yields:
            Dictionary containing response chunks
        """
        self.logger.info(f"Starting chat processing for session_id: {session_id}")

        # Track active chat to prevent resource conflicts
        async with self._chat_lock:
            if session_id in self._active_chats:
                self.logger.warning(
                    f"Chat already in progress for session_id: {session_id}"
                )
                yield {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": {
                        "content": "Another chat is already in progress for this session. Please wait."
                    },
                    "success": False,
                    "final": True,
                }
                return
            self._active_chats.add(session_id)

        try:
            # Create and process message
            self.logger.info(f"Creating TextMessage for user input: {user_message}")
            message = TextMessage(content=user_message, source="user")

            # Update session with user message
            self.logger.info(
                "Updating session memory for session_id: %s with user message.",
                session_id,
            )
            await self.session_manager.update_session_memory(
                session_id=session_id, message={"role": "user", "content": user_message}
            )

            # Ensure team is a RoundRobinGroupChat or similar object with run_stream method
            if not hasattr(team, "run_stream"):
                self.logger.error(
                    "Team object doesn't have run_stream method: %s", type(team)
                )
                raise TypeError(
                    "Team object doesn't have run_stream method: %s" % type(team)
                )

            # Process with team and stream responses
            self.logger.info("Processing chat with team for session_id: %s", session_id)

            # Patch UserProxyAgent input function to avoid interactive prompts
            self._patch_user_proxy_agents(team, user_message)

            # Stream responses from team as they arrive
            idx = 0
            async for response in team.run_stream(task=message):
                if isinstance(response, BaseChatMessage):
                    print(f"Received message: {response}")

                    # Process the message
                    agent_id, source, content = await self._extract_message_info(
                        response, agents
                    )

                    print(f"Processed message: {content}")

                    # Update session with agent response
                    self.logger.info(
                        "Updating session memory for session_id: %s with agent response.",
                        session_id,
                    )
                    await self.session_manager.update_session_memory(
                        session_id=session_id,
                        message={"role": "assistant", "content": content},
                    )

                    # Format agent_response as a dictionary
                    agent_response_dict = {
                        "content": content,
                        "source": source,
                        "agent_id": agent_id,
                    }

                    yield {
                        "run_id": run_id,
                        "session_id": session_id,
                        "agent_response": agent_response_dict,
                        "success": True,
                        "final": False,
                    }
                    idx += 1

            # Send final message with final=True
            yield {
                "run_id": run_id,
                "session_id": session_id,
                "agent_response": {"content": ""},
                "success": True,
                "final": True,
            }

        except Exception as e:
            self.logger.error(
                "Error processing chat for session_id %s: %s", session_id, str(e)
            )
            yield {
                "run_id": run_id,
                "session_id": session_id,
                "agent_response": {"content": f"Error: {str(e)}"},
                "success": False,
                "final": True,
            }
        finally:
            # Remove from active chats
            async with self._chat_lock:
                if session_id in self._active_chats:
                    self._active_chats.remove(session_id)

    def _patch_user_proxy_agents(self, obj, user_message):
        """Recursively patch UserProxyAgent input function in team/participants"""

        async def _patched_user_input(prompt, cancellation_token=None):
            return user_message

        if isinstance(obj, UserProxyAgent):
            obj.input = _patched_user_input
        elif hasattr(obj, "participants"):
            for p in getattr(obj, "participants", []):
                self._patch_user_proxy_agents(p, user_message)

    async def _extract_message_info(
        self, message: BaseChatMessage, agents: List[AssistantAgent]
    ) -> Tuple[Optional[str], str, str]:
        """Extract agent ID, source, and content from a message"""
        if message.source == "user":
            return None, "user", message.content

        # Initialize variables
        agent_id = None
        source = message.source
        content = message.content

        # Find the agent that sent this message
        for agent in agents:
            if message.source == agent.name:
                agent_id = getattr(agent, "id", None)
                break

        # If no agent was found but we have agents, use the first one
        if agent_id is None and agents:
            agent_id = getattr(agents[0], "id", None)

        return agent_id, source, content

    async def process_chat_stream(
        self, session_id: str, user_message: str
    ) -> AsyncGenerator[dict, None]:
        """
        Process a chat message and stream the responses.

        Args:
            session_id: The session identifier
            user_message: The user's message

        Yields:
            Response content dictionaries
        """
        # Get session data
        agent_config, communication_type, memory, cancellation_token = (
            await self.session_manager.get_session_data(session_id)
        )

        # Initialize agents and team
        agents, team, _ = await self.agent_factory.initialize_chat_session(
            run_id=session_id,
            agent_configs=[agent_config],
            chat_context=[],
            communication_type=communication_type,
            session_memory=memory,
        )

        # Process the chat and yield responses
        async for response in self.process_chat(
            session_id=session_id,
            user_message=user_message,
            agents=agents,
            team=team,
        ):
            if response.get("success", False) and not response.get("final", False):
                yield response.get("agent_response", {"content": ""})

    async def chat_once(
        self,
        session_id: str,
        user_message: str,
        agents,
        team,
        run_id: str = None,
    ) -> dict:
        """
        Process chat with the team agent and return a single (non-streaming) response.
        This is useful for synchronous chat scenarios.
        """
        self.logger.info(f"Starting single-response chat for session_id: {session_id}")

        try:
            # Patch UserProxyAgent input function to avoid interactive prompt
            self._patch_user_proxy_agents(team, user_message)

            # Update session with user message
            await self.session_manager.update_session_memory(
                session_id=session_id, message={"role": "user", "content": user_message}
            )

            # Create message
            message = TextMessage(content=user_message, source="user")

            self.logger.info("Running team for single-response chat.")
            result = await team.run(task=message)

            # Extract the response content
            agent_response = ""
            agent_id = None
            source = None

            if hasattr(result, "messages") and result.messages:
                # Get the last assistant message
                for msg in reversed(result.messages):
                    if getattr(msg, "source", None) != "user":
                        agent_id, source, content = await self._extract_message_info(
                            msg, agents
                        )
                        agent_response = content
                        break
            elif hasattr(result, "content"):
                agent_response = result.content
                source = getattr(result, "source", "assistant")
            else:
                agent_response = str(result)
                source = "assistant"

            # Format agent_response as a dictionary
            agent_response_dict = {
                "content": agent_response,
                "source": source,
                "agent_id": agent_id,
            }

            # Update session with agent response
            await self.session_manager.update_session_memory(
                session_id=session_id,
                message={"role": "assistant", "content": agent_response},
            )

            return {
                "run_id": run_id,
                "session_id": session_id,
                "agent_response": agent_response_dict,
                "success": True,
                "final": True,
            }

        except Exception as e:
            self.logger.error(
                "Single-response chat failed for session_id: %s. Error: %s",
                session_id,
                e,
                exc_info=True,
            )
            return {
                "run_id": run_id,
                "session_id": session_id,
                "agent_response": {"content": f"Error processing chat: {str(e)}"},
                "success": False,
                "final": True,
            }
        finally:
            self.logger.info(
                "Finished single-response chat for session_id: %s", session_id
            )

    async def chat_with_agent_once(
        self,
        session_id: str,
        user_message: str,
        agents,
        run_id: str = None,
        cancellation_token=None,
    ) -> dict:
        """
        Process chat with a single agent (agents[0]) and return a single response.
        Patches UserProxyAgent input to avoid interactive prompt.
        """
        self.logger.info(f"Starting single-agent chat for session_id: {session_id}")

        try:
            agent = agents[0]
            # Patch UserProxyAgent input function
            if isinstance(agent, UserProxyAgent):

                async def _patched_user_input(prompt, cancellation_token=None):
                    return user_message

                agent.input = _patched_user_input

            # Update session with user message
            await self.session_manager.update_session_memory(
                session_id=session_id, message={"role": "user", "content": user_message}
            )

            # Create message and get response
            message = TextMessage(content=user_message, source="user")
            response = await agent.on_messages(
                [message],
                cancellation_token=cancellation_token,
            )

            print(f"Response: {response}")

            # Extract response data
            agent_response = {
                "content": response.chat_message.content,
                "source": response.chat_message.source,
                "models_usage": {
                    "prompt_tokens": (
                        response.chat_message.models_usage.prompt_tokens
                        if hasattr(response.chat_message, "models_usage")
                        else 0
                    ),
                    "completion_tokens": (
                        response.chat_message.models_usage.completion_tokens
                        if hasattr(response.chat_message, "models_usage")
                        else 0
                    ),
                },
                "type": (
                    response.chat_message.type
                    if hasattr(response.chat_message, "type")
                    else None
                ),
                "metadata": (
                    response.chat_message.metadata
                    if hasattr(response.chat_message, "metadata")
                    else None
                ),
            }

            # Update session with agent response
            await self.session_manager.update_session_memory(
                session_id=session_id,
                message={"role": "assistant", "content": agent_response},
            )

            return {
                "run_id": run_id,
                "session_id": session_id,
                "agent_response": agent_response,
                "success": True,
                "final": True,
            }

        except Exception as e:
            self.logger.error(
                "Single-agent chat failed for session_id: %s. Error: %s",
                session_id,
                e,
                exc_info=True,
            )
            return {
                "run_id": run_id,
                "session_id": session_id,
                "agent_response": f"Error processing chat: {str(e)}",
                "success": False,
                "final": True,
            }
        finally:
            self.logger.info(
                "Finished single-agent chat for session_id: %s", session_id
            )

    async def end_chat_session(self, session_id: str) -> bool:
        """End a chat session and clean up resources"""
        try:
            # Remove from active chats if present
            async with self._chat_lock:
                if session_id in self._active_chats:
                    self._active_chats.remove(session_id)

            # Delete session data
            await self.session_manager.delete_session(session_id)
            self.logger.info(f"Chat session {session_id} ended successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error ending chat session {session_id}: {str(e)}")
            return False

    async def get_session_history(self, session_id: str) -> List[Dict]:
        """Retrieve chat history for a session"""
        try:
            return await self.session_manager.get_session_history(session_id)
        except Exception as e:
            self.logger.error(
                f"Error retrieving session history for {session_id}: {str(e)}"
            )
            return []

    # Process message stream
    async def _process_message_stream(
        self,
        stream: Any,
        agents: list[AgentConfig],
        message_callback: None = None,
    ) -> list[tuple[int, str, str]]:
        """Process message stream and collect agent responses.

        Args:
            stream: The message stream to process.
            agents (list[Agent]): List of agent models.
            message_callback (Optional[Callable], optional): Callback function to process each message as it arrives.
                The callback should accept (agent_id, source, content) as parameters.

        Returns:
            list[tuple[int, str, str]]: A list of tuples containing agent responses.
        """

        # List of agent responses
        agent_responses = []

        # Process the message stream
        async for message in stream:
            # If message is task result
            if isinstance(message, TaskResult):
                continue

            # If message is user input request
            if isinstance(message, UserInputRequestedEvent):
                break

            # If it is an agent's response
            if message.source != "user":
                # Initialize the agent found flag
                agent_found = False

                # Initialize the agent id
                agent_id = None

                # Set the message source & content
                source = message.source
                content = message.content

                # Traverse over the agents
                for agent in agents:

                    agent_slug = agent.name

                    # If the message source is the agent's name or "agent"
                    if message.source in (agent_slug, "agent"):
                        # Set the agent ID
                        agent_id = agent.id

                        # Mark the agent as found
                        agent_found = True

                        # Break the loop
                        break

                # If no agent was found
                if not agent_found and agents:
                    # Use the first agent as fallback agent
                    agent_id = agents[0].id

                # Create the response tuple
                response_tuple = (agent_id, source, content)

                # Add to the list of responses
                agent_responses.append(response_tuple)

                # If a callback was provided
                if message_callback and agent_id:
                    # Call the callback function with message
                    await message_callback(*response_tuple)

        # Return the list of agent responses
        return agent_responses
