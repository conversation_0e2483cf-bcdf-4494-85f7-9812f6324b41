from typing import Dict, Any, List, ClassVar

from app.components.hitl.base_hitl_component import (
    BaseHITLComponent,
)
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    MultilineInput,
    BoolInput,
)
from app.models.workflow_builder.components import Output


class SlackComponent(BaseHITLComponent):
    """
    Sends Slack messages.

    This component allows workflows to send messages to Slack channels or users,
    optionally waiting for a reply. It can be used for notifications, approvals,
    or data collection from humans as part of an automated workflow.
    """

    name: ClassVar[str] = "SlackComponent"
    display_name: ClassVar[str] = "Send Slack Message"
    description: ClassVar[str] = (
        "Sends a message to a Slack channel or user and optionally waits for a reply."
    )

    icon: ClassVar[str] = (
        "MessageSquare"  # Using a generic message icon as Slack might not be available
    )

    inputs: ClassVar[List[InputBase]] = [
        # Inherit input_data and timeout_seconds from base class
        # Channel/User ID - connection handle
        HandleInput(
            name="channel_handle",
            display_name="Channel/User ID",
            is_handle=True,
            input_types=["string"],
            info="Connect the Slack channel or user ID to send the message to.",
        ),
        # Channel/User ID - direct input in inspector
        StringInput(
            name="channel",
            display_name="Channel/User ID (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The Slack channel (e.g., #general) or user ID to send the message to. Used if no connection is provided.",
        ),
        # Message - connection handle (inherited from base but overridden for clarity)
        HandleInput(
            name="message_handle",
            display_name="Message",
            is_handle=True,
            input_types=["string"],
            info="Connect the message content to send to Slack.",
        ),
        # Message - direct input in inspector (overridden from base)
        MultilineInput(
            name="message",
            display_name="Message (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The message content to send to Slack. Used if no connection is provided.",
        ),
        # Wait for reply
        BoolInput(
            name="wait_for_reply",
            display_name="Wait for Reply",
            required=False,
            is_handle=False,
            value=False,
            info="If enabled, the component will wait for a reply in the thread before continuing.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        # Inherit response_data, timed_out, and error from base class
        Output(
            name="message_id",
            display_name="Message Timestamp (ID)",
            output_type="string",
            # "The unique timestamp identifier of the sent Slack message."
        ),
        Output(
            name="sent_status",
            display_name="Sent Status",
            output_type="bool",
            # "True if the message was sent successfully, False otherwise."
        ),
        # Override action_taken from base class for clarity
        Output(
            name="action_taken",
            display_name="Action Taken",
            output_type="string",
            # "The action taken by the component, e.g., 'SENT' or 'FAILED'."
        ),
    ]

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Sends a Slack message and optionally waits for a reply.

        Args:
            **kwargs: Contains the input values:
                - channel_handle: Connected Slack channel or user ID
                - channel: Direct input Slack channel or user ID
                - message_handle: Connected message content
                - message: Direct input message content
                - wait_for_reply: Whether to wait for a reply
                - timeout_seconds: Maximum time to wait for a reply
                - input_data: Additional data for context

        Returns:
            A dictionary with:
                - message_id: The timestamp ID of the sent Slack message
                - sent_status: Whether the message was sent successfully
                - response_data: The reply message content (if wait_for_reply is True)
                - timed_out: Whether waiting for a reply timed out
                - error: Any error message
                - action_taken: The action taken ('SENT', 'FAILED', etc.)
        """
        self.log("info", "Executing SlackComponent...")

        # Initialize output variables
        message_id = None
        sent_status = False
        response_data = None
        timed_out = False
        error_msg = None
        action_taken = "PENDING"

        # Get inputs - prioritize handle inputs over direct inputs
        channel_handle = kwargs.get("channel_handle")
        channel_direct = kwargs.get("channel")
        message_handle = kwargs.get("message_handle")
        message_direct = kwargs.get("message")
        wait_for_reply = kwargs.get("wait_for_reply", False)
        timeout_seconds = kwargs.get("timeout_seconds", 3600)

        # Process inputs - prioritize handle inputs over direct inputs
        channel = channel_handle if channel_handle is not None else channel_direct
        message_text = message_handle if message_handle is not None else message_direct

        # Validate inputs
        if not channel:
            error_msg = "Slack channel or user ID is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        if not message_text:
            error_msg = "Message content is missing. Please connect or provide directly."
            self.log("error", error_msg)
            return {
                "message_id": None,
                "sent_status": False,
                "response_data": None,
                "timed_out": False,
                "error": error_msg,
                "action_taken": "FAILED",
            }

        try:
            # Log attempt
            self.log("info", f"Attempting to send Slack message to {channel}")

            # Placeholder: Get Slack Bot Token
            # slack_token = self.get_config("SLACK_BOT_TOKEN")

            # Placeholder: Instantiate Slack client
            # from slack_sdk import WebClient
            # client = WebClient(token=slack_token)

            # Placeholder: Send message to Slack
            # response = client.chat_postMessage(
            #     channel=channel,
            #     text=message_text
            # )

            # Placeholder: Check if message was sent successfully
            # if response['ok']:
            #     message_id = response['ts']
            #     sent_status = True
            #     action_taken = "SENT"
            #     self.log("info", f"Slack message sent successfully with timestamp {message_id}")
            # else:
            #     raise Exception(f"Failed to send message: {response['error']}")

            # For demonstration, simulate successful sending
            message_id = "1234567890.123456"
            sent_status = True
            action_taken = "SENT"
            self.log("info", "Slack message sent successfully")

            # Placeholder: If wait_for_reply is True, implement waiting logic
            if wait_for_reply:
                self.log("info", f"Waiting for reply (timeout: {timeout_seconds}s)")

                # Placeholder: Complex monitoring logic would be needed here
                # This could involve:
                # 1. Polling the conversation history for new replies in the thread
                # 2. Using Slack's Events API with a webhook
                # 3. Using Slack's Socket Mode for real-time events

                # For demonstration, simulate no reply received
                response_data = None
                timed_out = True
                action_taken = "TIMED_OUT"
                self.log("info", "Waiting for reply timed out")

        except Exception as e:
            error_msg = f"Failed to send Slack message: {str(e)}"
            self.log("error", error_msg)
            sent_status = False
            action_taken = "FAILED"

        # Return all outputs
        return {
            "message_id": message_id,
            "sent_status": sent_status,
            "response_data": response_data,
            "timed_out": timed_out,
            "error": error_msg,
            "action_taken": action_taken,
        }
