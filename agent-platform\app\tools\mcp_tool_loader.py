from autogen_ext.tools.mcp import (
    SseServerParams,
    Mcp<PERSON>orkbench,
    SseMcpToolAdapter,
    mcp_server_tools,
)

from pydantic import create_model, BaseModel
from autogen_core.tools import BaseTool
from autogen_core import CancellationToken
import httpx
import asyncio
import json
import logging
from typing import Dict, List, Any
from ..shared.config.base import get_settings
from ..helper.api_call import HttpRequestHelper, HttpMethods, AuthType
from ..services.mcp_service import MCPSseClientWrapper

# Configure logger
logger = logging.getLogger(__name__)

# Map JSON Schema types to Python types
json_type_to_py = {
    "string": str,
    "integer": int,
    "number": float,
    "boolean": bool,
    "object": dict,
    "array": list,
}

mcp_execute_endpoint = "mcp-execute/server"
stream_endpoint = "mcp-execute/stream"


async def load_mcp_tool_workbench_from_schema(schema: dict):
    """
    Given a schema dict, return a list of MCP tool adapters (SseMcpToolAdapter or StdioMcpToolAdapter)
    using mcp_server_tools. This is the recommended way to get all tools for an agent's tools list.
    """
    if "mcp" in schema and "sse_url" in schema["mcp"]:
        server_params = SseServerParams(url=schema["mcp"]["sse_url"])

        print(f"Loading MCP tools from schema: {server_params}")
    else:
        raise ValueError("Schema must contain 'mcp.sse_url' for SSE MCP server.")
    return McpWorkbench(server_params)


async def load_mcp_tool_adapters_from_schema(schema: dict):
    """
    Given a schema dict, return a list of MCP tool adapters (SseMcpToolAdapter or StdioMcpToolAdapter)
    using mcp_server_tools. This is the recommended way to get all tools for an agent's tools list.
    """
    if "mcp" in schema and "sse_url" in schema["mcp"]:
        server_params = SseServerParams(url=schema["mcp"]["sse_url"])
        print(f"Loading MCP tools from schema: {server_params}")
    else:
        raise ValueError("Schema must contain 'mcp.sse_url' for SSE MCP server.")

    # Get the translation tool from the server
    return await SseMcpToolAdapter.from_server_params(server_params, "translation")


async def load_mcp_tool_from_schema(schema: dict):
    """
    Given a schema dict, return a list of MCP tool adapters (SseMcpToolAdapter or StdioMcpToolAdapter)
    using mcp_server_tools. This is the recommended way to get all tools for an agent's tools list.
    """
    if "mcp" in schema and "sse_url" in schema["mcp"]:
        server_params = SseServerParams(url=schema["mcp"]["sse_url"])
        print(f"Loading MCP tools from schema: {server_params}")
    else:
        raise ValueError("Schema must contain 'mcp.sse_url' for SSE MCP server.")
    return await mcp_server_tools(server_params)


async def load_all_mcp_tool_adapters_from_schema_list(schemas: list[dict]):
    """
    Given a list of schema dicts, return a flat list of all MCP tool adapters from all servers.
    This is the recommended entrypoint for building a full agent tools list.
    """
    all_tools = []
    for schema in schemas:
        adapters = await load_mcp_tool_from_schema(schema)
        print(f"Loaded tools from schema: {adapters}")
        all_tools.extend(adapters)
    return all_tools


class McpToolLoader:
    """
    Utility class to load mcps as dynamic tools and execute them.
    """

    def __init__(self):
        """
        Initialize the mcp tool loader.

        Args:
            mcp_api_base_url: Base URL for mcp execution API
            stream_api_base_url: Base URL for streaming results
            auth_token: Optional authentication token
            auth_type: Authentication type (default: BEARER)
        """
        self.settings = get_settings()
        logger.info("Initializing McpToolLoader")

        # Initialize HTTP request helper for API calls
        self.http_client = HttpRequestHelper(
            base_url=self.settings.workflow_api_gateway.api_url,
            auth_token=self.settings.workflow_api_gateway.api_key,
            auth_type=AuthType.API_KEY,
            api_key_name="X-Agent-Platform-Auth-Key",
            timeout=60,  # Longer timeout for mcp operations
        )
        self.mcp_client = MCPSseClientWrapper()
        logger.debug(
            f"HTTP client initialized with base URL: {self.settings.workflow_api_gateway.api_url}"
        )

    async def create_mcp_tool_from_metadata(
        self, mcp_metadata: Dict[str, Any]
    ) -> BaseTool:
        """
        Create a dynamic tool from mcp metadata.

        Args:
            mcp_metadata: Mcp metadata from the API

        Returns:
            A BaseTool instance that can execute the mcp
        """

        url = mcp_metadata.get("url")

        tools = await self.mcp_client.connect_to_server(
            url=mcp_metadata.get("url"),
            headers=None,
        )

        print(f"Tools: {tools}")

        mcp_tools = []

        for tool in tools:
            name = tool.name
            description = tool.description
            json_schema = tool.inputSchema

            logger.info(f"Mcp tool schema for {name}: {json_schema}")

            # Create the dynamic tool with our execution endpoint
            mcp_tools.append(
                self._create_mcp_execution_tool(
                    url=url,
                    name=name,
                    description=description,
                    json_schema=json_schema,
                )
            )

        return mcp_tools

    def _create_mcp_execution_tool(
        self, url: str, name: str, description: str, json_schema: Dict[str, Any]
    ) -> BaseTool:
        """
        Create a dynamic tool that executes a mcp.

        Args:
            url: url of the mcp to execute
            name: Name for the tool
            description: Description for the tool
            json_schema: JSON schema for the tool parameters

        Returns:
            A BaseTool instance
        """
        logger.debug(f"Creating execution tool for mcp {url} with name {name}")

        # Build fields dict with correct Python types
        fields = {}
        for k, v in json_schema.get("properties", {}).items():
            py_type = json_type_to_py.get(v.get("type", "string"), str)
            default = ... if k in json_schema.get("required", []) else None
            fields[k] = (py_type, default)
            logger.debug(f"Added field {k} with type {py_type} to tool schema")

        ArgsModel = create_model(f"{name}_Args", **fields)
        ArgsModel.model_rebuild()  # Ensure the model is fully defined
        logger.debug(f"Created args model for {name}: {ArgsModel}")

        class McpTool(BaseTool[ArgsModel, BaseModel]):
            def __init__(self, loader: McpToolLoader):
                super().__init__(
                    args_type=ArgsModel,
                    return_type=BaseModel,
                    name=name,
                    description=description,
                )
                self.url = url
                self.loader = loader
                logger.debug(f"Initialized McpTool for {url}")

            async def run(self, args: ArgsModel, cancellation_token: CancellationToken):
                """
                Execute the mcp and return the result as a string.
                If the mcp returns a stream, join the stream and return the full result.
                If an error occurs, return a user-friendly error message.
                """
                args_dict = args.model_dump(exclude_none=True)

                payload = {
                    "server_script_path": self.url,
                    "tool_name": name,
                    "tool_parameters": args_dict,
                    "retries": 3,
                }
                logger.debug(f"Mcp execution payload: {payload}")

                try:
                    logger.info(f"Sending execution request for mcp {self.url}")
                    response = await asyncio.to_thread(
                        self.loader.http_client.post,
                        mcp_execute_endpoint,
                        json_data=payload,
                    )
                    logger.debug(f"Execution response: {response}")

                    if isinstance(response, dict) and "RequestId" in response:
                        request_id = response["RequestId"]
                        logger.info(f"Got request ID from response: {request_id}")
                    else:
                        logger.warning(f"No request ID in response: {response}")

                    # Stream the results
                    async def stream_results():
                        stream_url = f"{stream_endpoint}/{request_id}"
                        logger.info(f"Starting to stream results from: {stream_url}")

                        async with httpx.AsyncClient(
                            base_url=self.loader.http_client.base_url,
                            headers=self.loader.http_client.headers,
                            timeout=300,
                        ) as client:
                            try:
                                logger.info(
                                    f"Opening stream connection to {stream_url}"
                                )
                                async with client.stream(
                                    HttpMethods.GET.value, stream_url
                                ) as response:
                                    response.raise_for_status()
                                    logger.info(
                                        f"Stream connection established, status: {response.status_code}"
                                    )

                                    buffer = ""
                                    async for chunk in response.aiter_text():
                                        logger.info(
                                            f"Received stream chunk of size: {len(chunk)} bytes"
                                        )
                                        buffer += chunk
                                        while "\n\n" in buffer:
                                            message, buffer = buffer.split("\n\n", 1)
                                            for line in message.split("\n"):
                                                if line.startswith("data:"):
                                                    data = line[5:].strip()
                                                    try:
                                                        json_data = json.loads(data)
                                                        logger.info(
                                                            f"Parsed JSON data from stream: {json_data}"
                                                        )
                                                        yield json.dumps(json_data)
                                                    except json.JSONDecodeError:
                                                        logger.warning(
                                                            f"Failed to parse JSON from stream: {data}"
                                                        )
                                                        yield data
                            except Exception as e:
                                logger.error(
                                    f"Error streaming results: {str(e)}", exc_info=True
                                )
                                yield json.dumps(
                                    {"error": f"Streaming error: {str(e)}"}
                                )

                    # Join the stream and return the full result as a string
                    logger.info(f"Returning stream generator for mcp {self.url}")
                    result_chunks = []
                    async for chunk in stream_results():
                        result_chunks.append(chunk)
                    return "\n".join(result_chunks)

                except Exception as e:
                    logger.error(
                        f"Error executing mcp {self.url}: {str(e)}",
                        exc_info=True,
                    )
                    return f"Mcp execution error: {str(e)}"

            async def join_stream(self, value):
                """
                Helper to join all chunks from an async generator.
                If value is already a string, return as is.
                """
                logger.info(f"Joining stream for mcp {self.url}")
                if hasattr(value, "__aiter__"):
                    results = []
                    async for chunk in value:
                        logger.info(f"Appending chunk of size: {len(chunk)} bytes")
                        results.append(chunk)
                    joined = "\n".join(results)
                    logger.info(f"Joined stream, total size: {len(joined)} bytes")
                    return joined
                logger.info("Value is not a stream, returning as string")
                return str(value)

        logger.info(f"Created mcp tool: {name}")
        return McpTool(self)

    async def load_mcps_as_tools(self, mcps) -> List[BaseTool]:
        """
        Load all available mcps as tools.

        Args:
            mcps_endpoint: API endpoint to get mcp metadata

        Returns:
            List of BaseTool instances for each mcp
        """
        try:
            logger.info(
                f"Loading mcps as tools, count: {len(mcps) if isinstance(mcps, list) else 'unknown'}"
            )

            if not isinstance(mcps, list):
                logger.error(f"Error: Expected list of mcps, got {type(mcps)}")
                return []

            # Create a tool for each mcp
            tools = []
            for mcp in mcps:
                try:
                    mcp_id = mcp.get("id", "unknown")
                    logger.info(f"Creating tool for mcp: {mcp_id}")
                    mcp_tools = await self.create_mcp_tool_from_metadata(mcp)
                    tools.extend(mcp_tools)
                    logger.info(f"Successfully created tool for mcp: {mcp_id}")
                except Exception as e:
                    logger.error(
                        f"Error creating tool for mcp {mcp.get('id', 'unknown')}: {str(e)}",
                        exc_info=True,
                    )

            logger.info(f"Successfully loaded {len(tools)} mcp tools")
            return tools

        except Exception as e:
            logger.error(f"Error loading mcps: {str(e)}", exc_info=True)
            return []
