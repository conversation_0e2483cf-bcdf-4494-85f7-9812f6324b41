import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, CheckCircle, XCircle, Loader2 } from "lucide-react";
import { sendApprovalDecision } from "@/lib/api";

interface ApprovalRequestProps {
  correlationId: string;
  nodeId: string;
  nodeName: string;
  onApprovalSent: () => void;
}

export function ApprovalRequest({
  correlationId,
  nodeId,
  nodeName,
  onApprovalSent,
}: ApprovalRequestProps) {
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  // Log when the approval component mounts
  useEffect(() => {
    console.log("ApprovalRequest component mounted with:", {
      correlationId,
      nodeId,
      nodeName
    });

    // Force a UI update by dispatching a custom event
    window.dispatchEvent(new CustomEvent('approval-ui-update'));

    return () => {
      console.log("ApprovalRequest component unmounted");
    };
  }, [correlationId, nodeId, nodeName]);

  const handleApproval = async (decision: "approve" | "reject") => {
    setIsSubmitting(true);
    setError(null);

    try {
      const result = await sendApprovalDecision(correlationId, decision);

      if (result.success) {
        console.log(`Successfully sent ${decision} decision for correlation ID: ${correlationId}`);
        onApprovalSent();
      } else {
        setError(result.error || `Failed to send ${decision} decision`);
      }
    } catch (err) {
      setError(`Failed to send approval: ${err instanceof Error ? err.message : "Unknown error"}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="mb-4 rounded-md border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-900/20">
      <div className="mb-2 flex items-center gap-2">
        <AlertCircle className="h-5 w-5 text-yellow-500" />
        <h3 className="font-medium">Approval Required</h3>
      </div>

      <p className="mb-3 text-sm">
        Node <span className="font-medium">{nodeName}</span> requires your approval to continue
        execution.
        {nodeId && <span className="block mt-1 text-xs text-gray-500">Node ID: {nodeId}</span>}
        {correlationId && (
          <span className="block text-xs text-gray-500">Correlation ID: {correlationId}</span>
        )}
      </p>

      {error && (
        <div className="mb-3 rounded border border-red-100 bg-red-50 p-2 text-sm text-red-800 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300">
          {error}
        </div>
      )}

      <div className="flex gap-2">
        <Button
          onClick={() => handleApproval("approve")}
          disabled={isSubmitting}
          className="bg-green-600 text-white hover:bg-green-700"
        >
          {isSubmitting ? (
            <Loader2 className="mr-1 h-4 w-4 animate-spin" />
          ) : (
            <CheckCircle className="mr-1 h-4 w-4" />
          )}
          Approve
        </Button>

        <Button
          onClick={() => handleApproval("reject")}
          disabled={isSubmitting}
          variant="outline"
          className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-800 dark:text-red-300 dark:hover:bg-red-900/20"
        >
          {isSubmitting ? (
            <Loader2 className="mr-1 h-4 w-4 animate-spin" />
          ) : (
            <XCircle className="mr-1 h-4 w-4" />
          )}
          Reject
        </Button>
      </div>
    </div>
  );
}
