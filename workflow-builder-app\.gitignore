# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
/.nyc_output
/tests/coverage

# next.js
/.next/
/out/
.swc/

# production
/build
/dist

# misc
.DS_Store
*.pem
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
tsconfig.tsbuildinfo

# logs
logs
*.log

# cache
.eslintcache
.stylelintcache

# storybook
storybook-static/

# generated files
src/generated/
tests/


docs/