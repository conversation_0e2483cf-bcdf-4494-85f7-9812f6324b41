"use client";

import React from "react";
import { LoginForm } from "@/components/auth/LoginForm";
import Link from "next/link";
import { Workflow } from "lucide-react";
import { Button } from "@/components/ui/button";
import { authRoute } from "@/shared/routes";

export default function LoginPage() {
  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    window.location.href = authRoute;
  };
  return (
    <div className="bg-background flex min-h-screen flex-col items-center justify-center p-4">
      <div className="bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg">
        <div className="flex flex-col items-center space-y-2">
          <div className="rounded-md bg-blue-600 p-2 shadow-md">
            <Workflow className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-center text-2xl font-bold">Workflow Builder</h1>
          <p className="text-muted-foreground text-center text-sm">
            Log in to access your workflows
          </p>
        </div>
        <form onSubmit={handleLogin}>
          <Button type="submit" className="w-full">
            Login
          </Button>
        </form>
        {/* <LoginForm /> */}

        {/* <div className="text-center text-sm">
          <p className="text-muted-foreground">
            Don&apos;t have an account?{" "}
            <Link href="/signup" className="text-primary font-medium hover:underline">
              Sign up
            </Link>
          </p>
        </div> */}
      </div>
    </div>
  );
}
