# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import workflow_pb2 as workflow__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in workflow_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class WorkflowServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.createWorkflow = channel.unary_unary(
                '/workflow.WorkflowService/createWorkflow',
                request_serializer=workflow__pb2.CreateWorkflowRequest.SerializeToString,
                response_deserializer=workflow__pb2.CreateWorkflowResponse.FromString,
                _registered_method=True)
        self.getWorkflow = channel.unary_unary(
                '/workflow.WorkflowService/getWorkflow',
                request_serializer=workflow__pb2.GetWorkflowRequest.SerializeToString,
                response_deserializer=workflow__pb2.WorkflowResponse.FromString,
                _registered_method=True)
        self.updateWorkflow = channel.unary_unary(
                '/workflow.WorkflowService/updateWorkflow',
                request_serializer=workflow__pb2.UpdateWorkflowRequest.SerializeToString,
                response_deserializer=workflow__pb2.UpdateWorkflowResponse.FromString,
                _registered_method=True)
        self.deleteWorkflow = channel.unary_unary(
                '/workflow.WorkflowService/deleteWorkflow',
                request_serializer=workflow__pb2.DeleteWorkflowRequest.SerializeToString,
                response_deserializer=workflow__pb2.DeleteWorkflowResponse.FromString,
                _registered_method=True)
        self.listWorkflows = channel.unary_unary(
                '/workflow.WorkflowService/listWorkflows',
                request_serializer=workflow__pb2.ListWorkflowsRequest.SerializeToString,
                response_deserializer=workflow__pb2.ListWorkflowsResponse.FromString,
                _registered_method=True)
        self.listWorkflowsByUserId = channel.unary_unary(
                '/workflow.WorkflowService/listWorkflowsByUserId',
                request_serializer=workflow__pb2.ListWorkflowsByUserIdRequest.SerializeToString,
                response_deserializer=workflow__pb2.ListWorkflowsResponse.FromString,
                _registered_method=True)
        self.createWorkflowFromTemplate = channel.unary_unary(
                '/workflow.WorkflowService/createWorkflowFromTemplate',
                request_serializer=workflow__pb2.CreateWorkflowFromTemplateRequest.SerializeToString,
                response_deserializer=workflow__pb2.CreateWorkflowFromTemplateResponse.FromString,
                _registered_method=True)
        self.createWorkflowTemplate = channel.unary_unary(
                '/workflow.WorkflowService/createWorkflowTemplate',
                request_serializer=workflow__pb2.CreateTemplateRequest.SerializeToString,
                response_deserializer=workflow__pb2.CreateTemplateResponse.FromString,
                _registered_method=True)
        self.getTemplate = channel.unary_unary(
                '/workflow.WorkflowService/getTemplate',
                request_serializer=workflow__pb2.GetTemplateRequest.SerializeToString,
                response_deserializer=workflow__pb2.GetTemplateResponse.FromString,
                _registered_method=True)
        self.listTemplates = channel.unary_unary(
                '/workflow.WorkflowService/listTemplates',
                request_serializer=workflow__pb2.ListTemplatesRequest.SerializeToString,
                response_deserializer=workflow__pb2.ListTemplatesResponse.FromString,
                _registered_method=True)
        self.updateTemplate = channel.unary_unary(
                '/workflow.WorkflowService/updateTemplate',
                request_serializer=workflow__pb2.UpdateTemplateRequest.SerializeToString,
                response_deserializer=workflow__pb2.UpdateTemplateResponse.FromString,
                _registered_method=True)
        self.deleteTemplate = channel.unary_unary(
                '/workflow.WorkflowService/deleteTemplate',
                request_serializer=workflow__pb2.DeleteTemplateRequest.SerializeToString,
                response_deserializer=workflow__pb2.DeleteTemplateResponse.FromString,
                _registered_method=True)
        self.getWorkflowsByIds = channel.unary_unary(
                '/workflow.WorkflowService/getWorkflowsByIds',
                request_serializer=workflow__pb2.GetWorkflowsByIdsRequest.SerializeToString,
                response_deserializer=workflow__pb2.GetWorkflowsByIdsResponse.FromString,
                _registered_method=True)
        self.toggleWorkflowVisibility = channel.unary_unary(
                '/workflow.WorkflowService/toggleWorkflowVisibility',
                request_serializer=workflow__pb2.ToggleWorkflowVisibilityRequest.SerializeToString,
                response_deserializer=workflow__pb2.ToggleWorkflowVisibilityResponse.FromString,
                _registered_method=True)
        self.updateWorkflowSettings = channel.unary_unary(
                '/workflow.WorkflowService/updateWorkflowSettings',
                request_serializer=workflow__pb2.UpdateWorkflowSettingsRequest.SerializeToString,
                response_deserializer=workflow__pb2.UpdateWorkflowSettingsResponse.FromString,
                _registered_method=True)
        self.listTemplatesByUserId = channel.unary_unary(
                '/workflow.WorkflowService/listTemplatesByUserId',
                request_serializer=workflow__pb2.ListTemplatesByUserIdRequest.SerializeToString,
                response_deserializer=workflow__pb2.ListTemplatesResponse.FromString,
                _registered_method=True)
        self.discoverComponents = channel.unary_unary(
                '/workflow.WorkflowService/discoverComponents',
                request_serializer=workflow__pb2.DiscoverComponentsRequest.SerializeToString,
                response_deserializer=workflow__pb2.DiscoverComponentsResponse.FromString,
                _registered_method=True)
        self.validateWorkflow = channel.unary_unary(
                '/workflow.WorkflowService/validateWorkflow',
                request_serializer=workflow__pb2.ValidateWorkflowRequest.SerializeToString,
                response_deserializer=workflow__pb2.ValidateWorkflowResponse.FromString,
                _registered_method=True)
        self.getMarketplaceWorkflows = channel.unary_unary(
                '/workflow.WorkflowService/getMarketplaceWorkflows',
                request_serializer=workflow__pb2.GetMarketplaceWorkflowsRequest.SerializeToString,
                response_deserializer=workflow__pb2.GetMarketplaceWorkflowsResponse.FromString,
                _registered_method=True)
        self.getMarketplaceWorkflowDetail = channel.unary_unary(
                '/workflow.WorkflowService/getMarketplaceWorkflowDetail',
                request_serializer=workflow__pb2.GetMarketplaceWorkflowDetailRequest.SerializeToString,
                response_deserializer=workflow__pb2.GetMarketplaceWorkflowDetailResponse.FromString,
                _registered_method=True)
        self.rateWorkflow = channel.unary_unary(
                '/workflow.WorkflowService/rateWorkflow',
                request_serializer=workflow__pb2.RateWorkflowRequest.SerializeToString,
                response_deserializer=workflow__pb2.RateWorkflowResponse.FromString,
                _registered_method=True)
        self.useWorkflow = channel.unary_unary(
                '/workflow.WorkflowService/useWorkflow',
                request_serializer=workflow__pb2.UseWorkflowRequest.SerializeToString,
                response_deserializer=workflow__pb2.UseWorkflowResponse.FromString,
                _registered_method=True)


class WorkflowServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def createWorkflow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getWorkflow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateWorkflow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteWorkflow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listWorkflows(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listWorkflowsByUserId(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createWorkflowFromTemplate(self, request, context):
        """Add this to the WorkflowTemplateService
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createWorkflowTemplate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getTemplate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listTemplates(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateTemplate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteTemplate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getWorkflowsByIds(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def toggleWorkflowVisibility(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateWorkflowSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listTemplatesByUserId(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def discoverComponents(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def validateWorkflow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getMarketplaceWorkflows(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getMarketplaceWorkflowDetail(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def rateWorkflow(self, request, context):
        """New endpoints for rating and using workflows
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def useWorkflow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_WorkflowServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'createWorkflow': grpc.unary_unary_rpc_method_handler(
                    servicer.createWorkflow,
                    request_deserializer=workflow__pb2.CreateWorkflowRequest.FromString,
                    response_serializer=workflow__pb2.CreateWorkflowResponse.SerializeToString,
            ),
            'getWorkflow': grpc.unary_unary_rpc_method_handler(
                    servicer.getWorkflow,
                    request_deserializer=workflow__pb2.GetWorkflowRequest.FromString,
                    response_serializer=workflow__pb2.WorkflowResponse.SerializeToString,
            ),
            'updateWorkflow': grpc.unary_unary_rpc_method_handler(
                    servicer.updateWorkflow,
                    request_deserializer=workflow__pb2.UpdateWorkflowRequest.FromString,
                    response_serializer=workflow__pb2.UpdateWorkflowResponse.SerializeToString,
            ),
            'deleteWorkflow': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteWorkflow,
                    request_deserializer=workflow__pb2.DeleteWorkflowRequest.FromString,
                    response_serializer=workflow__pb2.DeleteWorkflowResponse.SerializeToString,
            ),
            'listWorkflows': grpc.unary_unary_rpc_method_handler(
                    servicer.listWorkflows,
                    request_deserializer=workflow__pb2.ListWorkflowsRequest.FromString,
                    response_serializer=workflow__pb2.ListWorkflowsResponse.SerializeToString,
            ),
            'listWorkflowsByUserId': grpc.unary_unary_rpc_method_handler(
                    servicer.listWorkflowsByUserId,
                    request_deserializer=workflow__pb2.ListWorkflowsByUserIdRequest.FromString,
                    response_serializer=workflow__pb2.ListWorkflowsResponse.SerializeToString,
            ),
            'createWorkflowFromTemplate': grpc.unary_unary_rpc_method_handler(
                    servicer.createWorkflowFromTemplate,
                    request_deserializer=workflow__pb2.CreateWorkflowFromTemplateRequest.FromString,
                    response_serializer=workflow__pb2.CreateWorkflowFromTemplateResponse.SerializeToString,
            ),
            'createWorkflowTemplate': grpc.unary_unary_rpc_method_handler(
                    servicer.createWorkflowTemplate,
                    request_deserializer=workflow__pb2.CreateTemplateRequest.FromString,
                    response_serializer=workflow__pb2.CreateTemplateResponse.SerializeToString,
            ),
            'getTemplate': grpc.unary_unary_rpc_method_handler(
                    servicer.getTemplate,
                    request_deserializer=workflow__pb2.GetTemplateRequest.FromString,
                    response_serializer=workflow__pb2.GetTemplateResponse.SerializeToString,
            ),
            'listTemplates': grpc.unary_unary_rpc_method_handler(
                    servicer.listTemplates,
                    request_deserializer=workflow__pb2.ListTemplatesRequest.FromString,
                    response_serializer=workflow__pb2.ListTemplatesResponse.SerializeToString,
            ),
            'updateTemplate': grpc.unary_unary_rpc_method_handler(
                    servicer.updateTemplate,
                    request_deserializer=workflow__pb2.UpdateTemplateRequest.FromString,
                    response_serializer=workflow__pb2.UpdateTemplateResponse.SerializeToString,
            ),
            'deleteTemplate': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteTemplate,
                    request_deserializer=workflow__pb2.DeleteTemplateRequest.FromString,
                    response_serializer=workflow__pb2.DeleteTemplateResponse.SerializeToString,
            ),
            'getWorkflowsByIds': grpc.unary_unary_rpc_method_handler(
                    servicer.getWorkflowsByIds,
                    request_deserializer=workflow__pb2.GetWorkflowsByIdsRequest.FromString,
                    response_serializer=workflow__pb2.GetWorkflowsByIdsResponse.SerializeToString,
            ),
            'toggleWorkflowVisibility': grpc.unary_unary_rpc_method_handler(
                    servicer.toggleWorkflowVisibility,
                    request_deserializer=workflow__pb2.ToggleWorkflowVisibilityRequest.FromString,
                    response_serializer=workflow__pb2.ToggleWorkflowVisibilityResponse.SerializeToString,
            ),
            'updateWorkflowSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.updateWorkflowSettings,
                    request_deserializer=workflow__pb2.UpdateWorkflowSettingsRequest.FromString,
                    response_serializer=workflow__pb2.UpdateWorkflowSettingsResponse.SerializeToString,
            ),
            'listTemplatesByUserId': grpc.unary_unary_rpc_method_handler(
                    servicer.listTemplatesByUserId,
                    request_deserializer=workflow__pb2.ListTemplatesByUserIdRequest.FromString,
                    response_serializer=workflow__pb2.ListTemplatesResponse.SerializeToString,
            ),
            'discoverComponents': grpc.unary_unary_rpc_method_handler(
                    servicer.discoverComponents,
                    request_deserializer=workflow__pb2.DiscoverComponentsRequest.FromString,
                    response_serializer=workflow__pb2.DiscoverComponentsResponse.SerializeToString,
            ),
            'validateWorkflow': grpc.unary_unary_rpc_method_handler(
                    servicer.validateWorkflow,
                    request_deserializer=workflow__pb2.ValidateWorkflowRequest.FromString,
                    response_serializer=workflow__pb2.ValidateWorkflowResponse.SerializeToString,
            ),
            'getMarketplaceWorkflows': grpc.unary_unary_rpc_method_handler(
                    servicer.getMarketplaceWorkflows,
                    request_deserializer=workflow__pb2.GetMarketplaceWorkflowsRequest.FromString,
                    response_serializer=workflow__pb2.GetMarketplaceWorkflowsResponse.SerializeToString,
            ),
            'getMarketplaceWorkflowDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.getMarketplaceWorkflowDetail,
                    request_deserializer=workflow__pb2.GetMarketplaceWorkflowDetailRequest.FromString,
                    response_serializer=workflow__pb2.GetMarketplaceWorkflowDetailResponse.SerializeToString,
            ),
            'rateWorkflow': grpc.unary_unary_rpc_method_handler(
                    servicer.rateWorkflow,
                    request_deserializer=workflow__pb2.RateWorkflowRequest.FromString,
                    response_serializer=workflow__pb2.RateWorkflowResponse.SerializeToString,
            ),
            'useWorkflow': grpc.unary_unary_rpc_method_handler(
                    servicer.useWorkflow,
                    request_deserializer=workflow__pb2.UseWorkflowRequest.FromString,
                    response_serializer=workflow__pb2.UseWorkflowResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'workflow.WorkflowService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('workflow.WorkflowService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class WorkflowService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def createWorkflow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/createWorkflow',
            workflow__pb2.CreateWorkflowRequest.SerializeToString,
            workflow__pb2.CreateWorkflowResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getWorkflow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/getWorkflow',
            workflow__pb2.GetWorkflowRequest.SerializeToString,
            workflow__pb2.WorkflowResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateWorkflow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/updateWorkflow',
            workflow__pb2.UpdateWorkflowRequest.SerializeToString,
            workflow__pb2.UpdateWorkflowResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteWorkflow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/deleteWorkflow',
            workflow__pb2.DeleteWorkflowRequest.SerializeToString,
            workflow__pb2.DeleteWorkflowResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listWorkflows(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/listWorkflows',
            workflow__pb2.ListWorkflowsRequest.SerializeToString,
            workflow__pb2.ListWorkflowsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listWorkflowsByUserId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/listWorkflowsByUserId',
            workflow__pb2.ListWorkflowsByUserIdRequest.SerializeToString,
            workflow__pb2.ListWorkflowsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createWorkflowFromTemplate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/createWorkflowFromTemplate',
            workflow__pb2.CreateWorkflowFromTemplateRequest.SerializeToString,
            workflow__pb2.CreateWorkflowFromTemplateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createWorkflowTemplate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/createWorkflowTemplate',
            workflow__pb2.CreateTemplateRequest.SerializeToString,
            workflow__pb2.CreateTemplateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getTemplate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/getTemplate',
            workflow__pb2.GetTemplateRequest.SerializeToString,
            workflow__pb2.GetTemplateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listTemplates(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/listTemplates',
            workflow__pb2.ListTemplatesRequest.SerializeToString,
            workflow__pb2.ListTemplatesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateTemplate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/updateTemplate',
            workflow__pb2.UpdateTemplateRequest.SerializeToString,
            workflow__pb2.UpdateTemplateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteTemplate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/deleteTemplate',
            workflow__pb2.DeleteTemplateRequest.SerializeToString,
            workflow__pb2.DeleteTemplateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getWorkflowsByIds(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/getWorkflowsByIds',
            workflow__pb2.GetWorkflowsByIdsRequest.SerializeToString,
            workflow__pb2.GetWorkflowsByIdsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def toggleWorkflowVisibility(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/toggleWorkflowVisibility',
            workflow__pb2.ToggleWorkflowVisibilityRequest.SerializeToString,
            workflow__pb2.ToggleWorkflowVisibilityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateWorkflowSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/updateWorkflowSettings',
            workflow__pb2.UpdateWorkflowSettingsRequest.SerializeToString,
            workflow__pb2.UpdateWorkflowSettingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listTemplatesByUserId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/listTemplatesByUserId',
            workflow__pb2.ListTemplatesByUserIdRequest.SerializeToString,
            workflow__pb2.ListTemplatesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def discoverComponents(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/discoverComponents',
            workflow__pb2.DiscoverComponentsRequest.SerializeToString,
            workflow__pb2.DiscoverComponentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def validateWorkflow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/validateWorkflow',
            workflow__pb2.ValidateWorkflowRequest.SerializeToString,
            workflow__pb2.ValidateWorkflowResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getMarketplaceWorkflows(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/getMarketplaceWorkflows',
            workflow__pb2.GetMarketplaceWorkflowsRequest.SerializeToString,
            workflow__pb2.GetMarketplaceWorkflowsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getMarketplaceWorkflowDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/getMarketplaceWorkflowDetail',
            workflow__pb2.GetMarketplaceWorkflowDetailRequest.SerializeToString,
            workflow__pb2.GetMarketplaceWorkflowDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def rateWorkflow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/rateWorkflow',
            workflow__pb2.RateWorkflowRequest.SerializeToString,
            workflow__pb2.RateWorkflowResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def useWorkflow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/workflow.WorkflowService/useWorkflow',
            workflow__pb2.UseWorkflowRequest.SerializeToString,
            workflow__pb2.UseWorkflowResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
