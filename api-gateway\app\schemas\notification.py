from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

class NotificationInfo(BaseModel):
    id: str
    title: str
    user_id: str
    link: Optional[str] = None
    logo: Optional[str] = None
    seen: bool
    created_at: str

class PaginationMetadata(BaseModel):
    total: int
    totalPages: int
    currentPage: int
    pageSize: int
    hasNextPage: bool
    hasPreviousPage: bool

class PaginatedNotificationResponse(BaseModel):
    data: List[NotificationInfo]
    metadata: PaginationMetadata

class MarkAsSeenResponse(BaseModel):
    success: bool
    message: str

class GetNotificationByIdResponse(BaseModel):
    notification: NotificationInfo
    success: bool
    message: str