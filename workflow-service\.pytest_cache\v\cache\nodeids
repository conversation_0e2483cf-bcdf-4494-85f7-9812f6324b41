["tests/components/ai/test_agentic_ai.py::test_agentic_ai_missing_api_key", "tests/components/ai/test_agentic_ai.py::test_agentic_ai_missing_autogen", "tests/components/ai/test_agentic_ai.py::test_agentic_ai_missing_objective", "tests/components/ai/test_agentic_ai.py::test_agentic_ai_successful_execution", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_all_condition_input_handles_generated", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_condition_2_input_handle_generation", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_condition_count_logic", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_constants_and_limits", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_dynamic_outputs_generation", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_input_handle_visibility_rules", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_input_source_selection_logic", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_input_visibility_rules", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_num_additional_conditions_input", "tests/test_conditional_node_switch_case.py::TestConditionalNodeSwitchCase::test_evaluate_condition_equals_operator", "tests/test_conditional_node_switch_case.py::TestConditionalNodeSwitchCase::test_evaluate_condition_not_equals_operator", "tests/test_conditional_node_switch_case.py::TestConditionalNodeSwitchCase::test_successful_execute_method_single_condition", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_build_method_backward_compatibility", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_build_method_deprecation_warning", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_build_method_orientation_mapping", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_component_inputs", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_component_metadata", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_component_outputs", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_auto_detect_columns", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_auto_detect_records", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_columns_orientation", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_missing_input_data", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_pandas_import_error", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_records_orientation", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_get_input_value", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_backward_compatibility_traditional_structures", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_exact_path_at_notation_deeply_nested", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_exact_path_at_notation_nested", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_exact_path_at_notation_simple", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_exact_path_at_notation_with_array_index", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_field_matching_mode_key_based_only", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_field_matching_mode_property_based_only", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_property_based_duplicate_property_names", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_property_based_missing_data_field", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_smart_search_mixed_structure_key_based_priority", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_smart_search_mixed_structure_property_based_fallback", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_smart_search_property_based_flat_structure", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_smart_search_property_based_multiple_fields", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_smart_search_property_based_nested_structure", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_exact_path_mode_unchanged", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_legacy_build_method_with_smart_search", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_smart_search_field_not_found", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_smart_search_finds_nested_field", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_smart_search_returns_first_match", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_smart_search_with_array_in_structure", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_smart_search_with_simple_structure"]