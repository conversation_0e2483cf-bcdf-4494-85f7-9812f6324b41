import React from "react";
import { InputDefinition } from "@/types";
import { ValidationWrapper } from "./ValidationWrapper";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { DynamicInput } from "@/components/ui/dynamic-input";
import { cn } from "@/lib/utils";
import { formatValueForDisplay } from "@/utils/valueFormatting";

// Import specialized input components
import {
  StringInput,
  NumberInput,
  BooleanInput,
  ObjectInput,
  ArrayInput,
  SelectInput,
  HandleInput,
} from "./inputs";
import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import { ConnectedIndicator } from "../ui/connected-indicator";

interface InputRendererProps {
  inputDef: InputDefinition;
  value: any;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  connectionInfo?: {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };
  nodeId?: string;
}

/**
 * Component for rendering different types of inputs based on their definition
 */
export function InputRenderer({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  connectionInfo,
  nodeId,
}: InputRendererProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;

  // Render different input types
  switch (inputDef.input_type) {
    case "string":
    case "password":
      return (
        <StringInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          nodeId={nodeId}
        />
      );

    case "int":
    case "float":
      return (
        <NumberInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          nodeId={nodeId}
        />
      );

    case "multiline":
    case "code":
      return (
        <ValidationWrapper inputDef={inputDef} value={value}>
          <div className="relative">
            <Textarea
              id={inputId}
              value={value ?? ""}
              onChange={(e) => onChange(inputDef.name, e.target.value)}
              placeholder={`Enter ${inputDef.display_name}...`}
              className={cn("bg-background/50 mt-1 text-xs", isDisabled && "opacity-50")}
              rows={4}
              disabled={isDisabled}
            />
            {isDisabled && isConnected && <ConnectedIndicator />}
          </div>
        </ValidationWrapper>
      );

    case "bool":
      return (
        <BooleanInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          nodeId={nodeId}
        />
      );

    case "button":
      return (
        <div className="mt-2">
          <Button
            id={inputId}
            onClick={() => onChange(inputDef.name, true)}
            disabled={isDisabled}
            className="h-8 w-full text-xs"
            variant="default"
          >
            {(inputDef as any).button_text || inputDef.display_name || "Click"}
          </Button>
        </div>
      );

    case "select":
    case "dropdown":
      return (
        <SelectInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          nodeId={nodeId}
        />
      );

    case "object":
      return (
        <ObjectInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          nodeId={nodeId}
        />
      );

    case "dict":
    case "json":
      // Enhanced handling for JSON objects
      return (
        <ObjectInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          nodeId={nodeId}
        />
      );

    case "list":
    case "array":
      return (
        <ArrayInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          nodeId={nodeId}
        />
      );

    case "credential":
      // This is a simplified version - the full implementation would include
      // credential selection and management
      return (
        <div className="mt-1 space-y-2">
          <div className="flex items-center space-x-2">
            <Switch
              id={`${inputId}-use-credential`}
              checked={value?.use_credential_id || false}
              onCheckedChange={(checked) => {
                onChange(inputDef.name, {
                  ...(value || {}),
                  use_credential_id: checked,
                });
              }}
              disabled={isDisabled}
              className={isDisabled ? "opacity-50" : ""}
            />
            <Label htmlFor={`${inputId}-use-credential`} className="text-xs">
              Use credential from secure storage
            </Label>
          </div>

          {value?.use_credential_id ? (
            <p className="text-muted-foreground text-xs">Credential selection UI would go here</p>
          ) : (
            <div className="relative">
              <Input
                id={`${inputId}-value`}
                type="password"
                value={value?.value || ""}
                onChange={(e) => {
                  onChange(inputDef.name, {
                    ...(value || {}),
                    value: e.target.value,
                  });
                }}
                placeholder={`Enter ${inputDef.display_name}...`}
                className={cn("bg-background/50 h-8 text-xs", isDisabled && "opacity-50")}
                disabled={isDisabled}
              />
              {isDisabled && isConnected && <ConnectedIndicator />}
            </div>
          )}
        </div>
      );

    case "dynamic_handle":
      return (
        <DynamicInput
          inputDef={inputDef}
          currentValue={value}
          onChange={(name, value) => onChange(name, value)}
          isDisabled={isDisabled}
          isConnected={isConnected}
          minInputs={inputDef.min_handles || 0}
          maxInputs={inputDef.max_handles || 10}
          defaultInputs={inputDef.default_handles || 1}
        />
      );

    case "handle":
      return (
        <HandleInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          connectionInfo={connectionInfo}
          nodeId={nodeId}
        />
      );

    default:
      return (
        <p className="mt-1 text-xs text-red-500">Unsupported input type: {inputDef.input_type}</p>
      );
  }
}
