"use client";

import { CheckCircle, AlertCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { validatePassword } from "@/lib/schemas/auth";

export interface PasswordValidationIndicatorProps {
  password: string;
  showValidation?: boolean;
}

export function PasswordValidationIndicator({
  password,
  showValidation = false,
}: PasswordValidationIndicatorProps) {
  const [validations, setValidations] = useState({
    length: false,
    hasNumber: false,
    hasSymbol: false,
  });

  useEffect(() => {
    // Use the utility function to validate the password
    setValidations(validatePassword(password));
  }, [password]);

  return (
    <div className="flex flex-col gap-4 py-4 text-sm">
      <h3 className="mb-3 font-medium">Your password must have:</h3>
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          {validations.length ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : showValidation ? (
            <AlertCircle className="h-5 w-5 text-red-500" />
          ) : null}
          <span className="text-muted-foreground">6-15 characters</span>
        </div>
        <div className="flex items-center gap-2">
          {validations.hasNumber ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : showValidation ? (
            <AlertCircle className="h-5 w-5 text-red-500" />
          ) : null}
          <span className="text-muted-foreground">At least one number (0-9)</span>
        </div>
        <div className="flex items-center gap-2">
          {validations.hasSymbol ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : showValidation ? (
            <AlertCircle className="h-5 w-5 text-red-500" />
          ) : null}
          <span className="text-muted-foreground">At least one symbol (@, #, $, %, etc.)</span>
        </div>
      </div>
    </div>
  );
}
