#!/usr/bin/env python3
"""
Test the value wrapper fix in workflow-service SelectDataComponent.
"""

import asyncio
import sys
import os

# Add the app directory to sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

# Import workflow-service component
try:
    from components.processing.select_data import SelectDataComponent
    from models.workflow_builder.context import WorkflowContext
    print("✓ Successfully imported SelectDataComponent")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


async def test_value_wrapper_scenario():
    """Test the exact scenario from production logs."""
    print("\n=== Testing Value Wrapper Scenario (Workflow Service) ===")

    # This simulates the EXACT malformed JSON from the production logs (missing opening brace AND missing final closing brace)
    wrapped_data = {
        "value": '"data": {     "title": "${audio_script} transition-MCP_Audio_Generator_generate_audio-1747991748064",     "script": "This is the script content we want to extract from the malformed JSON",     "script_type": "TOPIC",     "video_type": "SHORT"   }'
    }

    selector = "data.script"

    print(f"Input data structure: {type(wrapped_data).__name__} with keys: {list(wrapped_data.keys())}")
    print(f"Original selector: '{selector}'")

    # Test workflow-service component
    workflow_component = SelectDataComponent()
    context = WorkflowContext()
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_data": wrapped_data,
        "data_type": "Auto-Detect",
        "selector": selector
    }

    try:
        result = await workflow_component.execute(context)
        output = result.outputs.get("output_data")
        error = result.outputs.get("error")

        print(f"\nResult:")
        print(f"  Output: {output}")
        print(f"  Error: {error}")
        print(f"  Success: {error is None and output is not None}")

        if error is None and output == "This is the script content we want to extract from the malformed JSON":
            print("✅ VALUE WRAPPER FIX WORKING! Successfully extracted script content")
            return True
        elif error is None and output is not None:
            print("⚠️  PARTIAL SUCCESS - Got output but not expected content")
            print(f"     Expected: 'This is the script content we want to extract from the malformed JSON'")
            print(f"     Got: '{output}'")
            return False
        else:
            print("❌ STILL FAILING - No output or error occurred")
            return False

    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False


async def test_normal_data_compatibility():
    """Test that normal data (without value wrapper) still works."""
    print("\n=== Testing Normal Data Compatibility (Workflow Service) ===")

    # Normal data structure (not wrapped)
    normal_data = {
        "data": {
            "title": "Test Title",
            "script": "Normal script content",
            "script_type": "TOPIC",
            "video_type": "SHORT"
        }
    }

    selector = "data.script"

    print(f"Input data structure: {type(normal_data).__name__} with keys: {list(normal_data.keys())}")
    print(f"Selector: '{selector}'")

    workflow_component = SelectDataComponent()
    context = WorkflowContext()
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_data": normal_data,
        "data_type": "Auto-Detect",
        "selector": selector
    }

    try:
        result = await workflow_component.execute(context)
        output = result.outputs.get("output_data")
        error = result.outputs.get("error")

        print(f"\nResult:")
        print(f"  Output: {output}")
        print(f"  Error: {error}")

        if error is None and output == "Normal script content":
            print("✅ NORMAL DATA STILL WORKS CORRECTLY")
            return True
        else:
            print("❌ NORMAL DATA HANDLING BROKEN")
            return False

    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False


async def test_selector_adjustment_logic():
    """Test the selector adjustment logic directly."""
    print("\n=== Testing Selector Adjustment Logic (Workflow Service) ===")

    component = SelectDataComponent()

    # Test case 1: Wrapped data should adjust selector
    wrapped_data = {"value": "some content"}
    original_selector = "data.script"
    adjusted_selector = component._adjust_selector_for_wrapped_data(wrapped_data, original_selector)

    print(f"Test 1 - Wrapped data:")
    print(f"  Input data: {wrapped_data}")
    print(f"  Original selector: '{original_selector}'")
    print(f"  Adjusted selector: '{adjusted_selector}'")
    print(f"  Expected: 'value.data.script'")
    print(f"  Correct: {adjusted_selector == 'value.data.script'}")

    # Test case 2: Normal data should not adjust selector
    normal_data = {"data": {"script": "content"}}
    adjusted_selector2 = component._adjust_selector_for_wrapped_data(normal_data, original_selector)

    print(f"\nTest 2 - Normal data:")
    print(f"  Input data: {normal_data}")
    print(f"  Original selector: '{original_selector}'")
    print(f"  Adjusted selector: '{adjusted_selector2}'")
    print(f"  Expected: 'data.script' (unchanged)")
    print(f"  Correct: {adjusted_selector2 == 'data.script'}")

    all_correct = (
        adjusted_selector == 'value.data.script' and
        adjusted_selector2 == 'data.script'
    )

    if all_correct:
        print("\n✅ SELECTOR ADJUSTMENT LOGIC IS WORKING CORRECTLY")
        return True
    else:
        print("\n❌ SELECTOR ADJUSTMENT LOGIC HAS ISSUES")
        return False


async def main():
    """Run all tests."""
    print("SelectDataComponent Value Wrapper Fix Test (Workflow Service)")
    print("=" * 70)

    test1_passed = await test_selector_adjustment_logic()
    test2_passed = await test_value_wrapper_scenario()
    test3_passed = await test_normal_data_compatibility()

    print(f"\n=== FINAL RESULTS ===")
    print(f"Selector Adjustment Logic: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Value Wrapper Fix: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"Normal Data Compatibility: {'✅ PASSED' if test3_passed else '❌ FAILED'}")

    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 ALL WORKFLOW-SERVICE TESTS PASSED!")
        print("✅ The value wrapper fix is working correctly")
        print("✅ Backward compatibility is maintained")
        print("✅ Production issue should be resolved")
        return 0
    else:
        print(f"\n❌ SOME TESTS FAILED")
        print("❌ The fix needs more work")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
