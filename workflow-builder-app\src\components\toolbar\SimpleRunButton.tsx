import React, { useState } from "react";
import { Play } from "lucide-react";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { SimpleExecutionDialog } from "@/components/execution/SimpleExecutionDialog";

interface SimpleRunButtonProps {
  nodes: Node<WorkflowNodeData>[];
  edges: Edge[];
  disabled?: boolean;
}

export function SimpleRunButton({ nodes, edges, disabled = false }: SimpleRunButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Simple handler to open the dialog
  const handleRun = () => {
    setIsDialogOpen(true);
  };

  // Close dialog
  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  return (
    <>
      <button
        type="button"
        onClick={handleRun}
        disabled={disabled}
        className="inline-flex h-8 items-center justify-center gap-1.5 rounded-md bg-green-600 px-3 py-2 text-sm font-medium text-white hover:bg-green-700 disabled:pointer-events-none disabled:opacity-50"
        title="Run workflow"
      >
        <Play className="h-4 w-4" />
        Run
      </button>

      {isDialogOpen && (
        <SimpleExecutionDialog onClose={handleCloseDialog} nodes={nodes} edges={edges} />
      )}
    </>
  );
}
