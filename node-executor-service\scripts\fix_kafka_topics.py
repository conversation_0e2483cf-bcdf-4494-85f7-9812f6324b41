import asyncio
from kafka.admin import KafkaAdminClient, NewTopic
from kafka.errors import TopicAlreadyExistsError, UnknownTopicOrPartitionError

async def fix_kafka_topics():
    print("Fixing Kafka topics...")
    
    # Connect to Kafka admin
    admin_client = KafkaAdminClient(
        bootstrap_servers='localhost:9092',
        client_id='fix-topics-admin'
    )
    
    # Topics to create/recreate
    topics_to_fix = [
        "node-execution-request",
        "node_results"
    ]
    
    # Delete topics if they exist
    for topic in topics_to_fix:
        try:
            print(f"Deleting topic {topic}...")
            admin_client.delete_topics([topic])
            print(f"✅ Deleted topic {topic}")
            # Wait for topic deletion to complete
            await asyncio.sleep(5)
        except UnknownTopicOrPartitionError:
            print(f"Topic {topic} doesn't exist, skipping deletion")
        except Exception as e:
            print(f"Error deleting topic {topic}: {str(e)}")
    
    # Create topics with proper settings
    for topic in topics_to_fix:
        try:
            print(f"Creating topic {topic}...")
            new_topic = NewTopic(
                name=topic,
                num_partitions=3,  # Multiple partitions for better parallelism
                replication_factor=1,  # Single replication factor for single-broker setup
                topic_configs={
                    "cleanup.policy": "delete",  # Use delete policy (not compact)
                    "retention.ms": "604800000",  # 7 days retention
                    "min.insync.replicas": "1"   # Only need 1 in-sync replica
                }
            )
            admin_client.create_topics([new_topic])
            print(f"✅ Created topic {topic} with proper settings")
        except TopicAlreadyExistsError:
            print(f"Topic {topic} already exists, skipping creation")
        except Exception as e:
            print(f"Error creating topic {topic}: {str(e)}")
    
    # Close the admin client
    admin_client.close()
    print("Done fixing Kafka topics")

if __name__ == "__main__":
    asyncio.run(fix_kafka_topics())
