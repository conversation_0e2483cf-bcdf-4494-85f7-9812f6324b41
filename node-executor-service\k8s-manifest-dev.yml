apiVersion: v1
kind: ServiceAccount
metadata:
  name: node-executor-service-ai-sa
  namespace: ruh-catalyst
  labels:
    name: node-executor-service-ai-sa
    namespace: ruh-catalyst
    app: node-executor-service-ai
    deployment: node-executor-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: node-executor-service-ai-dp
  namespace: ruh-catalyst
  labels:
    name: node-executor-service-ai-dp
    namespace: ruh-catalyst
    app: node-executor-service-ai
    serviceaccount: node-executor-service-ai-sa
    deployment: node-executor-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: node-executor-service-ai
      deployment: node-executor-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-catalyst
        app: node-executor-service-ai
        deployment: node-executor-service-ai-dp
    spec:
      serviceAccountName: node-executor-service-ai-sa      
      containers:
      - name: node-executor-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50052
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: node-executor-service-ai-svc
  namespace: ruh-catalyst
spec:
  selector:
    app: node-executor-service-ai
    deployment: node-executor-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50052
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:node-executor-service-node-executor-hpa
#   namespace:ruh-catalyst
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:node-executor-service-node-executor-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: node-executor-service-ingress
  namespace: ruh-catalyst
spec:
  ingressClassName: nginx
  rules:
  - host: node-executor-service-dev.rapidinnovation.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: node-executor-service-ai-svc
            port:
              number: 80
