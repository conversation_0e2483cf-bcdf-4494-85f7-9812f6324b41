from typing import Dict, Any, Optional
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.models.anthropic import AnthropicChatCompletionClient
from autogen_core.models import ModelInfo
import os
import logging

logger = logging.getLogger(__name__)


class ModelFactory:
    """Factory class to create model clients based on configuration."""

    @staticmethod
    def create_model_client(model_config: Dict[str, Any]) -> Optional[Any]:
        """
        Creates a model client based on the provided configuration.

        Args:
            model_config: Dictionary containing model configuration parameters
                - provider: The model provider (e.g., "OpenAIChatCompletionClient")
                - model: The model name (e.g., "gpt-4o")
                - api_key: API key (optional if set in environment)
                - temperature: Temperature setting (optional)
                - max_tokens: Maximum tokens (optional)
                - model_info: Model capabilities (optional)
                - base_url: Base URL for API (optional)

        Returns:
            A model client instance or None if creation fails
        """
        try:
            provider = model_config.get("provider", "OpenAIChatCompletionClient")
            model = model_config.get("model")
            api_key = model_config.get("api_key")

            # Validate required fields
            if not model:
                logger.error("Model name is required in model configuration")
                return None

            # Handle OpenAI models
            if provider == "OpenAIChatCompletionClient":
                # Use environment variable if api_key not provided
                if not api_key:
                    api_key = os.getenv("OPENAI_API_KEY", "")

                # Extract optional parameters
                temperature = model_config.get("temperature")
                max_tokens = model_config.get("max_tokens")
                base_url = model_config.get("base_url")

                # Build kwargs dictionary with only provided values
                kwargs = {"model": model, "api_key": api_key}

                if temperature is not None:
                    kwargs["temperature"] = temperature
                if max_tokens is not None:
                    kwargs["max_tokens"] = max_tokens
                if base_url:
                    kwargs["base_url"] = base_url

                # Add model_info if provided
                model_info_config = model_config.get("model_info")
                if model_info_config:
                    kwargs["model_info"] = ModelInfo(
                        vision=model_info_config.get("vision", False),
                        function_calling=model_info_config.get(
                            "function_calling", True
                        ),
                        json_output=model_info_config.get("json_output", False),
                        family=model_info_config.get("family", "unknown"),
                        structured_output=model_info_config.get(
                            "structured_output", True
                        ),
                    )

                return OpenAIChatCompletionClient(**kwargs)

            # Handle Anthropic models
            elif provider == "AnthropicChatCompletionClient":
                # Use environment variable if api_key not provided
                if not api_key:
                    api_key = os.getenv("ANTHROPIC_API_KEY", "")

                # Extract optional parameters
                temperature = model_config.get("temperature")
                max_tokens = model_config.get("max_tokens")

                # Build kwargs dictionary with only provided values
                kwargs = {"model": model, "api_key": api_key}

                if temperature is not None:
                    kwargs["temperature"] = temperature
                if max_tokens is not None:
                    kwargs["max_tokens"] = max_tokens

                return AnthropicChatCompletionClient(**kwargs)

            else:
                logger.warning(f"Model provider '{provider}' not supported yet")
                return None

        except Exception as e:
            logger.error(f"Error creating model client: {str(e)}")
            return None
