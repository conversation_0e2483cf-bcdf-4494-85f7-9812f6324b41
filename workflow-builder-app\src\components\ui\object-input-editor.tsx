import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { InputDefinition } from "@/types";

interface ObjectInputEditorProps {
  inputDef: InputDefinition;
  currentValue: any;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
}

/**
 * A component for editing object-type inputs with nested properties
 */
export function ObjectInputEditor({
  inputDef,
  currentValue,
  onChange,
  isDisabled = false,
  isConnected = false,
}: ObjectInputEditorProps) {
  // Initialize with current value or empty object
  const [objectValue, setObjectValue] = useState<Record<string, any>>(
    typeof currentValue === "object" && currentValue !== null ? currentValue : {},
  );

  // Update local state when currentValue changes
  useEffect(() => {
    if (typeof currentValue === "object" && currentValue !== null) {
      setObjectValue(currentValue);
    }
  }, [currentValue]);

  // Get the properties from the input definition
  const properties = inputDef.properties || {};

  // Update a specific property value
  const updatePropertyValue = (propertyName: string, value: any) => {
    const newValue = {
      ...objectValue,
      [propertyName]: value,
    };
    setObjectValue(newValue);
    onChange(inputDef.name, newValue);
  };

  // Extract options from property definition if available
  const getOptionsForProperty = (propertyName: string, propertyDef: any): string[] => {
    // Check if the property has an enum field (common in JSON schema)
    if (propertyDef.enum && Array.isArray(propertyDef.enum)) {
      return propertyDef.enum;
    }

    // Check if the property has a oneOf field with const values (another common pattern)
    if (propertyDef.oneOf && Array.isArray(propertyDef.oneOf)) {
      const options = propertyDef.oneOf
        .filter((item: any) => item && typeof item.const !== "undefined")
        .map((item: any) => String(item.const));

      if (options.length > 0) {
        return options;
      }
    }

    // Predefined options for specific properties in the keywords field
    // This is a fallback for when the schema doesn't provide options
    if (inputDef.name === "tool_arg_keywords") {
      const keywordsOptions: Record<string, string[]> = {
        time: ["15seconds", "30seconds", "1minute", "2minutes", "5minutes"],
        objective: ["educational", "informative", "entertaining", "promotional", "instructional"],
        audience: [
          "children",
          "teenagers",
          "18 to 30 ages",
          "30 to 50 ages",
          "50+ ages",
          "professionals",
        ],
        gender: ["male", "female", "neutral"],
        tone: ["Casual", "Professional", "Friendly", "Technical", "Formal", "Humorous"],
        speakers: ["single person", "multiple people", "influencer", "expert", "narrator"],
      };

      if (keywordsOptions[propertyName]) {
        return keywordsOptions[propertyName];
      }
    }

    // Return empty array if no options found
    return [];
  };

  // If there are predefined options for a property, render a dropdown
  const renderPropertyInput = (propertyName: string, propertyDef: any) => {
    const propertyValue = objectValue[propertyName] || "";
    const propertyType = propertyDef.type || "string";

    // Get options for this property from the property definition
    let propertyOptions = propertyDef.options || [];

    // If no options are defined in the property definition, check for enum or oneOf options
    if (propertyOptions.length === 0) {
      propertyOptions = getOptionsForProperty(propertyName, propertyDef);
    }

    if (propertyOptions.length > 0) {
      // Render a dropdown for properties with options
      return (
        <Select
          value={propertyValue}
          onValueChange={(value) => updatePropertyValue(propertyName, value)}
          disabled={isDisabled}
        >
          <SelectTrigger
            className={`bg-background/50 h-8 text-xs ${isDisabled ? "opacity-50" : ""}`}
          >
            <SelectValue placeholder={`Select ${propertyName}...`} />
          </SelectTrigger>
          <SelectContent>
            {propertyOptions.map((option: string) => (
              <SelectItem key={option} value={option} className="text-xs">
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    } else {
      // Render a text input for properties without options
      return (
        <Input
          type={propertyType === "number" ? "number" : "text"}
          value={propertyValue}
          onChange={(e) => updatePropertyValue(propertyName, e.target.value)}
          placeholder={`Enter ${propertyName}...`}
          disabled={isDisabled}
          className={`bg-background/50 h-8 text-xs ${isDisabled ? "opacity-50" : ""}`}
        />
      );
    }
  };

  // If the input is connected and disabled, show a simplified view
  if (isConnected && isDisabled) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between py-2">
          <span className="text-xs font-medium">{inputDef.display_name || inputDef.name}</span>
          <ConnectedIndicator />
        </div>
        <div className="bg-background/30 border-border/50 rounded-md border p-3 opacity-70">
          <div className="text-muted-foreground text-xs">
            Connected to another node. Values will be provided at runtime.
          </div>
          {typeof currentValue === "object" &&
            currentValue !== null &&
            Object.keys(currentValue).length > 0 && (
              <div className="bg-background/50 mt-2 max-h-32 overflow-auto rounded p-2 font-mono text-xs">
                {JSON.stringify(currentValue, null, 2)}
              </div>
            )}
        </div>
      </div>
    );
  }

  // Regular editable view
  return (
    <div className="space-y-2">
      <Accordion type="single" collapsible defaultValue="item-1" className="w-full">
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="py-2 text-xs font-medium">
            {inputDef.display_name || inputDef.name}
          </AccordionTrigger>
          <AccordionContent>
            <div className="bg-background/30 border-border/50 space-y-3 rounded-md border p-3 pt-2">
              {Object.entries(properties).map(([propertyName, propertyDef]) => (
                <div key={propertyName} className="space-y-1">
                  <Label className="flex items-center justify-between text-xs font-medium">
                    <span>
                      {propertyDef.description ||
                        propertyName.charAt(0).toUpperCase() + propertyName.slice(1)}
                    </span>
                    {propertyDef.required && (
                      <span className="text-destructive text-[10px]">Required</span>
                    )}
                  </Label>
                  <div className="relative">{renderPropertyInput(propertyName, propertyDef)}</div>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
