"""
Combine Text Component - Joins text inputs or a list with a separator.
"""
import logging
import traceback # Import traceback
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator, ValidationError

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class CombineTextRequest(BaseModel):
    """Schema for text combination requests."""
    input_data: Optional[Union[str, List[str], List[Any]]] = Field(None, description="The main input data to combine")
    input_datta: Optional[Union[str, List[str], List[Any]]] = Field(None, description="Alternative field name for input_data (typo in some workflows)")
    text_inputs: Optional[Union[str, List[str], List[Any]]] = Field(None, description="Alternative field name for input_data (used in some workflows)")
    additional_texts: Optional[List[str]] = Field(default_factory=list, description="Additional text entries to include")
    additional_texts_from_connection: Optional[List[str]] = Field(default_factory=list, description="Additional text entries from a connection")
    additional_data: Optional[Union[str, List[str], List[Any]]] = Field(None, description="Additional data from another source")
    separator: str = Field(" ", description="The separator to use for joining (default is a space)")

    # Add validator to ensure at least one input field is provided
    @field_validator('text_inputs')
    def check_input_fields(cls, v, info):
        values = info.data
        if (v is None and
            values.get('input_data') is None and
            values.get('input_datta') is None):
            raise ValueError("At least one of 'input_data', 'input_datta', or 'text_inputs' must be provided")
        return v


# Register with both names for backward compatibility
@register_component("combine_text")
@register_component("CombineTextComponent")
class CombineTextComponent(BaseComponent):
    """
    Component for combining text inputs with a separator.

    This component takes multiple text inputs or a list of strings and joins them
    with the specified separator.
    """

    def __init__(self):
        """
        Initialize the CombineTextComponent component.
        """
        logger.info("Initializing Combine Text Component")
        super().__init__()
        # Set the request schema for automatic validation
        self.request_schema = CombineTextRequest
        logger.info("Combine Text Component initialized successfully")
