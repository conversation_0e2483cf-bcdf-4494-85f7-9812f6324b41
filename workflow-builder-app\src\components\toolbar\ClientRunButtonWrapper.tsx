"use client";

import React, { useEffect, useState } from "react";
import { RunButtonWrapper } from "./RunButtonWrapper";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";

interface ClientRunButtonWrapperProps {
  nodes: Node<WorkflowNodeData>[];
  edges: Edge[];
  disabled?: boolean;
  onRun?: () => void;
}

/**
 * Client-side only wrapper for RunButtonWrapper to avoid hydration errors
 * This component only renders on the client side after hydration is complete
 */
export const ClientRunButtonWrapper = React.memo(function ClientRunButtonWrapper({
  nodes,
  edges,
  disabled,
  onRun,
}: ClientRunButtonWrapperProps) {
  // State to track if we're on the client side
  const [isClient, setIsClient] = useState(false);

  // Effect to set isClient to true once the component mounts on the client
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Only render the RunButtonWrapper on the client side
  if (!isClient) {
    // Return a placeholder button that will be replaced after hydration
    return (
      <button
        type="button"
        className="inline-flex h-8 items-center justify-center gap-1.5 rounded-md bg-green-600 px-3 py-2 text-sm font-medium text-white hover:bg-green-700 disabled:pointer-events-none disabled:opacity-50"
        disabled={true}
      >
        Run
      </button>
    );
  }

  // On client side, render the actual RunButtonWrapper
  return <RunButtonWrapper nodes={nodes} edges={edges} disabled={disabled} onRun={onRun} />;
});
