# Python Code Wrapper Component - New Implementation Plan

## Executive Summary

This implementation plan creates a Python Code Wrapper Component following the established patterns in the workflow platform. The approach starts with **Approach 1: Simple Convention (Type Hints + Single Return Dict)** as discussed in the comprehensive guide, providing a foundation for future evolution to multiple named returns.

## Input/Output Formatting Strategy

### Phase 1: Simple Convention (Current Implementation)
- **Inputs**: Use `inspect.signature` for parameter names and type hints
- **Outputs**: Function returns a single dictionary where keys become output port names
- **UI Generation**: Dynamic input fields based on function parameters
- **Execution**: Function called with dictionary of arguments, returns dictionary result

### Future Migration Path
- **Phase 2**: Add structured docstring parsing for multiple named tuple returns
- **Backward Compatibility**: Functions without docstring "Returns" section use dictionary return
- **Evolution Strategy**: Incremental enhancement without breaking existing workflows

## Current Codebase Analysis

### Workflow Service Patterns
```python
# Standard BaseNode structure
class ComponentName(BaseNode):
    name: ClassVar[str] = "ComponentName"
    display_name: ClassVar[str] = "Display Name"
    category: ClassVar[str] = "Processing"

    inputs: ClassVar[List[InputBase]] = [
        create_dual_purpose_input(
            name="input_name",
            display_name="Display Name",
            input_type="string",
            required=True,
            info="Description",
            input_types=["string", "Any"]
        )
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="result", display_name="Result", output_type="Any"),
        Output(name="error", display_name="Error", output_type="string")
    ]

    async def execute(self, context: WorkflowContext) -> NodeResult:
        start_time = time.time()
        try:
            # Get inputs
            value = self.get_input_value("input_name", context, "")

            # Package tool_parameters for orchestration engine
            tool_parameters = {"param": value}

            return NodeResult.success(
                outputs={},  # Actual outputs from NES
                tool_parameters=tool_parameters,
                execution_time=time.time() - start_time
            )
        except Exception as e:
            return NodeResult.error(str(e), time.time() - start_time)
```

### Node Executor Service Patterns
```python
@register_component("ComponentName")
class ComponentExecutor(BaseComponent):
    def __init__(self):
        super().__init__()
        # Component-specific initialization

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        # Extract tool_parameters
        if "tool_parameters" in payload:
            parameters = payload["tool_parameters"]
        else:
            parameters = payload

        try:
            # Process the data
            result = process_data(parameters)

            return {
                "status": "success",
                "result": result
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
```

## Implementation Plan

### Phase 1: Basic RestrictedPython Implementation (TDD)

#### 1.1 Dependencies Setup
```toml
# node-executor-service/pyproject.toml
[tool.poetry.dependencies]
RestrictedPython = "^6.0"
psutil = "^5.9.8"
```

#### 1.2 Workflow Service Component
**File**: `workflow-service/app/components/processing/python_code_wrapper.py`

Key features:
- Inherits from `BaseNode`
- Uses `create_dual_purpose_input` for all user inputs
- Modern `execute` method with `tool_parameters`
- Follows established input/output patterns

#### 1.3 Node Executor Service Component
**File**: `node-executor-service/app/components/python_code_wrapper_component.py`

Key features:
- Inherits from `BaseComponent` with `@register_component` decorator
- RestrictedPython execution environment
- Basic resource monitoring with psutil
- Structured error handling and logging

#### 1.4 Security Implementation
- **RestrictedPython**: Primary sandboxing mechanism
- **Safe Globals**: Curated set of allowed functions and modules
- **Resource Limits**: Basic timeout and memory monitoring
- **Allowed Libraries**: json, re, math, datetime, collections, copy, itertools

### Phase 2: Test-Driven Development Structure

#### 2.1 Test Files Structure
```
workflow-service/tests/components/processing/
├── test_python_code_wrapper.py

node-executor-service/tests/components/
├── test_python_code_wrapper_component.py
```

#### 2.2 Test Categories
1. **Unit Tests**: Component initialization, input validation
2. **Integration Tests**: End-to-end workflow execution
3. **Security Tests**: Sandboxing, malicious code handling
4. **Performance Tests**: Execution time, memory usage
5. **Error Tests**: Invalid code, runtime errors, timeouts

### Phase 3: Component Registration

#### 3.1 Workflow Service Registration
Add to `workflow-service/app/components/processing/__init__.py`:
```python
from .python_code_wrapper import PythonCodeWrapperComponent
```

#### 3.2 Node Executor Service Registration
Add to `node-executor-service/app/components/__init__.py`:
```python
from . import python_code_wrapper_component
```

## Technical Specifications

### Input/Output Interface

#### Workflow Service Inputs
- `python_code`: Code input with special UI rendering
- `input_data`: Dictionary/list/string data for function
- `timeout_seconds`: Execution timeout (1-30 seconds)

#### Workflow Service Outputs
- `result`: Function execution result (Any type)
- `output_logs`: Captured print statements (string)
- `execution_time`: Execution duration (float)
- `error`: Error message if failed (string)

### Execution Flow
1. **Workflow Service**: Validates inputs → packages `tool_parameters`
2. **Orchestration Engine**: Adds `request_id` → sends via Kafka
3. **Node Executor Service**: Extracts parameters → executes with RestrictedPython
4. **Result**: Returns structured response with result/error

### Security Architecture
- **RestrictedPython**: `compile_restricted()` and `safe_globals`
- **Limited Builtins**: Curated set of safe built-in functions
- **Module Restrictions**: Pre-imported safe modules only
- **Resource Monitoring**: Timeout and basic memory tracking

## Success Criteria

### Functional Requirements
- [x] Execute Python code safely using RestrictedPython
- [x] Handle various input data types (dict, list, string, numbers)
- [x] Provide structured output (result, logs, execution_time, error)
- [x] Enforce timeout limits (1-30 seconds)
- [x] Integrate seamlessly with existing workflow patterns

### Security Requirements
- [x] Prevent file system access
- [x] Prevent network access
- [x] Prevent dangerous imports
- [x] Enforce resource limits
- [x] Handle malicious code gracefully

### Quality Requirements
- [x] Follow established codebase patterns
- [x] Use TDD methodology
- [x] Comprehensive error handling
- [x] Clear logging and debugging
- [x] 90%+ test coverage

## Next Steps

1. **Setup Dependencies**: Add RestrictedPython and psutil to node-executor-service
2. **Create Test Files**: Implement comprehensive test suites first (TDD)
3. **Implement Workflow Service Component**: Build minimal implementation to pass tests
4. **Implement Node Executor Service Component**: Build executor with RestrictedPython
5. **Integration Testing**: Test end-to-end workflow execution
6. **Security Validation**: Test with malicious code examples
7. **Performance Optimization**: Profile and optimize execution

This plan provides a solid foundation for the Python Code Wrapper Component while maintaining consistency with existing platform patterns and preparing for future enhancements.

## Detailed Implementation Specifications

### Workflow Service Component Implementation

```python
# workflow-service/app/components/processing/python_code_wrapper.py
import time
from typing import List, ClassVar, Dict, Any

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase, Output, IntInput, BoolInput
)
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

class PythonCodeWrapperComponent(BaseNode):
    name: ClassVar[str] = "PythonCodeWrapperComponent"
    display_name: ClassVar[str] = "Python Code Wrapper"
    description: ClassVar[str] = (
        "Execute custom Python code in a secure sandboxed environment. "
        "Access input data via 'inputs' variable and assign results to 'result' variable."
    )
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Code"

    inputs: ClassVar[List[InputBase]] = [
        create_dual_purpose_input(
            name="python_code",
            display_name="Python Code",
            input_type="code",
            required=True,
            info="Python code to execute. Use 'inputs' dict for input data and assign results to 'result' variable.",
            input_types=["string"]
        ),
        create_dual_purpose_input(
            name="input_data",
            display_name="Input Data",
            input_type="dict",
            required=False,
            info="Data to pass to the Python code as 'inputs' variable (accessible as a dictionary).",
            input_types=["dict", "list", "string", "Any"]
        ),
        IntInput(
            name="timeout_seconds",
            display_name="Timeout (seconds)",
            value=5,
            info="Maximum execution time for the Python code in seconds (1-30)."
        ),
        BoolInput(
            name="enable_debugging",
            display_name="Enable Debugging",
            value=False,
            info="Include detailed execution information in output logs."
        )
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="result", display_name="Execution Result", output_type="Any"),
        Output(name="output_logs", display_name="Output Logs", output_type="string"),
        Output(name="execution_time", display_name="Execution Time (s)", output_type="float"),
        Output(name="error", display_name="Error", output_type="string")
    ]

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """Prepare parameters for Python code execution by the Node Executor Service."""
        start_time = time.time()
        context.log(f"Preparing {self.name} for execution...")

        try:
            python_code = self.get_input_value("python_code", context, "")
            input_data = self.get_input_value("input_data", context, {})
            timeout_seconds = int(self.get_input_value("timeout_seconds", context, 5))
            enable_debugging = self.get_input_value("enable_debugging", context, False)

            # Basic validation at Workflow Service level
            if not python_code or not python_code.strip():
                error_msg = "Python code is required and cannot be empty."
                context.log(error_msg, level="ERROR")
                return NodeResult.error(error_message=error_msg, execution_time=(time.time() - start_time))

            if not (1 <= timeout_seconds <= 30):
                error_msg = "Timeout must be between 1 and 30 seconds."
                context.log(error_msg, level="ERROR")
                return NodeResult.error(error_message=error_msg, execution_time=(time.time() - start_time))

            # Prepare tool_parameters for the Orchestration Engine / Node Executor Service
            tool_parameters = {
                "python_code": python_code,
                "input_data": input_data,
                "timeout_seconds": timeout_seconds,
                "enable_debugging": enable_debugging,
            }

            prep_time = time.time() - start_time
            context.log(f"{self.name} parameters prepared successfully in {prep_time:.2f}s. Forwarding for execution.")

            return NodeResult.success(
                outputs={},  # Actual outputs will be populated by NES result
                tool_parameters=tool_parameters,  # Key for orchestrator
                execution_time=prep_time
            )

        except Exception as e:
            error_msg = f"Error preparing {self.name}: {str(e)}"
            context.log(error_msg, level="ERROR")
            return NodeResult.error(
                error_message=error_msg,
                execution_time=(time.time() - start_time)
            )
```

### Node Executor Service Component Implementation

```python
# node-executor-service/app/components/python_code_wrapper_component.py
import logging
import time
import sys
import io
import traceback
from typing import Dict, Any
from contextlib import redirect_stdout, redirect_stderr

from RestrictedPython import compile_restricted, safe_globals
from RestrictedPython.Guards import limited_builtins
import psutil
import os

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)

@register_component("PythonCodeWrapperComponent")
class PythonCodeWrapperExecutor(BaseComponent):
    def __init__(self):
        super().__init__()
        self._setup_restricted_environment()
        logger.info("PythonCodeWrapperExecutor initialized with RestrictedPython.")

    def _setup_restricted_environment(self):
        """Setup the restricted execution environment."""
        # Pre-import allowed modules
        self.allowed_modules = {}
        try:
            import json
            import re
            import math
            import datetime
            import collections
            import copy
            import itertools

            self.allowed_modules = {
                'json': json,
                're': re,
                'math': math,
                'datetime': datetime,
                'collections': collections,
                'copy': copy,
                'itertools': itertools
            }
        except ImportError as e:
            logger.warning(f"Could not import some allowed modules: {e}")

    def _create_safe_globals(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a safe execution environment."""
        # Start with safe globals
        env_globals = safe_globals.copy()

        # Add limited builtins
        safe_builtins_dict = limited_builtins.copy()

        # Add essential safe builtins
        safe_builtins_dict.update({
            'abs': abs, 'all': all, 'any': any, 'bool': bool, 'bytes': bytes,
            'dict': dict, 'divmod': divmod, 'enumerate': enumerate, 'filter': filter,
            'float': float, 'int': int, 'isinstance': isinstance, 'issubclass': issubclass,
            'len': len, 'list': list, 'map': map, 'max': max, 'min': min,
            'pow': pow, 'range': range, 'repr': repr, 'reversed': reversed,
            'round': round, 'set': set, 'slice': slice, 'sorted': sorted,
            'str': str, 'sum': sum, 'tuple': tuple, 'type': type, 'zip': zip,
        })

        # Remove dangerous builtins
        dangerous_builtins = [
            'open', 'file', 'exec', 'eval', '__import__', 'compile', 'input',
            'memoryview', 'object', 'property', 'classmethod', 'staticmethod', 'super'
        ]
        for builtin_name in dangerous_builtins:
            safe_builtins_dict.pop(builtin_name, None)

        env_globals['__builtins__'] = safe_builtins_dict

        # Provide input data and result placeholder
        env_globals['inputs'] = input_data
        env_globals['result'] = None

        # Add allowed modules
        env_globals.update(self.allowed_modules)

        return env_globals

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute Python code using RestrictedPython and return the result."""
        request_id = payload.get("request_id", "unknown_request_id")
        start_process_time = time.time()

        try:
            # Extract parameters
            if "tool_parameters" in payload:
                parameters = payload["tool_parameters"]
            else:
                parameters = payload

            python_code = parameters.get("python_code", "")
            if not python_code.strip():
                return {
                    "status": "error",
                    "error": "Python code cannot be empty.",
                    "output_logs": "",
                    "execution_time": 0.0,
                    "result": None
                }

            input_data = parameters.get("input_data", {})
            timeout_seconds = parameters.get("timeout_seconds", 5)
            enable_debugging = parameters.get("enable_debugging", False)

            # Execute the code
            result = await self._execute_code(
                python_code, input_data, timeout_seconds, enable_debugging, request_id
            )

            total_execution_time = time.time() - start_process_time
            result["total_processing_time"] = total_execution_time

            return result

        except Exception as e:
            logger.exception(f"Critical error in PythonCodeWrapperExecutor for request {request_id}: {e}")
            return {
                "status": "error",
                "error": "A critical internal error occurred.",
                "execution_time": time.time() - start_process_time,
                "result": None,
                "output_logs": ""
            }

    async def _execute_code(self, python_code: str, input_data: Dict[str, Any],
                          timeout_seconds: int, enable_debugging: bool, request_id: str) -> Dict[str, Any]:
        """Execute the Python code with RestrictedPython."""
        start_time = time.time()

        # Capture stdout/stderr
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()

        try:
            # Create safe execution environment
            exec_globals = self._create_safe_globals(input_data)

            # Compile the code
            byte_code = compile_restricted(python_code, filename='<user_code>', mode='exec')
            if byte_code is None:
                return {
                    "status": "error",
                    "error": "Failed to compile Python code. Code may contain restricted operations.",
                    "execution_time": time.time() - start_time,
                    "result": None,
                    "output_logs": ""
                }

            # Execute with output capture
            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                exec(byte_code, exec_globals)

            # Get the result
            final_result = exec_globals.get('result')
            execution_time = time.time() - start_time

            # Capture output logs
            output_logs = stdout_capture.getvalue()
            error_logs = stderr_capture.getvalue()

            if error_logs and enable_debugging:
                output_logs += f"\nSTDERR:\n{error_logs}"

            return {
                "status": "success",
                "result": final_result,
                "output_logs": output_logs,
                "execution_time": execution_time
            }

        except SyntaxError as se:
            return {
                "status": "error",
                "error": f"Syntax error in Python code: {se.msg} on line {se.lineno}",
                "execution_time": time.time() - start_time,
                "result": None,
                "output_logs": stdout_capture.getvalue()
            }
        except Exception as e:
            error_msg = f"Runtime error: {type(e).__name__}: {str(e)}"
            if enable_debugging:
                error_msg += f"\nTraceback:\n{traceback.format_exc()}"

            return {
                "status": "error",
                "error": error_msg,
                "execution_time": time.time() - start_time,
                "result": None,
                "output_logs": stdout_capture.getvalue()
            }
```

## Test Implementation Specifications

### Test Structure and Examples

#### Workflow Service Tests
```python
# workflow-service/tests/components/processing/test_python_code_wrapper.py
import pytest
import time
from unittest.mock import Mock

from app.components.processing.python_code_wrapper import PythonCodeWrapperComponent
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeStatus

class TestPythonCodeWrapperComponent:

    @pytest.fixture
    def component(self):
        return PythonCodeWrapperComponent()

    @pytest.fixture
    def mock_context(self):
        context = Mock(spec=WorkflowContext)
        context.log = Mock()
        return context

    def test_component_metadata(self, component):
        """Test component metadata is correctly defined."""
        assert component.name == "PythonCodeWrapperComponent"
        assert component.display_name == "Python Code Wrapper"
        assert component.category == "Processing"
        assert len(component.inputs) == 4
        assert len(component.outputs) == 4

    @pytest.mark.asyncio
    async def test_execute_valid_code(self, component, mock_context):
        """Test execution with valid Python code."""
        mock_context.get_input_value.side_effect = [
            "result = inputs.get('value', 0) * 2",  # python_code
            {"value": 5},  # input_data
            10,  # timeout_seconds
            False  # enable_debugging
        ]

        result = await component.execute(mock_context)

        assert result.status == NodeStatus.SUCCESS
        assert "tool_parameters" in result.__dict__
        assert result.tool_parameters["python_code"] == "result = inputs.get('value', 0) * 2"
        assert result.tool_parameters["input_data"] == {"value": 5}
        assert result.tool_parameters["timeout_seconds"] == 10

    @pytest.mark.asyncio
    async def test_execute_empty_code(self, component, mock_context):
        """Test execution with empty Python code."""
        mock_context.get_input_value.side_effect = [
            "",  # python_code
            {},  # input_data
            5,   # timeout_seconds
            False  # enable_debugging
        ]

        result = await component.execute(mock_context)

        assert result.status == NodeStatus.ERROR
        assert "empty" in result.error_message.lower()

    @pytest.mark.asyncio
    async def test_execute_invalid_timeout(self, component, mock_context):
        """Test execution with invalid timeout."""
        mock_context.get_input_value.side_effect = [
            "result = 42",  # python_code
            {},  # input_data
            50,  # timeout_seconds (invalid)
            False  # enable_debugging
        ]

        result = await component.execute(mock_context)

        assert result.status == NodeStatus.ERROR
        assert "timeout" in result.error_message.lower()

# Test data examples
VALID_CODE_EXAMPLES = [
    ("result = inputs.get('value', 0) * 2", {"value": 5}, 10),
    ("result = {'processed': True, 'data': inputs}", {"test": "data"}, {"processed": True, "data": {"test": "data"}}),
    ("result = [x * 2 for x in inputs.get('numbers', [])]", {"numbers": [1, 2, 3]}, [2, 4, 6]),
    ("result = len(inputs.get('text', ''))", {"text": "hello"}, 5),
]

INVALID_CODE_EXAMPLES = [
    ("import os; os.system('rm -rf /')", "System access attempt"),
    ("open('/etc/passwd', 'r')", "File access attempt"),
    ("__import__('subprocess')", "Import restriction bypass"),
    ("exec('print(\"test\")')", "Exec function usage"),
    ("eval('1+1')", "Eval function usage"),
]
```

#### Node Executor Service Tests
```python
# node-executor-service/tests/components/test_python_code_wrapper_component.py
import pytest
import asyncio
from unittest.mock import Mock, patch

from app.components.python_code_wrapper_component import PythonCodeWrapperExecutor

class TestPythonCodeWrapperExecutor:

    @pytest.fixture
    def executor(self):
        return PythonCodeWrapperExecutor()

    @pytest.mark.asyncio
    async def test_process_valid_code(self, executor):
        """Test processing valid Python code."""
        payload = {
            "tool_parameters": {
                "python_code": "result = inputs.get('value', 0) * 2",
                "input_data": {"value": 5},
                "timeout_seconds": 5,
                "enable_debugging": False
            },
            "request_id": "test_request"
        }

        result = await executor.process(payload)

        assert result["status"] == "success"
        assert result["result"] == 10
        assert "execution_time" in result
        assert result["output_logs"] == ""

    @pytest.mark.asyncio
    async def test_process_with_print_statements(self, executor):
        """Test processing code with print statements."""
        payload = {
            "tool_parameters": {
                "python_code": "print('Hello, World!'); result = 42",
                "input_data": {},
                "timeout_seconds": 5,
                "enable_debugging": False
            }
        }

        result = await executor.process(payload)

        assert result["status"] == "success"
        assert result["result"] == 42
        assert "Hello, World!" in result["output_logs"]

    @pytest.mark.asyncio
    async def test_process_syntax_error(self, executor):
        """Test processing code with syntax error."""
        payload = {
            "tool_parameters": {
                "python_code": "result = inputs.get('value' * 2",  # Missing closing parenthesis
                "input_data": {"value": 5},
                "timeout_seconds": 5,
                "enable_debugging": False
            }
        }

        result = await executor.process(payload)

        assert result["status"] == "error"
        assert "syntax error" in result["error"].lower()
        assert result["result"] is None

    @pytest.mark.asyncio
    async def test_process_runtime_error(self, executor):
        """Test processing code with runtime error."""
        payload = {
            "tool_parameters": {
                "python_code": "result = 1 / 0",  # Division by zero
                "input_data": {},
                "timeout_seconds": 5,
                "enable_debugging": False
            }
        }

        result = await executor.process(payload)

        assert result["status"] == "error"
        assert "zerodivisionerror" in result["error"].lower()
        assert result["result"] is None

    @pytest.mark.asyncio
    async def test_process_restricted_operations(self, executor):
        """Test that restricted operations are blocked."""
        restricted_codes = [
            "import os; result = os.getcwd()",
            "open('/etc/passwd'); result = 'file_opened'",
            "__import__('subprocess'); result = 'imported'",
        ]

        for code in restricted_codes:
            payload = {
                "tool_parameters": {
                    "python_code": code,
                    "input_data": {},
                    "timeout_seconds": 5,
                    "enable_debugging": False
                }
            }

            result = await executor.process(payload)
            assert result["status"] == "error"

    @pytest.mark.asyncio
    async def test_allowed_modules(self, executor):
        """Test that allowed modules work correctly."""
        payload = {
            "tool_parameters": {
                "python_code": """
import json
import math
result = {
    'json_test': json.dumps({'test': True}),
    'math_test': math.sqrt(16)
}
""",
                "input_data": {},
                "timeout_seconds": 5,
                "enable_debugging": False
            }
        }

        result = await executor.process(payload)

        assert result["status"] == "success"
        assert result["result"]["json_test"] == '{"test": true}'
        assert result["result"]["math_test"] == 4.0

    def test_safe_globals_creation(self, executor):
        """Test that safe globals are created correctly."""
        input_data = {"test": "value"}
        safe_globals = executor._create_safe_globals(input_data)

        assert "inputs" in safe_globals
        assert safe_globals["inputs"] == input_data
        assert "result" in safe_globals
        assert safe_globals["result"] is None
        assert "json" in safe_globals
        assert "math" in safe_globals
        assert "open" not in safe_globals["__builtins__"]
        assert "exec" not in safe_globals["__builtins__"]
```

## Usage Examples and Test Cases

### Basic Usage Examples
```python
# Example 1: Simple calculation
code = "result = inputs.get('a', 0) + inputs.get('b', 0)"
input_data = {"a": 5, "b": 3}
# Expected result: 8

# Example 2: Data transformation
code = """
data = inputs.get('items', [])
result = [{'id': item['id'], 'name': item['name'].upper()} for item in data]
"""
input_data = {"items": [{"id": 1, "name": "test"}, {"id": 2, "name": "demo"}]}
# Expected result: [{"id": 1, "name": "TEST"}, {"id": 2, "name": "DEMO"}]

# Example 3: Text processing with allowed modules
code = """
import re
text = inputs.get('text', '')
result = {
    'word_count': len(re.findall(r'\\w+', text)),
    'char_count': len(text),
    'cleaned': re.sub(r'[^\\w\\s]', '', text.lower())
}
"""
input_data = {"text": "Hello, World! How are you?"}
# Expected result: {"word_count": 5, "char_count": 23, "cleaned": "hello world how are you"}
```

### Security Test Cases
```python
# These should all fail with appropriate error messages
MALICIOUS_CODE_EXAMPLES = [
    # File system access
    "import os; result = os.listdir('/')",
    "open('/etc/passwd', 'r').read()",

    # Network access
    "import urllib.request; urllib.request.urlopen('http://example.com')",

    # Process execution
    "import subprocess; subprocess.run(['ls', '-la'])",

    # Import restrictions
    "__import__('os').system('echo test')",
    "exec('import os')",
    "eval('__import__(\"os\")')",

    # Memory/CPU bombs
    "result = [i for i in range(10**8)]",  # Memory bomb
    "while True: pass",  # Infinite loop
]
```

## Integration Testing

### End-to-End Workflow Test
```python
# Integration test that verifies the complete workflow
@pytest.mark.integration
async def test_python_code_wrapper_end_to_end():
    """Test complete workflow from Workflow Service to Node Executor Service."""

    # 1. Test Workflow Service component preparation
    component = PythonCodeWrapperComponent()
    context = create_test_context({
        "python_code": "result = inputs.get('value', 0) * 2",
        "input_data": {"value": 5},
        "timeout_seconds": 10,
        "enable_debugging": False
    })

    workflow_result = await component.execute(context)
    assert workflow_result.status == NodeStatus.SUCCESS

    # 2. Test Node Executor Service processing
    executor = PythonCodeWrapperExecutor()
    payload = {
        "tool_parameters": workflow_result.tool_parameters,
        "request_id": "integration_test"
    }

    execution_result = await executor.process(payload)
    assert execution_result["status"] == "success"
    assert execution_result["result"] == 10
```

This comprehensive implementation plan provides a solid foundation for the Python Code Wrapper Component, following established patterns while implementing the "Simple Convention" approach for input/output handling. The plan includes complete implementations, comprehensive tests, and clear migration paths for future enhancements.