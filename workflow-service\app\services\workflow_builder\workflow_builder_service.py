# app/services/workflow_service.py
import grpc
import json
import structlog
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowTemplate
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
)
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from app.utils.json_validator import validate_transition_schema
from app.utils.file_upload import GCSUploadService
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum
from app.core.config import settings
from app.services.workflow_builder.service_instances import (
    enhanced_component_service,
)
from app.utils.workflow_builder.transform import (
    get_connected_nodes,
    collect_missing_required_fields,
)
import asyncio

# Initialize structured logger
logger = structlog.get_logger()


class WorkflowBuilderFunctions(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowService with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def discoverComponents(
        self, request: workflow_pb2.DiscoverComponentsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.DiscoverComponentsResponse:
        """
        Discover available workflow components.

        Args:
            request: Contains force_refresh flag to reload components
            context: gRPC service context

        Returns:
            Response containing list of available components
        """
        logger.info("discoverComponents request received", force_refresh=request.force_refresh)
        try:
            # Get components from the enhanced_component_service
            raw_components = asyncio.run(
                enhanced_component_service.discover_components(request.force_refresh)
            )
            # Convert components to protobuf format
            component_protos = []
            # raw_components is a dict of dicts, where the outer dict is keyed by category
            # and the inner dict is keyed by component name
            for category, components in raw_components.items():
                for name, comp in components.items():
                    # Create input protos
                    input_protos = []
                    for input_def in comp.inputs:

                        # Convert visibility rules to protobuf format
                        visibility_rule_protos = []
                        if hasattr(input_def, "visibility_rules") and input_def.visibility_rules:
                            for rule in input_def.visibility_rules:
                                # Create a protobuf VisibilityRule
                                rule_proto = workflow_pb2.VisibilityRule(
                                    field_name=rule.field_name,
                                    field_value=str(rule.field_value),
                                    operator=(
                                        rule.operator if hasattr(rule, "operator") else "equals"
                                    ),
                                )
                                visibility_rule_protos.append(rule_proto)

                        # Create the input proto with all fields
                        input_proto = workflow_pb2.ComponentInput(
                            name=input_def.name,
                            display_name=input_def.display_name,
                            info=input_def.info if hasattr(input_def, "info") else "",
                            input_type=(
                                input_def.input_type if hasattr(input_def, "input_type") else ""
                            ),
                            input_types=(
                                input_def.input_types if hasattr(input_def, "input_types") else []
                            ),
                            required=(
                                input_def.required if hasattr(input_def, "required") else False
                            ),
                            is_handle=(
                                input_def.is_handle if hasattr(input_def, "is_handle") else False
                            ),
                            is_list=input_def.is_list if hasattr(input_def, "is_list") else False,
                            real_time_refresh=(
                                input_def.real_time_refresh
                                if hasattr(input_def, "real_time_refresh")
                                else False
                            ),
                            advanced=(
                                input_def.advanced if hasattr(input_def, "advanced") else False
                            ),
                            value=str(input_def.value) if hasattr(input_def, "value") else "",
                            options=input_def.options if hasattr(input_def, "options") else [],
                            visibility_rules=visibility_rule_protos,
                            visibility_logic=(
                                input_def.visibility_logic
                                if hasattr(input_def, "visibility_logic")
                                else "OR"
                            ),
                            credential_type=(
                                input_def.credential_type
                                if hasattr(input_def, "credential_type")
                                else ""
                            ),
                            use_credential_id=(
                                input_def.use_credential_id
                                if hasattr(input_def, "use_credential_id")
                                else False
                            ),
                            credential_id=(
                                input_def.credential_id
                                if hasattr(input_def, "credential_id")
                                else ""
                            ),
                        )
                        input_protos.append(input_proto)

                    # Create output protos
                    output_protos = []
                    for output_def in comp.outputs:
                        output_proto = workflow_pb2.ComponentOutput(
                            name=output_def.name,
                            display_name=output_def.display_name,
                            output_type=(
                                output_def.output_type if hasattr(output_def, "output_type") else ""
                            ),
                            method=(
                                output_def.method
                                if hasattr(output_def, "method") and output_def.method
                                else ""
                            ),
                        )
                        output_protos.append(output_proto)

                    # Create component proto with inputs and outputs
                    # Use the path from the component definition if available, otherwise generate it
                    if hasattr(comp, "path") and comp.path:
                        path = comp.path
                    else:
                        path = f"components.{category.lower()}.{name.lower()}.{name}"

                    # Create MCP info if available
                    mcp_info_proto = None
                    if hasattr(comp, "mcp_info") and comp.mcp_info:
                        mcp_info_proto = workflow_pb2.MCPInfo(
                            server_id=(
                                comp.mcp_info.server_id
                                if hasattr(comp.mcp_info, "server_id")
                                else ""
                            ),
                            server_path=(
                                comp.mcp_info.server_path
                                if hasattr(comp.mcp_info, "server_path")
                                else ""
                            ),
                            tool_name=(
                                comp.mcp_info.tool_name
                                if hasattr(comp.mcp_info, "tool_name")
                                else ""
                            ),
                            tool_id=(
                                comp.mcp_info.tool_id if hasattr(comp.mcp_info, "tool_id") else ""
                            ),
                            endpoint=(
                                comp.mcp_info.endpoint if hasattr(comp.mcp_info, "endpoint") else ""
                            ),
                        )

                    component_proto = workflow_pb2.Component(
                        id=name,
                        name=name,
                        display_name=comp.display_name,
                        description=comp.description,
                        category=category,
                        icon=comp.icon,
                        type=getattr(comp, "type", ""),
                        inputs=input_protos,
                        outputs=output_protos,
                        is_valid=getattr(comp, "is_valid", True),
                        beta=getattr(comp, "beta", False),
                        requires_approval=getattr(comp, "requires_approval", False),
                        path=path,
                        mcp_info=mcp_info_proto,
                    )
                    component_protos.append(component_proto)
            # Log the number of components discovered

            logger.info("Components discovered successfully", count=len(component_protos))
            return workflow_pb2.DiscoverComponentsResponse(components=component_protos)
        except Exception as e:
            logger.error("Failed to discover components", error=str(e))
            return workflow_pb2.DiscoverComponentsResponse(components=[])

    def validateWorkflow(
        self, request: workflow_pb2.ValidateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ValidateWorkflowResponse:
        """
        Validate a workflow by checking for missing required fields.

        Args:
            request: Contains nodes and edges of the workflow
            context: gRPC service context

        Returns:
            Response containing validation results
        """
        logger.info(
            "validateWorkflow request received",
            node_count=len(request.nodes),
            edge_count=len(request.edges),
        )

        try:
            # Convert protobuf nodes and edges to dictionaries
            nodes = []
            for node in request.nodes:
                node_dict = {
                    "id": node.id,
                    "type": node.type,
                    "data": {},
                    "position": {"x": node.x, "y": node.y},
                }

                # Convert the map<string, string> to a dictionary
                for key, value in node.data.items():
                    # Try to parse JSON strings
                    try:
                        if (
                            value.startswith("{")
                            and value.endswith("}")
                            or value.startswith("[")
                            and value.endswith("]")
                        ):
                            node_dict["data"][key] = json.loads(value)
                        else:
                            node_dict["data"][key] = value
                    except (json.JSONDecodeError, AttributeError):
                        node_dict["data"][key] = value

                nodes.append(node_dict)
            print(f"[DEBUG] Converted nodes: {nodes}")
            edges = []
            for edge in request.edges:
                edge_dict = {
                    "id": edge.id,
                    "source": edge.source,
                    "target": edge.target,
                    "sourceHandle": edge.sourceHandle,
                    "targetHandle": edge.targetHandle,
                }
                edges.append(edge_dict)

            # Check if the workflow has a StartNode
            has_start_node = any(
                node.get("data", {}).get("originalType") == "StartNode" for node in nodes
            )
            print(f"[DEBUG] Has start node: {has_start_node}")
            if not has_start_node:
                return workflow_pb2.ValidateWorkflowResponse(
                    is_valid=False, error="Workflow must have a Start node"
                )

            # Get connected nodes
            connected_nodes = get_connected_nodes(nodes, edges)
            print(f"[DEBUG] Connected nodes: {connected_nodes}")
            # Collect missing required fields
            missing_fields_list = collect_missing_required_fields(nodes, edges, connected_nodes)
            print("[DEBUG] Missing fields: {missing_fields_list}")
            # Convert missing fields to protobuf format
            missing_fields_protos = []
            for field in missing_fields_list:
                missing_field_proto = workflow_pb2.MissingField(
                    node_id=field.get("node_id", ""),
                    node_label=field.get("node_label", ""),
                    input_name=field.get("input_name", ""),
                    input_display_name=field.get("input_display_name", ""),
                    is_handle=field.get("is_handle", False),
                )
                missing_fields_protos.append(missing_field_proto)
            print(f"[DEBUG] Missing fields protos: {missing_fields_protos}")
            # Create and return the response
            is_valid = len(missing_fields_protos) == 0
            logger.info(
                "Workflow validation completed",
                is_valid=is_valid,
                missing_field_count=len(missing_fields_protos),
            )

            return workflow_pb2.ValidateWorkflowResponse(
                is_valid=is_valid, missing_fields=missing_fields_protos
            )

        except Exception as e:
            logger.error("Failed to validate workflow", error=str(e))
            import traceback

            traceback.print_exc()
            return workflow_pb2.ValidateWorkflowResponse(
                is_valid=False, error=f"Error validating workflow: {str(e)}"
            )
