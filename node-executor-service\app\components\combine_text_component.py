"""
Combine Text Component - Joins text inputs or a list with a separator.
"""
import logging
import traceback # Import traceback
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator, ValidationError

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class CombineTextRequest(BaseModel):
    """Schema for text combination requests."""
    input_data: Optional[Union[str, List[str], List[Any]]] = Field(None, description="The main input data to combine")
    input_datta: Optional[Union[str, List[str], List[Any]]] = Field(None, description="Alternative field name for input_data (typo in some workflows)")
    text_inputs: Optional[Union[str, List[str], List[Any]]] = Field(None, description="Alternative field name for input_data (used in some workflows)")
    additional_texts: Optional[List[str]] = Field(default_factory=list, description="Additional text entries to include")
    additional_texts_from_connection: Optional[List[str]] = Field(default_factory=list, description="Additional text entries from a connection")
    additional_data: Optional[Union[str, List[str], List[Any]]] = Field(None, description="Additional data from another source")
    separator: str = Field(" ", description="The separator to use for joining (default is a space)")

    # Add validator to ensure at least one input field is provided
    @field_validator('text_inputs')
    def check_input_fields(cls, v, info):
        values = info.data
        if (v is None and
            values.get('input_data') is None and
            values.get('input_datta') is None):
            raise ValueError("At least one of 'input_data', 'input_datta', or 'text_inputs' must be provided")
        return v


# Register with both names for backward compatibility
@register_component("combine_text")
@register_component("CombineTextComponent")
class CombineTextComponent(BaseComponent):
    """
    Component for combining text inputs with a separator.

    This component takes multiple text inputs or a list of strings and joins them
    with the specified separator.
    """

    def __init__(self):
        """
        Initialize the CombineTextComponent component.
        """
        logger.info("Initializing Combine Text Component")
        super().__init__()
        # Set the request schema for automatic validation
        self.request_schema = CombineTextRequest
        logger.info("Combine Text Component initialized successfully")


    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a text combination payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating text combination payload for request_id: {request_id}")
        logger.debug(f"Payload to validate for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for validation.")
            # Use the tool_parameters as the actual payload for validation
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        logger.info(f"VALIDATION PARAMETERS KEYS: {list(parameters.keys())}")

        try:
            # First use schema validation (from parent class)
            # We still validate the original payload for backward compatibility
            schema_validation = await super().validate(payload)
            if not schema_validation.is_valid:
                # Try validating the parameters instead
                try:
                    CombineTextRequest(**parameters)
                except ValidationError as e:
                    logger.error(f"Schema validation failed for request_id {request_id}: {schema_validation.error_message}")
                    return schema_validation
            logger.debug(f"Schema validation passed for request_id {request_id}")

            # Check for input_data, input_datta, or text_inputs (handling different field names in workflows)
            if ("input_data" not in parameters and
                "input_datta" not in parameters and
                "text_inputs" not in parameters):
                error_msg = f"Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters for request_id {request_id}"
                logger.error(error_msg)
                logger.error(f"Available keys in parameters: {list(parameters.keys())}")
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"input_data": "One of 'input_data', 'input_datta', or 'text_inputs' is required"}
                )

            # Log which field is being used
            if "input_data" in parameters:
                logger.info(f"Using 'input_data' field for request_id {request_id}")
            elif "input_datta" in parameters:
                logger.warning(f"Using 'input_datta' field instead of 'input_data' for request_id {request_id}. Please update your workflow definition.")
            elif "text_inputs" in parameters:
                logger.info(f"Using 'text_inputs' field instead of 'input_data' for request_id {request_id}")

            # Additional checks for types if necessary, but Pydantic handles Union
            # For example, if additional_texts is provided, ensure it's a list
            additional_texts = parameters.get("additional_texts")
            if additional_texts is not None and not isinstance(additional_texts, list):
                 error_msg = f"Field 'additional_texts' must be a list, got {type(additional_texts).__name__} for request_id {request_id}"
                 logger.error(error_msg)
                 return ValidationResult(is_valid=False, error_message=error_msg, error_details={"additional_texts": "must be a list"})
            logger.debug(f"additional_texts type validation passed for request_id {request_id}")

            # Check additional_texts_from_connection type if provided
            additional_texts_from_connection = parameters.get("additional_texts_from_connection")
            if additional_texts_from_connection is not None and not isinstance(additional_texts_from_connection, list):
                 error_msg = f"Field 'additional_texts_from_connection' must be a list, got {type(additional_texts_from_connection).__name__} for request_id {request_id}"
                 logger.error(error_msg)
                 return ValidationResult(is_valid=False, error_message=error_msg, error_details={"additional_texts_from_connection": "must be a list"})
            logger.debug(f"additional_texts_from_connection type validation passed for request_id {request_id}")

            # Check additional_data type if provided
            additional_data = parameters.get("additional_data")
            if additional_data is not None and not isinstance(additional_data, (str, list)):
                 error_msg = f"Field 'additional_data' must be a string or list, got {type(additional_data).__name__} for request_id {request_id}"
                 logger.error(error_msg)
                 return ValidationResult(is_valid=False, error_message=error_msg, error_details={"additional_data": "must be string or list"})
            logger.debug(f"additional_data type validation passed for request_id {request_id}")


            logger.info(f"Text combination payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(is_valid=False, error_message=error_msg, error_details={"validation_error": str(e)})

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the text combination operation.

        Args:
            payload: The request payload containing:
                - input_data: The main input data (string or list)
                - additional_texts: Additional text entries (optional)
                - additional_data: Additional data from another source (optional)
                - separator: The separator to use for joining (optional)

        Returns:
            A dictionary containing the combined text or an error message
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing text combination request for request_id: {request_id}")
        # Log the full payload with all keys to help debug
        logger.info(f"PAYLOAD KEYS: {list(payload.keys())}")
        logger.info(f"FULL PAYLOAD: {payload}")
        logger.debug(f"Full payload for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for parameters.")
            # Use the tool_parameters as the actual payload
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        logger.info(f"PARAMETERS KEYS: {list(parameters.keys())}")

        # Check for text_inputs field (which might be used instead of input_data)
        if "text_inputs" in parameters:
            logger.info(f"Found 'text_inputs' field in parameters: {parameters['text_inputs']}")

        try:
            # Get inputs from parameters - support multiple field names for backward compatibility
            if "input_data" in parameters and parameters["input_data"] is not None:
                input_data = parameters["input_data"]
                logger.info(f"Using 'input_data' field for request_id {request_id}")
            elif "input_datta" in parameters and parameters["input_datta"] is not None:  # Handle potential typo in workflow definition
                input_data = parameters["input_datta"]
                logger.warning(f"Using 'input_datta' field instead of 'input_data' for request_id {request_id}. Please update your workflow definition.")
            elif "text_inputs" in parameters and parameters["text_inputs"] is not None:  # Handle different field name in some workflows
                input_data = parameters["text_inputs"]
                logger.info(f"Using 'text_inputs' field instead of 'input_data' for request_id {request_id}")
            else:
                logger.error(f"No valid input field found in parameters. Available keys: {list(parameters.keys())}")
                raise KeyError("Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters")

            additional_texts = parameters.get("additional_texts", []) or []  # Ensure it's never None
            additional_data = parameters.get("additional_data")
            separator = parameters.get("separator", " ")  # Default to space instead of newline

            logger.info(f"Combining text for request_id {request_id}. Separator: '{separator}'")
            logger.debug(f"Input data type: {type(input_data).__name__}, additional_texts count: {len(additional_texts)}, additional_data present: {additional_data is not None}")

            # Process separator - handle common separator patterns
            processed_separator = separator
            # Only process escape sequences if the separator is a string with escape characters
            if isinstance(separator, str):
                if separator == "\\n":
                    processed_separator = "\n"
                elif separator == "\\t":
                    processed_separator = "\t"
                elif separator == "\\r":
                    processed_separator = "\r"
                elif separator == "\\r\\n":
                    processed_separator = "\r\n"
                # For other cases, use the separator as is

            # Fix: Avoid backslash in f-string expression by performing replacements beforehand
            display_separator = processed_separator.replace('\n', '\\n').replace('\t', '\\t').replace('\r', '\\r')
            logger.debug(f"Processed separator: '{display_separator}' for request_id {request_id}")

            # Prepare the list of strings to join
            strings_to_join = []

            # Always treat input_data as a single string
            logger.debug(f"Processing main input_data for request_id {request_id}")
            main_text = ""

            # Convert input_data to string, regardless of its type
            if input_data is not None:
                if isinstance(input_data, list):
                    # If it's a list, convert it to a string representation
                    logger.debug(f"input_data is a list with {len(input_data)} items for request_id {request_id}")
                    # Just convert the entire list to a string
                    main_text = str(input_data)
                    logger.debug(f"Converted list input_data to string for request_id {request_id}")
                else:
                    # If it's not a list, use it as a single string
                    main_text = str(input_data)
                    logger.debug(f"Using single input_data as string for request_id {request_id}")

            # Start with the main text
            strings_to_join = [main_text] if main_text else []

            # Process additional texts - add them as individual items
            additional_text_items = []
            if additional_texts:
                logger.debug(f"Processing additional_texts with {len(additional_texts)} items for request_id {request_id}")
                for i, text in enumerate(additional_texts):
                    if text is not None:  # Skip None values
                        additional_text_items.append(str(text))
                        logger.debug(f"Added item {i} from additional_texts for request_id {request_id}")
                    else:
                        logger.debug(f"Skipping None item {i} in additional_texts for request_id {request_id}")

            # Process additional_texts_from_connection if present
            additional_texts_from_connection = parameters.get("additional_texts_from_connection")
            if additional_texts_from_connection:
                logger.debug(f"Processing additional_texts_from_connection with {len(additional_texts_from_connection)} items for request_id {request_id}")
                for i, text in enumerate(additional_texts_from_connection):
                    if text is not None:  # Skip None values
                        additional_text_items.append(str(text))
                        logger.debug(f"Added item {i} from additional_texts_from_connection for request_id {request_id}")
                    else:
                        logger.debug(f"Skipping None item {i} in additional_texts_from_connection for request_id {request_id}")

            # Process additional data
            if additional_data is not None:
                logger.debug(f"Processing additional_data (type {type(additional_data).__name__}) for request_id {request_id}")
                if isinstance(additional_data, list):
                    # If it's a list, add each non-None item
                    logger.debug(f"additional_data is a list with {len(additional_data)} items for request_id {request_id}")
                    for i, item in enumerate(additional_data):
                        if item is not None:
                            additional_text_items.append(str(item))
                            logger.debug(f"Added item {i} from additional_data for request_id {request_id}")
                        else:
                            logger.debug(f"Skipping None item {i} in additional_data for request_id {request_id}")
                else:
                    # If it's not a list, add it as a single item
                    additional_text_items.append(str(additional_data))
                    logger.debug(f"Added single additional_data as string for request_id {request_id}")

            # Add all additional texts to the strings to join
            if additional_text_items:
                strings_to_join.append(processed_separator.join(additional_text_items))

            logger.debug(f"Prepared final strings to join for request_id {request_id}")

            # Join everything with the separator
            result = processed_separator.join(strings_to_join)
            logger.info(f"Text combined successfully for request_id {request_id}. Result length: {len(result)}")
            logger.debug(f"Combined text (truncated for log) for request_id {request_id}: {result[:200]}...")

            return {
                "status": "success",
                "result": result
            }

        except Exception as e:
            error_msg = f"Error combining text for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg
            }
