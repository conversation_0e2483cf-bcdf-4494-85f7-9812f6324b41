import requests
import json
from app.config.config import settings
from app.utils.enhanced_logger import get_logger

logger = get_logger("WorkflowService")


def fetch_workflow_orchestration(workflow_id: int) -> dict:
    """
    Fetch workflow from the API.

    Args:
        workflow_id (int): The ID of the workflow to retrieve.

    Returns:
        dict: The response containing workflow details.

    Raises:
        requests.HTTPError: If the request fails or returns an error response.

    Example:
        >>> fetch_workflow_orchestration(16)
        {"success": True, "message": "Workflow string retrieved successfully", "workflow": {...}}
    """
    api_gateway_url = settings.api_gateway_url
    url = f"{api_gateway_url}/api/v1/workflows/orchestration/{workflow_id}"
    headers = {
        "X-Server-Auth-Key": settings.orchestration_server_auth_key.get_secret_value(),
    }

    logger.debug(f"Sending GET request to: {url}")
    response = requests.get(url, headers=headers)

    logger.debug(f"Received response with status code: {response.status_code}")

    if response.status_code != 200:
        logger.error(f"Request failed: {response.text}")
        raise requests.HTTPError(f"Error {response.status_code}: {response.text}")

    try:
        response_json = response.json()
        logger.debug(f"Parsed JSON response: {json.dumps(response_json, indent=2)}")
    except json.JSONDecodeError:
        logger.error(f"Failed to parse response JSON: {response.text}")
        raise ValueError("Invalid JSON response received")

    workflow_data = response_json.get("workflow", {})
    workflow_url = workflow_data.get("workflow_url")

    if not workflow_url:
        logger.error("'workflow_url' not found in response JSON")
        raise KeyError("'workflow_url'is missing in API response")

    try:
        workflow = requests.get(workflow_url)
        workflow.raise_for_status()  # Ensure no request errors

        workflow_data = workflow.json()  # Convert response to JSON
        # logger.debug(f"Retrieved Workflow Data:\n{json.dumps(workflow_data, indent=2)}")
    except requests.RequestException as e:
        logger.error(f"Unable to retrieve the workflow from URL: {str(e)}")
        raise ValueError("Unable to retrieve the workflow from URL")

    return workflow_data
