# 🛠️ Workflow Fix Guide: AlterMetadataComponent Data Flow Issue

## 🔍 **Problem Analysis**

Your workflow is failing because the **AlterMetadataComponent** is receiving a **string** instead of a **dictionary**. Here's what's happening:

### Current Data Flow (❌ Broken):
```
Script Generator → Select Data → Alter Metadata → Combine Text
     (array)        (string)      (expects dict)
```

**Error**: `'str' object has no attribute 'keys'`

### Script Generator Output:
```json
[
  {"data": "wildlife", "data_type": "string", "property_name": "title"},
  {"data": "**[Opening Scene...]**", "data_type": "string", "property_name": "script"},
  {"data": "TOPIC", "data_type": "string", "property_name": "script_type"},
  {"data": "SHORT", "data_type": "string", "property_name": "video_type"}
]
```

### Select Data Output:
```
"**[Opening Scene: Captivating wildlife footage...]**"  // Just the script text (string)
```

### AlterMetadataComponent Expected Input:
```json
{
  "input_metadata": {  // ← Needs to be a dictionary!
    "title": "wildlife",
    "script": "**[Opening Scene...]**",
    "script_type": "TOPIC",
    "video_type": "SHORT"
  },
  "updates": { /* your updates */ },
  "keys_to_remove": [ /* keys to remove */ ]
}
```

## 🎯 **Solutions**

### **Solution 1: Use ConvertScriptDataComponent (Recommended)**

I've created a new component to fix this. Here's the corrected workflow:

#### **New Workflow (✅ Fixed):**
```
Script Generator → ConvertScriptData → AlterMetadata → CombineText
     (array)         (dictionary)       (dictionary)
```

#### **Component Configuration:**

**1. ConvertScriptDataComponent:**
```json
{
  "name": "ConvertScriptDataComponent",
  "display_name": "Convert Script Data",
  "inputs": {
    "script_data": "← Connect to Script Generator output"
  }
}
```

**2. AlterMetadataComponent:**
```json
{
  "name": "AlterMetadataComponent", 
  "display_name": "Alter Metadata",
  "inputs": {
    "input_metadata": "← Connect to ConvertScriptData output",
    "updates": {
      "processed_date": "2024-01-15",
      "status": "processed",
      "word_count": 150
    },
    "keys_to_remove": []
  }
}
```

### **Solution 2: Reconfigure Existing Components**

If you don't want to add a new component, reconfigure your existing workflow:

#### **Option A: Use MergeDataComponent**
```
Script Generator → MergeDataComponent → AlterMetadataComponent
```

**MergeDataComponent Configuration:**
- **Input**: Connect to Script Generator
- **Merge Strategy**: "Deep Merge"
- This converts the array to a single dictionary

#### **Option B: Fix SelectDataComponent Configuration**
Instead of selecting just the script text, configure SelectDataComponent to:
- **Data Type**: "List"
- **Selector**: "0" (select first object)
- **Search Mode**: "Exact Path"

Then add another processing step to convert the object format.

### **Solution 3: Direct Input Configuration**

Configure AlterMetadataComponent with direct input instead of connection:

**AlterMetadataComponent Direct Input:**
```json
{
  "input_metadata": {
    "title": "wildlife",
    "script": "**[Opening Scene: Captivating wildlife footage...]**",
    "script_type": "TOPIC", 
    "video_type": "SHORT"
  },
  "updates": {
    "processed_date": "2024-01-15",
    "status": "enhanced"
  },
  "keys_to_remove": []
}
```

## 🚀 **Implementation Steps**

### **Step 1: Add ConvertScriptDataComponent**

The component is already created and registered. It will appear in your component library.

### **Step 2: Update Your Workflow**

1. **Remove the connection** from SelectDataComponent to AlterMetadataComponent
2. **Add ConvertScriptDataComponent** between Script Generator and AlterMetadataComponent
3. **Connect the components**:
   - Script Generator → ConvertScriptDataComponent
   - ConvertScriptDataComponent → AlterMetadataComponent
   - AlterMetadataComponent → CombineTextComponent

### **Step 3: Configure AlterMetadataComponent**

**Input Configuration:**
```json
{
  "input_metadata": "← Connect to ConvertScriptDataComponent output",
  "updates": {
    "processed_date": "2024-01-15T12:00:00Z",
    "status": "processed",
    "enhanced": true,
    "word_count": 150
  },
  "keys_to_remove": ["temp_field"] // if any
}
```

### **Step 4: Test the Workflow**

The workflow should now work correctly:

1. **Script Generator** outputs array of script data
2. **ConvertScriptDataComponent** converts to metadata dictionary
3. **AlterMetadataComponent** processes the dictionary (adds/removes fields)
4. **CombineTextComponent** combines the processed script

## 📊 **Expected Results**

### **ConvertScriptDataComponent Output:**
```json
{
  "title": "wildlife",
  "script": "**[Opening Scene: Captivating wildlife footage...]**",
  "script_type": "TOPIC",
  "video_type": "SHORT"
}
```

### **AlterMetadataComponent Output:**
```json
{
  "title": "wildlife",
  "script": "**[Opening Scene: Captivating wildlife footage...]**", 
  "script_type": "TOPIC",
  "video_type": "SHORT",
  "processed_date": "2024-01-15T12:00:00Z",  // Added
  "status": "processed",                      // Added
  "enhanced": true,                           // Added
  "word_count": 150                          // Added
}
```

## 🔧 **Alternative Quick Fix**

If you want a quick fix without changing the workflow structure:

### **Option: Modify AlterMetadataComponent Input**

Instead of connecting from SelectDataComponent, configure AlterMetadataComponent with:

**Input Metadata (Direct Input):**
```json
{
  "title": "wildlife",
  "script": "Connect this to SelectDataComponent output",
  "script_type": "TOPIC",
  "video_type": "SHORT"
}
```

**Updates:**
```json
{
  "processed_date": "2024-01-15",
  "status": "enhanced"
}
```

## 🎯 **Recommended Solution**

**Use ConvertScriptDataComponent** - it's the cleanest solution that:
- ✅ Maintains proper data flow
- ✅ Is reusable for other script processing workflows  
- ✅ Follows established component patterns
- ✅ Provides clear separation of concerns

## 🧪 **Testing**

After implementing the fix, test with:

```bash
# Test the new component
python testing_examples/manual_testing_guide.py

# Validate component registration
python validate_component_registration.py
```

The workflow should now execute successfully without the `'str' object has no attribute 'keys'` error.

## 📞 **Need Help?**

If you need assistance implementing this fix:
1. Add ConvertScriptDataComponent to your workflow
2. Connect Script Generator → ConvertScriptData → AlterMetadata
3. Configure AlterMetadataComponent with the metadata dictionary input
4. Test the workflow execution

The AlterMetadataComponent will now receive a proper dictionary and can successfully process your script metadata! 🚀
