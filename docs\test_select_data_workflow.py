#!/usr/bin/env python3
"""
Test for SelectDataComponent in workflow-service.

This test verifies that the SelectDataComponent works correctly
with both legacy build method and modern execute method.
"""

import asyncio
import sys
import os

# Add the app directory to sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

# Import the components
try:
    from components.processing.select_data import SelectDataComponent
    from models.workflow_builder.context import WorkflowContext
    print("✓ Successfully imported workflow-service components")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


async def test_execute_method_list():
    """Test the modern execute method with list data."""
    print("\n=== Testing Execute Method - List ===")

    # Test data
    test_list = ["apple", "banana", "cherry", "date"]

    # Create component and context
    component = SelectDataComponent()
    context = WorkflowContext()
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_data": test_list,
        "data_type": "List",
        "selector": "1"
    }

    # Execute the component
    result = await component.execute(context)
    print(f"Result: {result.outputs}")

    # Verify result
    if result.outputs.get("output_data") == "banana":
        print("✓ Execute method list test passed")
        return True
    else:
        print(f"✗ Execute method list test failed: {result.outputs}")
        return False


async def test_execute_method_dict():
    """Test the modern execute method with dictionary data."""
    print("\n=== Testing Execute Method - Dictionary ===")

    # Test data
    test_dict = {
        "user": {
            "name": "John Doe",
            "age": 30
        },
        "status": "active"
    }

    # Create component and context
    component = SelectDataComponent()
    context = WorkflowContext()
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_data": test_dict,
        "data_type": "Dictionary",
        "selector": "user.name"
    }

    # Execute the component
    result = await component.execute(context)
    print(f"Result: {result.outputs}")

    # Verify result
    if result.outputs.get("output_data") == "John Doe":
        print("✓ Execute method dictionary test passed")
        return True
    else:
        print(f"✗ Execute method dictionary test failed: {result.outputs}")
        return False


async def test_execute_method_auto_detect():
    """Test the modern execute method with auto-detection."""
    print("\n=== Testing Execute Method - Auto-Detection ===")

    # Test data
    test_data = {"key1": "value1", "key2": "value2"}

    # Create component and context
    component = SelectDataComponent()
    context = WorkflowContext()
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_data": test_data,
        "data_type": "Auto-Detect",
        "selector": "key1"
    }

    # Execute the component
    result = await component.execute(context)
    print(f"Result: {result.outputs}")

    # Verify result
    if result.outputs.get("output_data") == "value1":
        print("✓ Execute method auto-detection test passed")
        return True
    else:
        print(f"✗ Execute method auto-detection test failed: {result.outputs}")
        return False


async def test_execute_method_error():
    """Test the modern execute method error handling."""
    print("\n=== Testing Execute Method - Error Handling ===")

    # Test data with invalid selector
    test_list = ["apple", "banana"]

    # Create component and context
    component = SelectDataComponent()
    context = WorkflowContext()
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_data": test_list,
        "data_type": "List",
        "selector": "10"  # Index out of range
    }

    # Execute the component
    result = await component.execute(context)
    print(f"Result: {result.outputs}")

    # Verify error is returned
    if result.outputs.get("error"):
        print("✓ Execute method error handling test passed")
        return True
    else:
        print(f"✗ Execute method error handling test failed: {result.outputs}")
        return False


def test_build_method():
    """Test the legacy build method."""
    print("\n=== Testing Legacy Build Method ===")

    # Test data
    test_list = ["apple", "banana", "cherry"]

    # Create component
    component = SelectDataComponent()

    # Test build method
    inputs = {
        "input_data": test_list,
        "data_type": "List",
        "selector": "0"
    }

    result = component.build(**inputs)
    print(f"Result: {result}")

    # Verify result
    if result.get("output_data") == "apple":
        print("✓ Build method test passed")
        return True
    else:
        print(f"✗ Build method test failed: {result}")
        return False


async def main():
    """Run all workflow-service tests."""
    print("SelectDataComponent Workflow-Service Test")
    print("=" * 50)

    tests = [
        test_execute_method_list,
        test_execute_method_dict,
        test_execute_method_auto_detect,
        test_execute_method_error,
    ]

    # Add sync test
    sync_tests = [test_build_method]

    passed = 0
    total = len(tests) + len(sync_tests)

    # Run async tests
    for test in tests:
        try:
            if await test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")

    # Run sync tests
    for test in sync_tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")

    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")

    if passed == total:
        print("🎉 All workflow-service tests passed!")
        return 0
    else:
        print("❌ Some workflow-service tests failed.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
