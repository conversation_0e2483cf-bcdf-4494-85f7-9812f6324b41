import json
from app.core_.executor_core import EnhancedWorkflowEngine
from app.services.initialize_workflow import initialize_workflow_with_params
from app.utils.enhanced_logger import get_logger
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.config.config import settings
from aiokafka import AIOKafkaProducer

logger = get_logger("TestEngine")


async def run_engine():

    kafka_broker: str = settings.kafka_bootstrap_servers

    producer: AIOKafkaProducer = AIOKafkaProducer(
        bootstrap_servers=kafka_broker,
        max_request_size=524288000,
        value_serializer=lambda v: json.dumps(v).encode("utf-8"),
    )

    mcp_tool_executor = KafkaToolExecutor(producer=producer)

    with open("./app/shared/json_schemas/ciny.json", "r") as file:
        workflow = json.load(file)

    workflow_json_content = {
        "workflow_id": "ciny",
        "payload": {
            "user_dependent_fields": ["topic", "video_type"],
            "user_payload_template": {
                "topic": "latest nvidia event updates",
                "video_type": "SHORT",
            },
        },
    }
    init_workflow = initialize_workflow_with_params(
        workflow,
        params=workflow_json_content,
    )

    async def result_callback(result_info):
        logger.info("result_info", result_info)

    await mcp_tool_executor.start()

    engine = EnhancedWorkflowEngine(
        init_workflow=init_workflow,
        result_callback=result_callback,
        tool_executor=mcp_tool_executor,
        workflow_id="ciny",
    )

    logger.info("Engine initialized:", engine)

    await engine.execute()

    logger.info("Engine Executed, Closing engine")
