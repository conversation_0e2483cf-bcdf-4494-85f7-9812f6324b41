/**
 * WorkflowCard Component
 *
 * A reusable card component for displaying workflow information.
 * This component is memoized to prevent unnecessary re-renders.
 */

"use client";

import React, { memo } from "react";
import { formatDistanceToNow } from "date-fns";
import { Clock } from "lucide-react";
import { WorkflowSummary } from "@/lib/workflowApi";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";

interface WorkflowCardProps {
  workflow: WorkflowSummary;
  onClick: (workflow: WorkflowSummary) => void;
}

// Format date for display
const formatDate = (dateString: string) => {
  try {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true });
  } catch (e) {
    return "Unknown date";
  }
};

const WorkflowCard: React.FC<WorkflowCardProps> = ({ workflow, onClick }) => {
  return (
    <Card
      className="cursor-pointer border-blue-100 transition-shadow hover:border-blue-300 hover:shadow-md"
      onClick={() => onClick(workflow)}
      onKeyDown={(e) => {
        // Handle keyboard navigation - Enter or Space to select
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          onClick(workflow);
        }
      }}
      tabIndex={0}
      role="button"
      aria-label={`Select workflow: ${workflow.name || "Untitled Workflow"}`}
    >
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">{workflow.name || "Untitled Workflow"}</CardTitle>
        <CardDescription className="line-clamp-2">
          {workflow.description || "No description provided"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-muted-foreground text-sm">
          <div className="mt-2 flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span>Updated {formatDate(workflow.updated_at)}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="text-muted-foreground pt-2 text-xs">
        <div className="flex w-full justify-between">
          <span>ID: {workflow.id.substring(0, 8)}...</span>
          <span>{workflow.execution_count} executions</span>
        </div>
      </CardFooter>
    </Card>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(WorkflowCard);
