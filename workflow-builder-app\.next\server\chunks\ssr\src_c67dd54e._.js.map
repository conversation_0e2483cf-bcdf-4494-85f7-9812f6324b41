{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/config/features.ts"], "sourcesContent": ["/**\r\n * Feature flags for the application\r\n *\r\n * These flags control which features are enabled in the application.\r\n * They can be overridden by environment variables.\r\n */\r\nexport const FEATURES = {\r\n  // Validation features\r\n  FRONTEND_VALIDATION: true, // Enable frontend validation\r\n  BACKEND_VALIDATION: false, // Disable backend validation\r\n  HYBRID_VALIDATION: false, // Disable hybrid validation\r\n  VALIDATION_DEBUG: process.env.NEXT_PUBLIC_VALIDATION_DEBUG === \"true\", // Disable validation debugging by default\r\n\r\n  // Validation behavior\r\n  VALIDATE_ON_EDIT: false, // Disable validation during editing\r\n  VALIDATE_ON_SAVE: true, // Enable validation before saving\r\n  VALIDATE_ON_EXECUTE: true, // Enable validation before execution\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AACM,MAAM,WAAW;IACtB,sBAAsB;IACtB,qBAAqB;IACrB,oBAAoB;IACpB,mBAAmB;IACnB,kBAAkB,QAAQ,GAAG,CAAC,4BAA4B,KAAK;IAE/D,sBAAsB;IACtB,kBAAkB;IAClB,kBAAkB;IAClB,qBAAqB;AACvB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/store/validationStore.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { Node, Edge } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { validateWorkflow } from \"@/lib/validation\";\r\nimport {\r\n  validateWorkflowSmart,\r\n  validateWorkflowBeforeSave,\r\n  validateWorkflowBeforeExecution,\r\n  validateWorkflowDuringEditing,\r\n} from \"@/lib/validation/smartValidation\";\r\nimport {\r\n  ValidationError,\r\n  ValidationResult,\r\n  MissingField,\r\n  WorkflowValidationOptions,\r\n} from \"@/lib/validation/types\";\r\n\r\n/**\r\n * Validation state interface\r\n */\r\ninterface ValidationState {\r\n  // State\r\n  isValid: boolean;\r\n  errors: ValidationError[];\r\n  warnings: ValidationError[];\r\n  infos: ValidationError[];\r\n  missingFields: MissingField[];\r\n  startNodeId?: string;\r\n  connectedNodes?: Set<string>;\r\n  isValidating: boolean;\r\n  hasValidated: boolean;\r\n\r\n  // Actions\r\n  validateWorkflow: (\r\n    nodes: Node<WorkflowNodeData>[],\r\n    edges: Edge[],\r\n    options?: WorkflowValidationOptions\r\n  ) => ValidationResult;\r\n  validateWorkflowSmart: (\r\n    nodes: Node<WorkflowNodeData>[],\r\n    edges: Edge[],\r\n    options?: WorkflowValidationOptions\r\n  ) => Promise<ValidationResult>;\r\n  validateBeforeSave: (\r\n    nodes: Node<WorkflowNodeData>[],\r\n    edges: Edge[]\r\n  ) => Promise<ValidationResult>;\r\n  validateBeforeExecution: (\r\n    nodes: Node<WorkflowNodeData>[],\r\n    edges: Edge[]\r\n  ) => Promise<ValidationResult>;\r\n  validateDuringEditing: (\r\n    nodes: Node<WorkflowNodeData>[],\r\n    edges: Edge[]\r\n  ) => Promise<ValidationResult>;\r\n  clearValidation: () => void;\r\n}\r\n\r\n/**\r\n * Validation store using Zustand\r\n */\r\nexport const useValidationStore = create<ValidationState>()((set, get) => ({\r\n  // Initial state\r\n  isValid: true,\r\n  errors: [],\r\n  warnings: [],\r\n  infos: [],\r\n  missingFields: [],\r\n  isValidating: false,\r\n  hasValidated: false,\r\n\r\n  // Actions\r\n  validateWorkflow: (nodes, edges, options) => {\r\n    set({ isValidating: true });\r\n\r\n    // Perform validation\r\n    const result = validateWorkflow(nodes, edges, options);\r\n\r\n    // Update state with results\r\n    set({\r\n      isValid: result.isValid,\r\n      errors: result.errors,\r\n      warnings: result.warnings,\r\n      infos: result.infos || [],\r\n      missingFields: result.missingFields || [],\r\n      startNodeId: result.startNodeId,\r\n      connectedNodes: result.connectedNodes,\r\n      isValidating: false,\r\n      hasValidated: true,\r\n    });\r\n\r\n    return result;\r\n  },\r\n\r\n  validateWorkflowSmart: async (nodes, edges, options) => {\r\n    set({ isValidating: true });\r\n\r\n    // Perform validation\r\n    const result = await validateWorkflowSmart(nodes, edges, options);\r\n\r\n    // Update state with results\r\n    set({\r\n      isValid: result.isValid,\r\n      errors: result.errors,\r\n      warnings: result.warnings,\r\n      infos: result.infos || [],\r\n      missingFields: result.missingFields || [],\r\n      startNodeId: result.startNodeId,\r\n      connectedNodes: result.connectedNodes,\r\n      isValidating: false,\r\n      hasValidated: true,\r\n    });\r\n\r\n    return result;\r\n  },\r\n\r\n  validateBeforeSave: async (nodes, edges) => {\r\n    set({ isValidating: true });\r\n\r\n    // Perform validation\r\n    const result = await validateWorkflowBeforeSave(nodes, edges);\r\n\r\n    // Update state with results\r\n    set({\r\n      isValid: result.isValid,\r\n      errors: result.errors,\r\n      warnings: result.warnings,\r\n      infos: result.infos || [],\r\n      missingFields: result.missingFields || [],\r\n      startNodeId: result.startNodeId,\r\n      connectedNodes: result.connectedNodes,\r\n      isValidating: false,\r\n      hasValidated: true,\r\n    });\r\n\r\n    return result;\r\n  },\r\n\r\n  validateBeforeExecution: async (nodes, edges) => {\r\n    set({ isValidating: true });\r\n\r\n    // Perform validation\r\n    const result = await validateWorkflowBeforeExecution(nodes, edges);\r\n\r\n    // Update state with results\r\n    set({\r\n      isValid: result.isValid,\r\n      errors: result.errors,\r\n      warnings: result.warnings,\r\n      infos: result.infos || [],\r\n      missingFields: result.missingFields || [],\r\n      startNodeId: result.startNodeId,\r\n      connectedNodes: result.connectedNodes,\r\n      isValidating: false,\r\n      hasValidated: true,\r\n    });\r\n\r\n    return result;\r\n  },\r\n\r\n  validateDuringEditing: async (nodes, edges) => {\r\n    set({ isValidating: true });\r\n\r\n    // Perform validation\r\n    const result = await validateWorkflowDuringEditing(nodes, edges);\r\n\r\n    // Update state with results\r\n    set({\r\n      isValid: result.isValid,\r\n      errors: result.errors,\r\n      warnings: result.warnings,\r\n      infos: result.infos || [],\r\n      missingFields: result.missingFields || [],\r\n      startNodeId: result.startNodeId,\r\n      connectedNodes: result.connectedNodes,\r\n      isValidating: false,\r\n      hasValidated: true,\r\n    });\r\n\r\n    return result;\r\n  },\r\n\r\n  clearValidation: () => {\r\n    set({\r\n      isValid: true,\r\n      errors: [],\r\n      warnings: [],\r\n      infos: [],\r\n      missingFields: [],\r\n      startNodeId: undefined,\r\n      connectedNodes: undefined,\r\n      hasValidated: false,\r\n    });\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;AAAA;AAGA;AAAA;AACA;;;;AAyDO,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAAqB,CAAC,KAAK,MAAQ,CAAC;QACzE,gBAAgB;QAChB,SAAS;QACT,QAAQ,EAAE;QACV,UAAU,EAAE;QACZ,OAAO,EAAE;QACT,eAAe,EAAE;QACjB,cAAc;QACd,cAAc;QAEd,UAAU;QACV,kBAAkB,CAAC,OAAO,OAAO;YAC/B,IAAI;gBAAE,cAAc;YAAK;YAEzB,qBAAqB;YACrB,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,OAAO;YAE9C,4BAA4B;YAC5B,IAAI;gBACF,SAAS,OAAO,OAAO;gBACvB,QAAQ,OAAO,MAAM;gBACrB,UAAU,OAAO,QAAQ;gBACzB,OAAO,OAAO,KAAK,IAAI,EAAE;gBACzB,eAAe,OAAO,aAAa,IAAI,EAAE;gBACzC,aAAa,OAAO,WAAW;gBAC/B,gBAAgB,OAAO,cAAc;gBACrC,cAAc;gBACd,cAAc;YAChB;YAEA,OAAO;QACT;QAEA,uBAAuB,OAAO,OAAO,OAAO;YAC1C,IAAI;gBAAE,cAAc;YAAK;YAEzB,qBAAqB;YACrB,MAAM,SAAS,MAAM,CAAA,GAAA,2IAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,OAAO;YAEzD,4BAA4B;YAC5B,IAAI;gBACF,SAAS,OAAO,OAAO;gBACvB,QAAQ,OAAO,MAAM;gBACrB,UAAU,OAAO,QAAQ;gBACzB,OAAO,OAAO,KAAK,IAAI,EAAE;gBACzB,eAAe,OAAO,aAAa,IAAI,EAAE;gBACzC,aAAa,OAAO,WAAW;gBAC/B,gBAAgB,OAAO,cAAc;gBACrC,cAAc;gBACd,cAAc;YAChB;YAEA,OAAO;QACT;QAEA,oBAAoB,OAAO,OAAO;YAChC,IAAI;gBAAE,cAAc;YAAK;YAEzB,qBAAqB;YACrB,MAAM,SAAS,MAAM,CAAA,GAAA,2IAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO;YAEvD,4BAA4B;YAC5B,IAAI;gBACF,SAAS,OAAO,OAAO;gBACvB,QAAQ,OAAO,MAAM;gBACrB,UAAU,OAAO,QAAQ;gBACzB,OAAO,OAAO,KAAK,IAAI,EAAE;gBACzB,eAAe,OAAO,aAAa,IAAI,EAAE;gBACzC,aAAa,OAAO,WAAW;gBAC/B,gBAAgB,OAAO,cAAc;gBACrC,cAAc;gBACd,cAAc;YAChB;YAEA,OAAO;QACT;QAEA,yBAAyB,OAAO,OAAO;YACrC,IAAI;gBAAE,cAAc;YAAK;YAEzB,qBAAqB;YACrB,MAAM,SAAS,MAAM,CAAA,GAAA,2IAAA,CAAA,kCAA+B,AAAD,EAAE,OAAO;YAE5D,4BAA4B;YAC5B,IAAI;gBACF,SAAS,OAAO,OAAO;gBACvB,QAAQ,OAAO,MAAM;gBACrB,UAAU,OAAO,QAAQ;gBACzB,OAAO,OAAO,KAAK,IAAI,EAAE;gBACzB,eAAe,OAAO,aAAa,IAAI,EAAE;gBACzC,aAAa,OAAO,WAAW;gBAC/B,gBAAgB,OAAO,cAAc;gBACrC,cAAc;gBACd,cAAc;YAChB;YAEA,OAAO;QACT;QAEA,uBAAuB,OAAO,OAAO;YACnC,IAAI;gBAAE,cAAc;YAAK;YAEzB,qBAAqB;YACrB,MAAM,SAAS,MAAM,CAAA,GAAA,2IAAA,CAAA,gCAA6B,AAAD,EAAE,OAAO;YAE1D,4BAA4B;YAC5B,IAAI;gBACF,SAAS,OAAO,OAAO;gBACvB,QAAQ,OAAO,MAAM;gBACrB,UAAU,OAAO,QAAQ;gBACzB,OAAO,OAAO,KAAK,IAAI,EAAE;gBACzB,eAAe,OAAO,aAAa,IAAI,EAAE;gBACzC,aAAa,OAAO,WAAW;gBAC/B,gBAAgB,OAAO,cAAc;gBACrC,cAAc;gBACd,cAAc;YAChB;YAEA,OAAO;QACT;QAEA,iBAAiB;YACf,IAAI;gBACF,SAAS;gBACT,QAAQ,EAAE;gBACV,UAAU,EAAE;gBACZ,OAAO,EAAE;gBACT,eAAe,EAAE;gBACjB,aAAa;gBACb,gBAAgB;gBAChB,cAAc;YAChB;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/store/executionStore.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\n\r\n\r\nexport interface MissingField {\r\n  nodeId: string;\r\n  nodeName: string;\r\n  name: string;\r\n  displayName: string;\r\n  info?: string;\r\n  inputType: string;\r\n  connected_to_start?: boolean;\r\n  directly_connected_to_start?: boolean;\r\n  required?: boolean;\r\n  isEmpty?: boolean;\r\n  currentValue?: any;\r\n  options?: Array<string | { value: string; label: string }> | null;\r\n  // Handle connection information\r\n  is_handle?: boolean;\r\n  is_connected?: boolean;\r\n  // Schema information for JSON objects\r\n  schema?: {\r\n    type?: string;\r\n    properties?: Record<string, any>;\r\n    required?: string[];\r\n  };\r\n}\r\n\r\ninterface ExecutionState {\r\n  // Dialog state\r\n  isDialogOpen: boolean;\r\n  setDialogOpen: (isOpen: boolean) => void;\r\n\r\n  // Missing fields\r\n  missingFields: MissingField[];\r\n  setMissingFields: (fields: MissingField[]) => void;\r\n\r\n  // Tab state\r\n  activeTab: string;\r\n  setActiveTab: (tab: string) => void;\r\n\r\n  // Parameters state\r\n  fieldValues: Record<string, any>;\r\n  setFieldValues: (values: Record<string, any>) => void;\r\n  updateFieldValue: (fieldId: string, value: any) => void;\r\n\r\n  // Form validation\r\n  errors: Record<string, string>;\r\n  setErrors: (errors: Record<string, string>) => void;\r\n  isFormValid: boolean;\r\n  setIsFormValid: (isValid: boolean) => void;\r\n\r\n  // Logs state\r\n  logs: string[];\r\n  setLogs: (logs: string[]) => void;\r\n  addLog: (log: string) => void;\r\n  clearLogs: () => void;\r\n\r\n  // Execution state\r\n  isExecuting: boolean;\r\n  setIsExecuting: (isExecuting: boolean) => void;\r\n\r\n  // Correlation ID for SSE streaming\r\n  correlationId: string | null;\r\n  setCorrelationId: (id: string | null) => void;\r\n\r\n  // SSE connection state\r\n  isStreaming: boolean;\r\n  setIsStreaming: (isStreaming: boolean) => void;\r\n\r\n  // Track if execution is ongoing but dialog is closed\r\n  hasActiveExecution: boolean;\r\n  setHasActiveExecution: (hasActive: boolean) => void;\r\n\r\n  // Stop execution\r\n  stopExecution: () => void;\r\n\r\n  // Reopen dialog to view execution\r\n  viewExecution: () => void;\r\n\r\n  // Process values for execution\r\n  processFieldValues: () => Record<string, any>;\r\n\r\n  // Reset state\r\n  resetState: () => void;\r\n}\r\n\r\nexport const useExecutionStore = create<ExecutionState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      // Dialog state\r\n      isDialogOpen: false,\r\n      setDialogOpen: (isOpen) =>\r\n        set((state) => ({\r\n          isDialogOpen: isOpen,\r\n          // If dialog is being opened and there's an active execution, update the state\r\n          hasActiveExecution: isOpen ? false : state.hasActiveExecution || state.isStreaming,\r\n        })),\r\n\r\n      // Missing fields\r\n      missingFields: [],\r\n      setMissingFields: (fields) => set({ missingFields: fields }),\r\n\r\n      // Tab state - default to parameters tab\r\n      activeTab: \"parameters\",\r\n      setActiveTab: (tab) => set({ activeTab: tab }),\r\n\r\n      // Parameters state\r\n      fieldValues: {},\r\n      setFieldValues: (values) => set({ fieldValues: values }),\r\n      updateFieldValue: (fieldId, value) =>\r\n        set((state) => ({\r\n          fieldValues: { ...state.fieldValues, [fieldId]: value },\r\n        })),\r\n\r\n      // Form validation\r\n      errors: {},\r\n      setErrors: (errors) => set({ errors }),\r\n      isFormValid: false,\r\n      setIsFormValid: (isValid) => set({ isFormValid: isValid }),\r\n\r\n      // Logs state\r\n      logs: [],\r\n      setLogs: (logs) => set({ logs }),\r\n      addLog: (log) => set((state) => ({ logs: [...state.logs, log] })),\r\n      clearLogs: () => set({ logs: [] }),\r\n\r\n      // Execution state\r\n      isExecuting: false,\r\n      setIsExecuting: (isExecuting) => set({ isExecuting }),\r\n\r\n      // Correlation ID for SSE streaming\r\n      correlationId: null,\r\n      setCorrelationId: (id) => set({ correlationId: id }),\r\n\r\n      // SSE connection state\r\n      isStreaming: false,\r\n      setIsStreaming: (isStreaming) =>\r\n        set((state) => ({\r\n          isStreaming,\r\n          // Update hasActiveExecution based on streaming state\r\n          hasActiveExecution:\r\n            !state.isDialogOpen && isStreaming\r\n              ? true\r\n              : state.hasActiveExecution && !isStreaming\r\n                ? false\r\n                : state.hasActiveExecution,\r\n        })),\r\n\r\n      // Track if execution is ongoing but dialog is closed\r\n      hasActiveExecution: false,\r\n      setHasActiveExecution: (hasActive) => set({ hasActiveExecution: hasActive }),\r\n\r\n      // Stop execution function - this will be called by the Stop Execution button\r\n      stopExecution: () => {\r\n        const state = get();\r\n        // Add log entries\r\n        const timestamp = new Date().toISOString().substring(11, 19); // Extract time HH:MM:SS\r\n\r\n        // Add correlation ID to the log if available\r\n        const correlationIdInfo = state.correlationId\r\n          ? ` (Correlation ID: ${state.correlationId})`\r\n          : '';\r\n\r\n        const logEntry = `[${timestamp}] ⚠️ Workflow execution manually stopped by user${correlationIdInfo}`;\r\n        const backendLogEntry = `[${timestamp}] 📡 Stop request sent to backend server`;\r\n\r\n        // Update state\r\n        set((state) => ({\r\n          logs: [...state.logs, logEntry, backendLogEntry],\r\n          isStreaming: false,\r\n          hasActiveExecution: false,\r\n        }));\r\n\r\n        // Note: The actual SSE connection closing and backend API call are handled in the component\r\n      },\r\n\r\n      // View execution function - reopen dialog to view ongoing execution\r\n      viewExecution: () => {\r\n        console.log(\"Reopening execution dialog and resetting field values\");\r\n        // Reset field values to ensure fresh input values\r\n        set((state) => ({\r\n          isDialogOpen: true,\r\n          activeTab: \"logs\",\r\n          hasActiveExecution: false,\r\n          fieldValues: {}, // Reset field values to ensure fresh input\r\n          errors: {}, // Reset errors\r\n          isFormValid: false, // Reset form validation\r\n        }));\r\n      },\r\n\r\n      // Process values for execution\r\n      processFieldValues: () => {\r\n        const state = get();\r\n        const processedValues: Record<string, any> = {};\r\n\r\n        console.log(\"Processing field values from store:\", state.fieldValues);\r\n\r\n        state.missingFields.forEach((field) => {\r\n          const fieldId = `${field.nodeId}_${field.name}`;\r\n\r\n          // Use the current value from the node's configuration if available\r\n          // Otherwise, use the value from the form\r\n          let value;\r\n          if (field.isEmpty === false && field.currentValue !== undefined) {\r\n            value = field.currentValue;\r\n            console.log(`Using pre-configured value for field ${fieldId}:`, value);\r\n          } else {\r\n            value = state.fieldValues[fieldId];\r\n            console.log(`Using form value for field ${fieldId}:`, value);\r\n          }\r\n\r\n          console.log(`Processing field ${fieldId} with value:`, value);\r\n\r\n          // Parse JSON strings for object and array types\r\n          if (\r\n            (field.inputType === \"object\" ||\r\n              field.inputType === \"dict\" ||\r\n              field.inputType === \"json\" ||\r\n              field.inputType === \"array\" ||\r\n              field.inputType === \"list\") &&\r\n            typeof value === \"string\"\r\n          ) {\r\n            try {\r\n              value = JSON.parse(value);\r\n              console.log(`Successfully parsed JSON for field ${fieldId}:`, value);\r\n            } catch (e) {\r\n              console.error(`Failed to parse JSON for field ${fieldId}:`, e);\r\n            }\r\n          }\r\n\r\n          // Convert string numbers to actual numbers\r\n          if (\r\n            (field.inputType === \"number\" ||\r\n              field.inputType === \"int\" ||\r\n              field.inputType === \"float\") &&\r\n            typeof value === \"string\"\r\n          ) {\r\n            value = Number(value);\r\n            console.log(`Converted number field ${fieldId} to:`, value);\r\n          }\r\n\r\n          // Group values by node\r\n          if (!processedValues[field.nodeId]) {\r\n            processedValues[field.nodeId] = {};\r\n          }\r\n\r\n          processedValues[field.nodeId][field.name] = value;\r\n        });\r\n\r\n        console.log(\"Final processed values for execution:\", processedValues);\r\n        return processedValues;\r\n      },\r\n\r\n      // Reset state\r\n      resetState: () => {\r\n        // Clear field values to ensure fresh input values each time\r\n        console.log(\"Resetting execution store state - clearing all field values\");\r\n        set({\r\n          activeTab: \"parameters\",\r\n          fieldValues: {}, // Always reset to empty object\r\n          errors: {},\r\n          isFormValid: false,\r\n          logs: [],\r\n          isExecuting: false,\r\n          missingFields: [],\r\n          correlationId: null,\r\n          isStreaming: false,\r\n          hasActiveExecution: false,\r\n        });\r\n      },\r\n    }),\r\n    {\r\n      name: \"execution-store\",\r\n      partialize: (state) => ({\r\n        // Don't persist fieldValues to ensure fresh input values each time\r\n        logs: state.logs,\r\n      }),\r\n    },\r\n  ),\r\n);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAsFO,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACpC,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,eAAe;QACf,cAAc;QACd,eAAe,CAAC,SACd,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc;oBACd,8EAA8E;oBAC9E,oBAAoB,SAAS,QAAQ,MAAM,kBAAkB,IAAI,MAAM,WAAW;gBACpF,CAAC;QAEH,iBAAiB;QACjB,eAAe,EAAE;QACjB,kBAAkB,CAAC,SAAW,IAAI;gBAAE,eAAe;YAAO;QAE1D,wCAAwC;QACxC,WAAW;QACX,cAAc,CAAC,MAAQ,IAAI;gBAAE,WAAW;YAAI;QAE5C,mBAAmB;QACnB,aAAa,CAAC;QACd,gBAAgB,CAAC,SAAW,IAAI;gBAAE,aAAa;YAAO;QACtD,kBAAkB,CAAC,SAAS,QAC1B,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;wBAAE,GAAG,MAAM,WAAW;wBAAE,CAAC,QAAQ,EAAE;oBAAM;gBACxD,CAAC;QAEH,kBAAkB;QAClB,QAAQ,CAAC;QACT,WAAW,CAAC,SAAW,IAAI;gBAAE;YAAO;QACpC,aAAa;QACb,gBAAgB,CAAC,UAAY,IAAI;gBAAE,aAAa;YAAQ;QAExD,aAAa;QACb,MAAM,EAAE;QACR,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAC9B,QAAQ,CAAC,MAAQ,IAAI,CAAC,QAAU,CAAC;oBAAE,MAAM;2BAAI,MAAM,IAAI;wBAAE;qBAAI;gBAAC,CAAC;QAC/D,WAAW,IAAM,IAAI;gBAAE,MAAM,EAAE;YAAC;QAEhC,kBAAkB;QAClB,aAAa;QACb,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,mCAAmC;QACnC,eAAe;QACf,kBAAkB,CAAC,KAAO,IAAI;gBAAE,eAAe;YAAG;QAElD,uBAAuB;QACvB,aAAa;QACb,gBAAgB,CAAC,cACf,IAAI,CAAC,QAAU,CAAC;oBACd;oBACA,qDAAqD;oBACrD,oBACE,CAAC,MAAM,YAAY,IAAI,cACnB,OACA,MAAM,kBAAkB,IAAI,CAAC,cAC3B,QACA,MAAM,kBAAkB;gBAClC,CAAC;QAEH,qDAAqD;QACrD,oBAAoB;QACpB,uBAAuB,CAAC,YAAc,IAAI;gBAAE,oBAAoB;YAAU;QAE1E,6EAA6E;QAC7E,eAAe;YACb,MAAM,QAAQ;YACd,kBAAkB;YAClB,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,SAAS,CAAC,IAAI,KAAK,wBAAwB;YAEtF,6CAA6C;YAC7C,MAAM,oBAAoB,MAAM,aAAa,GACzC,CAAC,kBAAkB,EAAE,MAAM,aAAa,CAAC,CAAC,CAAC,GAC3C;YAEJ,MAAM,WAAW,CAAC,CAAC,EAAE,UAAU,gDAAgD,EAAE,mBAAmB;YACpG,MAAM,kBAAkB,CAAC,CAAC,EAAE,UAAU,wCAAwC,CAAC;YAE/E,eAAe;YACf,IAAI,CAAC,QAAU,CAAC;oBACd,MAAM;2BAAI,MAAM,IAAI;wBAAE;wBAAU;qBAAgB;oBAChD,aAAa;oBACb,oBAAoB;gBACtB,CAAC;QAED,4FAA4F;QAC9F;QAEA,oEAAoE;QACpE,eAAe;YACb,QAAQ,GAAG,CAAC;YACZ,kDAAkD;YAClD,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc;oBACd,WAAW;oBACX,oBAAoB;oBACpB,aAAa,CAAC;oBACd,QAAQ,CAAC;oBACT,aAAa;gBACf,CAAC;QACH;QAEA,+BAA+B;QAC/B,oBAAoB;YAClB,MAAM,QAAQ;YACd,MAAM,kBAAuC,CAAC;YAE9C,QAAQ,GAAG,CAAC,uCAAuC,MAAM,WAAW;YAEpE,MAAM,aAAa,CAAC,OAAO,CAAC,CAAC;gBAC3B,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE;gBAE/C,mEAAmE;gBACnE,yCAAyC;gBACzC,IAAI;gBACJ,IAAI,MAAM,OAAO,KAAK,SAAS,MAAM,YAAY,KAAK,WAAW;oBAC/D,QAAQ,MAAM,YAAY;oBAC1B,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,QAAQ,CAAC,CAAC,EAAE;gBAClE,OAAO;oBACL,QAAQ,MAAM,WAAW,CAAC,QAAQ;oBAClC,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,QAAQ,CAAC,CAAC,EAAE;gBACxD;gBAEA,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ,YAAY,CAAC,EAAE;gBAEvD,gDAAgD;gBAChD,IACE,CAAC,MAAM,SAAS,KAAK,YACnB,MAAM,SAAS,KAAK,UACpB,MAAM,SAAS,KAAK,UACpB,MAAM,SAAS,KAAK,WACpB,MAAM,SAAS,KAAK,MAAM,KAC5B,OAAO,UAAU,UACjB;oBACA,IAAI;wBACF,QAAQ,KAAK,KAAK,CAAC;wBACnB,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ,CAAC,CAAC,EAAE;oBAChE,EAAE,OAAO,GAAG;wBACV,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,QAAQ,CAAC,CAAC,EAAE;oBAC9D;gBACF;gBAEA,2CAA2C;gBAC3C,IACE,CAAC,MAAM,SAAS,KAAK,YACnB,MAAM,SAAS,KAAK,SACpB,MAAM,SAAS,KAAK,OAAO,KAC7B,OAAO,UAAU,UACjB;oBACA,QAAQ,OAAO;oBACf,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,IAAI,CAAC,EAAE;gBACvD;gBAEA,uBAAuB;gBACvB,IAAI,CAAC,eAAe,CAAC,MAAM,MAAM,CAAC,EAAE;oBAClC,eAAe,CAAC,MAAM,MAAM,CAAC,GAAG,CAAC;gBACnC;gBAEA,eAAe,CAAC,MAAM,MAAM,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG;YAC9C;YAEA,QAAQ,GAAG,CAAC,yCAAyC;YACrD,OAAO;QACT;QAEA,cAAc;QACd,YAAY;YACV,4DAA4D;YAC5D,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,WAAW;gBACX,aAAa,CAAC;gBACd,QAAQ,CAAC;gBACT,aAAa;gBACb,MAAM,EAAE;gBACR,aAAa;gBACb,eAAe,EAAE;gBACjB,eAAe;gBACf,aAAa;gBACb,oBAAoB;YACtB;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,mEAAmE;YACnE,MAAM,MAAM,IAAI;QAClB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/store/mcpToolsStore.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\n\r\n// Generic component state store that works for any component type\r\ninterface ComponentState {\r\n  [key: string]: any;\r\n}\r\n\r\ninterface ComponentStateStore {\r\n  // Store state by nodeId and then by key\r\n  nodes: Record<string, ComponentState>;\r\n  // Set a value for a specific node and key\r\n  setValue: (nodeId: string, key: string, value: any) => void;\r\n  // Get a value for a specific node and key with optional default\r\n  getValue: (nodeId: string, key: string, defaultValue?: any) => any;\r\n  // Clear all state for a specific node\r\n  clearNodeState: (nodeId: string) => void;\r\n  // Clear state for a specific key in a node\r\n  clearNodeKey: (nodeId: string, key: string) => void;\r\n  // Clear all state for all nodes\r\n  clearAllState: () => void;\r\n}\r\n\r\nexport const useComponentStateStore = create<ComponentStateStore>()(\r\n  persist(\r\n    (set, get) => ({\r\n      nodes: {},\r\n      setValue: (nodeId, key, value) =>\r\n        set((state) => ({\r\n          nodes: {\r\n            ...state.nodes,\r\n            [nodeId]: {\r\n              ...(state.nodes[nodeId] || {}),\r\n              [key]: value,\r\n            },\r\n          },\r\n        })),\r\n      getValue: (nodeId, key, defaultValue = undefined) => {\r\n        const state = get();\r\n        if (!state.nodes[nodeId]) return defaultValue;\r\n        return state.nodes[nodeId][key] !== undefined ? state.nodes[nodeId][key] : defaultValue;\r\n      },\r\n      clearNodeState: (nodeId) =>\r\n        set((state) => {\r\n          const newNodes = { ...state.nodes };\r\n          delete newNodes[nodeId];\r\n          return { nodes: newNodes };\r\n        }),\r\n      clearNodeKey: (nodeId, key) =>\r\n        set((state) => {\r\n          if (!state.nodes[nodeId]) return state;\r\n          const nodeState = { ...state.nodes[nodeId] };\r\n          delete nodeState[key];\r\n          return {\r\n            nodes: {\r\n              ...state.nodes,\r\n              [nodeId]: nodeState,\r\n            },\r\n          };\r\n        }),\r\n      clearAllState: () => set({ nodes: {} }),\r\n    }),\r\n    { name: \"component-state-store\" },\r\n  ),\r\n);\r\n\r\n// Helper functions for MCP Tools component\r\nexport const getMcpToolsValue = (nodeId: string, key: string, defaultValue?: any) => {\r\n  return useComponentStateStore.getState().getValue(nodeId, key, defaultValue);\r\n};\r\n\r\nexport const setMcpToolsValue = (nodeId: string, key: string, value: any) => {\r\n  useComponentStateStore.getState().setValue(nodeId, key, value);\r\n};\r\n\r\nexport const clearMcpToolsState = (nodeId: string) => {\r\n  // Clear from Zustand store\r\n  useComponentStateStore.getState().clearNodeState(nodeId);\r\n\r\n  console.log(`MCP tools state cleared for node ${nodeId}`);\r\n};\r\n\r\n// For backward compatibility\r\nexport const clearAllMcpToolsState = () => {\r\n  try {\r\n    // Clear from local storage directly\r\n    localStorage.removeItem(\"mcp-tools-store\");\r\n    // Also clear from the new store\r\n    useComponentStateStore.getState().clearAllState();\r\n    console.log(\"All MCP tools state cleared from local storage\");\r\n  } catch (error) {\r\n    console.error(\"Error clearing MCP tools state from local storage:\", error);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAsBO,MAAM,yBAAyB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACzC,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO,CAAC;QACR,UAAU,CAAC,QAAQ,KAAK,QACtB,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO;wBACL,GAAG,MAAM,KAAK;wBACd,CAAC,OAAO,EAAE;4BACR,GAAI,MAAM,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;4BAC7B,CAAC,IAAI,EAAE;wBACT;oBACF;gBACF,CAAC;QACH,UAAU,CAAC,QAAQ,KAAK,eAAe,SAAS;YAC9C,MAAM,QAAQ;YACd,IAAI,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,OAAO;YACjC,OAAO,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,YAAY,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG;QAC7E;QACA,gBAAgB,CAAC,SACf,IAAI,CAAC;gBACH,MAAM,WAAW;oBAAE,GAAG,MAAM,KAAK;gBAAC;gBAClC,OAAO,QAAQ,CAAC,OAAO;gBACvB,OAAO;oBAAE,OAAO;gBAAS;YAC3B;QACF,cAAc,CAAC,QAAQ,MACrB,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,OAAO;gBACjC,MAAM,YAAY;oBAAE,GAAG,MAAM,KAAK,CAAC,OAAO;gBAAC;gBAC3C,OAAO,SAAS,CAAC,IAAI;gBACrB,OAAO;oBACL,OAAO;wBACL,GAAG,MAAM,KAAK;wBACd,CAAC,OAAO,EAAE;oBACZ;gBACF;YACF;QACF,eAAe,IAAM,IAAI;gBAAE,OAAO,CAAC;YAAE;IACvC,CAAC,GACD;IAAE,MAAM;AAAwB;AAK7B,MAAM,mBAAmB,CAAC,QAAgB,KAAa;IAC5D,OAAO,uBAAuB,QAAQ,GAAG,QAAQ,CAAC,QAAQ,KAAK;AACjE;AAEO,MAAM,mBAAmB,CAAC,QAAgB,KAAa;IAC5D,uBAAuB,QAAQ,GAAG,QAAQ,CAAC,QAAQ,KAAK;AAC1D;AAEO,MAAM,qBAAqB,CAAC;IACjC,2BAA2B;IAC3B,uBAAuB,QAAQ,GAAG,cAAc,CAAC;IAEjD,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,QAAQ;AAC1D;AAGO,MAAM,wBAAwB;IACnC,IAAI;QACF,oCAAoC;QACpC,aAAa,UAAU,CAAC;QACxB,gCAAgC;QAChC,uBAAuB,QAAQ,GAAG,aAAa;QAC/C,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sDAAsD;IACtE;AACF", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/store/inspectorStore.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport { Node } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\n\r\ninterface InspectorState {\r\n  // UI state\r\n  activeTab: \"settings\" | \"info\" | \"advanced\";\r\n  showValidation: boolean;\r\n  validationErrors: Record<string, { isValid: boolean; message: string }>;\r\n  \r\n  // Preferences (persisted)\r\n  preferences: {\r\n    defaultTab: \"settings\" | \"info\" | \"advanced\";\r\n    expandedSections: string[];\r\n  };\r\n  \r\n  // Actions\r\n  setActiveTab: (tab: \"settings\" | \"info\" | \"advanced\") => void;\r\n  setShowValidation: (show: boolean) => void;\r\n  setValidationError: (inputName: string, error: { isValid: boolean; message: string }) => void;\r\n  clearValidationErrors: () => void;\r\n  setPreference: <K extends keyof InspectorState[\"preferences\"]>(\r\n    key: K,\r\n    value: InspectorState[\"preferences\"][K]\r\n  ) => void;\r\n  toggleExpandedSection: (sectionId: string) => void;\r\n}\r\n\r\nexport const useInspectorStore = create<InspectorState>()(\r\n  persist(\r\n    (set) => ({\r\n      // Default UI state\r\n      activeTab: \"settings\",\r\n      showValidation: false,\r\n      validationErrors: {},\r\n      \r\n      // Default preferences\r\n      preferences: {\r\n        defaultTab: \"settings\",\r\n        expandedSections: [],\r\n      },\r\n      \r\n      // Actions\r\n      setActiveTab: (tab) => set({ activeTab: tab }),\r\n      setShowValidation: (show) => set({ showValidation: show }),\r\n      setValidationError: (inputName, error) => \r\n        set((state) => ({\r\n          validationErrors: {\r\n            ...state.validationErrors,\r\n            [inputName]: error,\r\n          },\r\n        })),\r\n      clearValidationErrors: () => set({ validationErrors: {}, showValidation: false }),\r\n      setPreference: (key, value) => \r\n        set((state) => ({\r\n          preferences: {\r\n            ...state.preferences,\r\n            [key]: value,\r\n          },\r\n        })),\r\n      toggleExpandedSection: (sectionId) =>\r\n        set((state) => {\r\n          const expandedSections = [...state.preferences.expandedSections];\r\n          const index = expandedSections.indexOf(sectionId);\r\n          \r\n          if (index === -1) {\r\n            expandedSections.push(sectionId);\r\n          } else {\r\n            expandedSections.splice(index, 1);\r\n          }\r\n          \r\n          return {\r\n            preferences: {\r\n              ...state.preferences,\r\n              expandedSections,\r\n            },\r\n          };\r\n        }),\r\n    }),\r\n    {\r\n      name: \"inspector-preferences\",\r\n      partialize: (state) => ({ preferences: state.preferences }),\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA4BO,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACpC,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,mBAAmB;QACnB,WAAW;QACX,gBAAgB;QAChB,kBAAkB,CAAC;QAEnB,sBAAsB;QACtB,aAAa;YACX,YAAY;YACZ,kBAAkB,EAAE;QACtB;QAEA,UAAU;QACV,cAAc,CAAC,MAAQ,IAAI;gBAAE,WAAW;YAAI;QAC5C,mBAAmB,CAAC,OAAS,IAAI;gBAAE,gBAAgB;YAAK;QACxD,oBAAoB,CAAC,WAAW,QAC9B,IAAI,CAAC,QAAU,CAAC;oBACd,kBAAkB;wBAChB,GAAG,MAAM,gBAAgB;wBACzB,CAAC,UAAU,EAAE;oBACf;gBACF,CAAC;QACH,uBAAuB,IAAM,IAAI;gBAAE,kBAAkB,CAAC;gBAAG,gBAAgB;YAAM;QAC/E,eAAe,CAAC,KAAK,QACnB,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,CAAC,IAAI,EAAE;oBACT;gBACF,CAAC;QACH,uBAAuB,CAAC,YACtB,IAAI,CAAC;gBACH,MAAM,mBAAmB;uBAAI,MAAM,WAAW,CAAC,gBAAgB;iBAAC;gBAChE,MAAM,QAAQ,iBAAiB,OAAO,CAAC;gBAEvC,IAAI,UAAU,CAAC,GAAG;oBAChB,iBAAiB,IAAI,CAAC;gBACxB,OAAO;oBACL,iBAAiB,MAAM,CAAC,OAAO;gBACjC;gBAEA,OAAO;oBACL,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB;oBACF;gBACF;YACF;IACJ,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YAAE,aAAa,MAAM,WAAW;QAAC,CAAC;AAC5D", "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/hooks/useWorkflowValidation.ts"], "sourcesContent": ["import { useCallback } from \"react\";\r\nimport { Node, Edge, useReactFlow, useStore } from \"reactflow\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { useValidationStore } from \"@/store/validationStore\";\r\nimport { WorkflowValidationOptions } from \"@/lib/validation/types\";\r\nimport { debounce } from \"@/lib/utils\";\r\n\r\n/**\r\n * Hook for validating workflows\r\n */\r\nexport function useWorkflowValidation() {\r\n  const {\r\n    isValid,\r\n    errors,\r\n    warnings,\r\n    infos,\r\n    missingFields,\r\n    hasValidated,\r\n    isValidating,\r\n    validateWorkflow,\r\n    validateWorkflowSmart,\r\n    validateBeforeSave,\r\n    validateBeforeExecution,\r\n    validateDuringEditing,\r\n    clearValidation,\r\n  } = useValidationStore();\r\n\r\n  const { getNodes, getEdges } = useReactFlow<WorkflowNodeData>();\r\n\r\n  /**\r\n   * Validate the current workflow\r\n   */\r\n  const validateCurrentWorkflow = useCallback(\r\n    async (options?: WorkflowValidationOptions) => {\r\n      const nodes = getNodes();\r\n      const edges = getEdges();\r\n      return await validateWorkflowSmart(nodes, edges, options);\r\n    },\r\n    [getNodes, getEdges, validateWorkflowSmart]\r\n  );\r\n\r\n  /**\r\n   * Validate the current workflow before saving\r\n   */\r\n  const validateCurrentWorkflowBeforeSave = useCallback(\r\n    async () => {\r\n      const nodes = getNodes();\r\n      const edges = getEdges();\r\n      return await validateBeforeSave(nodes, edges);\r\n    },\r\n    [getNodes, getEdges, validateBeforeSave]\r\n  );\r\n\r\n  /**\r\n   * Validate the current workflow before execution\r\n   *\r\n   * @param providedNodes Optional nodes to use instead of getting from React Flow\r\n   * @param providedEdges Optional edges to use instead of getting from React Flow\r\n   */\r\n  const validateCurrentWorkflowBeforeExecution = useCallback(\r\n    async (providedNodes?: Node<WorkflowNodeData>[], providedEdges?: Edge[]) => {\r\n      // Use provided nodes/edges if available, otherwise get from React Flow\r\n      const nodes = providedNodes || getNodes();\r\n      const edges = providedEdges || getEdges();\r\n\r\n      console.log(`[useWorkflowValidation] Validating workflow with ${nodes.length} nodes and ${edges.length} edges`);\r\n\r\n      // If nodes array is empty, log a warning\r\n      if (nodes.length === 0) {\r\n        console.warn('[useWorkflowValidation] WARNING: Empty nodes array for validation');\r\n      }\r\n\r\n      return await validateBeforeExecution(nodes, edges);\r\n    },\r\n    [getNodes, getEdges, validateBeforeExecution]\r\n  );\r\n\r\n  /**\r\n   * Validate the current workflow during editing\r\n   */\r\n  const validateCurrentWorkflowDuringEditing = useCallback(\r\n    async () => {\r\n      const nodes = getNodes();\r\n      const edges = getEdges();\r\n      return await validateDuringEditing(nodes, edges);\r\n    },\r\n    [getNodes, getEdges, validateDuringEditing]\r\n  );\r\n\r\n  /**\r\n   * Validate a workflow with debouncing\r\n   */\r\n  const debouncedValidate = useCallback(\r\n    debounce(\r\n      async (\r\n        nodes: Node<WorkflowNodeData>[],\r\n        edges: Edge[],\r\n        options?: WorkflowValidationOptions\r\n      ) => {\r\n        await validateDuringEditing(nodes, edges);\r\n      },\r\n      500\r\n    ),\r\n    [validateDuringEditing]\r\n  );\r\n\r\n  /**\r\n   * Get validation errors for a specific node\r\n   */\r\n  const getNodeErrors = useCallback(\r\n    (nodeId: string) => {\r\n      return errors.filter((error) => error.nodeId === nodeId);\r\n    },\r\n    [errors]\r\n  );\r\n\r\n  /**\r\n   * Get validation errors for a specific field\r\n   */\r\n  const getFieldErrors = useCallback(\r\n    (nodeId: string, fieldId: string) => {\r\n      return errors.filter(\r\n        (error) => error.nodeId === nodeId && error.fieldId === fieldId\r\n      );\r\n    },\r\n    [errors]\r\n  );\r\n\r\n  /**\r\n   * Check if a node has any validation errors\r\n   */\r\n  const hasNodeErrors = useCallback(\r\n    (nodeId: string) => {\r\n      return errors.some((error) => error.nodeId === nodeId);\r\n    },\r\n    [errors]\r\n  );\r\n\r\n  /**\r\n   * Check if a field has any validation errors\r\n   */\r\n  const hasFieldErrors = useCallback(\r\n    (nodeId: string, fieldId: string) => {\r\n      return errors.some(\r\n        (error) => error.nodeId === nodeId && error.fieldId === fieldId\r\n      );\r\n    },\r\n    [errors]\r\n  );\r\n\r\n  /**\r\n   * Get missing fields for a specific node\r\n   */\r\n  const getNodeMissingFields = useCallback(\r\n    (nodeId: string) => {\r\n      return missingFields.filter((field) => field.nodeId === nodeId);\r\n    },\r\n    [missingFields]\r\n  );\r\n\r\n  return {\r\n    // State\r\n    isValid,\r\n    errors,\r\n    warnings,\r\n    infos,\r\n    missingFields,\r\n    hasValidated,\r\n    isValidating,\r\n\r\n    // Actions\r\n    validateWorkflow,\r\n    validateWorkflowSmart,\r\n    validateCurrentWorkflow,\r\n    validateBeforeSave,\r\n    validateBeforeExecution,\r\n    validateDuringEditing,\r\n    validateCurrentWorkflowBeforeSave,\r\n    validateCurrentWorkflowBeforeExecution,\r\n    validateCurrentWorkflowDuringEditing,\r\n    debouncedValidate,\r\n    clearValidation,\r\n\r\n    // Helpers\r\n    getNodeErrors,\r\n    getFieldErrors,\r\n    hasNodeErrors,\r\n    hasFieldErrors,\r\n    getNodeMissingFields,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AAEA;;;;;AAKO,SAAS;IACd,MAAM,EACJ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,KAAK,EACL,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,qBAAqB,EACrB,kBAAkB,EAClB,uBAAuB,EACvB,qBAAqB,EACrB,eAAe,EAChB,GAAG,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD;IAErB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD;IAE1C;;GAEC,GACD,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxC,OAAO;QACL,MAAM,QAAQ;QACd,MAAM,QAAQ;QACd,OAAO,MAAM,sBAAsB,OAAO,OAAO;IACnD,GACA;QAAC;QAAU;QAAU;KAAsB;IAG7C;;GAEC,GACD,MAAM,oCAAoC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClD;QACE,MAAM,QAAQ;QACd,MAAM,QAAQ;QACd,OAAO,MAAM,mBAAmB,OAAO;IACzC,GACA;QAAC;QAAU;QAAU;KAAmB;IAG1C;;;;;GAKC,GACD,MAAM,yCAAyC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvD,OAAO,eAA0C;QAC/C,uEAAuE;QACvE,MAAM,QAAQ,iBAAiB;QAC/B,MAAM,QAAQ,iBAAiB;QAE/B,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,MAAM,MAAM,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;QAE9G,yCAAyC;QACzC,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,QAAQ,IAAI,CAAC;QACf;QAEA,OAAO,MAAM,wBAAwB,OAAO;IAC9C,GACA;QAAC;QAAU;QAAU;KAAwB;IAG/C;;GAEC,GACD,MAAM,uCAAuC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrD;QACE,MAAM,QAAQ;QACd,MAAM,QAAQ;QACd,OAAO,MAAM,sBAAsB,OAAO;IAC5C,GACA;QAAC;QAAU;QAAU;KAAsB;IAG7C;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EACL,OACE,OACA,OACA;QAEA,MAAM,sBAAsB,OAAO;IACrC,GACA,MAEF;QAAC;KAAsB;IAGzB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK;IACnD,GACA;QAAC;KAAO;IAGV;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC/B,CAAC,QAAgB;QACf,OAAO,OAAO,MAAM,CAClB,CAAC,QAAU,MAAM,MAAM,KAAK,UAAU,MAAM,OAAO,KAAK;IAE5D,GACA;QAAC;KAAO;IAGV;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,OAAO,OAAO,IAAI,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK;IACjD,GACA;QAAC;KAAO;IAGV;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC/B,CAAC,QAAgB;QACf,OAAO,OAAO,IAAI,CAChB,CAAC,QAAU,MAAM,MAAM,KAAK,UAAU,MAAM,OAAO,KAAK;IAE5D,GACA;QAAC;KAAO;IAGV;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC;QACC,OAAO,cAAc,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK;IAC1D,GACA;QAAC;KAAc;IAGjB,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/hooks/useConnectedHandles.ts"], "sourcesContent": ["import { useCallback, useEffect, useState } from \"react\";\r\nimport { Edge, Node } from \"reactflow\";\r\nimport { InputDefinition, WorkflowNodeData } from \"@/types\";\r\n\r\ninterface ConnectedHandlesResult {\r\n  connectedInputs: Record<string, boolean>;\r\n  isInputConnected: (inputName: string) => boolean;\r\n  shouldDisableInput: (inputName: string) => boolean;\r\n  getConnectedSource: (inputName: string) => { nodeId: string; handleId: string } | null;\r\n  getConnectionInfo: (inputName: string) => {\r\n    isConnected: boolean;\r\n    sourceNodeId?: string;\r\n    sourceNodeType?: string;\r\n    sourceNodeLabel?: string;\r\n  };\r\n}\r\n\r\n/**\r\n * Custom hook to track which handles of a node are connected\r\n * @param selectedNode The currently selected node\r\n * @param edges All edges in the workflow\r\n * @param nodes All nodes in the workflow\r\n * @returns An object with the connected input handles and helper functions\r\n */\r\nexport function useConnectedHandles(\r\n  selectedNode: Node<WorkflowNodeData> | null,\r\n  edges: Edge[],\r\n  nodes: Node<WorkflowNodeData>[],\r\n): ConnectedHandlesResult {\r\n  // State to track connected input handles\r\n  const [connectedInputs, setConnectedInputs] = useState<Record<string, boolean>>({});\r\n  // State to track connection sources\r\n  const [connectionSources, setConnectionSources] = useState<\r\n    Record<string, { nodeId: string; handleId: string }>\r\n  >({});\r\n\r\n  // Update connected inputs whenever the selected node or edges change\r\n  useEffect(() => {\r\n    if (!selectedNode) {\r\n      setConnectedInputs({});\r\n      setConnectionSources({});\r\n      return;\r\n    }\r\n\r\n    // Find all edges that target the selected node\r\n    const nodeInputEdges = edges.filter((edge) => edge.target === selectedNode.id);\r\n\r\n    // Create a map of input handle names to connection status\r\n    const connectedHandles: Record<string, boolean> = {};\r\n    const sources: Record<string, { nodeId: string; handleId: string }> = {};\r\n\r\n    // Mark all handles as disconnected initially\r\n    if (selectedNode.data.definition?.inputs) {\r\n      selectedNode.data.definition.inputs.forEach((input) => {\r\n        if (input.is_handle) {\r\n          connectedHandles[input.name] = false;\r\n        }\r\n      });\r\n\r\n      // Also check for dynamic inputs in the config\r\n      if (selectedNode.data.config?.inputs) {\r\n        selectedNode.data.config.inputs.forEach((input: InputDefinition) => {\r\n          if (input.is_handle) {\r\n            connectedHandles[input.name] = false;\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    // Mark connected handles and store connection sources\r\n    nodeInputEdges.forEach((edge) => {\r\n      if (edge.targetHandle) {\r\n        connectedHandles[edge.targetHandle] = true;\r\n        if (edge.source && edge.sourceHandle) {\r\n          sources[edge.targetHandle] = {\r\n            nodeId: edge.source,\r\n            handleId: edge.sourceHandle,\r\n          };\r\n        }\r\n      }\r\n    });\r\n\r\n    setConnectedInputs(connectedHandles);\r\n    setConnectionSources(sources);\r\n  }, [selectedNode, edges]);\r\n\r\n  // Helper function to check if a specific input is connected\r\n  const isInputConnected = useCallback(\r\n    (inputName: string) => {\r\n      return !!connectedInputs[inputName];\r\n    },\r\n    [connectedInputs],\r\n  );\r\n\r\n  // Helper function to check if an input should be disabled\r\n  // This handles both direct inputs and handle inputs\r\n  const shouldDisableInput = useCallback(\r\n    (inputName: string) => {\r\n      // If this is a handle input, it should never be disabled\r\n      if (inputName.endsWith(\"_handle\")) {\r\n        return false;\r\n      }\r\n\r\n      // For inputs with is_handle=true, check if they're connected\r\n      if (isInputConnected(inputName)) {\r\n        return true;\r\n      }\r\n\r\n      // Check if there's a corresponding handle input\r\n      const handleName = `${inputName}_handle`;\r\n\r\n      // If the handle is connected, disable the direct input\r\n      return isInputConnected(handleName);\r\n    },\r\n    [connectedInputs, isInputConnected],\r\n  );\r\n\r\n  // Helper function to get the source node and handle for a connected input\r\n  const getConnectedSource = useCallback(\r\n    (inputName: string) => {\r\n      return connectionSources[inputName] || null;\r\n    },\r\n    [connectionSources],\r\n  );\r\n\r\n  // Helper function to get detailed connection information\r\n  const getConnectionInfo = useCallback(\r\n    (inputName: string) => {\r\n      const isConnected = isInputConnected(inputName);\r\n      if (!isConnected) {\r\n        return { isConnected: false };\r\n      }\r\n\r\n      const source = getConnectedSource(inputName);\r\n      if (!source) {\r\n        return { isConnected: true };\r\n      }\r\n\r\n      // Find the source node\r\n      const sourceNode = nodes.find((node) => node.id === source.nodeId);\r\n      if (!sourceNode) {\r\n        return { isConnected: true, sourceNodeId: source.nodeId };\r\n      }\r\n\r\n      return {\r\n        isConnected: true,\r\n        sourceNodeId: source.nodeId,\r\n        sourceNodeType: sourceNode.data.type,\r\n        sourceNodeLabel: sourceNode.data.label || sourceNode.data.type,\r\n      };\r\n    },\r\n    [isInputConnected, getConnectedSource, nodes],\r\n  );\r\n\r\n  return {\r\n    connectedInputs,\r\n    isInputConnected,\r\n    shouldDisableInput,\r\n    getConnectedSource,\r\n    getConnectionInfo,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAwBO,SAAS,oBACd,YAA2C,EAC3C,KAAa,EACb,KAA+B;IAE/B,yCAAyC;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACjF,oCAAoC;IACpC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAEvD,CAAC;IAEH,qEAAqE;IACrE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,cAAc;YACjB,mBAAmB,CAAC;YACpB,qBAAqB,CAAC;YACtB;QACF;QAEA,+CAA+C;QAC/C,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,aAAa,EAAE;QAE7E,0DAA0D;QAC1D,MAAM,mBAA4C,CAAC;QACnD,MAAM,UAAgE,CAAC;QAEvE,6CAA6C;QAC7C,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE,QAAQ;YACxC,aAAa,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC3C,IAAI,MAAM,SAAS,EAAE;oBACnB,gBAAgB,CAAC,MAAM,IAAI,CAAC,GAAG;gBACjC;YACF;YAEA,8CAA8C;YAC9C,IAAI,aAAa,IAAI,CAAC,MAAM,EAAE,QAAQ;gBACpC,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACvC,IAAI,MAAM,SAAS,EAAE;wBACnB,gBAAgB,CAAC,MAAM,IAAI,CAAC,GAAG;oBACjC;gBACF;YACF;QACF;QAEA,sDAAsD;QACtD,eAAe,OAAO,CAAC,CAAC;YACtB,IAAI,KAAK,YAAY,EAAE;gBACrB,gBAAgB,CAAC,KAAK,YAAY,CAAC,GAAG;gBACtC,IAAI,KAAK,MAAM,IAAI,KAAK,YAAY,EAAE;oBACpC,OAAO,CAAC,KAAK,YAAY,CAAC,GAAG;wBAC3B,QAAQ,KAAK,MAAM;wBACnB,UAAU,KAAK,YAAY;oBAC7B;gBACF;YACF;QACF;QAEA,mBAAmB;QACnB,qBAAqB;IACvB,GAAG;QAAC;QAAc;KAAM;IAExB,4DAA4D;IAC5D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,CAAC;QACC,OAAO,CAAC,CAAC,eAAe,CAAC,UAAU;IACrC,GACA;QAAC;KAAgB;IAGnB,0DAA0D;IAC1D,oDAAoD;IACpD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC;QACC,yDAAyD;QACzD,IAAI,UAAU,QAAQ,CAAC,YAAY;YACjC,OAAO;QACT;QAEA,6DAA6D;QAC7D,IAAI,iBAAiB,YAAY;YAC/B,OAAO;QACT;QAEA,gDAAgD;QAChD,MAAM,aAAa,GAAG,UAAU,OAAO,CAAC;QAExC,uDAAuD;QACvD,OAAO,iBAAiB;IAC1B,GACA;QAAC;QAAiB;KAAiB;IAGrC,0EAA0E;IAC1E,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC;QACC,OAAO,iBAAiB,CAAC,UAAU,IAAI;IACzC,GACA;QAAC;KAAkB;IAGrB,yDAAyD;IACzD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,MAAM,cAAc,iBAAiB;QACrC,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,aAAa;YAAM;QAC9B;QAEA,MAAM,SAAS,mBAAmB;QAClC,IAAI,CAAC,QAAQ;YACX,OAAO;gBAAE,aAAa;YAAK;QAC7B;QAEA,uBAAuB;QACvB,MAAM,aAAa,MAAM,IAAI,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK,OAAO,MAAM;QACjE,IAAI,CAAC,YAAY;YACf,OAAO;gBAAE,aAAa;gBAAM,cAAc,OAAO,MAAM;YAAC;QAC1D;QAEA,OAAO;YACL,aAAa;YACb,cAAc,OAAO,MAAM;YAC3B,gBAAgB,WAAW,IAAI,CAAC,IAAI;YACpC,iBAAiB,WAAW,IAAI,CAAC,KAAK,IAAI,WAAW,IAAI,CAAC,IAAI;QAChE;IACF,GACA;QAAC;QAAkB;QAAoB;KAAM;IAG/C,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/utils/visibility-rules.ts"], "sourcesContent": ["import { InputDefinition, InputVisibilityRule } from \"@/types\";\r\n\r\n/**\r\n * Evaluates a single visibility rule against the current configuration\r\n *\r\n * @param rule The visibility rule to evaluate\r\n * @param config The current component configuration\r\n * @returns True if the rule passes, false otherwise\r\n */\r\nexport function evaluateVisibilityRule(\r\n  rule: InputVisibilityRule,\r\n  config: Record<string, any>,\r\n): boolean {\r\n  // Get the target field value from config\r\n  const targetValue = config?.[rule.field_name];\r\n\r\n  // Simple equality check\r\n  if (rule.operator === undefined || rule.operator === \"equals\") {\r\n    return targetValue === rule.field_value;\r\n  }\r\n\r\n  // Not equals\r\n  if (rule.operator === \"not_equals\") {\r\n    return targetValue !== rule.field_value;\r\n  }\r\n\r\n  // Contains (for arrays and strings)\r\n  if (rule.operator === \"contains\") {\r\n    if (Array.isArray(targetValue)) {\r\n      return targetValue.includes(rule.field_value);\r\n    }\r\n    if (typeof targetValue === \"string\") {\r\n      return targetValue.includes(String(rule.field_value));\r\n    }\r\n    return false;\r\n  }\r\n\r\n  // Greater than (for numbers)\r\n  if (rule.operator === \"greater_than\") {\r\n    return (\r\n      typeof targetValue === \"number\" &&\r\n      typeof rule.field_value === \"number\" &&\r\n      targetValue > rule.field_value\r\n    );\r\n  }\r\n\r\n  // Less than (for numbers)\r\n  if (rule.operator === \"less_than\") {\r\n    return (\r\n      typeof targetValue === \"number\" &&\r\n      typeof rule.field_value === \"number\" &&\r\n      targetValue < rule.field_value\r\n    );\r\n  }\r\n\r\n  // Exists (field exists and is not null/undefined)\r\n  if (rule.operator === \"exists\") {\r\n    return targetValue !== undefined && targetValue !== null;\r\n  }\r\n\r\n  // Not exists (field doesn't exist or is null/undefined)\r\n  if (rule.operator === \"not_exists\") {\r\n    return targetValue === undefined || targetValue === null;\r\n  }\r\n\r\n  // Default to false for unknown operators\r\n  return false;\r\n}\r\n\r\n/**\r\n * Evaluates a set of visibility rules against the current configuration\r\n *\r\n * @param rules The visibility rules to evaluate\r\n * @param config The current component configuration\r\n * @param logicOperator The logic operator to use when combining rules (default: 'OR')\r\n * @returns True if the rules pass, false otherwise\r\n */\r\nexport function evaluateVisibilityRules(\r\n  rules: InputVisibilityRule[],\r\n  config: Record<string, any>,\r\n  logicOperator: \"AND\" | \"OR\" = \"OR\",\r\n): boolean {\r\n  // If no rules, always show\r\n  if (!rules || rules.length === 0) {\r\n    return true;\r\n  }\r\n\r\n  // Evaluate each rule\r\n  const results = rules.map((rule) => evaluateVisibilityRule(rule, config));\r\n\r\n  // Combine results based on logic operator\r\n  if (logicOperator === \"AND\") {\r\n    return results.every((result) => result);\r\n  } else {\r\n    return results.some((result) => result);\r\n  }\r\n}\r\n\r\n/**\r\n * Determines if an input should be visible based on its visibility rules\r\n *\r\n * @param input The input definition\r\n * @param config The current component configuration\r\n * @returns True if the input should be visible, false otherwise\r\n */\r\nexport function shouldShowInput(input: InputDefinition, config: Record<string, any>): boolean {\r\n  // If no visibility rules, always show\r\n  if (!input.visibility_rules || input.visibility_rules.length === 0) {\r\n    return true;\r\n  }\r\n\r\n  // Get the logic operator from the input definition or default to 'OR'\r\n  const logicOperator = input.visibility_logic || \"OR\";\r\n\r\n  // Evaluate the visibility rules\r\n  return evaluateVisibilityRules(input.visibility_rules, config, logicOperator as \"AND\" | \"OR\");\r\n}\r\n\r\n/**\r\n * Filters a list of inputs based on visibility rules\r\n *\r\n * @param inputs The list of input definitions\r\n * @param config The current component configuration\r\n * @returns A filtered list of inputs that should be visible\r\n */\r\nexport function filterVisibleInputs(\r\n  inputs: InputDefinition[],\r\n  config: Record<string, any>,\r\n): InputDefinition[] {\r\n  return inputs.filter((input) => shouldShowInput(input, config));\r\n}\r\n"], "names": [], "mappings": ";;;;;;AASO,SAAS,uBACd,IAAyB,EACzB,MAA2B;IAE3B,yCAAyC;IACzC,MAAM,cAAc,QAAQ,CAAC,KAAK,UAAU,CAAC;IAE7C,wBAAwB;IACxB,IAAI,KAAK,QAAQ,KAAK,aAAa,KAAK,QAAQ,KAAK,UAAU;QAC7D,OAAO,gBAAgB,KAAK,WAAW;IACzC;IAEA,aAAa;IACb,IAAI,KAAK,QAAQ,KAAK,cAAc;QAClC,OAAO,gBAAgB,KAAK,WAAW;IACzC;IAEA,oCAAoC;IACpC,IAAI,KAAK,QAAQ,KAAK,YAAY;QAChC,IAAI,MAAM,OAAO,CAAC,cAAc;YAC9B,OAAO,YAAY,QAAQ,CAAC,KAAK,WAAW;QAC9C;QACA,IAAI,OAAO,gBAAgB,UAAU;YACnC,OAAO,YAAY,QAAQ,CAAC,OAAO,KAAK,WAAW;QACrD;QACA,OAAO;IACT;IAEA,6BAA6B;IAC7B,IAAI,KAAK,QAAQ,KAAK,gBAAgB;QACpC,OACE,OAAO,gBAAgB,YACvB,OAAO,KAAK,WAAW,KAAK,YAC5B,cAAc,KAAK,WAAW;IAElC;IAEA,0BAA0B;IAC1B,IAAI,KAAK,QAAQ,KAAK,aAAa;QACjC,OACE,OAAO,gBAAgB,YACvB,OAAO,KAAK,WAAW,KAAK,YAC5B,cAAc,KAAK,WAAW;IAElC;IAEA,kDAAkD;IAClD,IAAI,KAAK,QAAQ,KAAK,UAAU;QAC9B,OAAO,gBAAgB,aAAa,gBAAgB;IACtD;IAEA,wDAAwD;IACxD,IAAI,KAAK,QAAQ,KAAK,cAAc;QAClC,OAAO,gBAAgB,aAAa,gBAAgB;IACtD;IAEA,yCAAyC;IACzC,OAAO;AACT;AAUO,SAAS,wBACd,KAA4B,EAC5B,MAA2B,EAC3B,gBAA8B,IAAI;IAElC,2BAA2B;IAC3B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAChC,OAAO;IACT;IAEA,qBAAqB;IACrB,MAAM,UAAU,MAAM,GAAG,CAAC,CAAC,OAAS,uBAAuB,MAAM;IAEjE,0CAA0C;IAC1C,IAAI,kBAAkB,OAAO;QAC3B,OAAO,QAAQ,KAAK,CAAC,CAAC,SAAW;IACnC,OAAO;QACL,OAAO,QAAQ,IAAI,CAAC,CAAC,SAAW;IAClC;AACF;AASO,SAAS,gBAAgB,KAAsB,EAAE,MAA2B;IACjF,sCAAsC;IACtC,IAAI,CAAC,MAAM,gBAAgB,IAAI,MAAM,gBAAgB,CAAC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,sEAAsE;IACtE,MAAM,gBAAgB,MAAM,gBAAgB,IAAI;IAEhD,gCAAgC;IAChC,OAAO,wBAAwB,MAAM,gBAAgB,EAAE,QAAQ;AACjE;AASO,SAAS,oBACd,MAAyB,EACzB,MAA2B;IAE3B,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,gBAAgB,OAAO;AACzD", "debugId": null}}, {"offset": {"line": 866, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/utils/inputVisibility.ts"], "sourcesContent": ["import { InputDefinition, WorkflowNodeData } from \"@/types\";\r\nimport { Node } from \"reactflow\";\r\nimport { shouldShowInput } from \"@/utils/visibility-rules\";\r\n\r\n/**\r\n * Determines if an input should be visible based on node type and configuration\r\n * @param inputDef The input definition\r\n * @param node The workflow node\r\n * @param config The current configuration\r\n * @returns True if the input should be visible, false otherwise\r\n */\r\nexport function checkInputVisibility(\r\n  inputDef: InputDefinition,\r\n  node: Node<WorkflowNodeData> | null,\r\n  config: Record<string, any>\r\n): boolean {\r\n  if (!node) return false;\r\n\r\n  // Special handling for DynamicCombineTextComponent\r\n  if (\r\n    node.data.type === \"DynamicCombineTextComponent\" &&\r\n    inputDef.name.startsWith(\"input_\") &&\r\n    !inputDef.is_handle\r\n  ) {\r\n    // Extract the index from the input name (e.g., \"input_3\" -> 3)\r\n    const match = inputDef.name.match(/input_(\\d+)/);\r\n    if (match && match[1]) {\r\n      const inputIndex = parseInt(match[1], 10);\r\n      const numAdditionalInputs = parseInt(config.num_additional_inputs || \"0\", 10);\r\n\r\n      // Show the input if its index is less than or equal to the number of additional inputs\r\n      return inputIndex <= numAdditionalInputs;\r\n    }\r\n  }\r\n\r\n  // Special handling for ConditionalNode\r\n  if (node.data.originalType === \"ConditionalNode\") {\r\n    // Handle dynamic condition inputs (condition_3_*, condition_4_*, etc.)\r\n    const conditionMatch = inputDef.name.match(/condition_(\\d+)_/);\r\n    if (conditionMatch && conditionMatch[1]) {\r\n      const conditionIndex = parseInt(conditionMatch[1], 10);\r\n      const numAdditionalConditions = parseInt(config.num_additional_conditions || \"0\", 10);\r\n      const totalConditions = 2 + numAdditionalConditions; // Base 2 + additional\r\n\r\n      // For conditions 3 and above, check if they should be visible\r\n      if (conditionIndex > 2) {\r\n        // Show if condition index is within total conditions\r\n        if (conditionIndex > totalConditions) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // For input handles, also check the source setting\r\n      if (inputDef.name.endsWith(\"_input_handle\")) {\r\n        const sourceValue = config[`condition_${conditionIndex}_source`];\r\n        return sourceValue === \"node_output\";\r\n      }\r\n    }\r\n  }\r\n\r\n  // Special handling for MCP Marketplace components\r\n  if (isMCPMarketplaceComponent(node)) {\r\n    return checkMCPMarketplaceInputVisibility(inputDef, node, config);\r\n  }\r\n\r\n  // Special handling for MCP Tools component\r\n  if (node.data.type === \"MCPToolsComponent\") {\r\n    return checkMCPToolsInputVisibility(inputDef, config);\r\n  }\r\n\r\n  // Use the utility function for standard visibility rules\r\n  return shouldShowInput(inputDef, config);\r\n}\r\n\r\n/**\r\n * Checks if a node is an MCP Marketplace component\r\n */\r\nfunction isMCPMarketplaceComponent(node: Node<WorkflowNodeData> | null): boolean {\r\n  if (!node) return false;\r\n  return node.data.type === \"MCPMarketplaceComponent\" ||\r\n         (node.data.definition?.category === \"MCP Marketplace\");\r\n}\r\n\r\n/**\r\n * Checks visibility for MCP Marketplace component inputs\r\n */\r\nfunction checkMCPMarketplaceInputVisibility(\r\n  inputDef: InputDefinition,\r\n  node: Node<WorkflowNodeData>,\r\n  config: Record<string, any>\r\n): boolean {\r\n  // For explicit handle inputs (ending with _handle), always show them\r\n  if (inputDef.input_type === \"handle\" || inputDef.name.endsWith(\"_handle\")) {\r\n    // Always show explicit handle inputs\r\n    return true;\r\n  }\r\n\r\n  // Hide connection fields that have a direct input equivalent\r\n  if (inputDef.name.endsWith(\"_connection\")) {\r\n    // Check if there's a direct input with the same base name\r\n    const baseName = inputDef.name.replace(\"_connection\", \"\");\r\n    const hasDirectInput =\r\n      node.data?.definition?.inputs?.some((input) => input.name === baseName) || false;\r\n    if (hasDirectInput) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // For inputs with is_handle=true, always show them\r\n  if (inputDef.is_handle) {\r\n    return true;\r\n  }\r\n\r\n  // For regular inputs, check if there's a corresponding handle\r\n  if (hasCorrespondingHandle(inputDef.name, node)) {\r\n    // If the handle is connected, hide the direct input\r\n    // This would require the isInputConnected function from useConnectedHandles\r\n    // For now, we'll return true and handle this in the component\r\n    return true;\r\n  }\r\n\r\n  // Default to showing the input\r\n  return true;\r\n}\r\n\r\n/**\r\n * Checks if an input has a corresponding handle\r\n */\r\nfunction hasCorrespondingHandle(inputName: string, node: Node<WorkflowNodeData>): boolean {\r\n  if (!node?.data?.definition?.inputs) return false;\r\n\r\n  // Check for handle with a suffix pattern (e.g., input_dict_handle for input_dict)\r\n  const handleSuffix = node.data.definition.inputs.find(\r\n    (input) =>\r\n      (input.is_handle || input.input_type === \"handle\") && input.name === `${inputName}_handle`,\r\n  );\r\n\r\n  return !!handleSuffix;\r\n}\r\n\r\n/**\r\n * Checks visibility for MCP Tools component inputs\r\n */\r\nfunction checkMCPToolsInputVisibility(\r\n  inputDef: InputDefinition,\r\n  config: Record<string, any>\r\n): boolean {\r\n  // For selected_tool_name, check connection_status\r\n  if (inputDef.name === \"selected_tool_name\") {\r\n    // Check connection status\r\n    const connectionStatus = config.connection_status || \"Not Connected\";\r\n\r\n    // Show if connected\r\n    return connectionStatus === \"Connected\";\r\n  }\r\n\r\n  // For refresh_tools and disconnect buttons, always hide them\r\n  if (inputDef.name === \"refresh_tools\" || inputDef.name === \"disconnect\") {\r\n    return false; // Always hide these buttons\r\n  }\r\n\r\n  // Use the utility function for standard visibility rules\r\n  return shouldShowInput(inputDef, config);\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;AASO,SAAS,qBACd,QAAyB,EACzB,IAAmC,EACnC,MAA2B;IAE3B,IAAI,CAAC,MAAM,OAAO;IAElB,mDAAmD;IACnD,IACE,KAAK,IAAI,CAAC,IAAI,KAAK,iCACnB,SAAS,IAAI,CAAC,UAAU,CAAC,aACzB,CAAC,SAAS,SAAS,EACnB;QACA,+DAA+D;QAC/D,MAAM,QAAQ,SAAS,IAAI,CAAC,KAAK,CAAC;QAClC,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,MAAM,aAAa,SAAS,KAAK,CAAC,EAAE,EAAE;YACtC,MAAM,sBAAsB,SAAS,OAAO,qBAAqB,IAAI,KAAK;YAE1E,uFAAuF;YACvF,OAAO,cAAc;QACvB;IACF;IAEA,uCAAuC;IACvC,IAAI,KAAK,IAAI,CAAC,YAAY,KAAK,mBAAmB;QAChD,uEAAuE;QACvE,MAAM,iBAAiB,SAAS,IAAI,CAAC,KAAK,CAAC;QAC3C,IAAI,kBAAkB,cAAc,CAAC,EAAE,EAAE;YACvC,MAAM,iBAAiB,SAAS,cAAc,CAAC,EAAE,EAAE;YACnD,MAAM,0BAA0B,SAAS,OAAO,yBAAyB,IAAI,KAAK;YAClF,MAAM,kBAAkB,IAAI,yBAAyB,sBAAsB;YAE3E,8DAA8D;YAC9D,IAAI,iBAAiB,GAAG;gBACtB,qDAAqD;gBACrD,IAAI,iBAAiB,iBAAiB;oBACpC,OAAO;gBACT;YACF;YAEA,mDAAmD;YACnD,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,kBAAkB;gBAC3C,MAAM,cAAc,MAAM,CAAC,CAAC,UAAU,EAAE,eAAe,OAAO,CAAC,CAAC;gBAChE,OAAO,gBAAgB;YACzB;QACF;IACF;IAEA,kDAAkD;IAClD,IAAI,0BAA0B,OAAO;QACnC,OAAO,mCAAmC,UAAU,MAAM;IAC5D;IAEA,2CAA2C;IAC3C,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,qBAAqB;QAC1C,OAAO,6BAA6B,UAAU;IAChD;IAEA,yDAAyD;IACzD,OAAO,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE,UAAU;AACnC;AAEA;;CAEC,GACD,SAAS,0BAA0B,IAAmC;IACpE,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,6BAClB,KAAK,IAAI,CAAC,UAAU,EAAE,aAAa;AAC7C;AAEA;;CAEC,GACD,SAAS,mCACP,QAAyB,EACzB,IAA4B,EAC5B,MAA2B;IAE3B,qEAAqE;IACrE,IAAI,SAAS,UAAU,KAAK,YAAY,SAAS,IAAI,CAAC,QAAQ,CAAC,YAAY;QACzE,qCAAqC;QACrC,OAAO;IACT;IAEA,6DAA6D;IAC7D,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,gBAAgB;QACzC,0DAA0D;QAC1D,MAAM,WAAW,SAAS,IAAI,CAAC,OAAO,CAAC,eAAe;QACtD,MAAM,iBACJ,KAAK,IAAI,EAAE,YAAY,QAAQ,KAAK,CAAC,QAAU,MAAM,IAAI,KAAK,aAAa;QAC7E,IAAI,gBAAgB;YAClB,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,IAAI,SAAS,SAAS,EAAE;QACtB,OAAO;IACT;IAEA,8DAA8D;IAC9D,IAAI,uBAAuB,SAAS,IAAI,EAAE,OAAO;QAC/C,oDAAoD;QACpD,4EAA4E;QAC5E,8DAA8D;QAC9D,OAAO;IACT;IAEA,+BAA+B;IAC/B,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,uBAAuB,SAAiB,EAAE,IAA4B;IAC7E,IAAI,CAAC,MAAM,MAAM,YAAY,QAAQ,OAAO;IAE5C,kFAAkF;IAClF,MAAM,eAAe,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CACnD,CAAC,QACC,CAAC,MAAM,SAAS,IAAI,MAAM,UAAU,KAAK,QAAQ,KAAK,MAAM,IAAI,KAAK,GAAG,UAAU,OAAO,CAAC;IAG9F,OAAO,CAAC,CAAC;AACX;AAEA;;CAEC,GACD,SAAS,6BACP,QAAyB,EACzB,MAA2B;IAE3B,kDAAkD;IAClD,IAAI,SAAS,IAAI,KAAK,sBAAsB;QAC1C,0BAA0B;QAC1B,MAAM,mBAAmB,OAAO,iBAAiB,IAAI;QAErD,oBAAoB;QACpB,OAAO,qBAAqB;IAC9B;IAEA,6DAA6D;IAC7D,IAAI,SAAS,IAAI,KAAK,mBAAmB,SAAS,IAAI,KAAK,cAAc;QACvE,OAAO,OAAO,4BAA4B;IAC5C;IAEA,yDAAyD;IACzD,OAAO,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE,UAAU;AACnC", "debugId": null}}, {"offset": {"line": 985, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/utils/inputValidation.ts"], "sourcesContent": ["import { InputDefinition } from \"@/types\";\r\nimport { z } from \"zod\";\r\n\r\n/**\r\n * Validates an input value based on its definition\r\n * @param inputDef The input definition\r\n * @param value The value to validate\r\n * @returns Validation result with isValid flag and message\r\n */\r\nexport function validateInput(\r\n  inputDef: InputDefinition,\r\n  value: any\r\n): { isValid: boolean; message: string } {\r\n  // Skip validation for empty optional inputs\r\n  if (!inputDef.required && (value === undefined || value === null || value === \"\")) {\r\n    return { isValid: true, message: \"\" };\r\n  }\r\n\r\n  // Validate based on input type\r\n  switch (inputDef.input_type) {\r\n    case \"string\":\r\n      return validateString(inputDef, value);\r\n    case \"int\":\r\n    case \"float\":\r\n    case \"number\":\r\n      return validateNumber(inputDef, value);\r\n    case \"list\":\r\n    case \"array\":\r\n      return validateArray(inputDef, value);\r\n    case \"dict\":\r\n    case \"json\":\r\n    case \"object\":\r\n      return validateObject(inputDef, value);\r\n    default:\r\n      // For other types, assume valid\r\n      return { isValid: true, message: \"\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Validates a string input\r\n */\r\nfunction validateString(\r\n  inputDef: InputDefinition,\r\n  value: any\r\n): { isValid: boolean; message: string } {\r\n  if (typeof value !== \"string\") {\r\n    return { isValid: false, message: \"Must be a string\" };\r\n  }\r\n\r\n  const minLength = inputDef.min_length || 0;\r\n  const maxLength = inputDef.max_length || Number.MAX_SAFE_INTEGER;\r\n\r\n  if (value.length < minLength) {\r\n    return { isValid: false, message: `Must be at least ${minLength} characters` };\r\n  }\r\n  if (value.length > maxLength) {\r\n    return { isValid: false, message: `Must be at most ${maxLength} characters` };\r\n  }\r\n\r\n  // Check pattern if specified\r\n  if (inputDef.pattern) {\r\n    try {\r\n      const regex = new RegExp(inputDef.pattern);\r\n      if (!regex.test(value)) {\r\n        return { isValid: false, message: inputDef.pattern_error || \"Invalid format\" };\r\n      }\r\n    } catch (e) {\r\n      console.error(\"Invalid regex pattern:\", inputDef.pattern);\r\n    }\r\n  }\r\n\r\n  return { isValid: true, message: \"Valid input\" };\r\n}\r\n\r\n/**\r\n * Validates a number input\r\n */\r\nfunction validateNumber(\r\n  inputDef: InputDefinition,\r\n  value: any\r\n): { isValid: boolean; message: string } {\r\n  const numValue = Number(value);\r\n  if (isNaN(numValue)) {\r\n    return { isValid: false, message: \"Must be a number\" };\r\n  }\r\n\r\n  const minValue =\r\n    inputDef.min_value !== undefined ? Number(inputDef.min_value) : Number.MIN_SAFE_INTEGER;\r\n  const maxValue =\r\n    inputDef.max_value !== undefined ? Number(inputDef.max_value) : Number.MAX_SAFE_INTEGER;\r\n\r\n  if (numValue < minValue) {\r\n    return { isValid: false, message: `Must be at least ${minValue}` };\r\n  }\r\n  if (numValue > maxValue) {\r\n    return { isValid: false, message: `Must be at most ${maxValue}` };\r\n  }\r\n  \r\n  return { isValid: true, message: \"Valid number\" };\r\n}\r\n\r\n/**\r\n * Validates an array input\r\n */\r\nfunction validateArray(\r\n  inputDef: InputDefinition,\r\n  value: any\r\n): { isValid: boolean; message: string } {\r\n  // Check if it's a list\r\n  let listValue = value;\r\n  if (typeof value === \"string\") {\r\n    try {\r\n      listValue = JSON.parse(value);\r\n    } catch (e) {\r\n      return { isValid: false, message: \"Invalid JSON format\" };\r\n    }\r\n  }\r\n\r\n  if (!Array.isArray(listValue)) {\r\n    return { isValid: false, message: \"Must be an array\" };\r\n  }\r\n\r\n  // Check min/max items\r\n  const minItems = inputDef.min_items || 0;\r\n  const maxItems = inputDef.max_items || Number.MAX_SAFE_INTEGER;\r\n\r\n  if (listValue.length < minItems) {\r\n    return { isValid: false, message: `Must have at least ${minItems} items` };\r\n  }\r\n  if (listValue.length > maxItems) {\r\n    return { isValid: false, message: `Must have at most ${maxItems} items` };\r\n  }\r\n  \r\n  return { isValid: true, message: \"Valid list\" };\r\n}\r\n\r\n/**\r\n * Validates an object input\r\n */\r\nfunction validateObject(\r\n  inputDef: InputDefinition,\r\n  value: any\r\n): { isValid: boolean; message: string } {\r\n  // Check if it's a valid JSON object\r\n  let dictValue = value;\r\n  if (typeof value === \"string\") {\r\n    try {\r\n      dictValue = JSON.parse(value);\r\n    } catch (e) {\r\n      return { isValid: false, message: \"Invalid JSON format\" };\r\n    }\r\n  }\r\n\r\n  if (typeof dictValue !== \"object\" || dictValue === null || Array.isArray(dictValue)) {\r\n    return { isValid: false, message: \"Must be an object\" };\r\n  }\r\n\r\n  // Check required keys\r\n  const requiredKeys = inputDef.required_keys || [];\r\n  for (const key of requiredKeys) {\r\n    if (!(key in dictValue)) {\r\n      return { isValid: false, message: `Missing required key: ${key}` };\r\n    }\r\n  }\r\n  \r\n  return { isValid: true, message: \"Valid object\" };\r\n}\r\n\r\n/**\r\n * Validates all inputs in a node configuration\r\n * @param inputs Array of input definitions\r\n * @param config The current configuration values\r\n * @returns Object with validation results for each input\r\n */\r\nexport function validateAllInputs(\r\n  inputs: InputDefinition[],\r\n  config: Record<string, any>\r\n): Record<string, { isValid: boolean; message: string }> {\r\n  const errors: Record<string, { isValid: boolean; message: string }> = {};\r\n\r\n  // Validate each input\r\n  inputs.forEach((inputDef) => {\r\n    // Skip handle inputs\r\n    if (inputDef.is_handle) return;\r\n\r\n    // Get current value\r\n    const value = config[inputDef.name];\r\n\r\n    // Validate\r\n    errors[inputDef.name] = validateInput(inputDef, value);\r\n  });\r\n\r\n  return errors;\r\n}\r\n\r\n/**\r\n * Creates a Zod schema for an input definition\r\n * @param inputDef The input definition\r\n * @returns A Zod schema for the input\r\n */\r\nexport function createSchemaForInput(inputDef: InputDefinition): z.ZodTypeAny {\r\n  switch (inputDef.input_type) {\r\n    case \"string\":\r\n      let schema = z.string();\r\n      \r\n      if (inputDef.required) {\r\n        schema = schema.min(1, { message: \"This field is required\" });\r\n      } else {\r\n        schema = schema.optional();\r\n      }\r\n      \r\n      if (inputDef.min_length) {\r\n        schema = schema.min(inputDef.min_length, { \r\n          message: `Must be at least ${inputDef.min_length} characters` \r\n        });\r\n      }\r\n      \r\n      if (inputDef.max_length) {\r\n        schema = schema.max(inputDef.max_length, { \r\n          message: `Must be at most ${inputDef.max_length} characters` \r\n        });\r\n      }\r\n      \r\n      if (inputDef.pattern) {\r\n        schema = schema.regex(new RegExp(inputDef.pattern), { \r\n          message: inputDef.pattern_error || \"Invalid format\" \r\n        });\r\n      }\r\n      \r\n      return schema;\r\n      \r\n    case \"int\":\r\n      let intSchema = z.coerce.number().int();\r\n      \r\n      if (!inputDef.required) {\r\n        intSchema = intSchema.optional();\r\n      }\r\n      \r\n      if (inputDef.min_value !== undefined) {\r\n        intSchema = intSchema.min(Number(inputDef.min_value), { \r\n          message: `Must be at least ${inputDef.min_value}` \r\n        });\r\n      }\r\n      \r\n      if (inputDef.max_value !== undefined) {\r\n        intSchema = intSchema.max(Number(inputDef.max_value), { \r\n          message: `Must be at most ${inputDef.max_value}` \r\n        });\r\n      }\r\n      \r\n      return intSchema;\r\n      \r\n    case \"float\":\r\n    case \"number\":\r\n      let numSchema = z.coerce.number();\r\n      \r\n      if (!inputDef.required) {\r\n        numSchema = numSchema.optional();\r\n      }\r\n      \r\n      if (inputDef.min_value !== undefined) {\r\n        numSchema = numSchema.min(Number(inputDef.min_value), { \r\n          message: `Must be at least ${inputDef.min_value}` \r\n        });\r\n      }\r\n      \r\n      if (inputDef.max_value !== undefined) {\r\n        numSchema = numSchema.max(Number(inputDef.max_value), { \r\n          message: `Must be at most ${inputDef.max_value}` \r\n        });\r\n      }\r\n      \r\n      return numSchema;\r\n      \r\n    case \"bool\":\r\n      return inputDef.required ? z.boolean() : z.boolean().optional();\r\n      \r\n    case \"list\":\r\n    case \"array\":\r\n      let arraySchema = z.array(z.any());\r\n      \r\n      if (!inputDef.required) {\r\n        arraySchema = arraySchema.optional();\r\n      }\r\n      \r\n      if (inputDef.min_items !== undefined) {\r\n        arraySchema = arraySchema.min(inputDef.min_items, { \r\n          message: `Must have at least ${inputDef.min_items} items` \r\n        });\r\n      }\r\n      \r\n      if (inputDef.max_items !== undefined) {\r\n        arraySchema = arraySchema.max(inputDef.max_items, { \r\n          message: `Must have at most ${inputDef.max_items} items` \r\n        });\r\n      }\r\n      \r\n      return arraySchema;\r\n      \r\n    case \"dict\":\r\n    case \"json\":\r\n    case \"object\":\r\n      let objSchema = z.record(z.any());\r\n      \r\n      if (!inputDef.required) {\r\n        objSchema = objSchema.optional();\r\n      }\r\n      \r\n      // Add validation for required keys if specified\r\n      if (inputDef.required_keys && inputDef.required_keys.length > 0) {\r\n        // This is a simplified approach - for more complex validation,\r\n        // we would need to create a proper object schema with all properties\r\n        objSchema = objSchema.refine(\r\n          (obj) => {\r\n            if (!obj) return false;\r\n            return inputDef.required_keys!.every((key) => key in obj);\r\n          },\r\n          {\r\n            message: `Missing required keys: ${inputDef.required_keys.join(\", \")}`,\r\n          }\r\n        );\r\n      }\r\n      \r\n      return objSchema;\r\n      \r\n    default:\r\n      // For other types, use any schema\r\n      return inputDef.required ? z.any() : z.any().optional();\r\n  }\r\n}\r\n\r\n/**\r\n * Creates a Zod schema for a complete node configuration\r\n * @param inputs Array of input definitions\r\n * @returns A Zod schema for the node configuration\r\n */\r\nexport function createNodeConfigSchema(inputs: InputDefinition[]): z.ZodObject<any> {\r\n  const shape: Record<string, z.ZodTypeAny> = {};\r\n  \r\n  inputs.forEach((input) => {\r\n    // Skip handle inputs\r\n    if (input.is_handle) return;\r\n    \r\n    shape[input.name] = createSchemaForInput(input);\r\n  });\r\n  \r\n  return z.object(shape);\r\n}\r\n"], "names": [], "mappings": ";;;;;;AACA;;AAQO,SAAS,cACd,QAAyB,EACzB,KAAU;IAEV,4CAA4C;IAC5C,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,UAAU,aAAa,UAAU,QAAQ,UAAU,EAAE,GAAG;QACjF,OAAO;YAAE,SAAS;YAAM,SAAS;QAAG;IACtC;IAEA,+BAA+B;IAC/B,OAAQ,SAAS,UAAU;QACzB,KAAK;YACH,OAAO,eAAe,UAAU;QAClC,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,eAAe,UAAU;QAClC,KAAK;QACL,KAAK;YACH,OAAO,cAAc,UAAU;QACjC,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,eAAe,UAAU;QAClC;YACE,gCAAgC;YAChC,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAG;IACxC;AACF;AAEA;;CAEC,GACD,SAAS,eACP,QAAyB,EACzB,KAAU;IAEV,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;YAAE,SAAS;YAAO,SAAS;QAAmB;IACvD;IAEA,MAAM,YAAY,SAAS,UAAU,IAAI;IACzC,MAAM,YAAY,SAAS,UAAU,IAAI,OAAO,gBAAgB;IAEhE,IAAI,MAAM,MAAM,GAAG,WAAW;QAC5B,OAAO;YAAE,SAAS;YAAO,SAAS,CAAC,iBAAiB,EAAE,UAAU,WAAW,CAAC;QAAC;IAC/E;IACA,IAAI,MAAM,MAAM,GAAG,WAAW;QAC5B,OAAO;YAAE,SAAS;YAAO,SAAS,CAAC,gBAAgB,EAAE,UAAU,WAAW,CAAC;QAAC;IAC9E;IAEA,6BAA6B;IAC7B,IAAI,SAAS,OAAO,EAAE;QACpB,IAAI;YACF,MAAM,QAAQ,IAAI,OAAO,SAAS,OAAO;YACzC,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;gBACtB,OAAO;oBAAE,SAAS;oBAAO,SAAS,SAAS,aAAa,IAAI;gBAAiB;YAC/E;QACF,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,0BAA0B,SAAS,OAAO;QAC1D;IACF;IAEA,OAAO;QAAE,SAAS;QAAM,SAAS;IAAc;AACjD;AAEA;;CAEC,GACD,SAAS,eACP,QAAyB,EACzB,KAAU;IAEV,MAAM,WAAW,OAAO;IACxB,IAAI,MAAM,WAAW;QACnB,OAAO;YAAE,SAAS;YAAO,SAAS;QAAmB;IACvD;IAEA,MAAM,WACJ,SAAS,SAAS,KAAK,YAAY,OAAO,SAAS,SAAS,IAAI,OAAO,gBAAgB;IACzF,MAAM,WACJ,SAAS,SAAS,KAAK,YAAY,OAAO,SAAS,SAAS,IAAI,OAAO,gBAAgB;IAEzF,IAAI,WAAW,UAAU;QACvB,OAAO;YAAE,SAAS;YAAO,SAAS,CAAC,iBAAiB,EAAE,UAAU;QAAC;IACnE;IACA,IAAI,WAAW,UAAU;QACvB,OAAO;YAAE,SAAS;YAAO,SAAS,CAAC,gBAAgB,EAAE,UAAU;QAAC;IAClE;IAEA,OAAO;QAAE,SAAS;QAAM,SAAS;IAAe;AAClD;AAEA;;CAEC,GACD,SAAS,cACP,QAAyB,EACzB,KAAU;IAEV,uBAAuB;IACvB,IAAI,YAAY;IAChB,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI;YACF,YAAY,KAAK,KAAK,CAAC;QACzB,EAAE,OAAO,GAAG;YACV,OAAO;gBAAE,SAAS;gBAAO,SAAS;YAAsB;QAC1D;IACF;IAEA,IAAI,CAAC,MAAM,OAAO,CAAC,YAAY;QAC7B,OAAO;YAAE,SAAS;YAAO,SAAS;QAAmB;IACvD;IAEA,sBAAsB;IACtB,MAAM,WAAW,SAAS,SAAS,IAAI;IACvC,MAAM,WAAW,SAAS,SAAS,IAAI,OAAO,gBAAgB;IAE9D,IAAI,UAAU,MAAM,GAAG,UAAU;QAC/B,OAAO;YAAE,SAAS;YAAO,SAAS,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC;QAAC;IAC3E;IACA,IAAI,UAAU,MAAM,GAAG,UAAU;QAC/B,OAAO;YAAE,SAAS;YAAO,SAAS,CAAC,kBAAkB,EAAE,SAAS,MAAM,CAAC;QAAC;IAC1E;IAEA,OAAO;QAAE,SAAS;QAAM,SAAS;IAAa;AAChD;AAEA;;CAEC,GACD,SAAS,eACP,QAAyB,EACzB,KAAU;IAEV,oCAAoC;IACpC,IAAI,YAAY;IAChB,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI;YACF,YAAY,KAAK,KAAK,CAAC;QACzB,EAAE,OAAO,GAAG;YACV,OAAO;gBAAE,SAAS;gBAAO,SAAS;YAAsB;QAC1D;IACF;IAEA,IAAI,OAAO,cAAc,YAAY,cAAc,QAAQ,MAAM,OAAO,CAAC,YAAY;QACnF,OAAO;YAAE,SAAS;YAAO,SAAS;QAAoB;IACxD;IAEA,sBAAsB;IACtB,MAAM,eAAe,SAAS,aAAa,IAAI,EAAE;IACjD,KAAK,MAAM,OAAO,aAAc;QAC9B,IAAI,CAAC,CAAC,OAAO,SAAS,GAAG;YACvB,OAAO;gBAAE,SAAS;gBAAO,SAAS,CAAC,sBAAsB,EAAE,KAAK;YAAC;QACnE;IACF;IAEA,OAAO;QAAE,SAAS;QAAM,SAAS;IAAe;AAClD;AAQO,SAAS,kBACd,MAAyB,EACzB,MAA2B;IAE3B,MAAM,SAAgE,CAAC;IAEvE,sBAAsB;IACtB,OAAO,OAAO,CAAC,CAAC;QACd,qBAAqB;QACrB,IAAI,SAAS,SAAS,EAAE;QAExB,oBAAoB;QACpB,MAAM,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;QAEnC,WAAW;QACX,MAAM,CAAC,SAAS,IAAI,CAAC,GAAG,cAAc,UAAU;IAClD;IAEA,OAAO;AACT;AAOO,SAAS,qBAAqB,QAAyB;IAC5D,OAAQ,SAAS,UAAU;QACzB,KAAK;YACH,IAAI,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM;YAErB,IAAI,SAAS,QAAQ,EAAE;gBACrB,SAAS,OAAO,GAAG,CAAC,GAAG;oBAAE,SAAS;gBAAyB;YAC7D,OAAO;gBACL,SAAS,OAAO,QAAQ;YAC1B;YAEA,IAAI,SAAS,UAAU,EAAE;gBACvB,SAAS,OAAO,GAAG,CAAC,SAAS,UAAU,EAAE;oBACvC,SAAS,CAAC,iBAAiB,EAAE,SAAS,UAAU,CAAC,WAAW,CAAC;gBAC/D;YACF;YAEA,IAAI,SAAS,UAAU,EAAE;gBACvB,SAAS,OAAO,GAAG,CAAC,SAAS,UAAU,EAAE;oBACvC,SAAS,CAAC,gBAAgB,EAAE,SAAS,UAAU,CAAC,WAAW,CAAC;gBAC9D;YACF;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,KAAK,CAAC,IAAI,OAAO,SAAS,OAAO,GAAG;oBAClD,SAAS,SAAS,aAAa,IAAI;gBACrC;YACF;YAEA,OAAO;QAET,KAAK;YACH,IAAI,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG;YAErC,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACtB,YAAY,UAAU,QAAQ;YAChC;YAEA,IAAI,SAAS,SAAS,KAAK,WAAW;gBACpC,YAAY,UAAU,GAAG,CAAC,OAAO,SAAS,SAAS,GAAG;oBACpD,SAAS,CAAC,iBAAiB,EAAE,SAAS,SAAS,EAAE;gBACnD;YACF;YAEA,IAAI,SAAS,SAAS,KAAK,WAAW;gBACpC,YAAY,UAAU,GAAG,CAAC,OAAO,SAAS,SAAS,GAAG;oBACpD,SAAS,CAAC,gBAAgB,EAAE,SAAS,SAAS,EAAE;gBAClD;YACF;YAEA,OAAO;QAET,KAAK;QACL,KAAK;YACH,IAAI,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC,MAAM;YAE/B,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACtB,YAAY,UAAU,QAAQ;YAChC;YAEA,IAAI,SAAS,SAAS,KAAK,WAAW;gBACpC,YAAY,UAAU,GAAG,CAAC,OAAO,SAAS,SAAS,GAAG;oBACpD,SAAS,CAAC,iBAAiB,EAAE,SAAS,SAAS,EAAE;gBACnD;YACF;YAEA,IAAI,SAAS,SAAS,KAAK,WAAW;gBACpC,YAAY,UAAU,GAAG,CAAC,OAAO,SAAS,SAAS,GAAG;oBACpD,SAAS,CAAC,gBAAgB,EAAE,SAAS,SAAS,EAAE;gBAClD;YACF;YAEA,OAAO;QAET,KAAK;YACH,OAAO,SAAS,QAAQ,GAAG,oIAAA,CAAA,IAAC,CAAC,OAAO,KAAK,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;QAE/D,KAAK;QACL,KAAK;YACH,IAAI,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,GAAG;YAE/B,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACtB,cAAc,YAAY,QAAQ;YACpC;YAEA,IAAI,SAAS,SAAS,KAAK,WAAW;gBACpC,cAAc,YAAY,GAAG,CAAC,SAAS,SAAS,EAAE;oBAChD,SAAS,CAAC,mBAAmB,EAAE,SAAS,SAAS,CAAC,MAAM,CAAC;gBAC3D;YACF;YAEA,IAAI,SAAS,SAAS,KAAK,WAAW;gBACpC,cAAc,YAAY,GAAG,CAAC,SAAS,SAAS,EAAE;oBAChD,SAAS,CAAC,kBAAkB,EAAE,SAAS,SAAS,CAAC,MAAM,CAAC;gBAC1D;YACF;YAEA,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;YACH,IAAI,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC,oIAAA,CAAA,IAAC,CAAC,GAAG;YAE9B,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACtB,YAAY,UAAU,QAAQ;YAChC;YAEA,gDAAgD;YAChD,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,MAAM,GAAG,GAAG;gBAC/D,+DAA+D;gBAC/D,qEAAqE;gBACrE,YAAY,UAAU,MAAM,CAC1B,CAAC;oBACC,IAAI,CAAC,KAAK,OAAO;oBACjB,OAAO,SAAS,aAAa,CAAE,KAAK,CAAC,CAAC,MAAQ,OAAO;gBACvD,GACA;oBACE,SAAS,CAAC,uBAAuB,EAAE,SAAS,aAAa,CAAC,IAAI,CAAC,OAAO;gBACxE;YAEJ;YAEA,OAAO;QAET;YACE,kCAAkC;YAClC,OAAO,SAAS,QAAQ,GAAG,oIAAA,CAAA,IAAC,CAAC,GAAG,KAAK,oIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;IACzD;AACF;AAOO,SAAS,uBAAuB,MAAyB;IAC9D,MAAM,QAAsC,CAAC;IAE7C,OAAO,OAAO,CAAC,CAAC;QACd,qBAAqB;QACrB,IAAI,MAAM,SAAS,EAAE;QAErB,KAAK,CAAC,MAAM,IAAI,CAAC,GAAG,qBAAqB;IAC3C;IAEA,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;AAClB", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/utils/valueFormatting.ts"], "sourcesContent": ["import { InputDefinition } from \"@/types\";\r\n\r\n/**\r\n * Formats a value for display based on its type\r\n * @param value The value to format\r\n * @param inputType The type of the input\r\n * @returns Formatted value as a string\r\n */\r\nexport function formatValueForDisplay(value: any, inputType: string): string {\r\n  if (value === undefined || value === null) {\r\n    return \"\";\r\n  }\r\n\r\n  switch (inputType) {\r\n    case \"object\":\r\n    case \"dict\":\r\n    case \"json\":\r\n    case \"list\":\r\n    case \"array\":\r\n      return typeof value === \"object\"\r\n        ? JSON.stringify(value, null, 2)\r\n        : String(value);\r\n    case \"bool\":\r\n      return value ? \"True\" : \"False\";\r\n    default:\r\n      return String(value);\r\n  }\r\n}\r\n\r\n/**\r\n * Parses a string value to the appropriate type\r\n * @param value The string value to parse\r\n * @param inputType The type of the input\r\n * @returns Parsed value in the appropriate type\r\n */\r\nexport function parseInputValue(value: string, inputType: string): any {\r\n  switch (inputType) {\r\n    case \"int\":\r\n      return parseInt(value, 10);\r\n    case \"float\":\r\n    case \"number\":\r\n      return parseFloat(value);\r\n    case \"bool\":\r\n      return value === \"true\" || value === \"True\" || value === \"1\";\r\n    case \"object\":\r\n    case \"dict\":\r\n    case \"json\":\r\n    case \"list\":\r\n    case \"array\":\r\n      try {\r\n        return JSON.parse(value);\r\n      } catch (e) {\r\n        return value; // Return as string if parsing fails\r\n      }\r\n    default:\r\n      return value;\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the appropriate input type for an HTML input element\r\n * @param inputType The type of the input from the definition\r\n * @returns HTML input type\r\n */\r\nexport function getHtmlInputType(inputType: string): string {\r\n  switch (inputType) {\r\n    case \"int\":\r\n    case \"float\":\r\n    case \"number\":\r\n      return \"number\";\r\n    case \"password\":\r\n      return \"password\";\r\n    default:\r\n      return \"text\";\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the config value for an input, with special handling for different node types\r\n * @param inputName The name of the input\r\n * @param defaultValue Default value if not found\r\n * @param config The node configuration\r\n * @param nodeType The type of the node\r\n * @param nodeId The ID of the node\r\n * @param getStoreValue Function to get a value from the store\r\n * @returns The config value\r\n */\r\nexport function getConfigValue(\r\n  inputName: string,\r\n  defaultValue: any,\r\n  config: Record<string, any>,\r\n  nodeType: string,\r\n  nodeId: string,\r\n  getStoreValue?: (nodeId: string, key: string, defaultValue: any) => any\r\n): any {\r\n  // Special handling for MCP Tools component\r\n  if (nodeType === \"MCPToolsComponent\" && getStoreValue) {\r\n    if (inputName === \"sse_url\") {\r\n      return getStoreValue(nodeId, \"sse_url\", config?.sse_url || defaultValue);\r\n    }\r\n    if (inputName === \"mcp_type\") {\r\n      return getStoreValue(nodeId, \"mcp_type\", config?.mcp_type || defaultValue);\r\n    }\r\n    if (inputName === \"selected_tool_name\") {\r\n      return getStoreValue(\r\n        nodeId,\r\n        \"selected_tool_name\",\r\n        config?.selected_tool_name || defaultValue\r\n      );\r\n    }\r\n    // For connection_status, check if it's in the raw inputs\r\n    if (inputName === \"connection_status\") {\r\n      const rawInputs = config?.inputs || [];\r\n      const statusInput = rawInputs.find((input: any) => input.name === \"connection_status\");\r\n      if (statusInput && statusInput.value) {\r\n        return statusInput.value;\r\n      }\r\n      if (config?.connection_status) {\r\n        return config.connection_status;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Default behavior for other components and inputs\r\n  return config?.[inputName] ?? defaultValue;\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAQO,SAAS,sBAAsB,KAAU,EAAE,SAAiB;IACjE,IAAI,UAAU,aAAa,UAAU,MAAM;QACzC,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,OAAO,UAAU,WACpB,KAAK,SAAS,CAAC,OAAO,MAAM,KAC5B,OAAO;QACb,KAAK;YACH,OAAO,QAAQ,SAAS;QAC1B;YACE,OAAO,OAAO;IAClB;AACF;AAQO,SAAS,gBAAgB,KAAa,EAAE,SAAiB;IAC9D,OAAQ;QACN,KAAK;YACH,OAAO,SAAS,OAAO;QACzB,KAAK;QACL,KAAK;YACH,OAAO,WAAW;QACpB,KAAK;YACH,OAAO,UAAU,UAAU,UAAU,UAAU,UAAU;QAC3D,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC;YACpB,EAAE,OAAO,GAAG;gBACV,OAAO,OAAO,oCAAoC;YACpD;QACF;YACE,OAAO;IACX;AACF;AAOO,SAAS,iBAAiB,SAAiB;IAChD,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAYO,SAAS,eACd,SAAiB,EACjB,YAAiB,EACjB,MAA2B,EAC3B,QAAgB,EAChB,MAAc,EACd,aAAuE;IAEvE,2CAA2C;IAC3C,IAAI,aAAa,uBAAuB,eAAe;QACrD,IAAI,cAAc,WAAW;YAC3B,OAAO,cAAc,QAAQ,WAAW,QAAQ,WAAW;QAC7D;QACA,IAAI,cAAc,YAAY;YAC5B,OAAO,cAAc,QAAQ,YAAY,QAAQ,YAAY;QAC/D;QACA,IAAI,cAAc,sBAAsB;YACtC,OAAO,cACL,QACA,sBACA,QAAQ,sBAAsB;QAElC;QACA,yDAAyD;QACzD,IAAI,cAAc,qBAAqB;YACrC,MAAM,YAAY,QAAQ,UAAU,EAAE;YACtC,MAAM,cAAc,UAAU,IAAI,CAAC,CAAC,QAAe,MAAM,IAAI,KAAK;YAClE,IAAI,eAAe,YAAY,KAAK,EAAE;gBACpC,OAAO,YAAY,KAAK;YAC1B;YACA,IAAI,QAAQ,mBAAmB;gBAC7B,OAAO,OAAO,iBAAiB;YACjC;QACF;IACF;IAEA,mDAAmD;IACnD,OAAO,QAAQ,CAAC,UAAU,IAAI;AAChC", "debugId": null}}, {"offset": {"line": 1394, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/app/%28features%29/workflows/api.ts"], "sourcesContent": ["/**\r\n * Workflow API Module\r\n *\r\n * This module provides functions for interacting with the workflow API endpoints.\r\n */\r\n\r\nimport axios from \"axios\";\r\nimport { getAccessToken } from \"@/lib/cookies\";\r\nimport { getClientAccessToken } from \"@/lib/clientCookies\";\r\nimport { API_BASE_URL, API_ENDPOINTS } from \"@/lib/apiConfig\";\r\n\r\n// Types for workflow API responses\r\nexport interface WorkflowMetadata {\r\n  total: number;\r\n  totalPages: number;\r\n  currentPage: number;\r\n  pageSize: number;\r\n  hasNextPage: boolean;\r\n  hasPreviousPage: boolean;\r\n}\r\n\r\nexport interface WorkflowDetails {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  workflow_url?: string;\r\n  builder_url?: string;\r\n  start_nodes?: string[];\r\n  owner_id?: string;\r\n  user_id?: string; // Some APIs use user_id instead of owner_id\r\n  user_ids?: string[];\r\n  owner_type?: string;\r\n  workflow_template_id?: string;\r\n  parent_workflow_id?: string;\r\n  url?: string;\r\n  is_imported?: boolean;\r\n  execution_count?: number;\r\n  version?: string;\r\n  visibility?: string;\r\n  category?: string;\r\n  tags?: Record<string, any> | string[];\r\n  status?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface WorkflowSummary {\r\n  success?: boolean;\r\n  message?: string;\r\n  workflow: WorkflowDetails;\r\n}\r\n\r\nexport interface WorkflowListItem {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  workflow_url?: string;\r\n  builder_url?: string;\r\n  start_nodes?: string[];\r\n  owner_id?: string;\r\n  user_ids?: string[];\r\n  owner_type?: string;\r\n  workflow_template_id?: string;\r\n  parent_workflow_id?: string;\r\n  url?: string;\r\n  is_imported?: boolean;\r\n  execution_count?: number;\r\n  version?: string;\r\n  visibility?: string;\r\n  category?: string;\r\n  tags?: Record<string, any>;\r\n  status?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface WorkflowListResponse {\r\n  data: WorkflowListItem[];\r\n  metadata: WorkflowMetadata;\r\n}\r\n\r\nexport interface CreateWorkflowRequest {\r\n  name: string;\r\n  description: string;\r\n  workflow_data: {\r\n    nodes: Record<string, any>[];\r\n    edges: Record<string, any>[];\r\n  };\r\n  start_node_data?: string[];\r\n}\r\n\r\nexport interface CreateWorkflowResponse {\r\n  success: boolean;\r\n  message: string;\r\n  workflow_id: WorkflowDetails;\r\n}\r\n\r\n// Create axios instance with interceptors\r\nconst workflowAxios = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  withCredentials: false,\r\n});\r\n\r\n// Add request interceptor to include token\r\nworkflowAxios.interceptors.request.use(\r\n  async (config) => {\r\n    // Get token using the appropriate method based on environment\r\n    let token;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      token = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      token = await getAccessToken();\r\n    }\r\n\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => Promise.reject(error),\r\n);\r\n\r\n/**\r\n * Fetches workflows for the current user\r\n *\r\n * This function uses the /user/me endpoint which automatically identifies the user\r\n * from their authentication token, eliminating the need to pass a user ID.\r\n */\r\nexport async function fetchWorkflowsByUser(\r\n  page: number = 1,\r\n  pageSize: number = 10,\r\n  accessToken?: string,\r\n): Promise<WorkflowListResponse> {\r\n  try {\r\n    console.log(`[DEBUG] Fetching workflows with page=${page}, pageSize=${pageSize}`);\r\n    console.log(`[DEBUG] Using endpoint: ${API_ENDPOINTS.WORKFLOWS.LIST}`);\r\n\r\n    // If no token is provided, try to get it from client or server\r\n    if (!accessToken) {\r\n      try {\r\n        if (typeof window !== \"undefined\") {\r\n          // Client-side\r\n          accessToken = getClientAccessToken();\r\n          console.log(\r\n            `[DEBUG] Retrieved client token (length: ${accessToken ? accessToken.length : 0})`,\r\n          );\r\n        } else {\r\n          // Server-side\r\n          accessToken = await getAccessToken();\r\n          console.log(\r\n            `[DEBUG] Retrieved server token (length: ${accessToken ? accessToken.length : 0})`,\r\n          );\r\n        }\r\n      } catch (tokenError) {\r\n        console.error(`[DEBUG] Error retrieving token:`, tokenError);\r\n      }\r\n    } else {\r\n      console.log(`[DEBUG] Using provided access token (length: ${accessToken.length})`);\r\n    }\r\n\r\n    // Prepare config with authentication if token is provided\r\n    const config: any = {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      withCredentials: false,\r\n      params: {\r\n        page: page,\r\n        page_size: pageSize,\r\n      },\r\n    };\r\n\r\n    // Add Authorization header if access token is provided\r\n    if (accessToken) {\r\n      // Ensure token has Bearer prefix\r\n      if (accessToken.startsWith(\"Bearer \")) {\r\n        config.headers.Authorization = accessToken;\r\n      } else {\r\n        config.headers.Authorization = `Bearer ${accessToken}`;\r\n      }\r\n      console.log(`[DEBUG] Added Authorization header`);\r\n    } else {\r\n      console.warn(`[DEBUG] No access token available for request`);\r\n    }\r\n\r\n    // Use the workflowAxios instance that has the interceptors\r\n    // and the correct endpoint from API_ENDPOINTS\r\n    console.log(`[DEBUG] Making request to: ${API_ENDPOINTS.WORKFLOWS.LIST}`);\r\n    const response = await workflowAxios.get(API_ENDPOINTS.WORKFLOWS.LIST, config);\r\n    console.log(`[DEBUG] Successful response with ${response.data?.data?.length || 0} workflows`);\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error fetching workflows:\", error);\r\n    // Add more detailed error logging\r\n    if (error.response) {\r\n      console.error(`[DEBUG] Response status: ${error.response.status}`);\r\n      console.error(`[DEBUG] Response data:`, error.response.data);\r\n      throw new Error(\r\n        `Failed to fetch workflows: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    } else if (error.request) {\r\n      // The request was made but no response was received\r\n      console.error(`[DEBUG] No response received:`, error.request);\r\n      throw new Error(\"Failed to fetch workflows: No response received from server\");\r\n    } else {\r\n      // Something happened in setting up the request\r\n      console.error(`[DEBUG] Request setup error:`, error.message);\r\n      throw new Error(`Failed to fetch workflows: ${error.message}`);\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Creates a new workflow with a StartNode already added\r\n */\r\nexport async function createEmptyWorkflow(): Promise<CreateWorkflowResponse> {\r\n  try {\r\n    // Create a StartNode for the initial workflow\r\n    const startNode = {\r\n      id: \"start-node\",\r\n      type: \"WorkflowNode\",\r\n      position: { x: 100, y: 100 },\r\n      data: {\r\n        label: \"Start\",\r\n        type: \"component\",\r\n        originalType: \"StartNode\",\r\n        definition: {\r\n          name: \"StartNode\",\r\n          display_name: \"Start\",\r\n          description:\r\n            \"The starting point for all workflows. Only nodes connected to this node will be executed.\",\r\n          category: \"Input/Output\",\r\n          icon: \"Play\",\r\n          beta: false,\r\n          inputs: [],\r\n          outputs: [\r\n            {\r\n              name: \"flow\",\r\n              display_name: \"Flow\",\r\n              output_type: \"Any\",\r\n            },\r\n          ],\r\n          is_valid: true,\r\n          path: \"components.io.start_node\",\r\n        },\r\n        config: {\r\n          collected_parameters: {},\r\n        },\r\n      },\r\n    };\r\n\r\n    const payload: CreateWorkflowRequest = {\r\n      name: \"Untitled Workflow\",\r\n      description: \"New workflow\",\r\n      workflow_data: {\r\n        nodes: [startNode],\r\n        edges: [],\r\n      },\r\n      start_node_data: [],\r\n    };\r\n\r\n    // Get token for authorization\r\n    let token;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      token = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      token = await getAccessToken();\r\n    }\r\n\r\n    const config = {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        ...(token && { Authorization: `Bearer ${token}` }),\r\n      },\r\n      withCredentials: false,\r\n    };\r\n\r\n    const response = await axios.post(API_ENDPOINTS.WORKFLOWS.CREATE, payload, config);\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error creating workflow:\", error);\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to create workflow: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches a specific workflow by ID\r\n *\r\n * @returns A WorkflowSummary object with the workflow details nested in the workflow property\r\n */\r\nexport async function fetchWorkflowById(id: string): Promise<WorkflowSummary> {\r\n  try {\r\n    // Get token for authorization\r\n    let token;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      token = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      token = await getAccessToken();\r\n    }\r\n\r\n    const config = {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        ...(token && { Authorization: `Bearer ${token}` }),\r\n      },\r\n      withCredentials: false,\r\n    };\r\n\r\n    const response = await axios.get(API_ENDPOINTS.WORKFLOWS.GET(id), config);\r\n    console.log(\"Workflow API response:\", response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error fetching workflow:\", error);\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to fetch workflow: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches workflow data from a builder URL\r\n * @param url The URL to fetch the workflow data from\r\n * @returns The workflow data\r\n */\r\nexport async function fetchWorkflowFromBuilderUrl(url: string): Promise<any> {\r\n  try {\r\n    // For external URLs, we need to use axios directly\r\n    const response = await axios.get(url, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error fetching workflow from builder URL:\", error);\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to fetch workflow data: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Deletes a workflow by ID\r\n *\r\n * @param id The ID of the workflow to delete\r\n * @returns A response object indicating success or failure\r\n */\r\nexport async function deleteWorkflow(id: string): Promise<{ success: boolean; message: string }> {\r\n  try {\r\n    // Get token for authorization\r\n    let token;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      token = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      token = await getAccessToken();\r\n    }\r\n\r\n    const config = {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        ...(token && { Authorization: `Bearer ${token}` }),\r\n      },\r\n      withCredentials: false,\r\n    };\r\n\r\n    const response = await axios.delete(API_ENDPOINTS.WORKFLOWS.DELETE(id), config);\r\n    console.log(\"Workflow delete response:\", response.data);\r\n    return {\r\n      success: true,\r\n      message: response.data?.message || \"Workflow deleted successfully\",\r\n    };\r\n  } catch (error: any) {\r\n    console.error(\"Error deleting workflow:\", error);\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to delete workflow: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Updates a workflow's metadata (name and description)\r\n */\r\nexport async function updateWorkflowMetadata(\r\n  id: string,\r\n  data: { name?: string; description?: string },\r\n): Promise<WorkflowSummary> {\r\n  try {\r\n    // Get token for authorization\r\n    let token;\r\n    if (typeof window !== \"undefined\") {\r\n      // Client-side\r\n      token = getClientAccessToken();\r\n    } else {\r\n      // Server-side\r\n      token = await getAccessToken();\r\n    }\r\n\r\n    const config = {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        ...(token && { Authorization: `Bearer ${token}` }),\r\n      },\r\n      withCredentials: false,\r\n    };\r\n\r\n    const response = await axios.patch(API_ENDPOINTS.WORKFLOWS.UPDATE(id), data, config);\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error updating workflow metadata:\", error);\r\n    if (error.response) {\r\n      throw new Error(\r\n        `Failed to update workflow: ${error.response.status} ${error.response.statusText}`,\r\n      );\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport default {\r\n  fetchWorkflowsByUser,\r\n  createEmptyWorkflow,\r\n  fetchWorkflowById,\r\n  fetchWorkflowFromBuilderUrl,\r\n  deleteWorkflow,\r\n  updateWorkflowMetadata,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;AAED;AACA;AACA;AACA;;;;;AAwFA,0CAA0C;AAC1C,MAAM,gBAAgB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS,uHAAA,CAAA,eAAY;IACrB,iBAAiB;AACnB;AAEA,2CAA2C;AAC3C,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,OAAO;IACL,8DAA8D;IAC9D,IAAI;IACJ,uCAAmC;;IAGnC,OAAO;QACL,cAAc;QACd,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC7B;IAEA,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;AASrB,eAAe,qBACpB,OAAe,CAAC,EAChB,WAAmB,EAAE,EACrB,WAAoB;IAEpB,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,KAAK,WAAW,EAAE,UAAU;QAChF,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,EAAE;QAErE,+DAA+D;QAC/D,IAAI,CAAC,aAAa;YAChB,IAAI;gBACF,uCAAmC;;gBAMnC,OAAO;oBACL,cAAc;oBACd,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;oBACjC,QAAQ,GAAG,CACT,CAAC,wCAAwC,EAAE,cAAc,YAAY,MAAM,GAAG,EAAE,CAAC,CAAC;gBAEtF;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAE;YACnD;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,YAAY,MAAM,CAAC,CAAC,CAAC;QACnF;QAEA,0DAA0D;QAC1D,MAAM,SAAc;YAClB,SAAS;gBACP,gBAAgB;YAClB;YACA,iBAAiB;YACjB,QAAQ;gBACN,MAAM;gBACN,WAAW;YACb;QACF;QAEA,uDAAuD;QACvD,IAAI,aAAa;YACf,iCAAiC;YACjC,IAAI,YAAY,UAAU,CAAC,YAAY;gBACrC,OAAO,OAAO,CAAC,aAAa,GAAG;YACjC,OAAO;gBACL,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa;YACxD;YACA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC;QAClD,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,6CAA6C,CAAC;QAC9D;QAEA,2DAA2D;QAC3D,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,EAAE;QACxE,MAAM,WAAW,MAAM,cAAc,GAAG,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,EAAE;QACvE,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS,IAAI,EAAE,MAAM,UAAU,EAAE,UAAU,CAAC;QAE5F,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,kCAAkC;QAClC,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,MAAM,QAAQ,CAAC,MAAM,EAAE;YACjE,QAAQ,KAAK,CAAC,CAAC,sBAAsB,CAAC,EAAE,MAAM,QAAQ,CAAC,IAAI;YAC3D,MAAM,IAAI,MACR,CAAC,2BAA2B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAEtF,OAAO,IAAI,MAAM,OAAO,EAAE;YACxB,oDAAoD;YACpD,QAAQ,KAAK,CAAC,CAAC,6BAA6B,CAAC,EAAE,MAAM,OAAO;YAC5D,MAAM,IAAI,MAAM;QAClB,OAAO;YACL,+CAA+C;YAC/C,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE,MAAM,OAAO;YAC3D,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,MAAM,OAAO,EAAE;QAC/D;IACF;AACF;AAKO,eAAe;IACpB,IAAI;QACF,8CAA8C;QAC9C,MAAM,YAAY;YAChB,IAAI;YACJ,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAI;YAC3B,MAAM;gBACJ,OAAO;gBACP,MAAM;gBACN,cAAc;gBACd,YAAY;oBACV,MAAM;oBACN,cAAc;oBACd,aACE;oBACF,UAAU;oBACV,MAAM;oBACN,MAAM;oBACN,QAAQ,EAAE;oBACV,SAAS;wBACP;4BACE,MAAM;4BACN,cAAc;4BACd,aAAa;wBACf;qBACD;oBACD,UAAU;oBACV,MAAM;gBACR;gBACA,QAAQ;oBACN,sBAAsB,CAAC;gBACzB;YACF;QACF;QAEA,MAAM,UAAiC;YACrC,MAAM;YACN,aAAa;YACb,eAAe;gBACb,OAAO;oBAAC;iBAAU;gBAClB,OAAO,EAAE;YACX;YACA,iBAAiB,EAAE;QACrB;QAEA,8BAA8B;QAC9B,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAC7B;QAEA,MAAM,SAAS;YACb,SAAS;gBACP,gBAAgB;gBAChB,GAAI,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC,CAAC;YACnD;YACA,iBAAiB;QACnB;QAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS;QAE3E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,2BAA2B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAEtF;QACA,MAAM;IACR;AACF;AAOO,eAAe,kBAAkB,EAAU;IAChD,IAAI;QACF,8BAA8B;QAC9B,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAC7B;QAEA,MAAM,SAAS;YACb,SAAS;gBACP,gBAAgB;gBAChB,GAAI,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC,CAAC;YACnD;YACA,iBAAiB;QACnB;QAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK;QAClE,QAAQ,GAAG,CAAC,0BAA0B,SAAS,IAAI;QACnD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAErF;QACA,MAAM;IACR;AACF;AAOO,eAAe,4BAA4B,GAAW;IAC3D,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;YACpC,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAE1F;QACA,MAAM;IACR;AACF;AAQO,eAAe,eAAe,EAAU;IAC7C,IAAI;QACF,8BAA8B;QAC9B,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAC7B;QAEA,MAAM,SAAS;YACb,SAAS;gBACP,gBAAgB;gBAChB,GAAI,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC,CAAC;YACnD;YACA,iBAAiB;QACnB;QAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK;QACxE,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;QACtD,OAAO;YACL,SAAS;YACT,SAAS,SAAS,IAAI,EAAE,WAAW;QACrC;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,2BAA2B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAEtF;QACA,MAAM;IACR;AACF;AAKO,eAAe,uBACpB,EAAU,EACV,IAA6C;IAE7C,IAAI;QACF,8BAA8B;QAC9B,IAAI;QACJ,uCAAmC;;QAGnC,OAAO;YACL,cAAc;YACd,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAC7B;QAEA,MAAM,SAAS;YACb,SAAS;gBACP,gBAAgB;gBAChB,GAAI,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC,CAAC;YACnD;YACA,iBAAiB;QACnB;QAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,KAAK,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,MAAM;QAE7E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,qCAAqC;QACnD,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,MACR,CAAC,2BAA2B,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;QAEtF;QACA,MAAM;IACR;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 1700, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/app/page.tsx"], "sourcesContent": ["// frontend\\src\\app\\page.tsx\r\n\"use client\"; // Required for hooks like useState, useEffect\r\nimport \"reactflow/dist/style.css\";\r\n\r\nimport React, { useState, useEffect, useCallback, useMemo, Suspense } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Link from \"next/link\";\r\nimport { Sidebar } from \"@/components/layout/Sidebar\";\r\nimport { TopBar } from \"@/components/layout/TopBar\";\r\nimport WorkflowCanvasWrapper from \"@/components/canvas/WorkflowCanvas\";\r\nimport { WorkflowNodeData } from \"@/types\";\r\nimport { ComponentsApiResponse } from \"@/types\";\r\nimport {\r\n  fetchComponents,\r\n  fetchMCPComponents,\r\n  ExecutionResult,\r\n  saveWorkflowToServer,\r\n} from \"@/lib/api\";\r\nimport { saveWorkflowToFile, loadWorkflowFromJson } from \"@/lib/workflowUtils\";\r\nimport { fetchWorkflowById, fetchWorkflowFromBuilderUrl } from \"@/lib/workflowApi\";\r\nimport { LoadWorkflowModal } from \"@/components/modals/LoadWorkflowModal\";\r\nimport { UnsavedChangesDialog } from \"@/components/modals/UnsavedChangesDialog\";\r\nimport { Node, Edge } from \"reactflow\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { useValidationStore } from \"@/store/validationStore\";\r\nimport { useExecutionStore } from \"@/store/executionStore\";\r\n\r\n// Import useSearchParams in a separate component\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { WorkflowSummary } from \"@/app/(features)/workflows/api\";\r\n\r\n// Define a proper type for workflow data\r\ninterface WorkflowData {\r\n  nodes: Node<WorkflowNodeData>[];\r\n  edges: Edge[];\r\n  workflow_name?: string;\r\n  [key: string]: unknown; // Allow for additional properties\r\n}\r\n\r\n// Create a separate component for the part that uses useSearchParams\r\nfunction WorkflowLoader({\r\n  onWorkflowLoad,\r\n  onWorkflowIdChange,\r\n  onLoadingChange,\r\n  onErrorChange,\r\n  workflowId,\r\n  loadedWorkflow,\r\n}: {\r\n  onWorkflowLoad: (data: WorkflowData, showMessage: boolean) => void;\r\n  onWorkflowIdChange: (id: string | null) => void;\r\n  onLoadingChange: (loading: boolean) => void;\r\n  onErrorChange: (error: string | null) => void;\r\n  workflowId: string | null;\r\n  loadedWorkflow: {\r\n    nodes: Node<WorkflowNodeData>[];\r\n    edges: Edge[];\r\n  } | null;\r\n}) {\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const loadWorkflowFromId = async () => {\r\n      const id = searchParams.get(\"workflow_id\");\r\n      if (!id) {\r\n        // Redirect to workflows page if no workflow_id is provided\r\n        router.push(\"/workflows\");\r\n        return;\r\n      }\r\n\r\n      // Skip if we've already loaded this workflow ID\r\n      if (id === workflowId && loadedWorkflow) {\r\n        console.log(\"Workflow already loaded, skipping fetch\");\r\n        return;\r\n      }\r\n\r\n      onWorkflowIdChange(id);\r\n      onLoadingChange(true);\r\n      onErrorChange(null);\r\n\r\n      try {\r\n        // API URL is configured in the fetchWorkflowById function\r\n\r\n        // Fetch workflow details to get the builder_url\r\n        const workflowDetails: WorkflowSummary = await fetchWorkflowById(id);\r\n        console.log(\"Workflow details:\", workflowDetails);\r\n\r\n        if (\r\n          !workflowDetails ||\r\n          !workflowDetails.workflow ||\r\n          !workflowDetails.workflow.builder_url\r\n        ) {\r\n          console.error(\r\n            \"Workflow data URL not found inside workflow details.\",\r\n            workflowDetails?.workflow?.builder_url,\r\n          );\r\n          throw new Error(\"Workflow data URL not found\");\r\n        }\r\n\r\n        // Fetch the actual workflow data from builder_url\r\n        const workflowData: WorkflowData = await fetchWorkflowFromBuilderUrl(\r\n          workflowDetails.workflow.builder_url,\r\n        );\r\n\r\n        // Ensure the workflow_name is set in the workflowData\r\n        if (!workflowData.workflow_name && workflowDetails.workflow.name) {\r\n          console.log(\"Setting workflow_name from workflowDetails:\", workflowDetails.workflow.name);\r\n          workflowData.workflow_name = workflowDetails.workflow.name;\r\n        }\r\n\r\n        console.log(\"Final workflowData before loading:\", workflowData);\r\n\r\n        // Load the workflow into the canvas without showing success message\r\n        // Pass false to prevent showing the success notification when loading from URL\r\n        onWorkflowLoad(workflowData, false);\r\n      } catch (error) {\r\n        console.error(\"Error loading workflow:\", error);\r\n        onErrorChange(\r\n          error instanceof Error ? error.message : \"Failed to load workflow. Please try again.\",\r\n        );\r\n      } finally {\r\n        onLoadingChange(false);\r\n      }\r\n    };\r\n\r\n    loadWorkflowFromId();\r\n  }, [\r\n    searchParams,\r\n    onWorkflowLoad,\r\n    workflowId,\r\n    loadedWorkflow,\r\n    router,\r\n    onWorkflowIdChange,\r\n    onLoadingChange,\r\n    onErrorChange,\r\n  ]);\r\n\r\n  return null; // This component doesn't render anything\r\n}\r\n\r\nexport default function Home() {\r\n  // We don't need router in this component as redirection is handled in WorkflowLoader\r\n\r\n  // State for loading components\r\n  const [componentsData, setComponentsData] = useState<ComponentsApiResponse>({});\r\n  // Memoize components to prevent unnecessary re-renders of the Sidebar\r\n  const components = useMemo(() => componentsData, [componentsData]);\r\n  // Rename for clarity\r\n  const [isLoadingComponents, setIsLoadingComponents] = useState(true); // <<< RENAME\r\n  const [componentError, setComponentError] = useState<string | null>(null); // <<< RENAME\r\n\r\n  // State for the workflow itself\r\n  const [currentNodes, setCurrentNodes] = useState<Node<WorkflowNodeData>[]>([]);\r\n  const [currentEdges, setCurrentEdges] = useState<Edge[]>([]);\r\n\r\n  // State for workflow title and theme\r\n  const [workflowTitle, setWorkflowTitle] = useState(\"Untitled Workflow\");\r\n  const [isDarkMode, setIsDarkMode] = useState(false);\r\n\r\n  // State for execution status\r\n  const [isExecuting, setIsExecuting] = useState(false);\r\n  const [executionResult, setExecutionResult] = useState<ExecutionResult | null>(null);\r\n\r\n  // State for workflow loading\r\n  const [isLoadModalOpen, setIsLoadModalOpen] = useState(false);\r\n  const [loadedWorkflow, setLoadedWorkflow] = useState<{\r\n    nodes: Node<WorkflowNodeData>[];\r\n    edges: Edge[];\r\n  } | null>(null);\r\n\r\n  // State for sidebar collapse\r\n  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);\r\n\r\n  // State for unsaved changes dialog\r\n  const [isUnsavedChangesDialogOpen, setIsUnsavedChangesDialogOpen] = useState(false);\r\n  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);\r\n\r\n  // State for workflow ID and loading from URL\r\n  const [workflowId, setWorkflowId] = useState<string | null>(null);\r\n  const [isLoadingWorkflow, setIsLoadingWorkflow] = useState(false);\r\n  const [workflowLoadError, setWorkflowLoadError] = useState<string | null>(null);\r\n\r\n  // State for tracking unsaved changes\r\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\r\n\r\n  // Effect to load components on mount\r\n  useEffect(() => {\r\n    const loadComponents = async () => {\r\n      try {\r\n        setIsLoadingComponents(true); // <<< Use renamed state\r\n        setComponentError(null); // <<< Use renamed state\r\n\r\n        // Fetch regular components\r\n        const fetchedComponents = await fetchComponents();\r\n        console.log(\r\n          \"DEBUG: Fetched regular components categories:\",\r\n          Object.keys(fetchedComponents),\r\n        );\r\n\r\n        // Log AI components if they exist\r\n        if (fetchedComponents.AI) {\r\n          console.log(\"DEBUG: AI components found:\", Object.keys(fetchedComponents.AI));\r\n        } else {\r\n          console.log(\"DEBUG: No AI category found in components\");\r\n        }\r\n\r\n        // Fetch MCP components\r\n        const mcpComponents = await fetchMCPComponents();\r\n        console.log(\"DEBUG: Fetched MCP components:\", mcpComponents);\r\n\r\n        // Log detailed information about MCP components\r\n        console.log(\"DEBUG: MCP components structure:\", mcpComponents);\r\n        if (mcpComponents.MCP) {\r\n          console.log(\r\n            \"DEBUG: MCP category exists with components:\",\r\n            Object.keys(mcpComponents.MCP),\r\n          );\r\n          console.log(\"DEBUG: First MCP component:\", Object.values(mcpComponents.MCP)[0]);\r\n        } else {\r\n          console.log(\"DEBUG: MCP category does not exist in mcpComponents\");\r\n        }\r\n\r\n        // Merge the components\r\n        const mergedComponents = {\r\n          ...fetchedComponents,\r\n          ...mcpComponents,\r\n        };\r\n\r\n        // If both have MCP category, merge them\r\n        if (fetchedComponents.MCP && mcpComponents.MCP) {\r\n          console.log(\r\n            \"DEBUG: Both fetchedComponents and mcpComponents have MCP category, merging them\",\r\n          );\r\n          mergedComponents.MCP = {\r\n            ...fetchedComponents.MCP,\r\n            ...mcpComponents.MCP,\r\n          };\r\n        }\r\n\r\n        console.log(\"DEBUG: Merged components categories:\", Object.keys(mergedComponents));\r\n        console.log(\r\n          \"DEBUG: Final MCP components count:\",\r\n          mergedComponents.MCP ? Object.keys(mergedComponents.MCP).length : 0,\r\n        );\r\n        setComponentsData(mergedComponents);\r\n      } catch (err) {\r\n        const errorMsg = err instanceof Error ? err.message : \"Unknown error\";\r\n        setComponentError(`Failed to load components: ${errorMsg}. Ensure the backend is running.`); // <<< Use renamed state\r\n        console.error(err);\r\n      } finally {\r\n        setIsLoadingComponents(false); // <<< Use renamed state\r\n      }\r\n    };\r\n    console.log(\"Loading components...\");\r\n    loadComponents();\r\n  }, []); // Empty dependency array ensures this runs once on mount\r\n\r\n  // Callback when nodes/edges change in the canvas\r\n  const handleFlowChange = useCallback((nodes: Node<WorkflowNodeData>[], edges: Edge[]) => {\r\n    setCurrentNodes(nodes);\r\n    setCurrentEdges(edges);\r\n    // Mark as having unsaved changes when the flow is updated\r\n    setHasUnsavedChanges(true);\r\n    // Optional: console.log('Flow updated in parent:', nodes, edges);\r\n  }, []); // Empty dependency array means this callback is stable\r\n\r\n  // Callback for the Save to Server button\r\n  const handleSaveWorkflowToServer = useCallback(async () => {\r\n    try {\r\n      // Get the current workflow title from state\r\n      const filename = `${workflowTitle.replace(/\\s+/g, \"_\")}`;\r\n\r\n      // Call the API function to save to server\r\n      const result = await saveWorkflowToServer({\r\n        nodes: currentNodes,\r\n        edges: currentEdges,\r\n        filename: filename, // Use workflow title as filename\r\n        workflow_name: workflowTitle, // Include the workflow name\r\n        ...(workflowId && { workflow_id: workflowId }), // Include workflow ID if available\r\n      });\r\n\r\n      if (result.success) {\r\n        // If this was a new workflow and we got an ID back, update our state\r\n        if (!workflowId && result.workflow_id) {\r\n          console.log(\"Received workflow ID from server:\", result.workflow_id);\r\n          setWorkflowId(result.workflow_id);\r\n          // Update URL without full page reload\r\n          window.history.replaceState({}, \"\", `?workflow_id=${result.workflow_id}`);\r\n        }\r\n\r\n        // Reset unsaved changes flag\r\n        setHasUnsavedChanges(false);\r\n\r\n        // Use the message field if available, otherwise use the default success message\r\n        alert(result.message || `Workflow saved successfully on server at: ${result.filepath}`);\r\n      } else {\r\n        alert(`Failed to save workflow on server: ${result.error || \"Unknown error\"}`);\r\n      }\r\n    } catch (err) {\r\n      const errorMsg = err instanceof Error ? err.message : String(err);\r\n      console.error(\"Error in handleSaveWorkflowToServer:\", err);\r\n      alert(`Error saving workflow to server: ${errorMsg}`);\r\n    }\r\n  }, [currentNodes, currentEdges, workflowTitle, workflowId]); // Depends on the current flow state\r\n\r\n  // Callback for the Save button - validates and saves the workflow\r\n  const handleSaveWorkflow = useCallback(async () => {\r\n    try {\r\n      // First validate the workflow\r\n      const { validateBeforeSave } = useValidationStore.getState();\r\n      const validationResult = await validateBeforeSave(currentNodes, currentEdges);\r\n\r\n      if (!validationResult.isValid) {\r\n        // Show validation errors\r\n        const errorMessages = validationResult.errors.map((err) => err.message).join(\"\\n\");\r\n        alert(`Validation failed. Please fix the following issues:\\n${errorMessages}`);\r\n        return;\r\n      }\r\n\r\n      // Proceed with saving to server\r\n      const filename = `${workflowTitle.replace(/\\s+/g, \"_\")}`;\r\n      const result = await saveWorkflowToServer({\r\n        nodes: currentNodes,\r\n        edges: currentEdges,\r\n        filename: filename,\r\n        workflow_name: workflowTitle,\r\n        ...(workflowId && { workflow_id: workflowId }),\r\n      });\r\n\r\n      if (result.success) {\r\n        // If this was a new workflow and we got an ID back, update our state\r\n        if (!workflowId && result.workflow_id) {\r\n          console.log(\"Received workflow ID from server:\", result.workflow_id);\r\n          setWorkflowId(result.workflow_id);\r\n          // Update URL without full page reload\r\n          window.history.replaceState({}, \"\", `?workflow_id=${result.workflow_id}`);\r\n        }\r\n\r\n        // Reset unsaved changes flag\r\n        setHasUnsavedChanges(false);\r\n\r\n        // Show success message\r\n        alert(result.message || \"Workflow saved successfully!\");\r\n      } else {\r\n        // Show error message\r\n        alert(`Failed to save workflow: ${result.error || \"Unknown error\"}`);\r\n      }\r\n    } catch (err) {\r\n      const errorMsg = err instanceof Error ? err.message : String(err);\r\n      console.error(\"Error in handleSaveWorkflow:\", err);\r\n      alert(`Error saving workflow: ${errorMsg}`);\r\n    }\r\n  }, [currentNodes, currentEdges, workflowTitle, workflowId]); // Depends on the current flow state\r\n\r\n  // --- Callback for the Run button ---\r\n  const handleRunWorkflow = useCallback(async () => {\r\n    if (isExecuting) return; // Prevent multiple simultaneous runs\r\n\r\n    setIsExecuting(true);\r\n    setExecutionResult(null); // Clear previous results\r\n\r\n    try {\r\n      // First validate the workflow\r\n      const { validateBeforeExecution } = useValidationStore.getState();\r\n      const validationResult = await validateBeforeExecution(currentNodes, currentEdges);\r\n\r\n      if (!validationResult.isValid) {\r\n        // Show validation errors\r\n        const errorMessages = validationResult.errors.map((err) => err.message).join(\"\\n\");\r\n        alert(`Validation failed. Please fix the following issues:\\n${errorMessages}`);\r\n        setIsExecuting(false);\r\n        return;\r\n      }\r\n\r\n      // Save the workflow first\r\n      const filename = `${workflowTitle.replace(/\\s+/g, \"_\")}`;\r\n      const saveResult = await saveWorkflowToServer({\r\n        nodes: currentNodes,\r\n        edges: currentEdges,\r\n        filename: filename,\r\n        workflow_name: workflowTitle,\r\n        ...(workflowId && { workflow_id: workflowId }),\r\n      });\r\n\r\n      if (!saveResult.success) {\r\n        alert(`Failed to save workflow before execution: ${saveResult.error || \"Unknown error\"}`);\r\n        setIsExecuting(false);\r\n        return;\r\n      }\r\n\r\n      // Update workflow ID if this was a new workflow\r\n      let currentWorkflowId = workflowId;\r\n      if (!currentWorkflowId && saveResult.workflow_id) {\r\n        currentWorkflowId = saveResult.workflow_id;\r\n        setWorkflowId(currentWorkflowId);\r\n        // Update URL without full page reload\r\n        window.history.replaceState({}, \"\", `?workflow_id=${currentWorkflowId}`);\r\n      }\r\n\r\n      // Reset unsaved changes flag after successful save\r\n      setHasUnsavedChanges(false);\r\n\r\n      // Get the execution store\r\n      const executionStore = useExecutionStore.getState();\r\n\r\n      // Set the missing fields from the validation result\r\n      if (validationResult.missingFields) {\r\n        executionStore.setMissingFields(validationResult.missingFields);\r\n      }\r\n\r\n      // Open the execution dialog\r\n      executionStore.setDialogOpen(true);\r\n      executionStore.setActiveTab(\"parameters\");\r\n\r\n      // Add a log entry\r\n      executionStore.addLog(\"Workflow validated and saved successfully. Ready for execution.\");\r\n\r\n      // Set executing state to false since we're just opening the dialog\r\n      setIsExecuting(false);\r\n\r\n      // The actual execution will be handled by the ExecutionDialog component\r\n      // when the user clicks the \"Run\" button in the dialog\r\n    } catch (err) {\r\n      // Catch unexpected errors\r\n      const errorMsg = err instanceof Error ? err.message : String(err);\r\n      console.error(\"Error in handleRunWorkflow:\", err);\r\n      setExecutionResult({ success: false, error: `Frontend error: ${errorMsg}` });\r\n      alert(`Error preparing workflow for execution: ${errorMsg}`);\r\n      setIsExecuting(false);\r\n    }\r\n  }, [currentNodes, currentEdges, isExecuting, workflowId, workflowTitle, setHasUnsavedChanges]); // Dependencies\r\n\r\n  // Theme toggle handler\r\n  const handleToggleTheme = useCallback(() => {\r\n    setIsDarkMode((prev) => !prev);\r\n    // Apply dark mode class to the document\r\n    document.documentElement.classList.toggle(\"dark\");\r\n  }, []);\r\n\r\n  // Function to handle actions that might discard unsaved changes\r\n  const handleActionWithUnsavedChanges = useCallback(\r\n    (action: () => void) => {\r\n      if (hasUnsavedChanges) {\r\n        // If there are unsaved changes, show the confirmation dialog\r\n        setPendingAction(() => action);\r\n        setIsUnsavedChangesDialogOpen(true);\r\n      } else {\r\n        // If no unsaved changes, proceed with the action\r\n        action();\r\n      }\r\n    },\r\n    [hasUnsavedChanges],\r\n  );\r\n\r\n  // Handler for opening the load workflow modal\r\n  const handleOpenLoadModal = useCallback(() => {\r\n    handleActionWithUnsavedChanges(() => {\r\n      setIsLoadModalOpen(true);\r\n    });\r\n  }, [handleActionWithUnsavedChanges]);\r\n\r\n  // Handler for loading a workflow\r\n  const handleLoadWorkflow = useCallback(\r\n    (workflowData: WorkflowData, showSuccessMessage: boolean = true) => {\r\n      try {\r\n        console.log(\"Loading workflow data:\", workflowData);\r\n\r\n        // Validate and convert the workflow data\r\n        const result = loadWorkflowFromJson(workflowData);\r\n        console.log(\"Validation result:\", result);\r\n\r\n        if (!result.isValid || !result.data) {\r\n          // Use a more modern notification approach instead of alert\r\n          console.error(`Failed to load workflow: ${result.error || \"Invalid workflow data\"}`);\r\n          return;\r\n        }\r\n\r\n        // We don't need this check anymore as we're using the UnsavedChangesDialog\r\n        // The dialog is shown before opening the load modal if there are unsaved changes\r\n\r\n        // Check if the workflow data has a StartNode and ensure its configuration is preserved\r\n        const nodes = result.data.nodes;\r\n        const startNode = nodes.find(\r\n          (node: Node<WorkflowNodeData>) => node.data.originalType === \"StartNode\",\r\n        );\r\n\r\n        if (startNode) {\r\n          console.log(\"StartNode found in loaded workflow:\", startNode);\r\n\r\n          // Ensure the StartNode has a config object with collected_parameters\r\n          if (!startNode.data.config) {\r\n            startNode.data.config = {};\r\n          }\r\n\r\n          if (!startNode.data.config.collected_parameters) {\r\n            startNode.data.config.collected_parameters = {};\r\n          }\r\n\r\n          // Ensure all parameters in collected_parameters have the required property set\r\n          // This is critical for pre-built workflows where the required property might not be set\r\n          if (startNode.data.config.collected_parameters) {\r\n            Object.keys(startNode.data.config.collected_parameters).forEach((paramId) => {\r\n              const param = startNode.data.config.collected_parameters[paramId];\r\n              // If required is undefined, set it to true (consider required unless explicitly false)\r\n              if (param.required === undefined) {\r\n                console.log(`Setting required=true for parameter ${paramId} in StartNode`);\r\n                param.required = true;\r\n              }\r\n            });\r\n          }\r\n\r\n          console.log(\"StartNode config after ensuring structure:\", startNode.data.config);\r\n\r\n          // Store the StartNode's collected parameters in the window object\r\n          // This will make them available to the ExecutionDialog component\r\n          window.startNodeCollectedParameters = startNode.data.config?.collected_parameters || {};\r\n          console.log(\r\n            \"Stored StartNode collected parameters in window object:\",\r\n            window.startNodeCollectedParameters,\r\n          );\r\n        } else {\r\n          console.log(\"No StartNode found in loaded workflow, this may cause issues\");\r\n          // Clear any previously stored parameters\r\n          window.startNodeCollectedParameters = {};\r\n        }\r\n\r\n        // Update the workflow state\r\n        console.log(\"Setting workflow state with:\", result.data);\r\n        setLoadedWorkflow(result.data);\r\n        setCurrentNodes(result.data.nodes);\r\n        setCurrentEdges(result.data.edges);\r\n\r\n        // Update the workflow title if available\r\n        if (workflowData.workflow_name) {\r\n          console.log(\r\n            \"Setting workflow title from workflowData.workflow_name:\",\r\n            workflowData.workflow_name,\r\n          );\r\n          setWorkflowTitle(workflowData.workflow_name);\r\n        } else {\r\n          console.log(\r\n            \"No workflow_name found in workflowData, keeping current title:\",\r\n            workflowTitle,\r\n          );\r\n        }\r\n\r\n        // Reset unsaved changes flag after successful load\r\n        setHasUnsavedChanges(false);\r\n\r\n        // Only show success message if explicitly requested\r\n        // This prevents showing alerts when loading from URL parameters\r\n        if (showSuccessMessage) {\r\n          // Use a temporary notification in the UI instead of an alert\r\n          setExecutionResult({\r\n            success: true,\r\n            message: \"Workflow loaded successfully!\",\r\n          });\r\n\r\n          // Auto-hide the notification after 3 seconds\r\n          setTimeout(() => {\r\n            setExecutionResult(null);\r\n          }, 3000);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading workflow:\", error);\r\n        // Use a more modern notification approach instead of alert\r\n        setExecutionResult({\r\n          success: false,\r\n          error: `Error loading workflow: ${error instanceof Error ? error.message : \"Unknown error\"}`,\r\n        });\r\n\r\n        // Auto-hide the error notification after 5 seconds\r\n        setTimeout(() => {\r\n          setExecutionResult(null);\r\n        }, 5000);\r\n      }\r\n    },\r\n    [workflowTitle],\r\n  ); // Include workflowTitle as a dependency\r\n\r\n  // Apply initial theme based on system preference\r\n  useEffect(() => {\r\n    const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\r\n    setIsDarkMode(prefersDark);\r\n    if (prefersDark) {\r\n      document.documentElement.classList.add(\"dark\");\r\n    }\r\n  }, []);\r\n\r\n  // Effect to add beforeunload event listener for unsaved changes\r\n  useEffect(() => {\r\n    const handleBeforeUnload = (e: BeforeUnloadEvent) => {\r\n      if (hasUnsavedChanges) {\r\n        // Standard way to show a confirmation dialog when leaving the page\r\n        const message = \"You have unsaved changes. Are you sure you want to leave?\";\r\n        e.preventDefault();\r\n        // Modern browsers require both preventDefault and setting returnValue\r\n        // Even though returnValue is deprecated, it's still required for this to work\r\n        // @ts-expect-error - Ignore the deprecation warning\r\n        e.returnValue = message;\r\n        return message;\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"beforeunload\", handleBeforeUnload);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"beforeunload\", handleBeforeUnload);\r\n    };\r\n  }, [hasUnsavedChanges]);\r\n\r\n  // Memoized callback for toggling sidebar\r\n  const handleToggleSidebar = useCallback(() => {\r\n    setIsSidebarCollapsed((prev) => !prev);\r\n  }, []);\r\n\r\n  // Effect to add keyboard shortcut for toggling sidebar\r\n  useEffect(() => {\r\n    const handleKeyDown = (e: KeyboardEvent) => {\r\n      // Toggle sidebar with Alt+S\r\n      if (e.altKey && e.key === \"s\") {\r\n        handleToggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"keydown\", handleKeyDown);\r\n    };\r\n  }, [handleToggleSidebar]);\r\n\r\n  // We'll use the WorkflowLoader component instead of this effect\r\n\r\n  return (\r\n    <main\r\n      className={`relative flex h-screen w-screen flex-col overflow-hidden ${isDarkMode ? \"dark\" : \"\"}`}\r\n    >\r\n      {/* Wrap the WorkflowLoader in a Suspense boundary */}\r\n      <Suspense fallback={<div>Loading workflow...</div>}>\r\n        <WorkflowLoader\r\n          onWorkflowLoad={handleLoadWorkflow}\r\n          onWorkflowIdChange={setWorkflowId}\r\n          onLoadingChange={setIsLoadingWorkflow}\r\n          onErrorChange={setWorkflowLoadError}\r\n          workflowId={workflowId}\r\n          loadedWorkflow={loadedWorkflow}\r\n        />\r\n      </Suspense>\r\n\r\n      <TopBar\r\n        onSave={handleSaveWorkflow}\r\n        onRun={handleRunWorkflow}\r\n        workflowTitle={workflowTitle}\r\n        onTitleChange={setWorkflowTitle}\r\n        onValidate={() => console.log(\"Validate workflow\")}\r\n        isDarkMode={isDarkMode}\r\n        onToggleTheme={handleToggleTheme}\r\n        onLoad={handleOpenLoadModal}\r\n        className=\"flex-shrink-0\"\r\n        nodes={currentNodes}\r\n        edges={currentEdges}\r\n      />\r\n\r\n      <div className=\"flex flex-grow overflow-hidden\">\r\n        <Sidebar\r\n          components={components}\r\n          collapsed={isSidebarCollapsed}\r\n          onToggleCollapse={handleToggleSidebar}\r\n        />\r\n        <div className=\"bg-background/50 flex flex-grow flex-col overflow-hidden backdrop-blur-sm\">\r\n          {isLoadingComponents && (\r\n            <div className=\"flex flex-grow items-center justify-center\">\r\n              <div className=\"flex animate-pulse flex-col items-center\">\r\n                <div className=\"bg-brand-primary/20 mb-4 h-12 w-12 rounded-full\"></div>\r\n                <div className=\"bg-brand-primary/20 h-4 w-48 rounded\"></div>\r\n                <div className=\"font-secondary text-brand-secondary-font mt-2 text-sm\">\r\n                  Loading components...\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          {componentError && (\r\n            <div className=\"flex flex-grow items-center justify-center\">\r\n              <div className=\"border-brand-unpublish/30 bg-brand-unpublish/10 max-w-md rounded-lg border p-6 text-center\">\r\n                <div className=\"font-primary text-brand-unpublish mb-2 font-medium\">\r\n                  Error Loading Components\r\n                </div>\r\n                <div className=\"font-secondary text-brand-secondary-font text-sm\">\r\n                  {componentError}\r\n                </div>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"border-brand-unpublish/30 text-brand-unpublish hover:bg-brand-unpublish/10 mt-4 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium\"\r\n                  onClick={() => window.location.reload()}\r\n                >\r\n                  Retry\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n          {isLoadingWorkflow && (\r\n            <div className=\"flex flex-grow items-center justify-center\">\r\n              <div className=\"border-brand-stroke bg-brand-card-hover max-w-md rounded-lg border p-6 text-center\">\r\n                <Loader2 className=\"text-brand-primary mx-auto mb-4 h-8 w-8 animate-spin\" />\r\n                <div className=\"font-primary text-brand-primary-font dark:text-brand-white-text mb-2 font-medium\">\r\n                  Loading Workflow\r\n                </div>\r\n                <div className=\"font-secondary text-brand-secondary-font text-sm\">\r\n                  Please wait while we load your workflow...\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {workflowLoadError && (\r\n            <div className=\"flex flex-grow items-center justify-center\">\r\n              <div className=\"border-brand-unpublish/30 bg-brand-unpublish/10 max-w-md rounded-lg border p-6 text-center\">\r\n                <div className=\"font-primary text-brand-unpublish mb-2 font-medium\">\r\n                  Error Loading Workflow\r\n                </div>\r\n                <div className=\"font-secondary text-brand-secondary-font mb-4 text-sm\">\r\n                  {workflowLoadError}\r\n                </div>\r\n                <div className=\"flex justify-center gap-2\">\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"border-brand-unpublish/30 text-brand-unpublish hover:bg-brand-unpublish/10 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium\"\r\n                    onClick={() => window.location.reload()}\r\n                  >\r\n                    Try Again\r\n                  </button>\r\n                  <Link href=\"/home\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"brand-gradient-indicator text-brand-white-text inline-flex h-8 items-center justify-center rounded-md px-3 py-2 text-sm font-medium\"\r\n                    >\r\n                      Back to Home\r\n                    </button>\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {!isLoadingComponents && !componentError && !isLoadingWorkflow && !workflowLoadError && (\r\n            <WorkflowCanvasWrapper\r\n              onFlowChange={handleFlowChange}\r\n              initialWorkflow={loadedWorkflow || undefined}\r\n            />\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Execution status overlay with animation */}\r\n      {isExecuting && (\r\n        <div className=\"brand-gradient-indicator text-brand-white-text animate-in fade-in slide-in-from-bottom-4 absolute bottom-4 left-4 z-50 rounded-lg p-3 text-sm shadow-lg\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"h-2 w-2 animate-ping rounded-full bg-white/80\"></div>\r\n            <span className=\"font-primary\">Executing workflow...</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Result notification */}\r\n      {executionResult && !isExecuting && (\r\n        <div\r\n          className={`animate-in fade-in slide-in-from-bottom-4 absolute right-4 bottom-4 z-50 rounded-lg p-3 text-sm shadow-lg ${executionResult.success ? \"bg-brand-tick text-white\" : \"bg-brand-unpublish text-white\"}`}\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            {executionResult.success ? (\r\n              <span className=\"font-primary\">Workflow executed successfully!</span>\r\n            ) : (\r\n              <span className=\"font-primary\">Error: {executionResult.error}</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Load Workflow Modal */}\r\n      <LoadWorkflowModal\r\n        isOpen={isLoadModalOpen}\r\n        onClose={() => setIsLoadModalOpen(false)}\r\n        onLoadWorkflow={handleLoadWorkflow}\r\n      />\r\n\r\n      {/* Unsaved Changes Dialog */}\r\n      <UnsavedChangesDialog\r\n        isOpen={isUnsavedChangesDialogOpen}\r\n        onClose={() => setIsUnsavedChangesDialogOpen(false)}\r\n        onContinue={() => {\r\n          setIsUnsavedChangesDialogOpen(false);\r\n          if (pendingAction) {\r\n            pendingAction();\r\n            setPendingAction(null);\r\n          }\r\n        }}\r\n      />\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;AAI5B;AACA;AACA;AACA;AACA;AACA;AAGA;AAMA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAxBA,cAAc,8CAA8C;;;;;;;;;;;;;;;;;;AAsC5D,qEAAqE;AACrE,SAAS,eAAe,EACtB,cAAc,EACd,kBAAkB,EAClB,eAAe,EACf,aAAa,EACb,UAAU,EACV,cAAc,EAWf;IACC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,MAAM,KAAK,aAAa,GAAG,CAAC;YAC5B,IAAI,CAAC,IAAI;gBACP,2DAA2D;gBAC3D,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,gDAAgD;YAChD,IAAI,OAAO,cAAc,gBAAgB;gBACvC,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,mBAAmB;YACnB,gBAAgB;YAChB,cAAc;YAEd,IAAI;gBACF,0DAA0D;gBAE1D,gDAAgD;gBAChD,MAAM,kBAAmC,MAAM,CAAA,GAAA,8IAAA,CAAA,oBAAiB,AAAD,EAAE;gBACjE,QAAQ,GAAG,CAAC,qBAAqB;gBAEjC,IACE,CAAC,mBACD,CAAC,gBAAgB,QAAQ,IACzB,CAAC,gBAAgB,QAAQ,CAAC,WAAW,EACrC;oBACA,QAAQ,KAAK,CACX,wDACA,iBAAiB,UAAU;oBAE7B,MAAM,IAAI,MAAM;gBAClB;gBAEA,kDAAkD;gBAClD,MAAM,eAA6B,MAAM,CAAA,GAAA,yIAAA,CAAA,8BAA2B,AAAD,EACjE,gBAAgB,QAAQ,CAAC,WAAW;gBAGtC,sDAAsD;gBACtD,IAAI,CAAC,aAAa,aAAa,IAAI,gBAAgB,QAAQ,CAAC,IAAI,EAAE;oBAChE,QAAQ,GAAG,CAAC,+CAA+C,gBAAgB,QAAQ,CAAC,IAAI;oBACxF,aAAa,aAAa,GAAG,gBAAgB,QAAQ,CAAC,IAAI;gBAC5D;gBAEA,QAAQ,GAAG,CAAC,sCAAsC;gBAElD,oEAAoE;gBACpE,+EAA+E;gBAC/E,eAAe,cAAc;YAC/B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,cACE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAE7C,SAAU;gBACR,gBAAgB;YAClB;QACF;QAEA;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,MAAM,yCAAyC;AACxD;AAEe,SAAS;IACtB,qFAAqF;IAErF,+BAA+B;IAC/B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,CAAC;IAC7E,sEAAsE;IACtE,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,gBAAgB;QAAC;KAAe;IACjE,qBAAqB;IACrB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,aAAa;IACnF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,aAAa;IAExF,gCAAgC;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC7E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAE3D,qCAAqC;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,6BAA6B;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAE/E,6BAA6B;IAC7B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGzC;IAEV,6BAA6B;IAC7B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,mCAAmC;IACnC,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAExE,6CAA6C;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,qCAAqC;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,uBAAuB,OAAO,wBAAwB;gBACtD,kBAAkB,OAAO,wBAAwB;gBAEjD,2BAA2B;gBAC3B,MAAM,oBAAoB,MAAM,CAAA,GAAA,iHAAA,CAAA,kBAAe,AAAD;gBAC9C,QAAQ,GAAG,CACT,iDACA,OAAO,IAAI,CAAC;gBAGd,kCAAkC;gBAClC,IAAI,kBAAkB,EAAE,EAAE;oBACxB,QAAQ,GAAG,CAAC,+BAA+B,OAAO,IAAI,CAAC,kBAAkB,EAAE;gBAC7E,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;gBAEA,uBAAuB;gBACvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD;gBAC7C,QAAQ,GAAG,CAAC,kCAAkC;gBAE9C,gDAAgD;gBAChD,QAAQ,GAAG,CAAC,oCAAoC;gBAChD,IAAI,cAAc,GAAG,EAAE;oBACrB,QAAQ,GAAG,CACT,+CACA,OAAO,IAAI,CAAC,cAAc,GAAG;oBAE/B,QAAQ,GAAG,CAAC,+BAA+B,OAAO,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,EAAE;gBAChF,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;gBAEA,uBAAuB;gBACvB,MAAM,mBAAmB;oBACvB,GAAG,iBAAiB;oBACpB,GAAG,aAAa;gBAClB;gBAEA,wCAAwC;gBACxC,IAAI,kBAAkB,GAAG,IAAI,cAAc,GAAG,EAAE;oBAC9C,QAAQ,GAAG,CACT;oBAEF,iBAAiB,GAAG,GAAG;wBACrB,GAAG,kBAAkB,GAAG;wBACxB,GAAG,cAAc,GAAG;oBACtB;gBACF;gBAEA,QAAQ,GAAG,CAAC,wCAAwC,OAAO,IAAI,CAAC;gBAChE,QAAQ,GAAG,CACT,sCACA,iBAAiB,GAAG,GAAG,OAAO,IAAI,CAAC,iBAAiB,GAAG,EAAE,MAAM,GAAG;gBAEpE,kBAAkB;YACpB,EAAE,OAAO,KAAK;gBACZ,MAAM,WAAW,eAAe,QAAQ,IAAI,OAAO,GAAG;gBACtD,kBAAkB,CAAC,2BAA2B,EAAE,SAAS,gCAAgC,CAAC,GAAG,wBAAwB;gBACrH,QAAQ,KAAK,CAAC;YAChB,SAAU;gBACR,uBAAuB,QAAQ,wBAAwB;YACzD;QACF;QACA,QAAQ,GAAG,CAAC;QACZ;IACF,GAAG,EAAE,GAAG,yDAAyD;IAEjE,iDAAiD;IACjD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAiC;QACrE,gBAAgB;QAChB,gBAAgB;QAChB,0DAA0D;QAC1D,qBAAqB;IACrB,kEAAkE;IACpE,GAAG,EAAE,GAAG,uDAAuD;IAE/D,yCAAyC;IACzC,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7C,IAAI;YACF,4CAA4C;YAC5C,MAAM,WAAW,GAAG,cAAc,OAAO,CAAC,QAAQ,MAAM;YAExD,0CAA0C;YAC1C,MAAM,SAAS,MAAM,CAAA,GAAA,iHAAA,CAAA,uBAAoB,AAAD,EAAE;gBACxC,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,eAAe;gBACf,GAAI,cAAc;oBAAE,aAAa;gBAAW,CAAC;YAC/C;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,qEAAqE;gBACrE,IAAI,CAAC,cAAc,OAAO,WAAW,EAAE;oBACrC,QAAQ,GAAG,CAAC,qCAAqC,OAAO,WAAW;oBACnE,cAAc,OAAO,WAAW;oBAChC,sCAAsC;oBACtC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,OAAO,WAAW,EAAE;gBAC1E;gBAEA,6BAA6B;gBAC7B,qBAAqB;gBAErB,gFAAgF;gBAChF,MAAM,OAAO,OAAO,IAAI,CAAC,0CAA0C,EAAE,OAAO,QAAQ,EAAE;YACxF,OAAO;gBACL,MAAM,CAAC,mCAAmC,EAAE,OAAO,KAAK,IAAI,iBAAiB;YAC/E;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,WAAW,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;YAC7D,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,CAAC,iCAAiC,EAAE,UAAU;QACtD;IACF,GAAG;QAAC;QAAc;QAAc;QAAe;KAAW,GAAG,oCAAoC;IAEjG,kEAAkE;IAClE,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,8BAA8B;YAC9B,MAAM,EAAE,kBAAkB,EAAE,GAAG,+HAAA,CAAA,qBAAkB,CAAC,QAAQ;YAC1D,MAAM,mBAAmB,MAAM,mBAAmB,cAAc;YAEhE,IAAI,CAAC,iBAAiB,OAAO,EAAE;gBAC7B,yBAAyB;gBACzB,MAAM,gBAAgB,iBAAiB,MAAM,CAAC,GAAG,CAAC,CAAC,MAAQ,IAAI,OAAO,EAAE,IAAI,CAAC;gBAC7E,MAAM,CAAC,qDAAqD,EAAE,eAAe;gBAC7E;YACF;YAEA,gCAAgC;YAChC,MAAM,WAAW,GAAG,cAAc,OAAO,CAAC,QAAQ,MAAM;YACxD,MAAM,SAAS,MAAM,CAAA,GAAA,iHAAA,CAAA,uBAAoB,AAAD,EAAE;gBACxC,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,eAAe;gBACf,GAAI,cAAc;oBAAE,aAAa;gBAAW,CAAC;YAC/C;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,qEAAqE;gBACrE,IAAI,CAAC,cAAc,OAAO,WAAW,EAAE;oBACrC,QAAQ,GAAG,CAAC,qCAAqC,OAAO,WAAW;oBACnE,cAAc,OAAO,WAAW;oBAChC,sCAAsC;oBACtC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,OAAO,WAAW,EAAE;gBAC1E;gBAEA,6BAA6B;gBAC7B,qBAAqB;gBAErB,uBAAuB;gBACvB,MAAM,OAAO,OAAO,IAAI;YAC1B,OAAO;gBACL,qBAAqB;gBACrB,MAAM,CAAC,yBAAyB,EAAE,OAAO,KAAK,IAAI,iBAAiB;YACrE;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,WAAW,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;YAC7D,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,CAAC,uBAAuB,EAAE,UAAU;QAC5C;IACF,GAAG;QAAC;QAAc;QAAc;QAAe;KAAW,GAAG,oCAAoC;IAEjG,sCAAsC;IACtC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI,aAAa,QAAQ,qCAAqC;QAE9D,eAAe;QACf,mBAAmB,OAAO,yBAAyB;QAEnD,IAAI;YACF,8BAA8B;YAC9B,MAAM,EAAE,uBAAuB,EAAE,GAAG,+HAAA,CAAA,qBAAkB,CAAC,QAAQ;YAC/D,MAAM,mBAAmB,MAAM,wBAAwB,cAAc;YAErE,IAAI,CAAC,iBAAiB,OAAO,EAAE;gBAC7B,yBAAyB;gBACzB,MAAM,gBAAgB,iBAAiB,MAAM,CAAC,GAAG,CAAC,CAAC,MAAQ,IAAI,OAAO,EAAE,IAAI,CAAC;gBAC7E,MAAM,CAAC,qDAAqD,EAAE,eAAe;gBAC7E,eAAe;gBACf;YACF;YAEA,0BAA0B;YAC1B,MAAM,WAAW,GAAG,cAAc,OAAO,CAAC,QAAQ,MAAM;YACxD,MAAM,aAAa,MAAM,CAAA,GAAA,iHAAA,CAAA,uBAAoB,AAAD,EAAE;gBAC5C,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,eAAe;gBACf,GAAI,cAAc;oBAAE,aAAa;gBAAW,CAAC;YAC/C;YAEA,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,CAAC,0CAA0C,EAAE,WAAW,KAAK,IAAI,iBAAiB;gBACxF,eAAe;gBACf;YACF;YAEA,gDAAgD;YAChD,IAAI,oBAAoB;YACxB,IAAI,CAAC,qBAAqB,WAAW,WAAW,EAAE;gBAChD,oBAAoB,WAAW,WAAW;gBAC1C,cAAc;gBACd,sCAAsC;gBACtC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,mBAAmB;YACzE;YAEA,mDAAmD;YACnD,qBAAqB;YAErB,0BAA0B;YAC1B,MAAM,iBAAiB,8HAAA,CAAA,oBAAiB,CAAC,QAAQ;YAEjD,oDAAoD;YACpD,IAAI,iBAAiB,aAAa,EAAE;gBAClC,eAAe,gBAAgB,CAAC,iBAAiB,aAAa;YAChE;YAEA,4BAA4B;YAC5B,eAAe,aAAa,CAAC;YAC7B,eAAe,YAAY,CAAC;YAE5B,kBAAkB;YAClB,eAAe,MAAM,CAAC;YAEtB,mEAAmE;YACnE,eAAe;QAEf,wEAAwE;QACxE,sDAAsD;QACxD,EAAE,OAAO,KAAK;YACZ,0BAA0B;YAC1B,MAAM,WAAW,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;YAC7D,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,mBAAmB;gBAAE,SAAS;gBAAO,OAAO,CAAC,gBAAgB,EAAE,UAAU;YAAC;YAC1E,MAAM,CAAC,wCAAwC,EAAE,UAAU;YAC3D,eAAe;QACjB;IACF,GAAG;QAAC;QAAc;QAAc;QAAa;QAAY;QAAe;KAAqB,GAAG,eAAe;IAE/G,uBAAuB;IACvB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,cAAc,CAAC,OAAS,CAAC;QACzB,wCAAwC;QACxC,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;IAC5C,GAAG,EAAE;IAEL,gEAAgE;IAChE,MAAM,iCAAiC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC/C,CAAC;QACC,IAAI,mBAAmB;YACrB,6DAA6D;YAC7D,iBAAiB,IAAM;YACvB,8BAA8B;QAChC,OAAO;YACL,iDAAiD;YACjD;QACF;IACF,GACA;QAAC;KAAkB;IAGrB,8CAA8C;IAC9C,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,+BAA+B;YAC7B,mBAAmB;QACrB;IACF,GAAG;QAAC;KAA+B;IAEnC,iCAAiC;IACjC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC,cAA4B,qBAA8B,IAAI;QAC7D,IAAI;YACF,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,yCAAyC;YACzC,MAAM,SAAS,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD,EAAE;YACpC,QAAQ,GAAG,CAAC,sBAAsB;YAElC,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE;gBACnC,2DAA2D;gBAC3D,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,KAAK,IAAI,yBAAyB;gBACnF;YACF;YAEA,2EAA2E;YAC3E,iFAAiF;YAEjF,uFAAuF;YACvF,MAAM,QAAQ,OAAO,IAAI,CAAC,KAAK;YAC/B,MAAM,YAAY,MAAM,IAAI,CAC1B,CAAC,OAAiC,KAAK,IAAI,CAAC,YAAY,KAAK;YAG/D,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC,uCAAuC;gBAEnD,qEAAqE;gBACrE,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,EAAE;oBAC1B,UAAU,IAAI,CAAC,MAAM,GAAG,CAAC;gBAC3B;gBAEA,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;oBAC/C,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG,CAAC;gBAChD;gBAEA,+EAA+E;gBAC/E,wFAAwF;gBACxF,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;oBAC9C,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;wBAC/D,MAAM,QAAQ,UAAU,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ;wBACjE,uFAAuF;wBACvF,IAAI,MAAM,QAAQ,KAAK,WAAW;4BAChC,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,QAAQ,aAAa,CAAC;4BACzE,MAAM,QAAQ,GAAG;wBACnB;oBACF;gBACF;gBAEA,QAAQ,GAAG,CAAC,8CAA8C,UAAU,IAAI,CAAC,MAAM;gBAE/E,kEAAkE;gBAClE,iEAAiE;gBACjE,OAAO,4BAA4B,GAAG,UAAU,IAAI,CAAC,MAAM,EAAE,wBAAwB,CAAC;gBACtF,QAAQ,GAAG,CACT,2DACA,OAAO,4BAA4B;YAEvC,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,yCAAyC;gBACzC,OAAO,4BAA4B,GAAG,CAAC;YACzC;YAEA,4BAA4B;YAC5B,QAAQ,GAAG,CAAC,gCAAgC,OAAO,IAAI;YACvD,kBAAkB,OAAO,IAAI;YAC7B,gBAAgB,OAAO,IAAI,CAAC,KAAK;YACjC,gBAAgB,OAAO,IAAI,CAAC,KAAK;YAEjC,yCAAyC;YACzC,IAAI,aAAa,aAAa,EAAE;gBAC9B,QAAQ,GAAG,CACT,2DACA,aAAa,aAAa;gBAE5B,iBAAiB,aAAa,aAAa;YAC7C,OAAO;gBACL,QAAQ,GAAG,CACT,kEACA;YAEJ;YAEA,mDAAmD;YACnD,qBAAqB;YAErB,oDAAoD;YACpD,gEAAgE;YAChE,IAAI,oBAAoB;gBACtB,6DAA6D;gBAC7D,mBAAmB;oBACjB,SAAS;oBACT,SAAS;gBACX;gBAEA,6CAA6C;gBAC7C,WAAW;oBACT,mBAAmB;gBACrB,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2DAA2D;YAC3D,mBAAmB;gBACjB,SAAS;gBACT,OAAO,CAAC,wBAAwB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAC9F;YAEA,mDAAmD;YACnD,WAAW;gBACT,mBAAmB;YACrB,GAAG;QACL;IACF,GACA;QAAC;KAAc,GACd,wCAAwC;IAE3C,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;QAC7E,cAAc;QACd,IAAI,aAAa;YACf,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC;IACF,GAAG,EAAE;IAEL,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,mBAAmB;gBACrB,mEAAmE;gBACnE,MAAM,UAAU;gBAChB,EAAE,cAAc;gBAChB,sEAAsE;gBACtE,8EAA8E;gBAC9E,oDAAoD;gBACpD,EAAE,WAAW,GAAG;gBAChB,OAAO;YACT;QACF;QAEA,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,OAAO;YACL,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG;QAAC;KAAkB;IAEtB,yCAAyC;IACzC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,sBAAsB,CAAC,OAAS,CAAC;IACnC,GAAG,EAAE;IAEL,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,4BAA4B;YAC5B,IAAI,EAAE,MAAM,IAAI,EAAE,GAAG,KAAK,KAAK;gBAC7B;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;KAAoB;IAExB,gEAAgE;IAEhE,qBACE,8OAAC;QACC,WAAW,CAAC,yDAAyD,EAAE,aAAa,SAAS,IAAI;;0BAGjG,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;8BAAI;;;;;;0BACvB,cAAA,8OAAC;oBACC,gBAAgB;oBAChB,oBAAoB;oBACpB,iBAAiB;oBACjB,eAAe;oBACf,YAAY;oBACZ,gBAAgB;;;;;;;;;;;0BAIpB,8OAAC,sIAAA,CAAA,SAAM;gBACL,QAAQ;gBACR,OAAO;gBACP,eAAe;gBACf,eAAe;gBACf,YAAY,IAAM,QAAQ,GAAG,CAAC;gBAC9B,YAAY;gBACZ,eAAe;gBACf,QAAQ;gBACR,WAAU;gBACV,OAAO;gBACP,OAAO;;;;;;0BAGT,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uIAAA,CAAA,UAAO;wBACN,YAAY;wBACZ,WAAW;wBACX,kBAAkB;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;;4BACZ,qCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDAAwD;;;;;;;;;;;;;;;;;4BAM5E,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqD;;;;;;sDAGpE,8OAAC;4CAAI,WAAU;sDACZ;;;;;;sDAEH,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;sDACtC;;;;;;;;;;;;;;;;;4BAMN,mCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAI,WAAU;sDAAmF;;;;;;sDAGlG,8OAAC;4CAAI,WAAU;sDAAmD;;;;;;;;;;;;;;;;;4BAOvE,mCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqD;;;;;;sDAGpE,8OAAC;4CAAI,WAAU;sDACZ;;;;;;sDAEH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;8DACtC;;;;;;8DAGD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC;wDACC,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BASV,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,mCACjE,8OAAC,8IAAA,CAAA,UAAqB;gCACpB,cAAc;gCACd,iBAAiB,kBAAkB;;;;;;;;;;;;;;;;;;YAO1C,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAe;;;;;;;;;;;;;;;;;YAMpC,mBAAmB,CAAC,6BACnB,8OAAC;gBACC,WAAW,CAAC,0GAA0G,EAAE,gBAAgB,OAAO,GAAG,6BAA6B,iCAAiC;0BAEhN,cAAA,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,OAAO,iBACtB,8OAAC;wBAAK,WAAU;kCAAe;;;;;6CAE/B,8OAAC;wBAAK,WAAU;;4BAAe;4BAAQ,gBAAgB,KAAK;;;;;;;;;;;;;;;;;0BAOpE,8OAAC,iJAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,gBAAgB;;;;;;0BAIlB,8OAAC,oJAAA,CAAA,uBAAoB;gBACnB,QAAQ;gBACR,SAAS,IAAM,8BAA8B;gBAC7C,YAAY;oBACV,8BAA8B;oBAC9B,IAAI,eAAe;wBACjB;wBACA,iBAAiB;oBACnB;gBACF;;;;;;;;;;;;AAIR", "debugId": null}}]}