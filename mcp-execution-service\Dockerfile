FROM python:3.11-slim

# Set environment variables (customize if needed)
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    POETRY_VERSION=1.7.1 \
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_CREATE=false


# Install system dependencies (customize if needed - build-essential often needed)
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install poetry
RUN curl -sSL https://install.python-poetry.org | python3 -

# Add poetry to PATH
ENV PATH="$POETRY_HOME/bin:$PATH"

# Set working directory (customize if needed - usually /app is good)
WORKDIR /app

# Copy poetry files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry install --no-interaction --no-ansi --no-root

# Copy application code (customize these COPY lines based on your project structure)
COPY app /app/app  

# Run the application (customize the entrypoint)

CMD ["poetry", "run", "python", "-m", "app.main"]