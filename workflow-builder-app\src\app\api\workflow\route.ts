/**
 * Workflow API Route Handler
 *
 * This file provides API routes for workflow operations, acting as a server-side
 * proxy between the client and the external workflow API.
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import {
  fetchWorkflowsByUser,
  fetchWorkflowById,
  createEmptyWorkflow,
  fetchWorkflowFromBuilderUrl,
  deleteWorkflow
} from '@/app/(features)/workflows/api';
import { getAccessToken } from '@/lib/cookies';

/**
 * GET handler for fetching workflows
 * Supports:
 * - /api/workflow - Fetch all workflows for the current user
 * - /api/workflow?id=123 - Fetch a specific workflow by ID
 * - /api/workflow?builderUrl=https://... - Fetch workflow from builder URL
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const builderUrl = searchParams.get('builderUrl');
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const pageSize = searchParams.get('pageSize') ? parseInt(searchParams.get('pageSize')!) : 10;

    // Get access token from cookies
    const accessToken = await getAccessToken();

    // Case 1: Fetch workflow by ID
    if (id) {
      const workflow = await fetchWorkflowById(id);
      return NextResponse.json(workflow);
    }

    // Case 2: Fetch workflow from builder URL
    if (builderUrl) {
      const workflow = await fetchWorkflowFromBuilderUrl(builderUrl);
      return NextResponse.json(workflow);
    }

    // Case 3: Fetch all workflows for the current user
    const workflows = await fetchWorkflowsByUser(page, pageSize, accessToken);
    return NextResponse.json(workflows);
  } catch (error: any) {
    console.error('Error in workflow API route:', error);
    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch workflows',
        details: error.response?.data || null
      },
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * POST handler for creating a new workflow
 */
export async function POST(request: NextRequest) {
  try {
    // For now, we'll just create an empty workflow
    // In the future, we could accept custom workflow data from the request body
    const workflow = await createEmptyWorkflow();
    return NextResponse.json(workflow);
  } catch (error: any) {
    console.error('Error creating workflow:', error);
    return NextResponse.json(
      {
        error: error.message || 'Failed to create workflow',
        details: error.response?.data || null
      },
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * DELETE handler for deleting a workflow
 */
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Missing workflow ID' },
        { status: 400 }
      );
    }

    const result = await deleteWorkflow(id);
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error deleting workflow:', error);
    return NextResponse.json(
      {
        error: error.message || 'Failed to delete workflow',
        details: error.response?.data || null
      },
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
