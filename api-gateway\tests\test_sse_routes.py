import pytest
from fastapi.testclient import TestClient
from app.main import app

class TestSseRoutes:
    @pytest.fixture(autouse=True)
    def setup(self):
        self.client = TestClient(app)

    def test_trigger_sample_event(self):
        client_id = "test_client"
        response = self.client.post(
            f"/api/v1/sse/trigger-event/{client_id}"
        )
        assert response.status_code == 200
        assert "status" in response.json()

    def test_trigger_broadcast_event(self):
        response = self.client.post("/api/v1/sse/trigger-broadcast-event")
        assert response.status_code == 200
        assert "status" in response.json()