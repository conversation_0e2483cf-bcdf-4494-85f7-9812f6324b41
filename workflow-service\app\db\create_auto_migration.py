#!/usr/bin/env python
"""
Automatic migration generator script.
Usage: python -m app.db.create_auto_migration "description of migration"
"""

import sys
import subprocess


def create_auto_migration(description):
    # Format the description for command line
    formatted_desc = description.replace(" ", "_").lower()

    try:
        # First, make sure the database is up to date with existing migrations
        print("Ensuring database is up to date with existing migrations...")
        subprocess.run(["alembic", "upgrade", "head"], check=True, capture_output=True, text=True)

        # Now create the new migration
        print("Creating new migration...")
        result = subprocess.run(
            ["alembic", "revision", "--autogenerate", "-m", formatted_desc],
            check=True,
            capture_output=True,
            text=True,
        )

        print("Migration created successfully!")
        print(result.stdout)

        # Extract the revision file path from output
        for line in result.stdout.split("\n"):
            if "Generating" in line and ".py" in line:
                file_path = line.split("Generating ")[1].strip()
                print(f"Generated migration file: {file_path}")
                print("Review the migration file to ensure it correctly captures your changes.")
                break

    except subprocess.CalledProcessError as e:
        print(f"Error creating migration: {e}")
        print(f"Error output: {e.stderr}")
        sys.exit(1)


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print('Usage: python -m app.db.create_auto_migration "description of migration"')
        sys.exit(1)

    description = sys.argv[1]
    create_auto_migration(description)
