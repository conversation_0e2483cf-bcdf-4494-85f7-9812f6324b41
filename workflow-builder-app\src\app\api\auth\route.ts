/**
 * Authentication API Route Handler
 *
 * This file provides API routes for authentication operations, acting as a server-side
 * proxy between the client and the external authentication API.
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { authApi } from '@/lib/authApi';
import { setAuthCookies, clearAuthCookies } from '@/lib/cookies';

/**
 * POST handler for authentication operations
 * Supports:
 * - /api/auth/login - Login with email and password
 * - /api/auth/signup - Register a new user
 * - /api/auth/logout - Logout the current user
 * - /api/auth/refresh - Refresh the access token
 */
export async function POST(request: NextRequest) {
  try {
    const { action, ...data } = await request.json();

    switch (action) {
      case 'login': {
        const result = await authApi.login(data);

        // Set cookies on the server side
        if (result.loginData.access_token) {
          await setAuthCookies(
            result.loginData.access_token,
            result.loginData.refresh_token,
            result.loginData.accessTokenAge || 3600,
            result.loginData.refreshTokenAge || 86400
          );
        }

        return NextResponse.json({
          success: true,
          redirectPath: result.redirectPath || '/workflows'
        });
      }

      case 'signup': {
        const result = await authApi.signup(data);
        return NextResponse.json({
          success: true,
          message: result.message || 'Registration successful'
        });
      }

      case 'logout': {
        await clearAuthCookies();
        return NextResponse.json({
          success: true,
          message: 'Logged out successfully'
        });
      }

      case 'refresh': {
        const cookieStore = await cookies();
        const refreshToken = cookieStore.get('refreshToken')?.value;

        if (!refreshToken) {
          return NextResponse.json(
            { success: false, message: 'No refresh token found' },
            { status: 401 }
          );
        }

        const result = await authApi.generateAccessToken(refreshToken);

        if (result.success && result.access_token) {
          // Set the new access token in cookies
          await setAuthCookies(
            result.access_token,
            refreshToken,
            3600, // Default token age
            86400 // Default refresh token age
          );

          return NextResponse.json({
            success: true,
            message: 'Token refreshed successfully'
          });
        } else {
          return NextResponse.json(
            { success: false, message: 'Failed to refresh token' },
            { status: 401 }
          );
        }
      }

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('Error in auth API route:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Authentication failed',
        details: error.response?.data || null
      },
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * GET handler for retrieving the current user
 */
export async function GET() {
  try {
    const user = await authApi.getCurrentUser();
    return NextResponse.json({ success: true, user });
  } catch (error: any) {
    console.error('Error fetching current user:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to fetch user',
        details: error.response?.data || null
      },
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
