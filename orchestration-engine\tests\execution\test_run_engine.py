import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock, mock_open
from app.execution.run_engine import run_engine
from app.core_.executor_core import EnhancedWorkflowEngine
from app.services.kafka_tool_executor import KafkaToolExecutor
from aiokafka import AIOKafkaProducer

class TestRunEngine:
    """
    Test suite for run_engine module.
    Tests workflow initialization, execution, and Kafka integration.
    """

    @pytest.fixture
    def mock_workflow_json(self):
        """
        Provides a mock workflow JSON configuration.
        """
        return {
            "name": "test_workflow",
            "version": "1.0",
            "nodes": [],
            "transitions": []
        }

    @pytest.fixture
    def mock_workflow_content(self):
        """
        Provides mock workflow content with parameters.
        """
        return {
            "workflow_id": "ciny",
            "payload": {
                "user_dependent_fields": ["topic", "video_type"],
                "user_payload_template": {
                    "topic": "latest nvidia event updates",
                    "video_type": "SHORT",
                }
            }
        }

    @pytest.mark.asyncio
    async def test_run_engine_successful_execution(self, mock_workflow_json, mock_workflow_content):
        """
        Test successful workflow engine execution with proper initialization.
        """
        # Mock <PERSON>fka producer
        mock_producer = AsyncMock(spec=AIOKafkaProducer)
        
        # Mock file operations
        mock_file = mock_open(read_data=json.dumps(mock_workflow_json))
        
        # Mock necessary components
        with patch('app.execution.run_engine.AIOKafkaProducer', return_value=mock_producer), \
             patch('app.execution.run_engine.KafkaToolExecutor') as mock_tool_executor, \
             patch('app.execution.run_engine.EnhancedWorkflowEngine') as mock_engine, \
             patch('builtins.open', mock_file), \
             patch('json.load', return_value=mock_workflow_json), \
             patch('app.execution.run_engine.initialize_workflow_with_params') as mock_init:

            # Setup mock returns
            mock_tool_executor_instance = AsyncMock(spec=KafkaToolExecutor)
            mock_tool_executor.return_value = mock_tool_executor_instance
            
            mock_engine_instance = AsyncMock(spec=EnhancedWorkflowEngine)
            mock_engine.return_value = mock_engine_instance
            
            # Execute run_engine
            await run_engine()

            # Verify producer initialization
            mock_producer.start.assert_called_once()
            
            # Verify tool executor initialization and start
            mock_tool_executor.assert_called_once_with(producer=mock_producer)
            mock_tool_executor_instance.start.assert_called_once()
            
            # Verify workflow initialization
            mock_init.assert_called_once()
            
            # Verify engine execution
            mock_engine_instance.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_engine_with_result_callback(self, mock_workflow_json):
        """
        Test run_engine with result callback functionality.
        """
        mock_producer = AsyncMock(spec=AIOKafkaProducer)
        mock_file = mock_open(read_data=json.dumps(mock_workflow_json))
        
        with patch('app.execution.run_engine.AIOKafkaProducer', return_value=mock_producer), \
             patch('app.execution.run_engine.KafkaToolExecutor') as mock_tool_executor, \
             patch('app.execution.run_engine.EnhancedWorkflowEngine') as mock_engine, \
             patch('builtins.open', mock_file), \
             patch('json.load', return_value=mock_workflow_json), \
             patch('app.execution.run_engine.initialize_workflow_with_params') as mock_init, \
             patch('app.execution.run_engine.get_logger') as mock_logger:

            logger_instance = MagicMock()
            mock_logger.return_value = logger_instance
            
            # Execute run_engine
            await run_engine()
            
            # Get the callback function that was passed to EnhancedWorkflowEngine
            callback_func = mock_engine.call_args[1]['result_callback']
            
            # Test the callback
            test_result = {"status": "completed", "data": "test"}
            await callback_func(test_result)
            
            # Verify logger was called with result info
            logger_instance.info.assert_called_with("result_info", test_result)

    @pytest.mark.asyncio
    async def test_run_engine_producer_configuration(self):
        """
        Test Kafka producer configuration in run_engine.
        """
        with patch('app.execution.run_engine.AIOKafkaProducer') as mock_producer_class, \
             patch('app.execution.run_engine.settings') as mock_settings, \
             patch('builtins.open', mock_open()), \
             patch('json.load'), \
             patch('app.execution.run_engine.KafkaToolExecutor'), \
             patch('app.execution.run_engine.EnhancedWorkflowEngine'):

            mock_settings.kafka_bootstrap_servers = "test-broker:9092"
            
            await run_engine()
            
            # Verify producer initialization with correct parameters
            mock_producer_class.assert_called_once_with(
                bootstrap_servers="test-broker:9092",
                max_request_size=524288000,
                value_serializer=mock_producer_class.call_args[1]['value_serializer']
            )

    @pytest.mark.asyncio
    async def test_run_engine_file_handling(self, mock_workflow_json):
        """
        Test workflow file loading and handling in run_engine.
        """
        mock_producer = AsyncMock(spec=AIOKafkaProducer)
        mock_file = mock_open(read_data=json.dumps(mock_workflow_json))
        
        with patch('app.execution.run_engine.AIOKafkaProducer', return_value=mock_producer), \
             patch('app.execution.run_engine.KafkaToolExecutor'), \
             patch('app.execution.run_engine.EnhancedWorkflowEngine'), \
             patch('builtins.open', mock_file) as mock_open_file, \
             patch('json.load', return_value=mock_workflow_json) as mock_json_load:

            await run_engine()
            
            # Verify file operations
            mock_open_file.assert_called_once_with("./app/shared/json_schemas/ciny.json", "r")
            mock_json_load.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_engine_error_handling(self):
        """
        Test error handling in run_engine.
        """
        with patch('app.execution.run_engine.AIOKafkaProducer') as mock_producer_class, \
             patch('app.execution.run_engine.get_logger') as mock_logger:
            
            # Simulate producer initialization error
            mock_producer_class.side_effect = Exception("Kafka connection error")
            logger_instance = MagicMock()
            mock_logger.return_value = logger_instance
            
            with pytest.raises(Exception) as exc_info:
                await run_engine()
            
            assert "Kafka connection error" in str(exc_info.value)
            logger_instance.error.assert_called()
