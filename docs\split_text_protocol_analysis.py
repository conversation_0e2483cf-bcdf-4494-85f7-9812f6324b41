#!/usr/bin/env python3
"""
Protocol and communication analysis for Split Text Component.
This shows the exact message formats and protocols used between services.
"""

import json
from typing import Dict, Any

def analyze_communication_protocols():
    """Analyze the communication protocols between services."""
    
    print("🌐 Split Text Component - Communication Protocol Analysis")
    print("=" * 70)
    
    protocols = {
        "frontend_to_api_gateway": {
            "protocol": "HTTP/1.1",
            "method": "POST",
            "content_type": "application/json",
            "endpoint": "/api/v1/workflows/execute",
            "headers": {
                "Authorization": "Bearer jwt_token_here",
                "Content-Type": "application/json",
                "X-Request-ID": "req_frontend_001"
            },
            "payload": {
                "workflow_id": "split_text_workflow",
                "nodes": [
                    {
                        "id": "split_node",
                        "type": "SplitTextComponent",
                        "inputs": {
                            "input_text": "data,to,split",
                            "delimiter": ",",
                            "max_splits": -1,
                            "include_delimiter": False
                        }
                    }
                ]
            }
        },
        
        "api_gateway_to_workflow_service": {
            "protocol": "gRPC",
            "service": "WorkflowService",
            "method": "ExecuteWorkflow",
            "message_type": "ExecuteWorkflowRequest",
            "serialization": "Protocol Buffers",
            "payload": {
                "workflow_id": "split_text_workflow",
                "correlation_id": "corr_12345",
                "user_context": {
                    "user_id": "user_123",
                    "session_id": "session_456"
                },
                "workflow_definition": {
                    "nodes": [
                        {
                            "id": "split_node",
                            "type": "SplitTextComponent",
                            "inputs": {
                                "input_text": "data,to,split",
                                "delimiter": ",",
                                "max_splits": -1,
                                "include_delimiter": False
                            }
                        }
                    ]
                }
            }
        },
        
        "orchestration_to_node_executor": {
            "protocol": "Apache Kafka",
            "topic": "node-execution-request",
            "partition_key": "SplitTextComponent",
            "serialization": "JSON",
            "headers": {
                "correlation-id": "corr_12345",
                "content-type": "application/json",
                "timestamp": "2024-01-15T10:30:00Z"
            },
            "payload": {
                "tool_name": "SplitTextComponent",
                "tool_parameters": {
                    "input_text": "data,to,split",
                    "delimiter": ",",
                    "max_splits": -1,
                    "include_delimiter": False
                },
                "request_id": "req_orch_789",
                "correlation_id": "corr_12345",
                "workflow_id": "split_text_workflow",
                "node_id": "split_node"
            }
        },
        
        "node_executor_to_orchestration": {
            "protocol": "Apache Kafka",
            "topic": "node_results",
            "partition_key": "req_orch_789",
            "serialization": "JSON",
            "headers": {
                "correlation-id": "corr_12345",
                "content-type": "application/json",
                "timestamp": "2024-01-15T10:30:00.156Z"
            },
            "payload": {
                "request_id": "req_orch_789",
                "status": "success",
                "result": {
                    "status": "success",
                    "output_list": ["data", "to", "split"]
                },
                "execution_time_ms": 45.6,
                "component_type": "SplitTextComponent"
            }
        }
    }
    
    for comm_name, details in protocols.items():
        print(f"\n📡 {comm_name.replace('_', ' ').title()}")
        print("-" * 50)
        print(f"Protocol: {details['protocol']}")
        
        if 'method' in details:
            print(f"Method: {details['method']}")
        if 'topic' in details:
            print(f"Topic: {details['topic']}")
        if 'serialization' in details:
            print(f"Serialization: {details['serialization']}")
        
        if 'headers' in details:
            print(f"\nHeaders:")
            for key, value in details['headers'].items():
                print(f"  {key}: {value}")
        
        print(f"\nPayload:")
        print(json.dumps(details['payload'], indent=2))

def analyze_tool_parameters_handling():
    """Analyze how tool_parameters wrapper is handled."""
    
    print(f"\n\n🔧 tool_parameters Wrapper Analysis")
    print("=" * 50)
    
    wrapper_flow = {
        "stage_1_orchestration_send": {
            "description": "Orchestration Engine sends to Node Executor",
            "format": "Kafka message with tool_parameters wrapper",
            "payload": {
                "tool_name": "SplitTextComponent",
                "tool_parameters": {
                    "input_text": "apple,banana,cherry",
                    "delimiter": ",",
                    "max_splits": -1,
                    "include_delimiter": False
                },
                "request_id": "req_123",
                "correlation_id": "corr_456"
            }
        },
        
        "stage_2_node_executor_receive": {
            "description": "Node Executor Service receives and unwraps",
            "format": "Component validation and processing",
            "code_logic": """
# In SplitTextComponent.validate() and process()
if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
    logger.info("Found 'tool_parameters' field in payload. Using it for parameters.")
    parameters = payload["tool_parameters"]
    parameters["request_id"] = payload.get("request_id")
else:
    parameters = payload
            """,
            "extracted_parameters": {
                "input_text": "apple,banana,cherry",
                "delimiter": ",",
                "max_splits": -1,
                "include_delimiter": False,
                "request_id": "req_123"
            }
        },
        
        "stage_3_component_processing": {
            "description": "Component processes extracted parameters",
            "format": "Direct parameter access",
            "processing_steps": [
                "input_text = parameters.get('input_text')",
                "delimiter = parameters.get('delimiter', settings.default_delimiter)",
                "max_splits = parameters.get('max_splits', settings.default_max_splits)",
                "include_delimiter = parameters.get('include_delimiter', False)"
            ]
        },
        
        "stage_4_response_format": {
            "description": "Component returns standardized response",
            "format": "Status + result structure",
            "response": {
                "status": "success",
                "output_list": ["apple", "banana", "cherry"]
            }
        }
    }
    
    for stage_name, details in wrapper_flow.items():
        print(f"\n📋 {details['description']}")
        print(f"Format: {details['format']}")
        
        if 'payload' in details:
            print("Payload:")
            print(json.dumps(details['payload'], indent=2))
        
        if 'code_logic' in details:
            print("Code Logic:")
            print(details['code_logic'])
        
        if 'extracted_parameters' in details:
            print("Extracted Parameters:")
            print(json.dumps(details['extracted_parameters'], indent=2))
        
        if 'processing_steps' in details:
            print("Processing Steps:")
            for step in details['processing_steps']:
                print(f"  • {step}")
        
        if 'response' in details:
            print("Response:")
            print(json.dumps(details['response'], indent=2))

def analyze_error_propagation():
    """Analyze how errors propagate through the system."""
    
    print(f"\n\n🚨 Error Propagation Analysis")
    print("=" * 50)
    
    error_flow = {
        "validation_error": {
            "origin": "Node Executor Service - SplitTextComponent.validate()",
            "trigger": "Missing input_text parameter",
            "propagation_chain": [
                {
                    "service": "Node Executor Service",
                    "component": "SplitTextComponent",
                    "method": "validate()",
                    "response": {
                        "is_valid": False,
                        "error_message": "Missing required field 'input_text' in payload",
                        "error_details": {"input_text": "required field missing"}
                    }
                },
                {
                    "service": "Node Executor Service", 
                    "component": "ToolExecutor",
                    "method": "execute_tool()",
                    "response": {
                        "request_id": "req_error_001",
                        "status": "error",
                        "error": "Validation failed: input_text: required field missing"
                    }
                },
                {
                    "service": "Orchestration Engine",
                    "component": "NodeExecutor",
                    "method": "execute_tool()",
                    "response": {
                        "correlation_id": "corr_error_001",
                        "status": "error",
                        "error_details": {
                            "component": "SplitTextComponent",
                            "validation_error": "Missing required field 'input_text'"
                        }
                    }
                },
                {
                    "service": "Workflow Service",
                    "component": "SplitTextComponent",
                    "method": "execute()",
                    "response": {
                        "node_id": "split_node_error",
                        "status": "error",
                        "outputs": {"error": "Component validation failed"},
                        "execution_time": 0.003
                    }
                }
            ]
        },
        
        "processing_error": {
            "origin": "Node Executor Service - SplitTextComponent.process()",
            "trigger": "Exception during text splitting",
            "propagation_chain": [
                {
                    "service": "Node Executor Service",
                    "component": "SplitTextComponent",
                    "method": "process()",
                    "response": {
                        "status": "error",
                        "error": "Error splitting text: regex pattern invalid"
                    }
                },
                {
                    "service": "Orchestration Engine",
                    "response": {
                        "status": "error",
                        "error_details": {
                            "component": "SplitTextComponent",
                            "processing_error": "regex pattern invalid"
                        }
                    }
                }
            ]
        }
    }
    
    for error_type, details in error_flow.items():
        print(f"\n📋 {error_type.replace('_', ' ').title()}")
        print(f"Origin: {details['origin']}")
        print(f"Trigger: {details['trigger']}")
        
        print(f"\nPropagation Chain:")
        for i, stage in enumerate(details['propagation_chain'], 1):
            print(f"\n  {i}. {stage['service']}")
            if 'component' in stage:
                print(f"     Component: {stage['component']}")
            if 'method' in stage:
                print(f"     Method: {stage['method']}")
            print(f"     Response: {json.dumps(stage['response'], indent=8)}")

if __name__ == "__main__":
    analyze_communication_protocols()
    analyze_tool_parameters_handling()
    analyze_error_propagation()
