/**
 * Workflow related types
 */

import { Node, Edge } from 'reactflow';

export interface Workflow {
  id: string;
  name: string;
  description?: string;
  nodes: Node[];
  edges: Edge[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  isPublished: boolean;
  version: number;
}

export interface WorkflowListItem {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  isPublished: boolean;
  version: number;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  result?: any;
  error?: string;
}

export interface WorkflowExecutionRequest {
  workflowId: string;
  input?: Record<string, any>;
}

export interface WorkflowState {
  workflows: WorkflowListItem[];
  currentWorkflow: Workflow | null;
  isLoading: boolean;
  error: string | null;
}

export interface NodeType {
  id: string;
  type: string;
  name: string;
  description: string;
  category: string;
  inputs: NodeInput[];
  outputs: NodeOutput[];
}

export interface NodeInput {
  id: string;
  name: string;
  type: string;
  required: boolean;
  default?: any;
  description?: string;
}

export interface NodeOutput {
  id: string;
  name: string;
  type: string;
  description?: string;
}
