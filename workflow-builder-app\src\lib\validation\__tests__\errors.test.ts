import { describe, it, expect } from "jest";
import { createValidationError } from "../errors";
import { ValidationErrorCode } from "../types";

describe("Validation Errors", () => {
  describe("createValidationError", () => {
    it("should create an error with the specified code and message", () => {
      const error = createValidationError(
        ValidationErrorCode.WORKFLOW_MISSING_NODES,
        "Workflow must contain nodes"
      );
      
      expect(error.code).toBe(ValidationErrorCode.WORKFLOW_MISSING_NODES);
      expect(error.message).toBe("Workflow must contain nodes");
      expect(error.severity).toBe("error"); // Default severity
    });

    it("should create an error with custom severity", () => {
      const error = createValidationError(
        ValidationErrorCode.WORKFLOW_CYCLE_DETECTED,
        "Workflow contains cycles",
        "warning"
      );
      
      expect(error.code).toBe(ValidationErrorCode.WORKFLOW_CYCLE_DETECTED);
      expect(error.message).toBe("Workflow contains cycles");
      expect(error.severity).toBe("warning");
    });

    it("should create an error with node ID", () => {
      const error = createValidationError(
        ValidationErrorCode.NODE_MISSING_DATA,
        "Node is missing data",
        "error",
        "node-123"
      );
      
      expect(error.code).toBe(ValidationErrorCode.NODE_MISSING_DATA);
      expect(error.message).toBe("Node is missing data");
      expect(error.severity).toBe("error");
      expect(error.nodeId).toBe("node-123");
    });

    it("should create an error with node ID and field ID", () => {
      const error = createValidationError(
        ValidationErrorCode.FIELD_REQUIRED,
        "Field is required",
        "error",
        "node-123",
        "field-456"
      );
      
      expect(error.code).toBe(ValidationErrorCode.FIELD_REQUIRED);
      expect(error.message).toBe("Field is required");
      expect(error.severity).toBe("error");
      expect(error.nodeId).toBe("node-123");
      expect(error.fieldId).toBe("field-456");
    });
  });
});
