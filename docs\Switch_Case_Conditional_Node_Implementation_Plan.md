# Switch-Case Conditional Node Implementation Plan

## 1. Architecture Analysis

### Current ConditionalNode Analysis
The existing `ConditionalNode` in `workflow-service/app/components/control_flow/conditionalNode.py` provides:
- Simple if-else logic with two outputs (true/false)
- Single condition evaluation
- Legacy `build` method (deprecated)
- Limited operator support (equals, not equals, contains, starts with, ends with, regex)
- Single input routing mechanism

### Orchestration Engine Switch-Case Logic
The orchestration engine's `_evaluate_switch_case` method in `workflow_utils.py` provides:
- Multiple condition evaluation with ALL matching transitions returned
- Support for 9 operators: equals, not_equals, contains, starts_with, ends_with, greater_than, less_than, exists, is_empty
- Source selection: node_output vs global_context
- Global context variable resolution
- Robust error handling (skip invalid conditions)
- Default transition when no conditions match

### Implementation Gaps
1. **Multiple Conditions**: Current node supports only one condition
2. **Dynamic Outputs**: Need variable number of outputs based on conditions
3. **Operator Coverage**: Missing greater_than, less_than, exists, is_empty
4. **Source Selection**: No global_context support
5. **Modern Patterns**: Missing execute method, dual-purpose inputs
6. **Input Routing**: No per-condition input configuration

## 2. Component Specification

### 2.1 Component Definition
```python
class ConditionalNode(BaseNode):
    name = "ConditionalNode"
    display_name = "Switch-Case Router"
    description = "Evaluates multiple conditions and routes data to matching outputs"
    category = "Logic"
    icon = "GitBranch"
    beta = False
```

### 2.2 Input Structure
```python
inputs: ClassVar[List[InputBase]] = [
    # Primary input data (optional)
    create_dual_purpose_input(
        name="primary_input_data",
        display_name="Primary Input Data",
        input_type="multiline",
        required=False,
        info="Main data to route through conditions"
    ),

    # Condition 1 configuration
    DropdownInput(
        name="condition_1_source",
        display_name="Condition 1 - Source",
        options=["node_output", "global_context"],
        value="node_output",
        required=True
    ),
    StringInput(
        name="condition_1_variable",
        display_name="Condition 1 - Variable Name",
        visibility_rules=[InputVisibilityRule(
            field_name="condition_1_source",
            field_value="global_context"
        )]
    ),
    DropdownInput(
        name="condition_1_operator",
        display_name="Condition 1 - Operator",
        options=["equals", "not_equals", "contains", "starts_with",
                "ends_with", "greater_than", "less_than", "exists", "is_empty"],
        value="equals",
        required=True
    ),
    StringInput(
        name="condition_1_expected_value",
        display_name="Condition 1 - Expected Value",
        visibility_rules=[InputVisibilityRule(
            field_name="condition_1_operator",
            field_value="exists",
            operator="not_equals"
        ), InputVisibilityRule(
            field_name="condition_1_operator",
            field_value="is_empty",
            operator="not_equals"
        )],
        visibility_logic="AND"
    ),
    BoolInput(
        name="condition_1_use_primary",
        display_name="Condition 1 - Use Primary Input Data",
        value=True,
        info="Route primary input data when this condition matches"
    ),
    create_dual_purpose_input(
        name="condition_1_custom_input",
        display_name="Condition 1 - Custom Input",
        input_type="multiline",
        required=True,
        visibility_rules=[InputVisibilityRule(
            field_name="condition_1_use_primary",
            field_value=False
        )],
        info="Custom data to route when condition matches"
    ),

    # Condition 2 configuration (similar structure)
    # ... (repeat for condition 2)

    # Dynamic condition management
    IntInput(
        name="num_conditions",
        display_name="Number of Conditions",
        value=2,
        min_value=1,
        max_value=10,
        advanced=True
    )
]
```

### 2.3 Output Structure
```python
outputs: ClassVar[List[Output]] = [
    Output(name="condition_1_output", display_name="Condition 1", output_type="Any"),
    Output(name="condition_2_output", display_name="Condition 2", output_type="Any"),
    Output(name="default_output", display_name="Default", output_type="Any")
]
```

## 3. Implementation Plan

### Phase 1: Workflow Service Component Implementation

#### 3.1 Replace ConditionalNode Implementation
**File**: `workflow-service/app/components/control_flow/conditionalNode.py`

**Tasks**:
- [ ] Replace existing class with new switch-case implementation
- [ ] Implement modern `execute` method
- [ ] Remove legacy `build` method
- [ ] Add comprehensive input validation
- [ ] Implement condition evaluation logic
- [ ] Add dynamic output generation
- [ ] Handle per-condition input routing

#### 3.2 Core Methods Implementation
```python
async def execute(self, context: WorkflowContext) -> NodeResult:
    """Modern execute method for switch-case logic"""

def _evaluate_condition(self, condition_config: Dict, input_data: Any,
                       global_context: Dict) -> bool:
    """Evaluate a single condition"""

def _get_condition_input_data(self, condition_num: int,
                             context: WorkflowContext) -> Any:
    """Get input data for specific condition"""

def _generate_dynamic_outputs(self, num_conditions: int) -> List[Output]:
    """Generate outputs based on number of conditions"""
```

#### 3.3 Input Validation & Error Handling
- [ ] Validate condition configurations
- [ ] Handle missing global context variables
- [ ] Skip invalid conditions gracefully
- [ ] Provide meaningful error messages

### Phase 2: Node Executor Service Implementation

#### 2.1 Create Conditional Executor
**File**: `node-executor-service/app/components/conditional_component.py`

```python
@register_component("ConditionalNode")
class ConditionalExecutor(BaseComponent):
    """Executor for ConditionalNode in node-executor-service"""

    async def process(self, payload: Dict[str, Any]) -> Any:
        """Process conditional routing logic"""
```

#### 2.2 Executor Implementation Tasks
- [ ] Create new conditional_component.py file
- [ ] Implement BaseComponent inheritance
- [ ] Add @register_component decorator
- [ ] Implement process method with switch-case logic
- [ ] Handle dual-purpose input value wrapping
- [ ] Add comprehensive error handling
- [ ] Update __init__.py imports

### Phase 3: Testing and Validation

#### 3.1 Unit Tests - Workflow Service
**File**: `workflow-service/tests/test_conditional_node_switch_case.py`

- [ ] Test all 9 operators with various data types
- [ ] Test multiple condition scenarios
- [ ] Test input routing variations
- [ ] Test global context variable resolution
- [ ] Test error handling and edge cases
- [ ] Test dynamic output generation

#### 3.2 Unit Tests - Node Executor Service
**File**: `node-executor-service/tests/test_conditional_executor.py`

- [ ] Test process method with various payloads
- [ ] Test dual-purpose input handling
- [ ] Test error conditions
- [ ] Test component registration

#### 3.3 Integration Tests
- [ ] End-to-end workflow execution tests
- [ ] Test workflow-service to node-executor-service communication
- [ ] Test with real workflow scenarios

## 4. Technical Requirements

### 4.1 Modern Execute Method Pattern
```python
async def execute(self, context: WorkflowContext) -> NodeResult:
    try:
        # Get current node inputs
        inputs = context.get_node_inputs(context.current_node_id)

        # Evaluate conditions and determine outputs
        results = await self._evaluate_all_conditions(inputs, context)

        return NodeResult.success(outputs=results)
    except Exception as e:
        return NodeResult.error(error_message=str(e))
```

### 4.2 Dual-Purpose Input Handling
- Use `create_dual_purpose_input` helper for all data inputs
- Handle both connected and direct input values
- Support input_types for type validation

### 4.3 Visibility Rules Implementation
```python
# Variable name only visible for global_context
InputVisibilityRule(field_name="condition_X_source", field_value="global_context")

# Expected value hidden for exists/is_empty
InputVisibilityRule(field_name="condition_X_operator", field_value="exists", operator="not_equals")
```

### 4.4 Component Registration
- Ensure proper registration in component discovery
- Update control_flow/__init__.py imports
- Maintain backward compatibility for existing workflows

## 5. UI/UX Specifications

### 5.1 Primary Input Section
```
┌─────────────────────────────────────────┐
│ 🔗 Primary Input Data (Optional)        │
│ [Handle/Text Input Field]               │
│ ℹ️ Main data to route through conditions│
└─────────────────────────────────────────┘
```

### 5.2 Condition Configuration Panel
```
┌─────────────────────────────────────────┐
│ Condition 1                        [×]  │
│ ┌─────────────────────────────────────┐ │
│ │ Source: [node_output ▼]             │ │
│ │ Variable: [hidden for node_output]  │ │
│ │ Operator: [equals ▼]                │ │
│ │ Expected: [success]                 │ │
│ │ ☑️ Use Primary Input Data           │ │
│ │ 🔗 Custom Input: [hidden]           │ │
│ └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 5.3 Dynamic Condition Management
- Start with 2 conditions by default
- "+ Add Condition" button (max 10 total)
- "×" remove button for each condition
- Automatic output generation based on condition count

### 5.4 Output Connections
- **Condition 1 Output**: Routes data when condition 1 matches
- **Condition 2 Output**: Routes data when condition 2 matches
- **Default Output**: Routes data when no conditions match
- Clear labeling with condition summary tooltips

## 6. Testing Strategy

### 6.1 Operator Test Cases
```python
# Test all operators with various data types
test_cases = [
    {"operator": "equals", "actual": "test", "expected": "test", "result": True},
    {"operator": "not_equals", "actual": "test", "expected": "other", "result": True},
    {"operator": "contains", "actual": "hello world", "expected": "world", "result": True},
    {"operator": "starts_with", "actual": "hello", "expected": "hel", "result": True},
    {"operator": "ends_with", "actual": "hello", "expected": "llo", "result": True},
    {"operator": "greater_than", "actual": 10, "expected": 5, "result": True},
    {"operator": "less_than", "actual": 3, "expected": 5, "result": True},
    {"operator": "exists", "actual": "value", "expected": None, "result": True},
    {"operator": "is_empty", "actual": "", "expected": None, "result": True}
]
```

### 6.2 Multiple Condition Scenarios
- All conditions match (multiple outputs active)
- No conditions match (default output only)
- Mixed condition results
- Invalid condition configurations

### 6.3 Input Routing Test Cases
- Primary input routing to multiple conditions
- Per-condition custom inputs
- Mixed routing configurations
- Missing input data handling

### 6.4 Error Condition Tests
- Invalid operators
- Missing global context variables
- Type conversion errors
- Malformed condition configurations

## Detailed TDD Implementation Task List

### Phase 1: Workflow Service Implementation (TDD Approach)

#### 1.1 Test Infrastructure Setup
- [ ] Create test file: `workflow-service/tests/test_conditional_node_switch_case.py`
- [ ] Set up test fixtures and mock data for all 9 operators
- [ ] Create test helper functions for condition evaluation
- [ ] Set up WorkflowContext mock objects for testing
- [ ] Create test data sets for global context variables

#### 1.2 Core Logic Tests (Write Tests First)
- [ ] Write test for `_evaluate_condition` method with `equals` operator
- [ ] Write test for `_evaluate_condition` method with `not_equals` operator
- [ ] Write test for `_evaluate_condition` method with `contains` operator
- [ ] Write test for `_evaluate_condition` method with `starts_with` operator
- [ ] Write test for `_evaluate_condition` method with `ends_with` operator
- [ ] Write test for `_evaluate_condition` method with `greater_than` operator
- [ ] Write test for `_evaluate_condition` method with `less_than` operator
- [ ] Write test for `_evaluate_condition` method with `exists` operator
- [ ] Write test for `_evaluate_condition` method with `is_empty` operator
- [ ] Write test for invalid operator handling (should skip condition)
- [ ] Write test for type conversion errors in numeric operators
- [ ] Write test for None/null value handling across all operators

#### 1.3 Input Routing Tests (Write Tests First)
- [ ] Write test for primary input data routing to single condition
- [ ] Write test for primary input data routing to multiple conditions
- [ ] Write test for per-condition custom input routing
- [ ] Write test for mixed routing (some primary, some custom inputs)
- [ ] Write test for missing input data handling
- [ ] Write test for dual-purpose input value unwrapping

#### 1.4 Global Context Tests (Write Tests First)
- [ ] Write test for global context variable resolution
- [ ] Write test for missing global context variable handling
- [ ] Write test for global context with `exists` operator
- [ ] Write test for global context with `is_empty` operator
- [ ] Write test for global context variable type validation

#### 1.5 Multiple Condition Scenarios Tests (Write Tests First)
- [ ] Write test for all conditions matching (multiple outputs)
- [ ] Write test for no conditions matching (default output only)
- [ ] Write test for partial condition matching
- [ ] Write test for condition evaluation order
- [ ] Write test for maximum 10 conditions limit
- [ ] Write test for minimum 1 condition requirement

#### 1.6 Component Definition Tests (Write Tests First)
- [ ] Write test for component registration and discovery
- [ ] Write test for input visibility rules validation
- [ ] Write test for dynamic output generation based on condition count
- [ ] Write test for component metadata (name, display_name, category, etc.)
- [ ] Write test for input validation and required field checking

#### 1.7 Execute Method Tests (Write Tests First)
- [ ] Write test for successful execute method with single condition
- [ ] Write test for successful execute method with multiple conditions
- [ ] Write test for execute method error handling
- [ ] Write test for NodeResult success response structure
- [ ] Write test for NodeResult error response structure
- [ ] Write test for context integration and node input retrieval

#### 1.8 Implementation Tasks (After Tests)
- [ ] Backup existing file: `workflow-service/app/components/control_flow/conditionalNode.py`
- [ ] Implement new ConditionalNode class structure with proper imports
- [ ] Implement `_evaluate_condition` method with all 9 operators
- [ ] Implement `_get_condition_input_data` method for input routing
- [ ] Implement `_resolve_global_context_variable` helper method
- [ ] Implement `_generate_dynamic_outputs` method
- [ ] Implement modern `execute` method with comprehensive error handling
- [ ] Remove legacy `build` method completely
- [ ] Update class metadata (name, display_name, description, etc.)

#### 1.9 Input Definition Implementation
- [ ] Implement primary input data using `create_dual_purpose_input`
- [ ] Implement condition 1 source dropdown with visibility rules
- [ ] Implement condition 1 variable name with global_context visibility
- [ ] Implement condition 1 operator dropdown with all 9 options
- [ ] Implement condition 1 expected value with exists/is_empty visibility rules
- [ ] Implement condition 1 use primary input toggle
- [ ] Implement condition 1 custom input with visibility rules
- [ ] Implement condition 2 inputs (duplicate structure)
- [ ] Implement dynamic condition management (num_conditions input)
- [ ] Add advanced input grouping and organization

#### 1.10 Output Definition Implementation
- [ ] Implement dynamic output generation based on condition count
- [ ] Implement condition-specific output naming and labeling
- [ ] Implement default output for unmatched conditions
- [ ] Add output type validation and metadata
- [ ] Implement output description and help text

#### 1.11 Validation and Error Handling
- [ ] Implement input validation for required fields
- [ ] Implement operator-specific validation logic
- [ ] Implement global context variable name validation
- [ ] Implement numeric value validation for comparison operators
- [ ] Add comprehensive error messages for validation failures
- [ ] Implement graceful handling of invalid conditions (skip, don't fail)

#### 1.12 Component Registration Updates
- [ ] Update `workflow-service/app/components/control_flow/__init__.py` imports
- [ ] Verify component discovery in `workflow-service/app/services/workflow_builder/component_service.py`
- [ ] Test component registration in development environment
- [ ] Validate component appears in API endpoint `/api/components`

### Phase 2: Node Executor Service Implementation (TDD Approach)

#### 2.1 Test Infrastructure Setup
- [ ] Create test file: `node-executor-service/tests/test_conditional_executor.py`
- [ ] Set up test fixtures for payload validation
- [ ] Create mock data for dual-purpose input handling
- [ ] Set up test cases for all operator scenarios
- [ ] Create integration test helpers

#### 2.2 Executor Logic Tests (Write Tests First)
- [ ] Write test for `process` method with single condition payload
- [ ] Write test for `process` method with multiple conditions payload
- [ ] Write test for dual-purpose input value unwrapping
- [ ] Write test for condition evaluation with node_output source
- [ ] Write test for condition evaluation with global_context source
- [ ] Write test for all 9 operators in executor context
- [ ] Write test for error handling and invalid payloads
- [ ] Write test for component registration verification

#### 2.3 Input Processing Tests (Write Tests First)
- [ ] Write test for primary input data extraction from payload
- [ ] Write test for per-condition custom input extraction
- [ ] Write test for input routing logic in executor
- [ ] Write test for missing input data handling
- [ ] Write test for input type validation and conversion

#### 2.4 Implementation Tasks (After Tests)
- [ ] Create new file: `node-executor-service/app/components/conditional_component.py`
- [ ] Implement ConditionalExecutor class inheriting from BaseComponent
- [ ] Add `@register_component("ConditionalNode")` decorator
- [ ] Implement `process` method with switch-case logic
- [ ] Implement condition evaluation logic matching workflow-service
- [ ] Implement input data extraction and routing
- [ ] Add comprehensive error handling and logging
- [ ] Implement payload validation and response formatting

#### 2.5 Component Registration
- [ ] Update `node-executor-service/app/components/__init__.py` to import conditional_component
- [ ] Verify component registration in COMPONENT_REGISTRY
- [ ] Test component discovery and initialization
- [ ] Validate component responds to ConditionalNode requests

#### 2.6 Integration with Existing Patterns
- [ ] Ensure compatibility with existing BaseComponent patterns
- [ ] Follow established error handling conventions
- [ ] Implement logging consistent with other components
- [ ] Add security validation following existing patterns

### Phase 3: Integration Testing and Validation

#### 3.1 End-to-End Testing
- [ ] Create integration test: `workflow-service/tests/test_conditional_node_e2e.py`
- [ ] Test workflow-service to node-executor-service communication
- [ ] Test complete workflow execution with conditional routing
- [ ] Test multiple condition scenarios in full workflow context
- [ ] Test error propagation between services

#### 3.2 Compatibility Testing
- [ ] Test component discovery and registration across services
- [ ] Test API endpoint responses for new component structure
- [ ] Test frontend compatibility with new input/output structure
- [ ] Test backward compatibility considerations
- [ ] Validate existing workflow migration requirements

#### 3.3 Performance Testing
- [ ] Test performance with maximum 10 conditions
- [ ] Test memory usage with large input data sets
- [ ] Test execution time with complex condition evaluations
- [ ] Test concurrent execution scenarios
- [ ] Validate resource cleanup and garbage collection

#### 3.4 Edge Case Validation
- [ ] Test with malformed input data
- [ ] Test with extremely large input values
- [ ] Test with special characters and unicode in text operations
- [ ] Test with null/undefined values across all operators
- [ ] Test with circular references in input data

### Phase 4: Documentation and Cleanup

#### 4.1 Code Documentation
- [ ] Add comprehensive docstrings to all new methods
- [ ] Update type hints and annotations
- [ ] Add inline comments for complex logic
- [ ] Document input/output specifications
- [ ] Add usage examples in docstrings

#### 4.2 User Documentation
- [ ] Create user guide: `docs/components/ConditionalNode_User_Guide.md`
- [ ] Document all 9 operators with examples
- [ ] Create migration guide from old ConditionalNode
- [ ] Add troubleshooting section for common issues
- [ ] Create video/visual documentation for UI interactions

#### 4.3 Technical Documentation
- [ ] Update API documentation for new component structure
- [ ] Document input visibility rules and behavior
- [ ] Add architectural decision records (ADRs) for design choices
- [ ] Update component registry documentation
- [ ] Document testing strategies and coverage

#### 4.4 Code Quality and Cleanup
- [ ] Run comprehensive linting and formatting
- [ ] Perform code review checklist validation
- [ ] Remove any debug logging and temporary code
- [ ] Optimize imports and remove unused dependencies
- [ ] Validate adherence to established coding standards

#### 4.5 Deployment Preparation
- [ ] Create deployment checklist for component updates
- [ ] Document rollback procedures if needed
- [ ] Prepare database migration scripts if required
- [ ] Create monitoring and alerting for new component
- [ ] Validate production readiness checklist

### Quality Assurance Checkpoints

#### Code Review Checkpoints
- [ ] Verify all tests pass before implementation
- [ ] Confirm TDD methodology followed throughout
- [ ] Validate adherence to existing code patterns
- [ ] Check error handling completeness
- [ ] Verify input validation coverage
- [ ] Confirm logging and monitoring integration

#### Architecture Review Checkpoints
- [ ] Validate component follows BaseNode patterns
- [ ] Confirm dual-purpose input implementation
- [ ] Verify modern execute method usage
- [ ] Check component registration consistency
- [ ] Validate service communication patterns
- [ ] Confirm scalability considerations

#### Testing Review Checkpoints
- [ ] Verify 100% test coverage for new code
- [ ] Confirm all edge cases covered
- [ ] Validate integration test completeness
- [ ] Check performance test results
- [ ] Verify error scenario coverage
- [ ] Confirm backward compatibility testing
