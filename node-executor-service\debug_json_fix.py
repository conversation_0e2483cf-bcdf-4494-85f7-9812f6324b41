#!/usr/bin/env python3
"""
Debug the JSON fixing logic.
"""

import json

def _looks_like_json(value):
    """Check if a string looks like it could be JSON."""
    if not isinstance(value, str):
        return False

    # Remove leading/trailing whitespace
    value = value.strip()

    # Check for standard JSON patterns
    if (value.startswith('{') and value.endswith('}')) or \
       (value.startswith('[') and value.endswith(']')):
        return True

    # Check for malformed JSON that starts with a key (missing opening brace)
    if value.startswith('"') and ':' in value and ('}' in value or ']' in value):
        return True

    return False

def _fix_malformed_json(value):
    """Try to fix common malformed JSON patterns."""
    value = value.strip()

    # If it starts with a quote and contains colons, it might be missing opening brace
    if value.startswith('"') and ':' in value and not value.startswith('{'):
        # Add missing opening brace
        value = '{' + value
        print(f"Added missing opening brace to JSON string")

    # Count braces to see if we need to add closing braces
    open_braces = value.count('{')
    close_braces = value.count('}')

    if open_braces > close_braces:
        # Add missing closing braces
        missing_braces = open_braces - close_braces
        value = value + ('}' * missing_braces)
        print(f"Added {missing_braces} missing closing brace(s)")

    return value

def test_json_fix():
    """Test the JSON fixing logic with the exact production data."""

    # This is the exact malformed JSON from production logs (note: missing opening brace AND missing final closing brace)
    malformed_json = '"data": {     "title": "${audio_script} transition-MCP_Audio_Generator_generate_audio-1747991748064",     "script": "**[Background Music: Light, professional tone]**\\n\\n**[Male Speaker: Friendly and Professional]**\\n\\n\\"Hey, have you ever thought... what if you could revolutionize your marketing approach in just, well, thirty seconds? Whoa, yeah, it\'s possible! Our mission is to help professionals like you achieve real results. Isn\'t that what your business deserves?\\n\\nImagine... just for a moment... having innovative marketing strategies at your fingertips, strategies that align perfectly with your goals. We know you\'re all about growth, visibility, and success. So, why not let us help elevate your game?\\n\\nJoin the ranks of visionary professionals who are already seeing those remarkable benefits. Your success is the real objective here. Ready to redefine success together?\\n\\n[Pause]\\n\\nReach out today, and let\'s turn those marketing dreams into reality.\\"\\n\\n**[Background Music gently fades out]**\\n\\n---\\n\\n**[Background: Soft instrumental music begins]**\\n\\n**Speaker (Male, Professional Tone):**\\n\\n\\"Hey there, professionals! Want to boost your business effortlessly? Well, that\'s what we\'re talking about today. Imagine marketing your business with zero hassle and maximum impact. Isn\'t that something we all dream of? Our latest audio generator is, quite frankly, a game-changer—delivering crisp, high-quality audio designed to truly engage your audience.\\n\\nThink about this: Businesses using advanced audio strategies can see a significant bump in customer engagement and retention. Now, picture having this level of effectiveness right at your fingertips, almost like your own personal audio engineer. It\'s crafted to not just impress but to bring in real results.\\n\\nSo, ready to elevate your marketing strategy? Together, let\'s step into the future....because making every sound count—well, that\'s the name of the game.\\"\\n\\n**[Background: Soft instrumental music fades out]**",     "script_type": "TOPIC",     "video_type": "SHORT"   }'

    print("=== JSON Fix Debug Test ===")
    print(f"Original malformed JSON: {malformed_json}")
    print(f"Length: {len(malformed_json)}")
    print(f"Starts with quote: {malformed_json.startswith('\"')}")
    print(f"Contains colon: {':' in malformed_json}")
    print(f"Contains closing brace: {'}' in malformed_json}")
    print(f"Looks like JSON: {_looks_like_json(malformed_json)}")

    if _looks_like_json(malformed_json):
        print("\n--- Attempting to fix JSON ---")
        fixed_json = _fix_malformed_json(malformed_json)
        print(f"Fixed JSON: {fixed_json}")

        print("\n--- Attempting to parse fixed JSON ---")
        try:
            parsed = json.loads(fixed_json)
            print(f"✅ Successfully parsed JSON!")
            print(f"Parsed data type: {type(parsed)}")
            print(f"Keys: {list(parsed.keys()) if isinstance(parsed, dict) else 'Not a dict'}")

            if isinstance(parsed, dict) and 'data' in parsed:
                data_obj = parsed['data']
                print(f"Data object: {data_obj}")
                if isinstance(data_obj, dict) and 'script' in data_obj:
                    script = data_obj['script']
                    print(f"✅ Successfully extracted script: {script}")
                else:
                    print(f"❌ No 'script' key in data object")
            else:
                print(f"❌ No 'data' key in parsed object")

        except (json.JSONDecodeError, ValueError) as e:
            print(f"❌ Failed to parse fixed JSON: {e}")
    else:
        print("❌ Does not look like JSON")

if __name__ == "__main__":
    test_json_fix()
