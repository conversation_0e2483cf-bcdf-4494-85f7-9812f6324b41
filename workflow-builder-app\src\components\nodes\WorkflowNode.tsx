import React, { memo } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, <PERSON><PERSON>ipP<PERSON>ider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
// Import icons based on node type
import {
  LucideIcon,
  FileText,
  Database,
  Code,
  Workflow,
  Cog,
  Cpu,
  ArrowRightLeft,
  LogIn,
  LogOut,
  ShieldCheck,
} from "lucide-react";

// Helper function to get appropriate icon based on node category or type
const getNodeIcon = (category: string, type: string, originalType?: string): LucideIcon => {
  // Special case for StartNode
  if (originalType === "StartNode") {
    return LogIn;
  }

  // Map categories to icons
  switch (category?.toLowerCase()) {
    case "io":
      return type.includes("input") ? LogIn : LogOut;
    case "data":
      return Database;
    case "processing":
      return Cpu;
    case "api":
      return ArrowRightLeft;
    case "control flow":
      return Workflow;
    case "text":
      return FileText;
    case "code":
      return Code;
    default:
      return Cog; // Default icon
  }
};

// Memoize the component for performance
const WorkflowNode = memo(({ data, isConnectable, selected }: NodeProps<WorkflowNodeData>) => {
  const { label, definition, type, originalType } = data;

  // Basic validation
  if (!definition) {
    return (
      <div className="bg-destructive/20 text-destructive rounded border p-2 text-xs font-medium">
        Missing Definition!
      </div>
    );
  }

  // Filter inputs to get ONLY handles and apply visibility rules
  const handleInputs = (definition.inputs || []).filter((inp) => {
    // Only include handle inputs
    if (!inp.is_handle) return false;

    // If no visibility rules, always show
    if (!inp.visibility_rules || inp.visibility_rules.length === 0) return true;

    // Special handling for dynamic inputs
    if (
      inp.input_type === "dynamic_handle" ||
      (type === "DynamicCombineTextComponent" &&
        inp.name.startsWith("input_") &&
        inp.name.endsWith("_handle")) ||
      (originalType === "ConditionalNode" &&
        inp.name.startsWith("condition_") &&
        inp.name.endsWith("_input_handle"))
    ) {
      // For DynamicHandleInput
      if (inp.input_type === "dynamic_handle") {
        const numHandles = data.config?.num_handles || inp.default_handles || 2;
        const baseNameMatch = inp.name.match(new RegExp(`${inp.base_name}_(\d+)`));
        if (baseNameMatch && baseNameMatch[1]) {
          const handleIndex = parseInt(baseNameMatch[1], 10);
          return handleIndex <= numHandles;
        }
        return true; // Show the base handle
      }

      // For DynamicCombineTextComponent (legacy support)
      if (
        type === "DynamicCombineTextComponent" &&
        inp.name.startsWith("input_") &&
        inp.name.endsWith("_handle")
      ) {
        // Extract the index from the handle name (e.g., "input_3_handle" -> 3)
        const match = inp.name.match(/input_(\d+)_handle/);
        if (match && match[1]) {
          const inputIndex = parseInt(match[1], 10);
          const numAdditionalInputs = parseInt(data.config?.num_additional_inputs || "0", 10);

          // Show the handle if its index is less than or equal to the number of additional inputs
          return inputIndex <= numAdditionalInputs;
        }
      }

      // For ConditionalNode input handles
      if (
        originalType === "ConditionalNode" &&
        inp.name.startsWith("condition_") &&
        inp.name.endsWith("_input_handle")
      ) {
        // Extract the condition number from the handle name (e.g., "condition_3_input_handle" -> 3)
        const match = inp.name.match(/condition_(\d+)_input_handle/);
        if (match && match[1]) {
          const conditionIndex = parseInt(match[1], 10);
          const numAdditionalConditions = parseInt(data.config?.num_additional_conditions || "0", 10);
          const totalConditions = 2 + numAdditionalConditions; // Base 2 + additional

          // Show the handle if the condition index is within the total conditions
          // AND the source for this condition is "node_output" (default to "node_output" if not set)
          if (conditionIndex <= totalConditions) {
            const sourceValue = data.config?.[`condition_${conditionIndex}_source`] || "node_output";
            return sourceValue === "node_output";
          }
          return false;
        }
      }
    }

    // For other components, check each rule - if ANY rule passes, show the input
    return inp.visibility_rules.some((rule) => {
      // Get the target field value from config
      const targetValue = data.config?.[rule.field_name];

      // Simple equality check - show if the field_name has the specified field_value
      return targetValue === rule.field_value;
    });
  });

  // Outputs are always handles in this model
  // Special handling for ConditionalNode dynamic outputs
  let handleOutputs = definition.outputs || [];

  if (originalType === "ConditionalNode") {
    const numAdditionalConditions = parseInt(data.config?.num_additional_conditions || "0", 10);
    const totalConditions = 2 + numAdditionalConditions; // Base 2 + additional

    // Generate dynamic outputs for conditions
    const dynamicOutputs = [];
    for (let i = 1; i <= totalConditions; i++) {
      dynamicOutputs.push({
        name: `condition_${i}_output`,
        display_name: `Condition ${i}`,
        output_type: "Any",
        info: `Outputs data when condition ${i} matches`,
      });
    }

    // Add default output
    dynamicOutputs.push({
      name: "default_output",
      display_name: "Default",
      output_type: "Any",
      info: "Outputs data when no conditions match",
    });

    handleOutputs = dynamicOutputs;
  }

  // Handle positioning - reduced to make nodes more compact
  const handleBaseOffset = 35;
  const handleMultiplier = 20; // Spacing between handles

  // Get the appropriate icon
  const NodeIcon = getNodeIcon(definition.category, type, originalType);

  // Determine if this is a StartNode
  const isStartNode = originalType === "StartNode";

  // Check if this node requires approval
  const requiresApproval = data.definition?.requires_approval === true;

  return (
    <TooltipProvider delayDuration={150}>
      <Card
        className={`workflow-node relative w-52 overflow-visible transition-all duration-200 ${selected ? "ring-primary scale-105 shadow-lg ring-2" : "shadow-md hover:shadow-lg"} ${isStartNode ? "border-primary bg-primary/5 border-2" : ""} ${requiresApproval ? "border-2 border-[#3F72AF]/50" : ""}`}
        style={{
          minHeight: `${Math.max(90, handleBaseOffset + Math.max(handleInputs.length, handleOutputs.length) * handleMultiplier)}px`,
          height: "auto", // Allow the card to grow based on content
          zIndex: 10, // Ensure node is above connections but below tooltips
        }}
      >
        <CardHeader className="flex flex-row items-center gap-1.5 space-y-0 border-b p-2">
          <div className="bg-primary/10 rounded-md p-1">
            <NodeIcon className="text-primary h-3.5 w-3.5" />
          </div>
          <div className="flex-1 overflow-hidden">
            <CardTitle className="truncate text-xs leading-tight font-medium">
              {label}
              {requiresApproval && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="ml-1 inline-flex">
                      <ShieldCheck className="h-3 w-3 text-[#3F72AF]" />
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="p-2 text-xs">
                    <div className="font-medium">Requires Approval</div>
                    <div className="text-muted-foreground text-[10px]">
                      This node requires approval before execution
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </CardTitle>
            <CardDescription className="truncate text-[9px] leading-tight">
              {definition.display_name}
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="flex flex-col gap-2 p-2 text-xs">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="bg-muted/50 h-5 px-1.5 py-0 text-[9px]">
              {definition.category}
            </Badge>

            {/* Show handle counts */}
            <div className="flex items-center gap-1">
              {handleInputs.length > 0 && (
                <span className="text-muted-foreground flex items-center gap-0.5 text-[9px]">
                  <span className="bg-primary/40 h-1.5 w-1.5 rounded-full"></span>
                  {handleInputs.length}
                </span>
              )}
              {handleOutputs.length > 0 && (
                <span className="text-muted-foreground flex items-center gap-0.5 text-[9px]">
                  <span className="bg-primary/40 h-1.5 w-1.5 rounded-full"></span>
                  {handleOutputs.length}
                </span>
              )}
            </div>
          </div>

          {/* Approval badge */}
          {requiresApproval && (
            <Badge
              variant="warning"
              className="flex h-5 w-fit items-center gap-1 px-1.5 py-0 text-[9px]"
            >
              <ShieldCheck className="h-3 w-3" />
              Requires Approval
            </Badge>
          )}
        </CardContent>

        {/* Input Handles (Left) with Tooltips */}
        {handleInputs.map((input, index) => (
          <React.Fragment key={`input-${input.name}`}>
            {/* Handle with Enhanced Tooltip */}
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="group">
                  <Handle
                    type="target"
                    position={Position.Left}
                    id={input.name}
                    className="!bg-primary hover:!bg-primary/80 transition-all duration-200"
                    style={{
                      top: `${handleBaseOffset + index * handleMultiplier}px`,
                      width: "10px",
                      height: "10px",
                      borderRadius: "5px",
                      border: "2px solid var(--background)",
                      left: "-5px",
                      zIndex: 50, // Ensure handle is above other elements
                    }}
                    isConnectable={isConnectable}
                  />
                  {/* Handle label indicator */}
                  <div
                    className="bg-primary/30 absolute h-[2px]"
                    style={{
                      top: `${handleBaseOffset + index * handleMultiplier}px`,
                      left: "5px",
                      width: "5px",
                      transform: "translateY(-50%)",
                    }}
                  />
                  {/* Small floating label that appears on hover - contained within node */}
                  <div
                    className="text-foreground bg-background/95 border-primary/20 pointer-events-none absolute z-30 max-w-[80%] overflow-hidden rounded-sm border px-1.5 py-0.5 text-[9px] font-medium text-ellipsis opacity-0 shadow-sm transition-all duration-150 group-hover:opacity-100"
                    style={{
                      top: `${handleBaseOffset + index * handleMultiplier}px`,
                      left: "10px",
                      transform: "translateY(-50%)",
                    }}
                  >
                    {input.display_name}
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent
                side="left"
                className="bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm"
              >
                <div className="font-medium">{input.display_name}</div>
                <div className="text-muted-foreground text-[10px]">
                  Type: {input.input_types?.join(", ") || "any"}
                </div>
              </TooltipContent>
            </Tooltip>
          </React.Fragment>
        ))}

        {/* Output Handles (Right) with Tooltips */}
        {handleOutputs.map((output, index) => (
          <React.Fragment key={`output-${output.name}`}>
            {/* Handle with Enhanced Tooltip */}
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="group">
                  <Handle
                    type="source"
                    position={Position.Right}
                    id={output.name}
                    className="!bg-primary hover:!bg-primary/80 transition-all duration-200"
                    style={{
                      top: `${handleBaseOffset + index * handleMultiplier}px`,
                      width: "10px",
                      height: "10px",
                      borderRadius: "5px",
                      border: "2px solid var(--background)",
                      right: "-5px",
                      zIndex: 50, // Ensure handle is above other elements
                    }}
                    isConnectable={isConnectable}
                  />
                  {/* Handle label indicator */}
                  <div
                    className="bg-primary/30 absolute h-[2px]"
                    style={{
                      top: `${handleBaseOffset + index * handleMultiplier}px`,
                      right: "5px",
                      width: "5px",
                      transform: "translateY(-50%)",
                    }}
                  />
                  {/* Small floating label that appears on hover - contained within node */}
                  <div
                    className="text-foreground bg-background/95 border-primary/20 pointer-events-none absolute z-30 max-w-[80%] overflow-hidden rounded-sm border px-1.5 py-0.5 text-right text-[9px] font-medium text-ellipsis opacity-0 shadow-sm transition-all duration-150 group-hover:opacity-100"
                    style={{
                      top: `${handleBaseOffset + index * handleMultiplier}px`,
                      right: "10px",
                      transform: "translateY(-50%)",
                    }}
                  >
                    {output.display_name}
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent
                side="right"
                className="bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm"
              >
                <div className="font-medium">{output.display_name}</div>
                <div className="text-muted-foreground text-[10px]">Type: {output.output_type}</div>
              </TooltipContent>
            </Tooltip>
          </React.Fragment>
        ))}
      </Card>
    </TooltipProvider>
  );
});

// Add display name for React DevTools
WorkflowNode.displayName = "WorkflowNode";

export default WorkflowNode;
