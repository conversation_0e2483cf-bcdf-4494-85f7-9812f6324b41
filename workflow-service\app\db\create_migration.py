#!/usr/bin/env python
"""
Migration generator script.
Usage: python -m app.db.create_migration "description of migration"
"""

import sys
import datetime
from pathlib import Path


def create_migration(description):
    # Get the migrations directory
    migrations_dir = Path(__file__).parent / "migrations" / "versions"

    # Find the latest revision number
    latest_rev = 0
    for file in migrations_dir.glob("*.py"):
        if file.name.startswith("0"):
            try:
                rev_num = int(file.name.split("_")[0])
                latest_rev = max(latest_rev, rev_num)
            except (ValueError, IndexError):
                pass

    # Create new revision number (increment by 1)
    new_rev = f"{latest_rev + 1:03d}"

    # Create a snake_case filename from the description
    snake_desc = description.lower().replace(" ", "_").replace("-", "_")
    filename = f"{new_rev}_{snake_desc}.py"

    # Get the previous revision
    prev_rev = f"{latest_rev:03d}" if latest_rev > 0 else None

    # Create the migration file content
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    content = f'''"""
    {description}

    Revision ID: {new_rev}
    Revises: {prev_rev}
    Create Date: {timestamp}

    """
    from alembic import op
    import sqlalchemy as sa
    from sqlalchemy.dialects import postgresql
    from sqlalchemy.sql import table, column

    # revision identifiers, used by Alembic.
    revision = '{new_rev}'
    down_revision = '{prev_rev}'
    branch_labels = None
    depends_on = None

    # Define table references for use in migrations
    workflows = table(
        'workflows',
        column('id', sa.String),
        column('name', sa.String),
        column('description', sa.String),
        column('tags', postgresql.JSON),
        # Add other columns as needed
    )

    workflow_templates = table(
        'workflow-templates',
        column('id', sa.String),
        column('name', sa.String),
        column('description', sa.String),
        column('tags', postgresql.JSON),
        # Add other columns as needed
    )

    def upgrade() -> None:
        """
        Implement your upgrade migrations here.
        """
        # Example: Add a new column
        # op.add_column('workflows', sa.Column('new_field', sa.String(), nullable=True))
        
        pass

    def downgrade() -> None:
        """
        Implement your downgrade migrations here (to revert changes made in upgrade).
        """
        # Example: Drop the added column
        # op.drop_column('workflows', 'new_field')
        pass
    '''

    # Write the file
    file_path = migrations_dir / filename
    with open(file_path, "w") as f:
        f.write(content)

    print(f"Created new migration: {file_path}")
    print("Remember to implement the upgrade() and downgrade() methods!")


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print('Usage: python -m app.db.create_migration "description of migration"')
        sys.exit(1)

    description = sys.argv[1]
    create_migration(description)
