import pytest
from unittest.mock import Mock, patch
import requests
import json
from app.services.node_url_service import (
    fetch_node_urls,
    update_workflow_node_urls,
    update_workflow_from_transition_schema,
    process_workflow_urls
)


class TestNodeURLService:
    """
    Test suite for node_url_service module.
    Tests API interactions, workflow updating, and error handling.
    """

    @pytest.fixture
    def mock_settings(self):
        """
        Provides mock settings for API configuration.
        """
        with patch('app.services.node_url_service.settings') as mock_settings:
            mock_settings.api_gateway_url = "http://test-api.com"
            mock_settings.orchestration_server_auth_key.get_secret_value.return_value = "test-auth-key"
            yield mock_settings

    @pytest.fixture
    def mock_successful_response(self):
        """
        Provides a mock successful response from the API.
        """
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "Node URLs retrieved successfully",
            "node_urls": {
                "node1": "https://example.com/node1",
                "node2": "https://example.com/node2",
                "node3": "https://example.com/node3"
            }
        }
        return mock_response

    @pytest.fixture
    def sample_workflow(self):
        """
        Provides a sample workflow for testing.
        """
        return {
            "nodes": [
                {
                    "id": "node1",
                    "server_script_path": "",
                    "server_tools": []
                },
                {
                    "id": "node2",
                    "server_script_path": "old-url",
                    "server_tools": []
                },
                {
                    "id": "node4",  # Not in node_urls
                    "server_script_path": "existing-url",
                    "server_tools": []
                }
            ],
            "transitions": [
                {
                    "id": "transition1",
                    "node_info": {
                        "node_id": "node1"
                    }
                },
                {
                    "id": "transition2",
                    "node_info": {
                        "node_id": "node2"
                    }
                }
            ]
        }

    def test_fetch_node_urls(self, mock_settings, mock_successful_response):
        """
        Test successful fetching of node URLs.
        """
        with patch('requests.get') as mock_get:
            mock_get.return_value = mock_successful_response
            
            result = fetch_node_urls("test-workflow")
            
            # Verify API call
            mock_get.assert_called_once_with(
                "http://test-api.com/api/v1/nodes/urls/test-workflow",
                headers={"X-Server-Auth-Key": "test-auth-key"}
            )
            
            # Verify result
            assert isinstance(result, dict)
            assert len(result) == 3
            assert result["node1"] == "https://example.com/node1"
            assert result["node2"] == "https://example.com/node2"
            assert result["node3"] == "https://example.com/node3"

    def test_fetch_node_urls_http_error(self, mock_settings):
        """
        Test handling of HTTP errors when fetching node URLs.
        """
        with patch('requests.get') as mock_get:
            mock_get.side_effect = requests.HTTPError("404 Not Found")
            
            with pytest.raises(requests.HTTPError):
                fetch_node_urls("test-workflow")

    def test_fetch_node_urls_json_error(self, mock_settings):
        """
        Test handling of JSON decode errors.
        """
        with patch('requests.get') as mock_get:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
            mock_get.return_value = mock_response
            
            with pytest.raises(ValueError, match="Invalid JSON response received"):
                fetch_node_urls("test-workflow")

    def test_update_workflow_node_urls(self, sample_workflow):
        """
        Test updating workflow node URLs.
        """
        node_urls = {
            "node1": "https://example.com/node1",
            "node2": "https://example.com/node2",
            "node3": "https://example.com/node3"  # Not in workflow
        }
        
        result = update_workflow_node_urls(sample_workflow, node_urls)
        
        # Verify result
        assert result != sample_workflow  # Should be a copy, not the same object
        assert result["nodes"][0]["server_script_path"] == "https://example.com/node1"
        assert result["nodes"][1]["server_script_path"] == "https://example.com/node2"
        assert result["nodes"][2]["server_script_path"] == "existing-url"  # Unchanged

    def test_update_workflow_node_urls_invalid_workflow(self):
        """
        Test handling of invalid workflow.
        """
        with pytest.raises(ValueError, match="Invalid workflow provided"):
            update_workflow_node_urls(None, {})
        
        with pytest.raises(KeyError, match="Workflow does not contain 'nodes' key"):
            update_workflow_node_urls({}, {})

    def test_update_workflow_from_transition_schema(self, sample_workflow):
        """
        Test updating workflow from transition schema.
        """
        result = update_workflow_from_transition_schema(sample_workflow)
        
        # Verify result
        assert result != sample_workflow  # Should be a copy, not the same object
        # No changes expected in this test case, just checking it processes correctly
        assert len(result["nodes"]) == len(sample_workflow["nodes"])
        assert len(result["transitions"]) == len(sample_workflow["transitions"])

    def test_process_workflow_urls(self, mock_settings, mock_successful_response, sample_workflow):
        """
        Test the main process_workflow_urls function.
        """
        with patch('requests.get') as mock_get:
            mock_get.return_value = mock_successful_response
            
            result = process_workflow_urls("test-workflow", sample_workflow)
            
            # Verify API call
            mock_get.assert_called_once()
            
            # Verify result
            assert result["nodes"][0]["server_script_path"] == "https://example.com/node1"
            assert result["nodes"][1]["server_script_path"] == "https://example.com/node2"
            assert result["nodes"][2]["server_script_path"] == "existing-url"  # Unchanged

    def test_process_workflow_urls_error(self, mock_settings, sample_workflow):
        """
        Test error handling in process_workflow_urls.
        """
        with patch('requests.get') as mock_get:
            mock_get.side_effect = requests.HTTPError("404 Not Found")
            
            with pytest.raises(requests.HTTPError):
                process_workflow_urls("test-workflow", sample_workflow)
