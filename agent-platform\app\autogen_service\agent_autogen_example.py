"""
AutoGen v0.4.9 Example: AssistantAgent & Custom Agent Chat with Tool, Memory, and Logging

This script demonstrates:
- Chatting with a default AssistantAgent (with memory and tool)
- Loading and chatting with a custom agent from a config JSON (with tool support)
- Logging (console and file)
- Memory (ListMemory)
- Tool addition via config

Requirements:
Set your OpenAI API key in the environment variable OPENAI_API_KEY.
"""

import asyncio
import os
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core import CancellationToken
from autogen_core.memory import ListMemory, MemoryContent, MemoryMimeType

import logging

from autogen_agentchat import EVENT_LOGGER_NAME, TRACE_LOGGER_NAME

# Configure logging for AutoGen AgentChat
logging.basicConfig(level=logging.WARNING)
logging.getLogger(EVENT_LOGGER_NAME).setLevel(logging.INFO)
logging.getLogger(TRACE_LOGGER_NAME).setLevel(logging.WARNING)
# To enable detailed trace logs, uncomment the following line:
# logging.getLogger(TRACE_LOGGER_NAME).setLevel(logging.INFO)

# Optional: Log agent events to a file
event_file_handler = logging.FileHandler("agent_events.log")
event_file_handler.setLevel(logging.INFO)
event_file_handler.setFormatter(
    logging.Formatter("%(asctime)s %(levelname)s %(name)s: %(message)s")
)
logging.getLogger(EVENT_LOGGER_NAME).addHandler(event_file_handler)


# Example tool: web search (dummy implementation)
async def web_search(query: str) -> str:
    """Find information on the web."""
    # Replace this with a real web search if desired
    return (
        f"Web search result for '{query}': "
        "AutoGen is a programming framework for building multi-agent "
        "applications."
    )


def get_openai_api_key():
    api_key = os.environ.get("OPENAI_API_KEY")
    # api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        raise RuntimeError("Please set the OPENAI_API_KEY environment variable.")
    return api_key


async def main():
    # Set up the model client (OpenAI GPT-4o)
    model_client = OpenAIChatCompletionClient(
        model="gpt-4o",
        api_key=get_openai_api_key(),
    )

    # Initialize user memory
    user_memory = ListMemory()

    # Add example user preferences to memory
    await user_memory.add(
        MemoryContent(
            content="The weather should be in metric units",
            mime_type=MemoryMimeType.TEXT,
        )
    )
    await user_memory.add(
        MemoryContent(
            content="Meal recipe must be vegan",
            mime_type=MemoryMimeType.TEXT,
        )
    )

    # Create the assistant agent with the tool and memory
    agent = AssistantAgent(
        name="assistant",
        model_client=model_client,
        tools=[web_search],
        system_message=("You are a helpful AI assistant. Use tools to solve tasks."),
        memory=[user_memory],
    )

    print("AutoGen v0.4.9 AssistantAgent Chat Example (with Memory)")
    print("Type 'exit' to quit.")
    print("Type 'addmem: your fact' to add a new memory entry.\n")

    cancellation_token = CancellationToken()

    while True:
        user_input = input("You: ")
        if user_input.strip().lower() == "exit":
            print("Exiting chat.")
            break

        # Allow user to add new memory entries interactively
        if user_input.strip().lower().startswith("addmem:"):
            fact = user_input.strip()[7:].strip()
            if fact:
                await user_memory.add(
                    MemoryContent(
                        content=fact,
                        mime_type=MemoryMimeType.TEXT,
                    )
                )
                print("Memory added.")
            else:
                print("No memory content provided.")
            continue

        # Send the user message to the agent and get the response
        response = await agent.on_messages(
            [TextMessage(content=user_input, source="user")],
            cancellation_token=cancellation_token,
        )
        print("Assistant:", response.chat_message.content)

    await user_memory.close()


if __name__ == "__main__":
    asyncio.run(main())
