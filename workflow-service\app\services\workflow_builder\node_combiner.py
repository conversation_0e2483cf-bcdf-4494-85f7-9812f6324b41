"""
Utility module for combining nodes with the same ID in a workflow schema.
"""


def combine_nodes(nodes):
    """
    Combine nodes with the same ID in a workflow schema.
    
    Args:
        nodes (list): List of nodes to combine.
        
    Returns:
        list: List of combined nodes.
    """
    # Create a dictionary to track nodes by their ID
    nodes_dict = {}
    
    # Process each node
    for node in nodes:
        node_id = node["id"]
        
        # Check if a node with this ID already exists
        if node_id in nodes_dict:
            # Add the tools from this node to the existing node
            existing_node = nodes_dict[node_id]
            
            # Get server_script_path from the node if it's not empty
            if node["server_script_path"] and not existing_node["server_script_path"]:
                existing_node["server_script_path"] = node["server_script_path"]
            
            # Add tools from this node to the existing node
            for tool in node["server_tools"]:
                # Check if this tool already exists in the existing node
                tool_exists = False
                for existing_tool in existing_node["server_tools"]:
                    if existing_tool["tool_name"] == tool["tool_name"]:
                        tool_exists = True
                        break
                
                # If the tool doesn't exist, add it with an incremented tool_id
                if not tool_exists:
                    # Set tool_id to be one more than the highest existing tool_id
                    max_tool_id = max([t["tool_id"] for t in existing_node["server_tools"]], default=0)
                    tool["tool_id"] = max_tool_id + 1
                    existing_node["server_tools"].append(tool)
        else:
            # Add the new node to the dictionary
            nodes_dict[node_id] = node
    
    # Convert the dictionary values to a list
    return list(nodes_dict.values())


def fix_schema(schema):
    """
    Fix a workflow schema by combining nodes with the same ID.
    
    Args:
        schema (dict): Workflow schema to fix.
        
    Returns:
        dict: Fixed workflow schema.
    """
    # Combine nodes with the same ID
    schema["nodes"] = combine_nodes(schema["nodes"])
    
    return schema
