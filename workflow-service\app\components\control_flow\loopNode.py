import re  # Keep if needed elsewhere, not directly used in LoopNode
import asyncio
from typing import List, Dict, Any, Optional, Literal

# Assuming these imports point to your framework's definitions
from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    DropdownInput,
    MultilineInput,
    BoolInput,
    IntInput,
    InputVisibilityRule,
    # Assuming HandleInput is defined as in your example or framework
    HandleInput,
    ListInput,  # Adding ListInput for clarity, though HandleInput might suffice
)
from app.models.workflow_builder.components import Output


class LoopNode(BaseNode):
    """
    Iterates over a list of items. For each item, it outputs the item and its index.
    It can optionally receive a value back for each iteration to aggregate results.
    When all items are processed, it outputs the aggregated list via 'Done'.

    **Note:** This node relies on internal state (_current_index, _aggregated_results)
    or expects the execution engine to manage loop state persistence between steps.
    The exact behavior depends on how the workflow engine executes loop-like nodes.
    """

    name = "LoopNode"
    display_name = "For Each Loop"
    description = (
        "Iterates over an input list, outputting each item. Aggregates results optionally."
    )
    category = "Logic"
    icon = "Repeat"  # Or 'Iteration', 'Loop' depending on your icon set
    beta = False  # Set to True if experimental

    inputs: List[InputBase] = [
        # Option 1: Using HandleInput (flexible, relies on connection type)
        HandleInput(
            name="input_list",
            display_name="List to Iterate",
            info="Connect the list of items to loop through.",
            required=True,
            is_handle=True,
            input_types=["list", "Any"],  # Accepts lists or implicitly convertible types
        ),
        # # Option 2: Using a specific ListInput (more explicit)
        # ListInput(
        #     name="input_list",
        #     display_name="List to Iterate",
        #     info="Provide the list of items to loop through (e.g., ['a', 'b', 'c'] or connect a list).",
        #     required=True,
        #     is_handle=True, # Allow connection
        #     input_types=["list"],
        #     value=[] # Default empty list
        # ),
        HandleInput(
            name="loop_input",
            display_name="Item Result (for Aggregation)",
            info="Connect the result from the loop's body for the *previous* item here if you want to aggregate results.",
            required=False,  # Aggregation is optional
            is_handle=True,
            input_types=["Any"],
        ),
        # Potentially add a 'Reset' input if manual loop control is needed
        # BoolInput(name="reset_loop", display_name="Reset Loop", info="Force restart loop from beginning.", value=False, advanced=True)
    ]

    outputs: List[Output] = [
        Output(
            name="item",
            display_name="Current Item",
            output_type="Any",  # Type matches items in the input list
            info="Outputs the item for the current iteration.",
            # NOTE: Your engine needs a mechanism to know this output triggers the loop body
            # and potentially pauses/resumes the workflow. This definition alone doesn't enforce it.
        ),
        Output(
            name="index",
            display_name="Current Index",
            output_type="int",
            info="Outputs the zero-based index of the current iteration.",
        ),
        Output(
            name="done",
            display_name="Done (Aggregated)",
            output_type="list",  # Outputs the list of aggregated results
            info="Outputs the list of aggregated results when all items have been processed. Triggers once per loop completion.",
        ),
        Output(
            name="is_looping",
            display_name="Is Looping?",
            output_type="bool",
            info="Outputs 'True' during iterations, 'False' otherwise. Useful for conditional logic outside the loop based on its state.",
        ),
    ]

    # --- State Management ---
    # These attributes store the loop's state.
    # WARNING: This relies on the node instance persisting across loop iterations
    # OR the engine providing/managing this state externally.
    _current_index: int
    _original_list: List[Any]
    _aggregated_results: List[Any]
    _is_initialized: bool  # Flag to track if the loop has started for the current list

    def __init__(self, **data: Any):
        """Initialize state variables."""
        super().__init__(**data)
        self.reset_state()

    def reset_state(self):
        """Resets the internal loop state."""
        self._current_index = 0
        self._original_list = []
        self._aggregated_results = []
        self._is_initialized = False
        # print(f"DEBUG ({self.name}): State Reset")

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the LoopNode.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Executes one step of the loop logic.

        Assumes this method might be called multiple times by the engine for a single
        loop execution cycle. Manages state internally.
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        # print(f"DEBUG ({self.name}): Build called with kwargs: {list(kwargs.keys())}") # Avoid printing large data

        # --- Get Inputs ---
        input_list = kwargs.get("input_list")
        loop_input = kwargs.get("loop_input")  # Result from the *previous* iteration's processing
        # reset_trigger = kwargs.get("reset_loop", False) # If using reset input

        # --- Handle Reset ---
        # if reset_trigger:
        #     print(f"DEBUG ({self.name}): Reset triggered.")
        #     self.reset_state()
        # Decide if reset should immediately exit or proceed with initialization
        # return {"item": None, "index": None, "done": None, "is_looping": False} # Example exit on reset

        # --- Initialize Loop on First Run (for a given input list) ---
        # We need a robust way to detect the *start* of processing a *new* list.
        # Comparing input_list might be unreliable if it's mutable or complex.
        # Using the _is_initialized flag is simpler for this example.
        # A more robust engine might pass a 'start_loop' signal.
        if not self._is_initialized:
            print(f"DEBUG ({self.name}): Initializing loop.")
            self.reset_state()  # Ensure clean state before starting

            if isinstance(input_list, list):
                self._original_list = input_list
            elif input_list is not None:
                # Attempt to handle single items as a list of one
                self._original_list = [input_list]
                print(f"DEBUG ({self.name}): Received single item, treating as list.")
            else:
                self._original_list = []  # Handle None or empty input

            if not self._original_list:
                print(f"DEBUG ({self.name}): Input list is empty. Loop finished immediately.")
                self._is_initialized = False  # Ready for potential next run
                return {"item": None, "index": None, "done": [], "is_looping": False}

            self._is_initialized = True
            self._current_index = 0
            # Don't aggregate on the very first step (loop_input is irrelevant here)
            print(f"DEBUG ({self.name}): Loop initialized with {len(self._original_list)} items.")

        # --- Aggregate Result from Previous Iteration ---
        # Aggregate *before* processing the current item.
        # The 'loop_input' corresponds to the result for the item at 'self._current_index - 1'.
        if self._current_index > 0 and loop_input is not None:
            # Check if loop_input was actually provided (connected and has value)
            # The key check (`"loop_input" in kwargs`) might be needed if None is a valid aggregation value
            if "loop_input" in kwargs:
                print(
                    f"DEBUG ({self.name}): Aggregating result for index {self._current_index - 1}: {type(loop_input)}"
                )
                self._aggregated_results.append(loop_input)
            else:
                print(
                    f"DEBUG ({self.name}): No result provided for index {self._current_index - 1} to aggregate."
                )
                # Optionally append None or skip based on desired behavior
                # self._aggregated_results.append(None)

        # --- Check for Loop Completion ---
        if self._current_index >= len(self._original_list):
            print(f"DEBUG ({self.name}): Loop finished. Outputting aggregated results.")
            final_results = self._aggregated_results[:]  # Return a copy
            self.reset_state()  # Reset for next potential run
            return {"item": None, "index": None, "done": final_results, "is_looping": False}

        # --- Process Current Item ---
        current_item = self._original_list[self._current_index]
        current_index_to_output = self._current_index  # Store index before incrementing

        print(
            f"DEBUG ({self.name}): Processing item at index {current_index_to_output}: {type(current_item)}"
        )

        # --- Advance State for Next Iteration ---
        self._current_index += 1

        # --- Return Outputs for Current Iteration ---
        # The engine needs to take 'item' and 'index', run the loop body,
        # and feed the result back into 'loop_input' on the *next* call to this build method.
        return {
            "item": current_item,
            "index": current_index_to_output,
            "done": None,  # Not done yet
            "is_looping": True,  # Still iterating
        }


# --- Helper definitions (ensure these match your actual framework files) ---
# definitions/inputs.py (ensure ListInput is defined if used)

# --- End Helper definitions ---
