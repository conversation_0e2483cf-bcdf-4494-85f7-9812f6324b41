"""
Dummy data for workflow service.
This file contains dummy data for workflow templates and workflows.
"""

import uuid
from datetime import datetime
from app.utils.constants.constants import (
    WorkflowCategoryEnum,
    WorkflowOwnerTypeEnum,
    WorkflowVisibilityEnum,
    WorkflowStatusEnum,
)

# Generate some consistent UUIDs for reference
USER_ID_1 = "user-" + str(uuid.uuid4())
USER_ID_2 = "user-" + str(uuid.uuid4())
ENTERPRISE_ID_1 = "enterprise-" + str(uuid.uuid4())
PLATFORM_ID = "platform-" + str(uuid.uuid4())

# Sample workflow schema (simplified)
SAMPLE_WORKFLOW_SCHEMA = {
    "connections": [
        {
            "from_transition_id": "start_node",
            "to_transition_id": "process_node",
            "conditional_routing": None
        },
        {
            "from_transition_id": "process_node",
            "to_transition_id": "end_node",
            "conditional_routing": None
        }
    ],
    "nodes": [
        {
            "id": "start_node",
            "type": "start",
            "position": {"x": 100, "y": 100},
            "data": {"label": "Start"}
        },
        {
            "id": "process_node",
            "type": "process",
            "position": {"x": 300, "y": 100},
            "data": {"label": "Process Data"}
        },
        {
            "id": "end_node",
            "type": "end",
            "position": {"x": 500, "y": 100},
            "data": {"label": "End"}
        }
    ]
}

# Sample builder schema (simplified)
SAMPLE_BUILDER_SCHEMA = {
    "layout": "default",
    "theme": "light",
    "zoom": 1.0,
    "pan": {"x": 0, "y": 0}
}

# Workflow Template Dummy Data
WORKFLOW_TEMPLATE_DUMMY_DATA = [
    {
        "id": "template-" + str(uuid.uuid4()),
        "name": "Data Processing Workflow",
        "description": "A workflow for processing and transforming data.",
        "image_url": "https://example.com/images/data_processing.png",
        "workflow_url": "https://storage.example.com/workflows/data_processing.json",
        "builder_url": "https://storage.example.com/builders/data_processing.json",
        "start_nodes": ["start_node"],
        "owner_id": PLATFORM_ID,
        "use_count": 85,
        "execution_count": 120,
        "average_rating": 4.6,
        "category": WorkflowCategoryEnum.DATA_PIPELINE,
        "tags": {
            "categories": ["data", "processing", "transformation"],
            "complexity": "medium",
            "use_cases": ["data cleaning", "ETL"]
        },
        "version": "1.0.0",
        "status": WorkflowStatusEnum.ACTIVE,
        "visibility": WorkflowVisibilityEnum.PUBLIC,
        "is_customizable": True,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    },
    {
        "id": "template-" + str(uuid.uuid4()),
        "name": "API Integration Workflow",
        "description": "A workflow for integrating with external APIs.",
        "image_url": "https://example.com/images/api_integration.png",
        "workflow_url": "https://storage.example.com/workflows/api_integration.json",
        "builder_url": "https://storage.example.com/builders/api_integration.json",
        "start_nodes": ["start_node"],
        "owner_id": PLATFORM_ID,
        "use_count": 120,
        "execution_count": 200,
        "average_rating": 4.8,
        "category": WorkflowCategoryEnum.INTEGRATION,
        "tags": {
            "categories": ["api", "integration", "external services"],
            "complexity": "high",
            "apis": ["REST", "GraphQL"]
        },
        "version": "1.0.0",
        "status": WorkflowStatusEnum.ACTIVE,
        "visibility": WorkflowVisibilityEnum.PUBLIC,
        "is_customizable": True,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    },
    {
        "id": "template-" + str(uuid.uuid4()),
        "name": "Document Processing Workflow",
        "description": "A workflow for processing and analyzing documents.",
        "image_url": "https://example.com/images/document_processing.png",
        "workflow_url": "https://storage.example.com/workflows/document_processing.json",
        "builder_url": "https://storage.example.com/builders/document_processing.json",
        "start_nodes": ["start_node"],
        "owner_id": PLATFORM_ID,
        "use_count": 65,
        "execution_count": 90,
        "average_rating": 4.5,
        "category": WorkflowCategoryEnum.DOCUMENT_PROCESSING,
        "tags": {
            "categories": ["document", "processing", "analysis"],
            "complexity": "medium",
            "document_types": ["PDF", "DOCX", "TXT"]
        },
        "version": "1.0.0",
        "status": WorkflowStatusEnum.ACTIVE,
        "visibility": WorkflowVisibilityEnum.PUBLIC,
        "is_customizable": True,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    }
]

# Workflow Dummy Data
WORKFLOW_DUMMY_DATA = [
    {
        "id": "workflow-" + str(uuid.uuid4()),
        "name": "My Data Processing Workflow",
        "description": "Customized data processing workflow for my specific needs.",
        "image_url": "https://example.com/images/my_data_processing.png",
        "workflow_url": "https://storage.example.com/workflows/my_data_processing.json",
        "builder_url": "https://storage.example.com/builders/my_data_processing.json",
        "start_nodes": ["start_node"],
        "owner_id": USER_ID_1,
        "user_ids": [USER_ID_1, USER_ID_2],
        "owner_type": WorkflowOwnerTypeEnum.USER,
        "workflow_template_id": WORKFLOW_TEMPLATE_DUMMY_DATA[0]["id"],
        "template_owner_id": PLATFORM_ID,
        "url": "https://example.com/workflows/my_data_processing",
        "is_imported": True,
        "version": "1.0.0",
        "is_changes_marketplace": False,
        "use_count": 15,
        "average_rating": 4.7,
        "visibility": WorkflowVisibilityEnum.PRIVATE,
        "category": WorkflowCategoryEnum.DATA_PIPELINE,
        "tags": {
            "categories": ["data", "processing"],
            "custom_tags": ["my-project"]
        },
        "status": WorkflowStatusEnum.ACTIVE,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    },
    {
        "id": "workflow-" + str(uuid.uuid4()),
        "name": "Team API Integration",
        "description": "API integration workflow customized for our team.",
        "image_url": "https://example.com/images/team_api.png",
        "workflow_url": "https://storage.example.com/workflows/team_api.json",
        "builder_url": "https://storage.example.com/builders/team_api.json",
        "start_nodes": ["start_node"],
        "owner_id": USER_ID_2,
        "user_ids": [USER_ID_1, USER_ID_2],
        "owner_type": WorkflowOwnerTypeEnum.USER,
        "workflow_template_id": WORKFLOW_TEMPLATE_DUMMY_DATA[1]["id"],
        "template_owner_id": PLATFORM_ID,
        "url": "https://example.com/workflows/team_api",
        "is_imported": True,
        "version": "1.0.0",
        "is_changes_marketplace": False,
        "use_count": 25,
        "average_rating": 4.9,
        "visibility": WorkflowVisibilityEnum.PRIVATE,
        "category": WorkflowCategoryEnum.INTEGRATION,
        "tags": {
            "categories": ["api", "integration"],
            "custom_tags": ["team-project"]
        },
        "status": WorkflowStatusEnum.ACTIVE,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    }
]

# Workflow Rating Dummy Data
WORKFLOW_RATING_DUMMY_DATA = [
    {
        "id": "rating-" + str(uuid.uuid4()),
        "workflow_id": WORKFLOW_DUMMY_DATA[0]["id"],
        "user_id": USER_ID_2,
        "rating": 5.0,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    },
    {
        "id": "rating-" + str(uuid.uuid4()),
        "workflow_id": WORKFLOW_DUMMY_DATA[0]["id"],
        "user_id": USER_ID_1,
        "rating": 4.5,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    },
    {
        "id": "rating-" + str(uuid.uuid4()),
        "workflow_id": WORKFLOW_DUMMY_DATA[1]["id"],
        "user_id": USER_ID_1,
        "rating": 4.8,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    },
    {
        "id": "rating-" + str(uuid.uuid4()),
        "workflow_id": WORKFLOW_DUMMY_DATA[1]["id"],
        "user_id": USER_ID_2,
        "rating": 5.0,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    }
]
