# Alter Metadata Component - User Guide

## Overview

The **Alter Metadata Component** is a powerful workflow node that allows you to modify metadata dictionaries by adding, updating, or removing key-value pairs. This component is essential for data processing workflows where you need to manipulate document metadata, configuration objects, or any dictionary-based data structures.

## Features

- ✅ **Add/Update Keys**: Add new keys or update existing values in metadata
- ✅ **Remove Keys**: Remove unwanted keys from metadata dictionaries
- ✅ **Combined Operations**: Perform both updates and removals in a single operation
- ✅ **Type Safety**: Validates input types and provides clear error messages
- ✅ **Deep Copy**: Preserves original data by creating deep copies
- ✅ **Dual-Purpose Inputs**: Each input can be connected from other nodes or entered directly

## Component Details

### Category
**Processing** - Data manipulation and transformation

### Inputs

| Input | Type | Required | Description |
|-------|------|----------|-------------|
| **Input Metadata** | Dictionary | Yes | The metadata dictionary to modify. Can be connected from another node or entered directly. |
| **Metadata Updates** | Dictionary | No | Key-value pairs to add or update in the metadata. Can be connected from another node or entered directly. |
| **Keys to Remove** | List | No | List of keys to remove from the metadata. Can be connected from another node or entered directly. |

### Outputs

| Output | Type | Description |
|--------|------|-------------|
| **Updated Metadata** | Dictionary | The modified metadata dictionary with updates applied and keys removed |
| **Error** | String | Error message if the operation fails |

## Usage Examples

### Example 1: Basic Metadata Update

**Scenario**: Update the version and add an author to a document's metadata.

**Input Metadata**:
```json
{
  "title": "My Document",
  "version": "1.0",
  "status": "draft"
}
```

**Metadata Updates**:
```json
{
  "version": "2.0",
  "author": "John Doe",
  "last_modified": "2024-01-15"
}
```

**Keys to Remove**: `[]` (empty)

**Result**:
```json
{
  "title": "My Document",
  "version": "2.0",
  "status": "draft",
  "author": "John Doe",
  "last_modified": "2024-01-15"
}
```

### Example 2: Remove Deprecated Fields

**Scenario**: Clean up metadata by removing deprecated and temporary fields.

**Input Metadata**:
```json
{
  "name": "Product A",
  "price": 99.99,
  "deprecated_field": "old_value",
  "temp_id": "12345",
  "category": "electronics"
}
```

**Metadata Updates**: `{}` (empty)

**Keys to Remove**: `["deprecated_field", "temp_id"]`

**Result**:
```json
{
  "name": "Product A",
  "price": 99.99,
  "category": "electronics"
}
```

### Example 3: Combined Update and Cleanup

**Scenario**: Update product information while removing old fields.

**Input Metadata**:
```json
{
  "product_name": "Widget",
  "old_price": 50.00,
  "version": "1.0",
  "temp_note": "remove this"
}
```

**Metadata Updates**:
```json
{
  "product_name": "Super Widget",
  "price": 75.00,
  "version": "2.0",
  "updated_date": "2024-01-15"
}
```

**Keys to Remove**: `["old_price", "temp_note"]`

**Result**:
```json
{
  "product_name": "Super Widget",
  "price": 75.00,
  "version": "2.0",
  "updated_date": "2024-01-15"
}
```

## Workflow Integration

### Connecting to Other Nodes

The Alter Metadata Component works seamlessly with other workflow nodes:

1. **Data Sources**: Connect from API calls, file readers, or database queries
2. **Processing Nodes**: Chain with other data processing components
3. **Output Nodes**: Send results to file writers, APIs, or databases

### Common Workflow Patterns

#### Pattern 1: API Response Processing
```
API Request → Parse JSON → Alter Metadata → Store Result
```

#### Pattern 2: Document Processing Pipeline
```
Read File → Extract Metadata → Alter Metadata → Update Database
```

#### Pattern 3: Configuration Management
```
Load Config → Validate → Alter Metadata → Apply Settings
```

## Best Practices

### 1. Input Validation
- Always validate that your input is a dictionary before processing
- Use the component's built-in validation to catch type errors early

### 2. Key Management
- Be careful when removing keys - ensure they exist or handle gracefully
- Use descriptive key names for better maintainability

### 3. Performance Considerations
- For large metadata objects (>1000 keys), consider breaking operations into smaller chunks
- The component creates deep copies, so memory usage scales with data size

### 4. Error Handling
- Always connect the Error output to handle potential failures
- Common errors include type mismatches and missing required fields

## Error Handling

### Common Error Scenarios

| Error | Cause | Solution |
|-------|-------|----------|
| "Input metadata must be a dictionary" | Wrong input type | Ensure input is a valid dictionary/object |
| "Updates must be a dictionary" | Wrong updates type | Provide updates as a dictionary/object |
| "Keys to remove must be a list" | Wrong keys type | Provide keys as an array/list |
| "Input metadata is missing" | No input provided | Connect or provide input metadata |

### Error Response Format
```json
{
  "error": "Descriptive error message explaining what went wrong"
}
```

## Advanced Usage

### Working with Nested Objects

The component can handle nested dictionary structures:

**Input**:
```json
{
  "user": {
    "name": "John",
    "profile": {
      "age": 30,
      "city": "New York"
    }
  },
  "settings": {
    "theme": "dark"
  }
}
```

**Updates**:
```json
{
  "user": {
    "name": "John Doe",
    "profile": {
      "age": 31,
      "city": "Boston",
      "country": "USA"
    }
  }
}
```

**Note**: Updates replace entire nested objects, so include all fields you want to preserve.

### Dynamic Key Operations

You can dynamically generate keys to remove or update by connecting from other processing nodes:

```
Process Data → Generate Key List → Alter Metadata
```

## Troubleshooting

### Issue: Component not appearing in sidebar
- **Solution**: Ensure the component is properly registered in both Workflow Service and Node Executor Service

### Issue: Validation errors during execution
- **Solution**: Check input types and ensure all required fields are provided

### Issue: Unexpected output format
- **Solution**: Verify that updates are properly formatted as key-value pairs

### Issue: Performance issues with large datasets
- **Solution**: Consider breaking large operations into smaller chunks or using batch processing

## Migration from Legacy Components

If you're migrating from older metadata processing components:

1. **Input Changes**: Use the new dual-purpose inputs instead of separate handle/direct inputs
2. **Method Updates**: The component now uses modern `execute` methods with better error handling
3. **Output Format**: Results are now wrapped in a structured format with proper error handling

## API Reference

### Component Class: `AlterMetadataComponent`

#### Methods

##### `async execute(context: WorkflowContext) -> NodeResult`
Modern execution method that processes metadata alterations.

**Parameters**:
- `context`: Workflow execution context containing input values

**Returns**:
- `NodeResult`: Contains execution status, outputs, and timing information

##### `build(**kwargs) -> Dict[str, Any]` (Legacy)
Legacy execution method maintained for backward compatibility.

**Parameters**:
- `**kwargs`: Input values as keyword arguments

**Returns**:
- `Dict`: Result dictionary with outputs or error information

**Note**: This method is deprecated. Use `execute` method for new implementations.

## Support and Resources

- **Component Documentation**: See inline help text in the workflow builder
- **Example Workflows**: Check the examples directory for sample implementations
- **API Documentation**: Refer to the full API documentation for advanced usage
- **Community Support**: Join the workflow platform community for help and best practices
