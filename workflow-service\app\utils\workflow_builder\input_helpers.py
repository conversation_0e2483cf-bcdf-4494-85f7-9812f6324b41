"""
Utility functions for creating and managing component inputs.
"""

from typing import List, Any, Optional, Type, Union
from app.models.workflow_builder.components import (
    InputBase,
    StringInput,
    IntInput,
    FloatInput,
    BoolInput,
    DropdownInput,
    ListInput,
    DictInput,
    MultilineInput,
    CodeInput,
    HandleInput,
    FileInput,
    HiddenInput,
    ButtonInput,
    DynamicHandleInput,
)


def create_dual_purpose_input(
    name: str,
    display_name: str,
    input_type: str = "string",
    value: Any = None,
    required: bool = False,
    info: Optional[str] = None,
    input_types: Optional[List[str]] = None,
    **kwargs,
) -> InputBase:
    """
    Creates an input that can be both connected and directly edited when not connected.

    Args:
        name: The name of the input
        display_name: The display name shown in the UI
        input_type: The type of input (string, int, float, bool, dropdown, list, dict, multiline, code, file)
        value: Default value
        required: Whether the input is required
        info: Help text for the input
        input_types: Types of inputs this can connect to
        **kwargs: Additional parameters for the input

    Returns:
        An InputBase object configured for dual-purpose use
    """
    if input_types is None:
        input_types = [input_type, "Any"]
    elif "Any" not in input_types:
        input_types.append("Any")

    # Create the appropriate input type based on input_type
    if input_type == "string":
        return StringInput(
            name=name,
            display_name=display_name,
            value=value if value is not None else "",
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
    elif input_type == "int":
        return IntInput(
            name=name,
            display_name=display_name,
            value=value if value is not None else 0,
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
    elif input_type == "float":
        return FloatInput(
            name=name,
            display_name=display_name,
            value=value if value is not None else 0.0,
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
    elif input_type == "bool":
        return BoolInput(
            name=name,
            display_name=display_name,
            value=value if value is not None else False,
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
    elif input_type == "dropdown":
        return DropdownInput(
            name=name,
            display_name=display_name,
            options=kwargs.get("options", []),
            value=(
                value
                if value is not None
                else (kwargs.get("options", [""])[0] if kwargs.get("options") else "")
            ),
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
    elif input_type == "list":
        return ListInput(
            name=name,
            display_name=display_name,
            value=value if value is not None else [],
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
    elif input_type == "dict":
        return DictInput(
            name=name,
            display_name=display_name,
            value=value if value is not None else {},
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
    elif input_type == "multiline":
        return MultilineInput(
            name=name,
            display_name=display_name,
            value=value if value is not None else "",
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
    elif input_type == "code":
        return CodeInput(
            name=name,
            display_name=display_name,
            value=value if value is not None else "",
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
    elif input_type == "file":
        return FileInput(
            name=name,
            display_name=display_name,
            value=value,
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
    else:
        # Default to string for unknown types
        return StringInput(
            name=name,
            display_name=display_name,
            value=str(value) if value is not None else "",
            required=required,
            info=info,
            is_handle=True,
            input_types=input_types,
            **kwargs,
        )
