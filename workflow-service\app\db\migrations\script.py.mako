"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql import table, column
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision: str = ${repr(up_revision)}
down_revision: Union[str, None] = ${repr(down_revision)}
branch_labels: Union[str, Sequence[str], None] = ${repr(branch_labels)}
depends_on: Union[str, Sequence[str], None] = ${repr(depends_on)}

# Define table references for use in migrations
workflows = table(
    'workflows',
    column('id', sa.String),
    column('name', sa.String),
    column('description', sa.String),
    column('tags', postgresql.JSON),
    # Add other columns as needed
)

workflow_templates = table(
    'workflow-templates',
    column('id', sa.String),
    column('name', sa.String),
    column('description', sa.String),
    column('tags', postgresql.JSON),
    # Add other columns as needed
)

def upgrade() -> None:
    """
    Implement your upgrade migrations here.
    """
    ${upgrades if upgrades else "pass"}

def downgrade() -> None:
    """
    Implement your downgrade migrations here (to revert changes made in upgrade).
    """
    ${downgrades if downgrades else "pass"}
