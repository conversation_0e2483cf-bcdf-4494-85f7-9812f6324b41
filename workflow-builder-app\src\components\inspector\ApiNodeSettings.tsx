import React from "react";
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { InputRenderer } from "./InputRenderer";
import { checkInputVisibility } from "@/utils/inputVisibility";
import { FormField } from "./FormField";

interface ApiNodeSettingsProps {
  node: Node<WorkflowNodeData>;
  onConfigChange: (inputName: string, value: any) => void;
  isInputConnected: (inputName: string) => boolean;
  shouldDisableInput: (inputName: string) => boolean;
  getConnectionInfo: (inputName: string) => {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };
}

/**
 * Component for API node specific settings
 */
export function ApiNodeSettings({
  node,
  onConfigChange,
  isInputConnected,
  shouldDisableInput,
  getConnectionInfo,
}: ApiNodeSettingsProps) {
  return (
    <div className="space-y-6">
      {/* Basic Settings Section */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Basic Settings</h3>
        {/* URL and Method fields */}
        {node.data.definition?.inputs
          .filter(
            (inputDef) =>
              !inputDef.is_handle &&
              ["url", "method"].includes(inputDef.name),
          )
          .map((inputDef) => (
            <FormField
              key={inputDef.name}
              inputDef={inputDef}
              node={node}
              onConfigChange={onConfigChange}
              isInputConnected={isInputConnected}
              shouldDisableInput={shouldDisableInput}
              getConnectionInfo={getConnectionInfo}
            />
          ))}
      </div>

      {/* Request Parameters Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Request Parameters</h3>
          {node.data.config?.method && (
            <Badge
              variant={(() => {
                const method = node.data.config.method;
                if (method === "GET") return "info";
                if (method === "POST") return "success";
                if (method === "PUT" || method === "PATCH")
                  return "warning";
                if (method === "DELETE") return "destructive";
                return "outline";
              })()}
              className="text-[10px]"
            >
              {node.data.config.method}
            </Badge>
          )}
        </div>
        
        {/* Show query_params for all methods */}
        {node.data.definition?.inputs
          .filter((inputDef) => inputDef.name === "query_params")
          .map((inputDef) => (
            <FormField
              key={inputDef.name}
              inputDef={inputDef}
              node={node}
              onConfigChange={onConfigChange}
              isInputConnected={isInputConnected}
              shouldDisableInput={shouldDisableInput}
              getConnectionInfo={getConnectionInfo}
            />
          ))}

        {/* Show body only for POST, PUT, PATCH */}
        {["POST", "PUT", "PATCH"].includes(
          node.data.config?.method,
        ) &&
          node.data.definition?.inputs
            .filter((inputDef) => inputDef.name === "body")
            .map((inputDef) => {
              // Check visibility rules
              const isVisible = checkInputVisibility(
                inputDef,
                node,
                node.data.config || {},
              );
              if (!isVisible) return null;

              return (
                <FormField
                  key={inputDef.name}
                  inputDef={inputDef}
                  node={node}
                  onConfigChange={onConfigChange}
                  isInputConnected={isInputConnected}
                  shouldDisableInput={shouldDisableInput}
                  getConnectionInfo={getConnectionInfo}
                />
              );
            })}

        {/* Headers for all methods */}
        {node.data.definition?.inputs
          .filter((inputDef) => inputDef.name === "headers")
          .map((inputDef) => (
            <FormField
              key={inputDef.name}
              inputDef={inputDef}
              node={node}
              onConfigChange={onConfigChange}
              isInputConnected={isInputConnected}
              shouldDisableInput={shouldDisableInput}
              getConnectionInfo={getConnectionInfo}
            />
          ))}
      </div>

      {/* Advanced Options Section */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Advanced Options</h3>
        {node.data.definition?.inputs
          .filter(
            (inputDef) =>
              !inputDef.is_handle &&
              ![
                "url",
                "method",
                "query_params",
                "headers",
                "body",
              ].includes(inputDef.name),
          )
          .map((inputDef) => {
            // Check visibility rules
            const isVisible = checkInputVisibility(
              inputDef,
              node,
              node.data.config || {},
            );
            if (!isVisible) return null;

            return (
              <FormField
                key={inputDef.name}
                inputDef={inputDef}
                node={node}
                onConfigChange={onConfigChange}
                isInputConnected={isInputConnected}
                shouldDisableInput={shouldDisableInput}
                getConnectionInfo={getConnectionInfo}
              />
            );
          })}
      </div>
    </div>
  );
}
