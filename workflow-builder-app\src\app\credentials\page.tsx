"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import dynamic from "next/dynamic";

// Dynamically import the CredentialManager component with SSR disabled
// This prevents hydration errors by ensuring the component only renders on the client
const CredentialManager = dynamic(() => import("@/components/credentials/CredentialManager"), {
  ssr: false,
  loading: () => <div className="p-4">Loading credential manager...</div>,
});

export default function CredentialsPage() {
  return (
    <main className="flex min-h-screen flex-col p-6">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Credential Management</h1>
        <Link href="/">
          <Button variant="outline">Back to Workflow Builder</Button>
        </Link>
      </div>

      <div className="flex-grow">
        <CredentialManager />
      </div>
    </main>
  );
}
