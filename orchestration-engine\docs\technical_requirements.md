# Orchestration Engine - Comprehensive Technical Requirements Document

## 1. Introduction

### 1.1 Purpose

This comprehensive technical requirements document specifies the detailed technical specifications, architecture, and implementation requirements for the Orchestration Engine. It serves as a guide for developers implementing, maintaining, or extending the system.

### 1.2 Scope

This document covers:

- Technical architecture and component specifications
- Data structures and schemas
- API specifications
- Implementation requirements
- Performance specifications
- Integration details
- Development, testing, and deployment requirements

### 1.3 System Context

The Orchestration Engine operates within a distributed system environment, interacting with:

- Kafka messaging infrastructure
- Redis data stores
- MCP (Model Context Protocol) servers for execution
- Client applications requesting workflow execution

Note: MCP (Model Context Protocol) servers are the only external execution components supported by the orchestration engine. The system is specifically designed to execute MCP servers, not generic tools (as of now).

## 2. System Architecture

### 2.1 High-Level Architecture

```text
┌─────────────────────────────────────────────────────────────────────────┐
│                         Orchestration Engine                            │
│                                                                         │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌──────────┐  │
│  │ Workflow    │    │ Transition  │    │ State       │    │ Workflow │  │
│  │ Engine      │◄──►│ Handler     │◄──►│ Manager     │◄──►│ Utils    │  │
│  └──────┬──────┘    └──────┬──────┘    └──────┬──────┘    └──────────┘  │
│         │                  │                  │                         │
│         │                  │                  │                         │
│         ▼                  ▼                  ▼                         │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                  │
│  │ MCP         │    │ Kafka       │    │ Redis       │                  │
│  │ Executor    │◄──►│ Interface   │◄──►│ Interface   │                  │
│  └──────┬──────┘    └──────┬──────┘    └──────┬──────┘                  │
│         │                  │                  │                         │
└─────────┼──────────────────┼──────────────────┼─────────────────────────┘
          │                  │                  │
          ▼                  ▼                  ▼
    ┌───────────┐     ┌────────────┐     ┌────────────┐
    │ MCP       │     │ Kafka      │     │ Redis      │
    │ Servers   │     │ Cluster    │     │ Cluster    │
    └───────────┘     └────────────┘     └────────────┘
```

### 2.2 Core Components

#### 2.2.1 Workflow Engine (EnhancedWorkflowEngine)

- **Implementation Language**: Python 3.8+
- **Core Libraries**:
  - `asyncio` for async operations
  - `jsonschema` for schema validation
  - `redis` for state management
  - `aiokafka` for message handling
- **Design Pattern**: Event-driven architecture
- **Responsibilities**:
  - Validates workflow schemas
  - Manages workflow execution flow
  - Coordinates transition execution
  - Handles reflection and conditional routing

#### 2.2.2 State Manager (WorkflowStateManager)

- **Responsibilities**:
  - Manages workflow execution state
  - Tracks transition states (pending, waiting, completed)
  - Persists state to Redis
  - Handles dependency resolution
  - Provides state recovery capabilities
- **State Structure**:

  ```python
  {
    "pending_transitions": ["transition_id"],
    "completed_transitions": ["transition_id"],
    "waiting_transitions": ["transition_id"],
    "terminated": boolean,
    "paused": boolean
  }
  ```

#### 2.2.3 Transition Handler (TransitionHandler)

- **Responsibilities**:
  - Executes individual transitions
  - Manages MCP server execution
  - Handles reflection logic
  - Processes conditional routing
  - Supports transition regeneration
- **Execution Modes**:
  - Sequential for reflection transitions
  - Parallel for standard transitions

#### 2.2.4 Workflow Utils (WorkflowUtils)

- **Responsibilities**:
  - Validates workflow schemas
  - Formats MCP server parameters
  - Evaluates conditional expressions
  - Resolves switch-case routing
- **Conditional Operators**:
  - equals, not_equals, contains
  - starts_with, ends_with
  - greater_than, less_than
  - exists, is_empty
  - ai_evaluate (for AI-based evaluation)

#### 2.2.5 MCP Executor (KafkaToolExecutor)

- **Responsibilities**:
  - Executes MCP servers via Kafka messaging
  - Manages request/response communication
  - Handles MCP server execution errors
  - Provides timeout management
- **Execution Flow**:
  1. Format MCP server parameters
  2. Send execution request to MCP Kafka topic
  3. Wait for response on MCP result topic
  4. Process and return result

### 2.3 Service Components

#### 2.3.1 Message Broker (Kafka)

- **Minimum Version**: 2.8+
- **Required Features**:
  - Topic partitioning
  - Message persistence
  - Consumer groups
  - Dead letter queues
- **Configuration**:
  - Replication factor: 3
  - Min in-sync replicas: 2
  - Message retention: 7 days
  - Max message size: 500MB
- **Topics**:
  - workflow-request
  - execution-request
  - approval-request
  - mcp-execution-request
  - mcp-execution-result

#### 2.3.2 State Store (Redis)

- **Minimum Version**: 6.0+
- **Required Features**:
  - Key-value storage
  - Pub/sub messaging
  - Transaction support
- **Configuration**:
  - Persistence mode: RDB + AOF
  - Eviction policy: volatile-lru
  - Multiple databases for separation of concerns:
    - DB 0: Results storage
    - DB 1: State persistence

## 3. Workflow Implementation

### 3.1 Workflow Definition

#### 3.1.1 JSON Schema Requirements

The system shall support a JSON schema for workflow definitions that includes:

```json
{
  "type": "object",
  "required": ["nodes", "transitions"],
  "properties": {
    "nodes": {
      "type": "array",
      "items": {
        "type": "object",
        "required": ["id", "server_script_path", "server_tools"],
        "properties": {
          "id": {"type": "string"},
          "server_script_path": {"type": "string"},
          "server_tools": {
            "type": "array",
            "items": {
              "type": "object",
              "required": ["tool_id", "tool_name", "input_schema", "output_schema"],
              "properties": {
                "tool_id": {"type": "string"},
                "tool_name": {"type": "string"},
                "input_schema": {"type": "object"},
                "output_schema": {"type": "object"}
              }
            }
          }
        }
      }
    },
    "transitions": {
      "type": "array",
      "items": {
        "type": "object",
        "required": ["id", "sequence", "transition_type", "node_info"],
        "properties": {
          "id": {"type": "string"},
          "sequence": {"type": "integer"},
          "transition_type": {
            "type": "string",
            "enum": ["initial", "standard", "reflection"]
          },
          "node_info": {
            "type": "object",
            "required": ["node_id"],
            "properties": {
              "node_id": {"type": "string"},
              "tools_to_use": {
                "type": "array",
                "items": {
                  "type": "object",
                  "required": ["tool_id", "tool_name"],
                  "properties": {
                    "tool_id": {"type": "string"},
                    "tool_name": {"type": "string"},
                    "tool_params": {
                      "type": "object",
                      "properties": {
                        "items": {
                          "type": "array",
                          "items": {
                            "type": "object",
                            "properties": {
                              "name": {"type": "string"},
                              "value": {}
                            }
                          }
                        }
                      }
                    }
                  }
                }
              },
              "input_data": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "from_transition_id": {"type": "string"},
                    "from_key": {"type": "string"},
                    "to_key": {"type": "string"}
                  }
                }
              },
              "output_data": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "to_transition_id": {"type": "string"}
                  }
                }
              }
            }
          },
          "conditional_routing": {
            "type": "object",
            "properties": {
              "cases": {
                "type": "array",
                "items": {
                  "type": "object",
                  "required": ["condition", "next_transition"],
                  "properties": {
                    "condition": {
                      "type": "object",
                      "required": ["operator"],
                      "properties": {
                        "source": {
                          "type": "string",
                          "enum": ["node_output", "global_context"]
                        },
                        "operator": {
                          "type": "string",
                          "enum": [
                            "equals", "not_equals", "contains",
                            "starts_with", "ends_with", "greater_than",
                            "less_than", "exists", "is_empty", "ai_evaluate"
                          ]
                        },
                        "variable_name": {"type": "string"},
                        "expected_value": {}
                      }
                    },
                    "next_transition": {"type": "string"}
                  }
                }
              },
              "default_transition": {"type": "string"},
              "global_context_definitions": {
                "type": "object",
                "properties": {
                  "variables": {
                    "type": "array",
                    "items": {
                      "type": "object",
                      "properties": {
                        "name": {"type": "string"},
                        "value": {}
                      }
                    }
                  }
                }
              }
            }
          },
          "reflection": {
            "type": "object",
            "properties": {
              "iteration_count": {"type": "integer"},
              "max_iterations": {"type": "integer"}
            }
          },
          "approval_required": {"type": "boolean"},
          "end": {"type": "boolean"}
        }
      }
    }
  }
}
```

#### 3.1.2 Validation Rules

- All node IDs must be unique
- All transition IDs must be unique
- Transition sequence numbers must be unique
- There must be exactly one initial transition
- All node_id references must point to existing nodes
- All transition_id references must point to existing transitions
- Circular dependencies are not allowed
- Schema validation using JSON Schema Draft-07

### 3.2 Task Execution

#### 3.2.1 Parallel Processing

- **Implementation**: `asyncio` tasks
- **Maximum concurrent tasks**: 1000
- **Task priority**:
  - Reflection transitions have priority over standard transitions
  - Standard transitions execute in parallel
- **Timeout handling**: Configurable per task

#### 3.2.2 State Management

- **Transition States**:
  - Pending: Ready to execute
  - Waiting: Dependencies not yet met
  - Completed: Successfully executed
- **Dependency Resolution**:
  - Transitions only execute when all dependencies are met
  - Dependencies are tracked in a dependency map
  - Waiting transitions move to pending when dependencies are satisfied
- **State Persistence**:
  - State is persisted to Redis after each execution cycle
  - In-memory fallback when Redis is unavailable
  - State can be loaded for workflow resumption

#### 3.2.3 Reflection Processing

- **Purpose**: Support for iterative processing
- **Features**:
  - Iteration count tracking
  - Maximum iteration limit
  - Priority execution over standard transitions
  - Conditional termination

#### 3.2.4 Conditional Routing

- **Evaluation Sources**:
  - Node output
  - Global context variables
- **Operators**:
  - Basic: equals, not_equals, contains, etc.
  - Advanced: ai_evaluate for complex conditions
- **Default Routing**: Fallback when no conditions match

## 4. API Specifications

### 4.1 Kafka API

#### 4.1.1 Workflow Request Topic

- **Topic Name**: `workflow-request`
- **Purpose**: Initiate workflow execution
- **Message Format**:

  ```json
  {
    "data": {
      "workflow_id": "string",
      "payload": {
        "user_dependent_fields": ["string"],
        "user_payload_template": {
          "key": "value"
        }
      }
    },
    "server_id": "string",
    "params": {
      "key": "value"
    },
    "approval": false
  }
  ```

- **Headers**:
  - `correlationId`: Unique identifier for the request
  - `reply-topic`: Topic to send responses to

#### 4.1.2 Execution Request Topic

- **Topic Name**: `execution-request`
- **Purpose**: Control running workflows (regenerate, retry)
- **Message Format**:

  ```json
  {
    "action": "string",
    "node_id": "string",
    "params": {
      "key": "value"
    }
  }
  ```

- **Headers**:
  - `correlationId`: Workflow identifier
  - `reply-topic`: Topic to send responses to

#### 4.1.3 MCP Server Execution Request Topic

- **Topic Name**: `mcp-execution-request`
- **Purpose**: Request MCP server execution
- **Message Format**:

  ```json
  {
    "server_script_path": "string",
    "tool_name": "string",
    "tool_parameters": {
      "key": "value"
    },
    "request_id": "string"
  }
  ```

#### 4.1.4 MCP Server Execution Result Topic

- **Topic Name**: `mcp-execution-result`
- **Purpose**: Receive MCP server execution results
- **Message Format**:

  ```json
  {
    "request_id": "string",
    "result": {},
    "error": "string"
  }
  ```

### 4.2 REST Endpoints (Optional)

#### 4.2.1 Workflow Management

```plaintext
POST /api/v1/workflows
GET /api/v1/workflows/{workflow_id}
GET /api/v1/workflows
PUT /api/v1/workflows/{workflow_id}
```

#### 4.2.2 Kafka Integration

```plaintext
POST /api/v1/kafka/workflow-requests
POST /api/v1/kafka/execution-requests
POST /api/v1/kafka/approval-requests
```

### 4.3 Programmatic API

#### 4.3.1 EnhancedWorkflowEngine

```python
class EnhancedWorkflowEngine:
    def __init__(
        self,
        init_workflow: dict,
        mcp_executor,
        result_callback,
        approval=False,
        workflow_id=None,
    ):
        """Initialize the workflow engine."""

    async def execute(self, state="init") -> bool:
        """Execute the workflow."""

    async def resume_workflow_from_state(self) -> bool:
        """Resume workflow execution from saved state."""
```

#### 4.3.2 WorkflowStateManager

```python
class WorkflowStateManager:
    def __init__(self, workflow_id=None):
        """Initialize the state manager."""

    def initialize_workflow(self, initial_transition_id):
        """Initialize workflow with first transition."""

    def mark_transition_completed(self, transition_id, result=None):
        """Mark transition as completed and store result."""

    def get_transition_result(self, transition_id):
        """Get result of a transition execution."""

    def reset_to_transition(self, start_transition_id, transitions_by_id, dependency_map):
        """Reset workflow to start from specific transition."""

    async def save_workflow_state(self):
        """Save workflow state to Redis."""

    async def load_workflow_state(self):
        """Load workflow state from Redis."""
```

#### 4.3.3 TransitionHandler

```python
class TransitionHandler:
    def __init__(
        self,
        state_manager,
        transitions_by_id,
        nodes,
        dependency_map,
        workflow_utils,
        mcp_executor,
        result_callback=None,
        approval=False,
    ):
        """Initialize the transition handler."""

    async def _execute_transition_with_tracking(self, transition):
        """Execute transition with tracking."""

    async def _handle_reflection_logic(self, transition) -> list[str]:
        """Handle reflection logic."""

    async def regenerate_transition(
        self, transition_id, action_type, server_params_override=None
    ):
        """Regenerate a specific transition."""
```

## 5. MCP Server Integration

### 5.1 MCP Server Registration

```python
{
  "tool_id": "string",
  "tool_name": "string",
  "server_script_path": "string",
  "input_schema": {
    "type": "object",
    "properties": {}
  },
  "output_schema": {
    "type": "object",
    "properties": {}
  },
  "timeout": "integer",
  "retry_config": {
    "max_retries": "integer",
    "backoff_factor": "float"
  }
}
```

### 5.2 MCP Server Execution Process

1. **Parameter Formatting**:
   - Format parameters based on MCP server input schema
   - Incorporate results from previous transitions
   - Apply parameter overrides if provided

2. **MCP Execution Request**:
   - Send request to MCP execution Kafka topic
   - Include unique request ID for correlation
   - Specify server_script_path and tool_name

3. **Result Processing**:
   - Receive result from MCP result topic
   - Format result based on output schema
   - Store result for use in subsequent transitions

4. **Error Handling**:
   - Detect and log MCP server execution errors
   - Provide detailed error information
   - Support retry mechanisms for transient failures

## 6. Error Handling

### 6.1 Error Categories

#### 6.1.1 System Errors

- Connection failures (Kafka, Redis)
- Timeout errors
- Resource exhaustion
- Configuration errors

#### 6.1.2 Business Logic Errors

- Invalid workflow definition
- Tool execution failures
- State transition errors
- Dependency resolution errors

### 6.2 Recovery Strategies

#### 6.2.1 Retry Policies

```python
{
  "max_retries": 3,
  "backoff_factor": 1.5,
  "max_delay": 300,  # seconds
  "retry_on": ["ConnectionError", "Timeout"]
}
```

#### 6.2.2 Fallback Mechanisms

- In-memory state when Redis is unavailable
- Default routing when conditional evaluation fails
- Graceful degradation during partial system failures

#### 6.2.3 Error Reporting

- Detailed error logs with context
- Error callbacks for client notification
- Error metrics for monitoring

## 7. Performance Requirements

### 7.1 Throughput

- Support for at least 100 concurrent workflow executions
- Support for at least 1000 transitions per minute
- Support for workflows with up to 1000 nodes

### 7.2 Latency

- Transition execution initiation within 100ms
- Tool execution response processing within 100ms
- State persistence within 50ms

### 7.3 Scalability

- Horizontal scaling through multiple instances
- Support for Kafka partitioning for load distribution
- Redis clustering for state storage scaling

## 8. Monitoring and Logging

### 8.1 Metrics

#### 8.1.1 Performance Metrics

- Task execution time
- Queue latency
- Memory usage
- CPU utilization
- Error rates

#### 8.1.2 Business Metrics

- Workflow completion rate
- Tool success rate
- Average workflow duration
- Active workflows count

### 8.2 Logging

#### 8.2.1 Log Format

```json
{
  "timestamp": "ISO8601",
  "level": "string",
  "logger": "string",
  "workflow_id": "string",
  "transition_id": "string",
  "message": "string",
  "context": "object"
}
```

#### 8.2.2 Log Levels

- DEBUG: Detailed debugging information
- INFO: General operational information
- WARNING: Potential issues that don't affect execution
- ERROR: Errors that affect execution but don't terminate
- CRITICAL: Critical errors that terminate execution

#### 8.2.3 Structured Logging

- JSON-formatted logs
- Consistent field names
- Correlation IDs for request tracing
- Context information for debugging

## 9. Security Requirements

### 9.1 Authentication and Authorization

- Kafka SASL authentication support
- Redis password authentication support
- API key validation for management endpoints

### 9.2 Data Protection

- TLS encryption for Kafka communication
- TLS encryption for Redis communication
- Secure handling of sensitive data in workflows

### 9.3 Audit Logging

- Log all workflow operations
- Log all administrative actions
- Include user/system identifiers in logs
- Maintain log integrity

## 10. Deployment Requirements

### 10.1 Docker Configuration

```yaml
version: '3.8'
services:
  orchestrator:
    image: orchestration-engine
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_RESULTS_DB_INDEX=0
      - REDIS_STATE_DB_INDEX=1
      - LOG_LEVEL=INFO
    resources:
      limits:
        memory: 4G
        cpus: '2'
```

### 10.2 Environment Variables

```plaintext
# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=kafka:9092
KAFKA_WORKFLOW_REQUEST_TOPIC=workflow-request
KAFKA_EXECUTION_REQUEST_TOPIC=execution-request
KAFKA_APPROVAL_REQUEST_TOPIC=approval-request
KAFKA_MCP_EXECUTION_REQUEST_TOPIC=mcp-execution-request
KAFKA_MCP_EXECUTION_RESULT_TOPIC=mcp-execution-result
KAFKA_MAIN_CONSUMER_GROUP_ID=orchestration-engine

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_RESULTS_DB_INDEX=0
REDIS_STATE_DB_INDEX=1

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Schema Configuration
SCHEMA_FILE_PATH=./app/shared/json_schemas/workflow_schema.json
```

### 10.3 Kubernetes Deployment

- Deployment manifest
- Service definition
- ConfigMap for configuration
- Readiness and liveness probes

## 11. Development and Testing

### 11.1 Development Environment

- Python 3.8+
- Poetry for dependency management
- Docker for containerization
- Local Kafka and Redis instances

### 11.2 Testing Requirements

#### 11.2.1 Unit Testing

- Test coverage target: 80%+ for core components
- Use pytest for test framework
- Use mock objects for external dependencies
- Test all error handling paths

#### 11.2.2 Integration Testing

- Test workflow execution end-to-end
- Test Kafka integration with real Kafka instance
- Test Redis integration with real Redis instance
- Test error recovery scenarios

#### 11.2.3 Performance Testing

- Test concurrent workflow execution
- Test large workflow handling
- Test recovery from failures
- Measure and report execution times

### 11.3 CI/CD Pipeline

- Automated testing on commit
- Code quality checks
- Docker image building
- Deployment to staging environment

## Appendix A: Dependency List

| Dependency | Version | Purpose |
|------------|---------|---------|
| aiokafka | ^0.8.0 | Async Kafka client |
| redis | ^4.5.1 | Redis client |
| jsonschema | ^4.17.3 | JSON schema validation |
| pydantic | ^1.10.7 | Data validation |
| fastapi | ^0.95.0 | API framework (optional) |
| uvicorn | ^0.21.1 | ASGI server (optional) |
| pytest | ^7.3.1 | Testing framework |
| pytest-asyncio | ^0.21.0 | Async testing support |
| black | ^23.3.0 | Code formatting |
| mypy | ^1.2.0 | Type checking |

## Appendix B: Performance Benchmarks

| Metric | Target | Method |
|--------|--------|--------|
| Workflow initialization | <100ms | Load test with 100 concurrent requests |
| Transition execution | <200ms | Execute workflow with 50 transitions |
| Tool execution round-trip | <500ms | Execute 100 tool calls in parallel |
| State persistence | <50ms | Save state for workflow with 100 transitions |
| Concurrent workflows | 100+ | Run 100 workflows simultaneously |
| Recovery time | <5s | Measure time to recover from process restart |

## Appendix C: Error Codes and Handling

| Error Code | Description | Handling Strategy |
|------------|-------------|------------------|
| E001 | Workflow validation error | Return detailed validation errors |
| E002 | Transition execution error | Log error, mark transition as failed |
| E003 | Tool execution error | Retry with exponential backoff |
| E004 | Redis connection error | Fall back to in-memory, retry connection |
| E005 | Kafka connection error | Retry with exponential backoff |
| E006 | State corruption error | Attempt recovery from last valid state |
| E007 | Dependency resolution error | Log detailed dependency information |
| E008 | Configuration error | Fail fast with clear error message |
