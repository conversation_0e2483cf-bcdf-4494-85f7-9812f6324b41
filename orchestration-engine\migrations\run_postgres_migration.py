#!/usr/bin/env python3
"""
PostgreSQL Migration Script for Orchestration Engine

This script runs the SQL migration to create the necessary tables
for the PostgreSQL persistent storage implementation.
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import argparse
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("postgres-migration")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run PostgreSQL migrations")
    parser.add_argument(
        "--host", default=os.environ.get("DB_HOST", "localhost"), help="Database host"
    )
    parser.add_argument(
        "--port", default=os.environ.get("DB_PORT", "5432"), help="Database port"
    )
    parser.add_argument(
        "--user", default=os.environ.get("DB_USER", "postgres"), help="Database user"
    )
    parser.add_argument(
        "--password",
        default=os.environ.get("DB_PASSWORD", ""),
        help="Database password",
    )
    parser.add_argument(
        "--dbname",
        default=os.environ.get("DB_NAME", "orchestration_engine"),
        help="Database name",
    )
    parser.add_argument(
        "--create-db",
        action="store_true",
        help="Create the database if it doesn't exist",
    )
    return parser.parse_args()


def create_database_if_not_exists(args):
    """Create the database if it doesn't exist."""
    try:
        # Connect to PostgreSQL server
        conn = psycopg2.connect(
            host=args.host,
            port=args.port,
            user=args.user,
            password=args.password,
            # Connect to 'postgres' database to create a new database
            dbname="postgres",
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()

        # Check if database exists
        cursor.execute(
            "SELECT 1 FROM pg_database WHERE datname = %s", (args.dbname,)
        )
        exists = cursor.fetchone()

        if not exists:
            logger.info(f"Creating database '{args.dbname}'...")
            cursor.execute(f'CREATE DATABASE "{args.dbname}"')
            logger.info(f"Database '{args.dbname}' created successfully")
        else:
            logger.info(f"Database '{args.dbname}' already exists")

        cursor.close()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        return False


def run_migration(args):
    """Run the SQL migration script."""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=args.host,
            port=args.port,
            user=args.user,
            password=args.password,
            dbname=args.dbname,
        )
        cursor = conn.cursor()

        # Read the migration SQL file
        migration_file = os.path.join(
            os.path.dirname(__file__), "create_postgres_tables.sql"
        )
        with open(migration_file, "r") as f:
            migration_sql = f.read()

        # Execute the migration
        logger.info("Running migration...")
        cursor.execute(migration_sql)
        conn.commit()
        logger.info("Migration completed successfully")

        # Verify tables were created
        cursor.execute(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
        )
        tables = cursor.fetchall()
        logger.info("Tables in database:")
        for table in tables:
            logger.info(f"  - {table[0]}")

        cursor.close()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error running migration: {e}")
        return False


def main():
    """Main function."""
    args = parse_args()

    # Create database if requested
    if args.create_db:
        if not create_database_if_not_exists(args):
            sys.exit(1)

    # Run migration
    if not run_migration(args):
        sys.exit(1)

    logger.info("Migration process completed successfully")


if __name__ == "__main__":
    main()
