import React from "react";
import { InputDefinition } from "@/types";
import { ValidationWrapper } from "../ValidationWrapper";
import { Textarea } from "@/components/ui/textarea";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { cn } from "@/lib/utils";
import { formatValueForDisplay } from "@/utils/valueFormatting";

interface ArrayInputProps {
  inputDef: InputDefinition;
  value: any[];
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId?: string;
}

/**
 * Component for rendering array inputs
 */
export function ArrayInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: ArrayInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;
  
  return (
    <ValidationWrapper inputDef={inputDef} value={value}>
      <div className="relative">
        <Textarea
          id={inputId}
          value={formatValueForDisplay(value, "array")}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              onChange(inputDef.name, parsed);
            } catch {
              onChange(inputDef.name, e.target.value);
            }
          }}
          placeholder={`Enter ${inputDef.display_name} (JSON format)`}
          className={cn(
            "bg-background/50 mt-1 font-mono text-xs",
            isDisabled && "opacity-50"
          )}
          rows={5}
          disabled={isDisabled}
        />
        {isDisabled && isConnected && <ConnectedIndicator />}
      </div>
    </ValidationWrapper>
  );
}
