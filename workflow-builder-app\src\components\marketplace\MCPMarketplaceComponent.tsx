import { useEffect, useRef } from "react";
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { useComponentStateStore } from "@/store/mcpToolsStore";
import { useMCPMarketplaceInputs } from "./MCPMarketplaceInputGenerator";

interface MCPMarketplaceComponentProps {
  node: Node<WorkflowNodeData>;
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void;
}

/**
 * Component for handling MCP Marketplace components in the workflow
 */
export function MCPMarketplaceComponent({ node, onNodeDataChange }: MCPMarketplaceComponentProps) {
  // Use the input generator to process inputs for the node
  const inputs = useMCPMarketplaceInputs(node, onNodeDataChange);

  // Track if we're currently being edited through the inspector panel
  const isBeingEditedRef = useRef(false);

  // Track the last config we processed to avoid circular updates
  const lastProcessedConfigRef = useRef<string>("");

  // Log the component info for debugging (only when not being edited and in development mode)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && node && node.data && node.data.definition) {
      console.log("MCP Marketplace component:", node.data.definition.name);
      console.log("MCP Marketplace component inputs:", inputs);
      console.log("MCP Marketplace component schema:", node.data.definition.mcp_info);
    }
  }, [node, inputs]);

  // Update the node config when inputs change, but only if not being edited through inspector
  useEffect(() => {
    // Skip this effect if we're currently being edited through the inspector panel
    // or if the node data hasn't actually changed
    if (!node || !node.data || inputs.length === 0) {
      return;
    }

    // Get the current config JSON to compare
    const currentConfigJson = JSON.stringify(node.data.config || {});

    // If this is the same config we just processed, skip to avoid circular updates
    if (currentConfigJson === lastProcessedConfigRef.current) {
      return;
    }

    // Update our reference to the current config
    lastProcessedConfigRef.current = currentConfigJson;

    // Create a new config object with default values for all inputs
    const newConfig = { ...node.data.config };

    // Update the config with default values for any missing inputs
    inputs.forEach((input) => {
      if (!input.is_handle && newConfig[input.name] === undefined) {
        newConfig[input.name] = input.value;
      }
    });

    // Also set values for all inputs with non-empty values
    inputs.forEach((input) => {
      if (
        input.value !== null &&
        input.value !== undefined &&
        input.value !== "" &&
        (typeof input.value !== "object" || Object.keys(input.value).length > 0)
      ) {
        newConfig[input.name] = input.value;
        if (process.env.NODE_ENV === 'development') {
          console.log(`Setting value for input ${input.name}:`, input.value);
        }
      }
    });

    // Store the config in the component state
    const stateStore = useComponentStateStore.getState();
    stateStore.setValue(node.id, "config", newConfig);
    if (process.env.NODE_ENV === 'development') {
      console.log(`Stored config in component state for node ${node.id}:`, newConfig);
    }

    // Only update the node data if the config has actually changed
    const newConfigJson = JSON.stringify(newConfig);
    if (newConfigJson !== currentConfigJson) {
      // Mark that we're updating through this component
      isBeingEditedRef.current = true;

      // Update the node data
      onNodeDataChange(node.id, {
        ...node.data,
        config: newConfig,
      });

      // Update our reference to the new config
      lastProcessedConfigRef.current = newConfigJson;

      // Reset the editing flag
      setTimeout(() => {
        isBeingEditedRef.current = false;
      }, 0);
    }
  }, [node, inputs, onNodeDataChange]);

  return null; // This is a logic component, not a UI component
}

/**
 * Helper function to get a value from the component state store for MCP Marketplace components
 */
export function getMCPMarketplaceValue(nodeId: string, key: string, defaultValue?: any) {
  const stateStore = useComponentStateStore.getState();
  const config = stateStore.getValue(nodeId, "config", {});
  return config[key] !== undefined ? config[key] : defaultValue;
}

/**
 * Helper function to set a value in the component state store for MCP Marketplace components
 */
export function setMCPMarketplaceValue(nodeId: string, key: string, value: any) {
  const stateStore = useComponentStateStore.getState();
  const config = stateStore.getValue(nodeId, "config", {});
  const newConfig = {
    ...config,
    [key]: value,
  };
  stateStore.setValue(nodeId, "config", newConfig);
}

/**
 * Check if a node is an MCP Marketplace component
 */
export function isMCPMarketplaceComponent(node: Node<WorkflowNodeData> | null): boolean {
  if (!node) return false;

  // Primary check: node.data.type is 'mcp'
  if (node.data.type === "mcp") return true;

  // Fallback checks for backward compatibility

  // Check if the node originalType is MCPToolsComponent
  if (node.data.originalType === "MCPToolsComponent") return true;

  // Check if the node has mcp_info property
  if (node.data.definition && node.data.definition.mcp_info) return true;

  // Check if the node path contains 'mcp_marketplace'
  if (
    node.data.definition &&
    node.data.definition.path &&
    node.data.definition.path.includes("mcp_marketplace")
  )
    return true;

  // Check if the node display name contains 'Script Generator' or 'Generator'
  if (
    node.data.definition &&
    node.data.definition.display_name &&
    (node.data.definition.display_name.includes("Script Generator") ||
      node.data.definition.display_name.includes("Generator"))
  )
    return true;

  // Check if the node label contains 'Script Generator' or 'Generator'
  if (
    node.data.label &&
    (node.data.label.includes("Script Generator") || node.data.label.includes("Generator"))
  )
    return true;

  return false;
}

// Track the last update to prevent circular updates
const lastUpdateMap = new Map<string, { inputName: string, value: any, timestamp: number }>();

/**
 * Update the config for an MCP Marketplace component
 */
export function updateMCPMarketplaceConfig(
  node: Node<WorkflowNodeData>,
  inputName: string,
  value: any,
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void,
) {
  // Check for duplicate/circular updates
  const nodeKey = `${node.id}-${inputName}`;
  const lastUpdate = lastUpdateMap.get(nodeKey);
  const now = Date.now();

  // If we have a recent update (within 100ms) with the same value, skip to prevent circular updates
  if (lastUpdate &&
      now - lastUpdate.timestamp < 100 &&
      JSON.stringify(lastUpdate.value) === JSON.stringify(value)) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Skipping duplicate update for node ${node.id}, input ${inputName} (debounced)`);
    }
    return;
  }

  // Record this update
  lastUpdateMap.set(nodeKey, { inputName, value, timestamp: now });

  if (process.env.NODE_ENV === 'development') {
    console.log(
      `updateMCPMarketplaceConfig called for node ${node.id}, input ${inputName}, value:`,
      value,
    );
  }

  // Get current config
  const currentConfig = node.data.config || {};

  // Check if the value is actually changing
  if (JSON.stringify(currentConfig[inputName]) === JSON.stringify(value)) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Value for ${inputName} hasn't changed, skipping update`);
    }
    return;
  }

  if (process.env.NODE_ENV === 'development') {
    console.log(`Current config for node ${node.id}:`, currentConfig);
  }

  // Update config
  const newConfig = {
    ...currentConfig,
    [inputName]: value,
  };

  if (process.env.NODE_ENV === 'development') {
    console.log(`New config for node ${node.id}:`, newConfig);
  }

  // Update node data
  const newData = {
    ...node.data,
    config: newConfig,
  };

  // Update component state first
  if (process.env.NODE_ENV === 'development') {
    console.log(`Updating component state for node ${node.id}, input ${inputName}, value:`, value);
  }

  setMCPMarketplaceValue(node.id, inputName, value);

  // Get the updated component state
  const stateStore = useComponentStateStore.getState();
  const updatedConfig = stateStore.getValue(node.id, "config", {});

  if (process.env.NODE_ENV === 'development') {
    console.log(`Updated component state config for node ${node.id}:`, updatedConfig);
  }

  // Call the onNodeDataChange callback
  onNodeDataChange(node.id, newData);

  if (process.env.NODE_ENV === 'development') {
    console.log(`Node data updated for node ${node.id}`);
  }
}

/**
 * Get the value for an input in an MCP Marketplace component
 */
export function getMCPMarketplaceInputValue(
  node: Node<WorkflowNodeData>,
  inputName: string,
  defaultValue: any,
) {
  // First try to get from the component state
  const stateStore = useComponentStateStore.getState();
  const config = stateStore.getValue(node.id, "config", {});
  if (config[inputName] !== undefined) {
    return config[inputName];
  }

  // Then try to get from the node config
  if (node.data.config && node.data.config[inputName] !== undefined) {
    return node.data.config[inputName];
  }

  // Finally, return the default value
  return defaultValue;
}
