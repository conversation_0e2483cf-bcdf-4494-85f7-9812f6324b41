#!/usr/bin/env python3
"""
Direct testing of Split Text Component in Node Executor Service.
Run this from the node-executor-service directory.
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

async def test_split_text_component():
    """Test the Split Text Component directly."""
    print("🧪 Testing Split Text Component - Node Executor Service")
    print("=" * 60)
    
    try:
        from app.components.split_text_component import SplitTextComponent
        
        component = SplitTextComponent()
        print("✅ Component initialized successfully")
        
        # Test cases
        test_cases = [
            {
                "name": "Basic comma split",
                "payload": {
                    "request_id": "test_001",
                    "tool_parameters": {
                        "input_text": "apple,banana,cherry,date",
                        "delimiter": ",",
                        "max_splits": -1,
                        "include_delimiter": False
                    }
                },
                "expected": ["apple", "banana", "cherry", "date"]
            },
            {
                "name": "Limited splits",
                "payload": {
                    "request_id": "test_002", 
                    "tool_parameters": {
                        "input_text": "one|two|three|four|five",
                        "delimiter": "|",
                        "max_splits": 2,
                        "include_delimiter": False
                    }
                },
                "expected": ["one", "two", "three|four|five"]
            },
            {
                "name": "Include delimiter",
                "payload": {
                    "request_id": "test_003",
                    "tool_parameters": {
                        "input_text": "hello;world;test",
                        "delimiter": ";",
                        "max_splits": -1,
                        "include_delimiter": True
                    }
                },
                "expected": ["hello;", "world;", "test"]
            },
            {
                "name": "Space delimiter",
                "payload": {
                    "request_id": "test_004",
                    "tool_parameters": {
                        "input_text": "The quick brown fox jumps",
                        "delimiter": " ",
                        "max_splits": 3,
                        "include_delimiter": False
                    }
                },
                "expected": ["The", "quick", "brown", "fox jumps"]
            }
        ]
        
        # Run tests
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test {i}: {test_case['name']}")
            print("-" * 40)
            
            # Validate
            validation = await component.validate(test_case["payload"])
            print(f"Validation: {'✅ Passed' if validation.is_valid else '❌ Failed'}")
            
            if not validation.is_valid:
                print(f"Validation Error: {validation.error_message}")
                continue
            
            # Process
            result = await component.process(test_case["payload"])
            print(f"Status: {result.get('status', 'unknown')}")
            
            if result.get("status") == "success":
                actual = result.get("output_list", [])
                expected = test_case["expected"]
                
                print(f"Expected: {expected}")
                print(f"Actual:   {actual}")
                
                if actual == expected:
                    print("✅ Test PASSED")
                else:
                    print("❌ Test FAILED")
            else:
                print(f"❌ Error: {result.get('error', 'Unknown error')}")
        
        # Test error cases
        print(f"\n🚨 Error Test: Missing input text")
        print("-" * 40)
        
        error_payload = {
            "request_id": "error_test",
            "tool_parameters": {
                "delimiter": ",",
                "max_splits": -1,
                "include_delimiter": False
                # Missing input_text
            }
        }
        
        validation = await component.validate(error_payload)
        print(f"Validation: {'❌ Failed (Expected)' if not validation.is_valid else '✅ Passed (Unexpected)'}")
        
        if not validation.is_valid:
            print(f"Error Message: {validation.error_message}")
            print("✅ Error handling works correctly")
        
        print(f"\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_split_text_component())
