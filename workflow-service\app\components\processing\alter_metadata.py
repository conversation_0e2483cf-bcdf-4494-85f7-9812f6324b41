from typing import Dict, Any, List, ClassVar
import copy
import time
import logging

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import InputBase
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import Node<PERSON><PERSON>ult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

logger = logging.getLogger(__name__)


class AlterMetadataComponent(BaseNode):
    """
    Modifies metadata dictionary keys.

    This component takes a metadata dictionary and applies updates and removals
    to its keys.
    """

    name: ClassVar[str] = "AlterMetadataComponent"
    display_name: ClassVar[str] = "Alter Metadata"
    description: ClassVar[str] = "Modifies metadata dictionary keys."
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Tag"

    inputs: ClassVar[List[InputBase]] = [
        # Input metadata - unified dual-purpose input
        create_dual_purpose_input(
            name="input_metadata",
            display_name="Input Metadata",
            input_type="dict",
            required=True,
            value={},
            info="The metadata dictionary to modify. Can be connected from another node or entered directly.",
            input_types=["dict", "Any"],
        ),
        # Updates - unified dual-purpose input
        create_dual_purpose_input(
            name="updates",
            display_name="Metadata Updates",
            input_type="dict",
            required=False,
            value={},
            info="Dictionary of key-value pairs to update or add to the metadata. Can be connected from another node or entered directly.",
            input_types=["dict", "Any"],
        ),
        # Keys to remove - unified dual-purpose input
        create_dual_purpose_input(
            name="keys_to_remove",
            display_name="Keys to Remove",
            input_type="list",
            required=False,
            value=[],
            info="List of keys to remove from the metadata. Can be connected from another node or entered directly.",
            input_types=["list", "Any"],
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="output_metadata", display_name="Updated Metadata", output_type="dict"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get input value from context, prioritizing handle inputs over direct inputs.

        Args:
            input_name: Name of the input to retrieve
            context: The workflow execution context
            default: Default value if input is not found

        Returns:
            The input value or default if not found
        """
        node_id = context.current_node_id
        if not node_id:
            return default

        # Check if there's a value in the node outputs
        node_outputs = context.node_outputs.get(node_id, {})
        if input_name in node_outputs:
            return node_outputs[input_name]

        # If not found, return the default
        return default

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the AlterMetadataComponent.

        This method modifies a metadata dictionary by applying updates and removing keys.

        Args:
            context: The workflow execution context containing input values.

        Returns:
            A NodeResult with the execution results.
        """
        # Start timing for performance measurement
        start_time = time.time()

        # Log execution start
        context.log(f"Executing {self.name}...")

        try:
            # Get inputs using helper method
            input_metadata = self.get_input_value("input_metadata", context, None)
            updates = self.get_input_value("updates", context, {})
            keys_to_remove = self.get_input_value("keys_to_remove", context, [])

            # Validate input metadata
            if input_metadata is None:
                error_msg = "Input metadata is missing. Please connect or provide metadata."
                context.log(error_msg)
                return NodeResult.error(error_msg, time.time() - start_time)

            if not isinstance(input_metadata, dict):
                error_msg = f"Input metadata must be a dictionary, got {type(input_metadata).__name__}"
                context.log(error_msg)
                return NodeResult.error(error_msg, time.time() - start_time)

            # Validate updates
            if updates is not None and not isinstance(updates, dict):
                error_msg = f"Updates must be a dictionary, got {type(updates).__name__}"
                context.log(error_msg)
                return NodeResult.error(error_msg, time.time() - start_time)

            # Validate keys to remove
            if keys_to_remove is not None and not isinstance(keys_to_remove, list):
                error_msg = f"Keys to remove must be a list, got {type(keys_to_remove).__name__}"
                context.log(error_msg)
                return NodeResult.error(error_msg, time.time() - start_time)

            # Create a copy of the input metadata
            result = copy.deepcopy(input_metadata)

            # Apply updates
            if updates:
                result.update(updates)
                context.log(f"Updated keys: {list(updates.keys())}")

            # Remove keys
            if keys_to_remove:
                for key in keys_to_remove:
                    if key in result:
                        del result[key]
                context.log(f"Removed keys: {keys_to_remove}")

            # Log success
            execution_time = time.time() - start_time
            context.log(f"Metadata altered successfully. Keys: {list(result.keys())}. Time: {execution_time:.2f}s")

            return NodeResult.success(
                outputs={"output_metadata": result},
                execution_time=execution_time
            )

        except Exception as e:
            error_msg = f"Error altering metadata: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(error_msg, time.time() - start_time)

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the AlterMetadataComponent.

        DEPRECATED: This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Args:
            **kwargs: Contains the input values

        Returns:
            A dictionary with the component's outputs
        """
        logger.warning(
            f"Using legacy build method for {self.name}. Please update to use execute method."
        )

        # Get inputs - now using unified dual-purpose inputs
        input_metadata = kwargs.get("input_metadata", {})
        updates = kwargs.get("updates", {})
        keys_to_remove = kwargs.get("keys_to_remove", [])

        # Validate input
        if input_metadata is None:
            return {"error": "Input metadata is missing. Please connect or provide metadata."}

        if not isinstance(input_metadata, dict):
            return {
                "error": f"Input metadata must be a dictionary, got {type(input_metadata).__name__}"
            }

        if updates is not None and not isinstance(updates, dict):
            return {"error": f"Updates must be a dictionary, got {type(updates).__name__}"}

        if keys_to_remove is not None and not isinstance(keys_to_remove, list):
            return {"error": f"Keys to remove must be a list, got {type(keys_to_remove).__name__}"}

        try:
            # Create a copy of the input metadata
            result = copy.deepcopy(input_metadata)

            # Apply updates
            if updates:
                result.update(updates)
                logger.info(f"Updated keys: {list(updates.keys())}")

            # Remove keys
            if keys_to_remove:
                for key in keys_to_remove:
                    if key in result:
                        del result[key]
                logger.info(f"Removed keys: {keys_to_remove}")

            logger.info(f"Metadata altered successfully. Keys: {list(result.keys())}")
            return {"output_metadata": result}

        except Exception as e:
            error_msg = f"Error altering metadata: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
