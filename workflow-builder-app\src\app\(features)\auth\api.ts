/**
 * Authentication API service
 */

import axios from "axios";
import { clearAuthCookies, getAccessToken, setAuthCookies } from "@/lib/cookies";
import {
  getClientAccessToken,
  checkClientAccessToken,
  setClientAuthCookies,
  clearClientAuthCookies,
} from "@/lib/clientCookies";
import { LoginType, SignupType, ResetPasswordType } from "./schemas";
import { useUserStore } from "@/store/userStore";
import { User, AuthResponse } from "./types";

// Import routes from shared routes for consistency
import { loginRoute, workflowsRoute } from "@/shared/routes";

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

// Create axios instance with interceptors
const authAxios = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true,
});

// Add request interceptor to include token
authAxios.interceptors.request.use(
  async (config) => {
    const token = await getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error),
);

// Add response interceptor to handle token refresh
authAxios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If error is 401 and we haven't tried to refresh the token yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const refreshToken = await getAccessToken();
        if (!refreshToken) {
          // No refresh token, user needs to login again
          return Promise.reject(error);
        }

        const response = await axios.post(`${API_BASE_URL}/refresh-token`, {
          refreshToken,
        });

        // Store the new tokens
        const { accessToken, refreshToken: newRefreshToken } = response.data;

        // Retry the original request with the new token
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        return axios(originalRequest);
      } catch (refreshError) {
        // Refresh failed, user needs to login again
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  },
);

/**
 * Checks if a user has completed the onboarding process
 */
export async function hasCompletedOnboarding(): Promise<boolean> {
  try {
    const userDetails = await authApi.getCurrentUser();
    return Boolean(userDetails?.department && userDetails?.jobRole);
  } catch (error: any) {
    // Check if this request has already been through the axios interceptor
    const originalRequest = error.config as any;
    if (originalRequest && originalRequest._retry) {
      // This request has already been through the interceptor and still failed
      // This means token refresh failed, so we should redirect to login
      throw error;
    }

    // For other errors, return false to allow normal flow
    return false;
  }
}

// Authentication API functions
export const authApi = {
  /**
   * Login user with email and password
   */
  login: async (data: LoginType): Promise<{ user: User; redirectPath: string }> => {
    const { email, password } = data;
    try {
      // Prepare login payload
      const payload = {
        email,
        password,
      };

      const response = await axios.post<AuthResponse>(`${API_BASE_URL}/login`, payload);

      if (!response.data.accessToken) {
        throw new Error("Login failed: Unexpected response from server.");
      }

      // Set auth cookies after successful login
      await setAuthCookies(
        response.data.accessToken,
        response.data.refreshToken || "",
        3600, // 1 hour
        86400, // 24 hours
      );

      // Also set client-side cookies for immediate access
      if (typeof window !== "undefined") {
        setClientAuthCookies(
          response.data.accessToken,
          response.data.refreshToken || "",
          3600,
          86400,
        );
      }

      let redirectPath = workflowsRoute; // Use workflows route directly

      // Update the user store with user data and access token
      useUserStore.getState().setUser({
        fullName: response.data.user.name || "",
        email: response.data.user.email,
        accessToken: response.data.accessToken,
      });

      // Return both user data and the determined redirect path
      return {
        user: response.data.user,
        redirectPath,
      };
    } catch (error: any) {
      // Clear user store on login failure
      useUserStore.getState().clearUser();

      if (error.response?.status === 404) {
        throw new Error("User not found.");
      }
      if (error.response?.status === 412) {
        throw new Error("Account inactive. Please check your email for verification.");
      }
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Invalid Credentials",
      );
    }
  },

  /**
   * Register new user
   */
  signup: async (data: SignupType): Promise<{ message: string }> => {
    try {
      const { email, fullName, password } = data;
      const payload = {
        name: fullName,
        email,
        password,
      };
      const response = await axios.post<{ message: string }>(`${API_BASE_URL}/register`, payload);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 409) {
        throw new Error(error.response?.data?.detail || "Email already registered.");
      }
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Signup failed",
      );
    }
  },

  /**
   * Logout user
   */
  logout: async (): Promise<void> => {
    try {
      // Clear server-side auth cookies
      await clearAuthCookies();

      // Clear client-side auth cookies
      if (typeof window !== "undefined") {
        clearClientAuthCookies();
      }

      // Clear user store (including accessToken)
      useUserStore.getState().clearUser();

      // Redirect to login page
      if (typeof window !== "undefined") {
        window.location.href = loginRoute;
      }
    } catch (error: any) {
      console.error("Error during logout:", error);

      // Even if there's an error, try to clear everything
      await clearAuthCookies();
      if (typeof window !== "undefined") {
        clearClientAuthCookies();
      }
      useUserStore.getState().clearUser();

      if (typeof window !== "undefined") {
        window.location.href = loginRoute;
      }
    }
  },

  /**
   * Forgot password
   */
  forgotPassword: async (email: string): Promise<{ message: string }> => {
    try {
      const response = await axios.post<{ message: string }>(`${API_BASE_URL}/reset-password`, {
        email,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to send password reset email",
      );
    }
  },

  /**
   * Reset user's password
   */
  resetPassword: async (token: string, data: ResetPasswordType): Promise<{ message: string }> => {
    const { newPassword } = data;
    try {
      const payload = {
        token,
        password: newPassword,
      };

      const response = await axios.post<{ message: string }>(
        `${API_BASE_URL}/update-password`,
        payload,
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Password reset failed",
      );
    }
  },

  /**
   * Get current user information
   */
  getCurrentUser: async (): Promise<User> => {
    try {
      // Get token using the appropriate method based on environment
      let token;
      if (typeof window !== "undefined") {
        // Client-side
        token = getClientAccessToken();
      } else {
        // Server-side
        token = await getAccessToken();
      }

      if (!token) {
        throw new Error("No access token available");
      }

      const response = await axios.get(`${API_BASE_URL}/me`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error: any) {
      // Check if this is a 403 Forbidden error
      if (error.response?.status === 403) {
        // Clear user state and cookies to force re-login
        useUserStore.getState().clearUser();
        if (typeof window !== "undefined") {
          clearClientAuthCookies();
        }
      }

      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to fetch user details",
      );
    }
  },

  /**
   * Verify current auth session by attempting to fetch user data.
   */
  isLoggedIn: async (): Promise<boolean> => {
    try {
      await authApi.getCurrentUser(); // Attempt to fetch user data
      return true; // If successful, user is considered logged in
    } catch (error) {
      // Any error (including 401/403 from getCurrentUser) means session is not valid
      return false;
    }
  },

  /**
   * Check if user is authenticated based on cookie presence
   */
  isAuthenticated: async (): Promise<boolean> => {
    // Check if we're in a browser environment
    if (typeof window !== "undefined") {
      // Use client-side cookie access
      const isAuth = checkClientAccessToken();
      return isAuth;
    } else {
      // Use server-side cookie access
      const token = await getAccessToken();
      return !!token;
    }
  },
};

export default authApi;
