#!/usr/bin/env python3
"""
Integration test for AlterMetadataComponent across both services.
This test simulates the actual workflow execution flow.
"""
import asyncio
import json
import sys
import os

# Add both service paths
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'workflow-service'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'node-executor-service'))

# Import from both services
from workflow_service.app.components.processing.alter_metadata import AlterMetadataComponent as WorkflowComponent
from workflow_service.app.models.workflow_builder.context import WorkflowContext
from workflow_service.app.models.workflow_builder.node_result import NodeStatus

# Import from node executor service
from node_executor_service.app.components.alter_metadata_component import AlterMetadataComponent as ExecutorComponent


async def test_end_to_end_integration():
    """Test the complete end-to-end integration between both services."""
    print("🔄 End-to-End Integration Test for AlterMetadataComponent")
    print("=" * 70)
    
    # Test 1: Component Definition Consistency
    print("\n📋 Test 1: Component Definition Consistency")
    print("-" * 50)
    
    workflow_component = WorkflowComponent()
    executor_component = ExecutorComponent()
    
    # Get workflow service component definition
    workflow_definition = workflow_component.get_definition()
    
    print(f"Workflow Service Component:")
    print(f"  Name: {workflow_definition['name']}")
    print(f"  Display Name: {workflow_definition['display_name']}")
    print(f"  Category: {workflow_definition['category']}")
    print(f"  Inputs: {len(workflow_definition['inputs'])}")
    print(f"  Outputs: {len(workflow_definition['outputs'])}")
    
    # Verify input consistency
    input_names = [inp['name'] for inp in workflow_definition['inputs']]
    expected_inputs = ['input_metadata', 'updates', 'keys_to_remove']
    
    if set(input_names) == set(expected_inputs):
        print("✅ Input definitions are consistent")
    else:
        print(f"❌ Input mismatch - Expected: {expected_inputs}, Got: {input_names}")
    
    # Test 2: Workflow Service Execution
    print("\n📋 Test 2: Workflow Service Execution")
    print("-" * 50)
    
    context = WorkflowContext(workflow_id="integration_test", execution_id="test_001")
    context.current_node_id = "alter_metadata_node"
    context.node_outputs["alter_metadata_node"] = {
        "input_metadata": {
            "name": "test_document",
            "version": "1.0",
            "status": "draft",
            "deprecated": True,
            "temp_field": "remove_me"
        },
        "updates": {
            "version": "2.0",
            "status": "published",
            "author": "integration_test",
            "last_modified": "2024-01-01"
        },
        "keys_to_remove": ["deprecated", "temp_field"]
    }
    
    workflow_result = await workflow_component.execute(context)
    
    print(f"Workflow Service Result:")
    print(f"  Status: {workflow_result.status}")
    print(f"  Execution Time: {workflow_result.execution_time:.4f}s")
    
    if workflow_result.status == NodeStatus.SUCCESS:
        workflow_output = workflow_result.outputs.get('output_metadata')
        print(f"  Output: {workflow_output}")
        print("✅ Workflow service execution successful")
    else:
        print(f"❌ Workflow service execution failed: {workflow_result.error_message}")
        return
    
    # Test 3: Node Executor Service Processing
    print("\n📋 Test 3: Node Executor Service Processing")
    print("-" * 50)
    
    # Simulate the payload that would be sent to node executor service
    executor_payload = {
        "request_id": "integration_test_001",
        "tool_parameters": {
            "input_metadata": {
                "name": "test_document",
                "version": "1.0",
                "status": "draft",
                "deprecated": True,
                "temp_field": "remove_me"
            },
            "updates": {
                "version": "2.0",
                "status": "published",
                "author": "integration_test",
                "last_modified": "2024-01-01"
            },
            "keys_to_remove": ["deprecated", "temp_field"]
        }
    }
    
    # Validate the payload
    validation_result = await executor_component.validate(executor_payload)
    print(f"Node Executor Validation: {'✅ PASS' if validation_result.is_valid else '❌ FAIL'}")
    
    if not validation_result.is_valid:
        print(f"Validation Error: {validation_result.error_message}")
        return
    
    # Process the payload
    executor_result = await executor_component.process(executor_payload)
    
    print(f"Node Executor Result:")
    print(f"  Status: {executor_result['status']}")
    
    if executor_result['status'] == 'success':
        executor_output = executor_result['result']
        print(f"  Output: {executor_output}")
        print("✅ Node executor service processing successful")
    else:
        print(f"❌ Node executor service processing failed: {executor_result.get('error')}")
        return
    
    # Test 4: Output Consistency Verification
    print("\n📋 Test 4: Output Consistency Verification")
    print("-" * 50)
    
    # Both services should produce the same output for the same input
    expected_output = {
        "name": "test_document",
        "version": "2.0",
        "status": "published",
        "author": "integration_test",
        "last_modified": "2024-01-01"
    }
    
    workflow_matches = workflow_output == expected_output
    executor_matches = executor_output == expected_output
    outputs_match = workflow_output == executor_output
    
    print(f"Expected Output: {expected_output}")
    print(f"Workflow Output Matches: {'✅' if workflow_matches else '❌'}")
    print(f"Executor Output Matches: {'✅' if executor_matches else '❌'}")
    print(f"Outputs Consistent: {'✅' if outputs_match else '❌'}")
    
    if workflow_matches and executor_matches and outputs_match:
        print("✅ Output consistency verification passed")
    else:
        print("❌ Output consistency verification failed")
        print(f"  Workflow Output: {workflow_output}")
        print(f"  Executor Output: {executor_output}")
        return
    
    # Test 5: Error Handling Consistency
    print("\n📋 Test 5: Error Handling Consistency")
    print("-" * 50)
    
    # Test with invalid input
    error_context = WorkflowContext(workflow_id="error_test", execution_id="test_002")
    error_context.current_node_id = "error_node"
    error_context.node_outputs["error_node"] = {
        "input_metadata": "invalid_type",  # Should be dict
        "updates": {},
        "keys_to_remove": []
    }
    
    workflow_error_result = await workflow_component.execute(error_context)
    
    error_payload = {
        "request_id": "error_test_001",
        "tool_parameters": {
            "input_metadata": "invalid_type",
            "updates": {},
            "keys_to_remove": []
        }
    }
    
    executor_error_validation = await executor_component.validate(error_payload)
    
    workflow_error_handled = workflow_error_result.status == NodeStatus.ERROR
    executor_error_handled = not executor_error_validation.is_valid
    
    print(f"Workflow Error Handling: {'✅' if workflow_error_handled else '❌'}")
    print(f"Executor Error Handling: {'✅' if executor_error_handled else '❌'}")
    
    if workflow_error_handled and executor_error_handled:
        print("✅ Error handling consistency verified")
    else:
        print("❌ Error handling consistency failed")
        return
    
    # Test 6: Performance Comparison
    print("\n📋 Test 6: Performance Comparison")
    print("-" * 50)
    
    import time
    
    # Measure workflow service performance
    start_time = time.time()
    for _ in range(100):
        await workflow_component.execute(context)
    workflow_time = time.time() - start_time
    
    # Measure node executor service performance
    start_time = time.time()
    for _ in range(100):
        await executor_component.process(executor_payload)
    executor_time = time.time() - start_time
    
    print(f"Workflow Service (100 executions): {workflow_time:.4f}s")
    print(f"Node Executor Service (100 executions): {executor_time:.4f}s")
    print(f"Performance Ratio: {executor_time/workflow_time:.2f}x")
    
    if abs(executor_time - workflow_time) / max(executor_time, workflow_time) < 0.5:
        print("✅ Performance is comparable between services")
    else:
        print("⚠️  Performance difference detected (may be expected due to different architectures)")
    
    print("\n🎯 Integration Test Summary")
    print("=" * 70)
    print("✅ Component Definition Consistency")
    print("✅ Workflow Service Execution")
    print("✅ Node Executor Service Processing")
    print("✅ Output Consistency Verification")
    print("✅ Error Handling Consistency")
    print("✅ Performance Comparison")
    print("\n🚀 AlterMetadataComponent is fully integrated and consistent across services!")


async def test_complex_scenarios():
    """Test complex real-world scenarios."""
    print("\n🧪 Complex Scenario Testing")
    print("=" * 50)
    
    workflow_component = WorkflowComponent()
    executor_component = ExecutorComponent()
    
    # Scenario 1: Large metadata object
    print("\n📋 Scenario 1: Large metadata object")
    large_metadata = {f"field_{i}": f"value_{i}" for i in range(1000)}
    large_metadata.update({
        "name": "large_document",
        "version": "1.0",
        "remove_me": "temp"
    })
    
    context = WorkflowContext(workflow_id="large_test", execution_id="test_003")
    context.current_node_id = "large_node"
    context.node_outputs["large_node"] = {
        "input_metadata": large_metadata,
        "updates": {"version": "2.0", "processed": True},
        "keys_to_remove": ["remove_me"]
    }
    
    start_time = time.time()
    result = await workflow_component.execute(context)
    execution_time = time.time() - start_time
    
    if result.status == NodeStatus.SUCCESS:
        output = result.outputs.get('output_metadata')
        if len(output) == 1001 and output.get('version') == '2.0' and 'remove_me' not in output:
            print(f"✅ Large metadata handled correctly in {execution_time:.4f}s")
        else:
            print("❌ Large metadata processing failed")
    else:
        print(f"❌ Large metadata execution failed: {result.error_message}")
    
    # Scenario 2: Nested metadata structures
    print("\n📋 Scenario 2: Nested metadata structures")
    nested_metadata = {
        "document": {
            "title": "Test Document",
            "metadata": {
                "author": "Test Author",
                "version": "1.0"
            }
        },
        "settings": {
            "visibility": "private",
            "deprecated": True
        },
        "temp_data": "remove_this"
    }
    
    context.node_outputs["large_node"] = {
        "input_metadata": nested_metadata,
        "updates": {
            "document": {
                "title": "Updated Document",
                "metadata": {
                    "author": "Updated Author",
                    "version": "2.0"
                }
            }
        },
        "keys_to_remove": ["temp_data"]
    }
    
    result = await workflow_component.execute(context)
    
    if result.status == NodeStatus.SUCCESS:
        output = result.outputs.get('output_metadata')
        if 'temp_data' not in output and output.get('document', {}).get('title') == 'Updated Document':
            print("✅ Nested metadata structures handled correctly")
        else:
            print("❌ Nested metadata processing failed")
    else:
        print(f"❌ Nested metadata execution failed: {result.error_message}")
    
    print("✅ Complex scenario testing completed")


if __name__ == "__main__":
    async def main():
        await test_end_to_end_integration()
        await test_complex_scenarios()
    
    asyncio.run(main())
