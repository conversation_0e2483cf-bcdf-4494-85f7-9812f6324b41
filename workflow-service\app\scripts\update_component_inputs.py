"""
<PERSON><PERSON><PERSON> to update component inputs to use the dual-purpose input pattern.

This script will:
1. Find all components in the codebase
2. Identify components with duplicate inputs (input and input_handle)
3. Update them to use the dual-purpose input pattern
"""

import os
import re
import glob
from typing import List, Dict, Tuple, Set

# Define the root directory for components
COMPONENTS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "components")

def find_component_files() -> List[str]:
    """Find all Python files in the components directory."""
    return glob.glob(os.path.join(COMPONENTS_DIR, "**", "*.py"), recursive=True)

def analyze_component_file(file_path: str) -> Dict:
    """
    Analyze a component file to identify duplicate inputs.

    Returns:
        Dict with information about the component and its inputs.
    """
    with open(file_path, "r") as f:
        content = f.read()

    # Extract component name
    component_name_match = re.search(r"class\s+(\w+)\(", content)
    if not component_name_match:
        return None

    component_name = component_name_match.group(1)

    # Skip base components that shouldn't be displayed in the UI
    if component_name in ["BaseNode", "BaseHITLComponent", "BaseDataInteractionComponent", "BaseAgentComponent"]:
        return None

    # Find all input definitions
    input_defs = []
    handle_inputs = []

    # Look for HandleInput definitions
    handle_pattern = r"HandleInput\s*\(\s*name\s*=\s*[\"']([^\"']+)[\"']"
    handle_matches = re.finditer(handle_pattern, content)
    for match in handle_matches:
        handle_name = match.group(1)
        handle_inputs.append(handle_name)

    # Look for all input definitions
    input_pattern = r"(?:StringInput|IntInput|FloatInput|BoolInput|DropdownInput|ListInput|DictInput|MultilineInput|CodeInput|FileInput)\s*\(\s*name\s*=\s*[\"']([^\"']+)[\"']"
    input_matches = re.finditer(input_pattern, content)
    for match in input_matches:
        input_name = match.group(1)
        input_defs.append(input_name)

    # Check if the component uses create_dual_purpose_input
    uses_dual_purpose = "create_dual_purpose_input" in content

    # Find potential duplicate inputs
    duplicates = []
    for handle_name in handle_inputs:
        base_name = handle_name.replace("_handle", "")
        if base_name in input_defs:
            duplicates.append((handle_name, base_name))

    # Also check for handle inputs in the build or execute methods
    method_pattern = r"(?:def\s+build|def\s+execute).*?(?:kwargs|inputs)\.get\(\s*[\"']([^\"']+)[\"']"
    method_matches = re.finditer(method_pattern, content, re.DOTALL)

    accessed_inputs = set()
    for match in method_matches:
        accessed_name = match.group(1)
        accessed_inputs.add(accessed_name)

    # Find inputs that are accessed but not defined
    undefined_inputs = accessed_inputs - set(input_defs) - set(handle_inputs)

    # Find handle inputs that are accessed in methods
    accessed_handles = [name for name in accessed_inputs if name.endswith("_handle")]

    return {
        "name": component_name,
        "file_path": file_path,
        "inputs": input_defs,
        "handle_inputs": handle_inputs,
        "duplicates": duplicates,
        "accessed_inputs": list(accessed_inputs),
        "undefined_inputs": list(undefined_inputs),
        "accessed_handles": accessed_handles,
        "uses_dual_purpose": uses_dual_purpose
    }

def print_analysis_results(results: List[Dict]):
    """Print the analysis results in a readable format."""
    # Components with duplicate inputs
    components_with_duplicates = [r for r in results if r and r.get("duplicates")]

    print(f"\nFound {len(components_with_duplicates)} components with duplicate inputs:")

    for comp in components_with_duplicates:
        print(f"\n{comp['name']} ({os.path.relpath(comp['file_path'], os.path.dirname(COMPONENTS_DIR))}):")
        for handle, direct in comp["duplicates"]:
            print(f"  - {handle} and {direct}")

    # Components with handle inputs accessed in methods
    components_with_accessed_handles = [r for r in results if r and r.get("accessed_handles")]

    print(f"\nFound {len(components_with_accessed_handles)} components with handle inputs accessed in methods:")

    for comp in components_with_accessed_handles:
        print(f"\n{comp['name']} ({os.path.relpath(comp['file_path'], os.path.dirname(COMPONENTS_DIR))}):")
        for handle in comp["accessed_handles"]:
            base_name = handle.replace("_handle", "")
            if base_name in comp["accessed_inputs"]:
                print(f"  - {handle} and {base_name} (both accessed)")
            else:
                print(f"  - {handle} (only handle accessed)")

    # Components already using dual-purpose inputs
    components_with_dual_purpose = [r for r in results if r and r.get("uses_dual_purpose")]

    print(f"\nFound {len(components_with_dual_purpose)} components already using dual-purpose inputs:")

    for comp in components_with_dual_purpose:
        print(f"  - {comp['name']} ({os.path.relpath(comp['file_path'], os.path.dirname(COMPONENTS_DIR))})")

def main():
    """Main function to analyze component files."""
    print("Analyzing component files...")
    component_files = find_component_files()
    print(f"Found {len(component_files)} component files.")

    results = []
    for file_path in component_files:
        result = analyze_component_file(file_path)
        if result:
            results.append(result)

    print_analysis_results(results)

if __name__ == "__main__":
    main()
