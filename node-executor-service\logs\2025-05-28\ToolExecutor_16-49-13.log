2025-05-28 16:49:13 - ToolExecutor - INFO - [setup_logger:467] Logger ToolExecutor configured with log file: logs\2025-05-28\ToolExecutor_16-49-13.log
2025-05-28 16:49:13 - ToolExecutor - INFO - [setup_tool_executor_logger:97] Ka<PERSON>ka logging enabled for ToolExecutor, sending to dedicated topic: tool_executor_logs
2025-05-28 16:49:13 - ToolExecutor - INFO - [get_tool_executor:281] Creating new global ToolExecutor instance
2025-05-28 16:49:13 - ToolExecutor - INFO - [__init__:76] Initializing ToolExecutor
2025-05-28 16:49:13 - ToolExecutor - INFO - [__init__:78] ToolExecutor initialized successfully
2025-05-28 16:49:20 - ToolExecutor - INFO - [execute_tool:94] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Executing tool for request_id: 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:49:20 - ToolExecutor - INFO - [execute_tool:97] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"social\":\"4\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "marketing": "2"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1",
  "correlation_id": "e22a7320-ed4c-4e62-990a-d3a2e5132645"
}
2025-05-28 16:49:20 - ToolExecutor - INFO - [execute_tool:116] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Tool name: MergeDataComponent for request_id: 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:49:20 - ToolExecutor - INFO - [execute_tool:151] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Processing payload with component MergeDataComponent for request_id: 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:49:20 - ToolExecutor - INFO - [execute_tool:155] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Component MergeDataComponent processed payload successfully for request_id: 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:49:20 - ToolExecutor - INFO - [execute_tool:225] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] ToolExecutor returning success: {
  "request_id": "553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1",
  "status": "success",
  "result": {
    "status": "success",
    "result": {
      "value": "{\"social\":\"4\"}",
      "marketing": "2"
    }
  }
}
