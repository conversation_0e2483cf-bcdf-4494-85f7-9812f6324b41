2025-05-28 16:44:55 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-05-28\ComponentSystem_16-44-55.log
2025-05-28 16:44:55 - ComponentSystem - INFO - [get_component_manager:1418] Creating new global ComponentManager instance
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:87] Initializing ComponentManager with Kafka bootstrap servers: **************:9092
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:92] Kafka Consumer Topic: node-execution-request
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:93] Kafka Results Topic: node_results
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:94] Kafka Consumer Group ID: node_executor_service
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:95] Kafka Producer Request Timeout: 60000ms
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:98] Kafka Consumer Fetch Min Bytes: 100
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:101] Kafka Consumer Fetch Max Wait: 500ms
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:104] Kafka Consumer Session Timeout: 10000ms
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:107] Kafka Consumer Heartbeat Interval: 3000ms
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:110] Default Node Retries: 3
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:128] Initializing ThreadPoolExecutor with 4 workers
2025-05-28 16:44:55 - ComponentSystem - INFO - [__init__:132] ThreadPoolExecutor initialized
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_components:141] Discovering components
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_components:142] Component registry before discovery: []
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_components:153] Discovered components: []
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1360] Discovering component modules
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1376] Found 17 potential component files: ['alter_metadata_component.py', 'api_component.py', 'combine_text_component.py', 'combine_text_component_new.py', 'conditional_component.py', 'convert_script_data_component.py', 'data_to_dataframe_component.py', 'doc_component.py', 'dynamic_combine_text_component.py', 'gmail_component.py', 'gmail_tracker_component.py', 'id_generator_component.py', 'merge_data_component.py', 'message_to_data_component.py', 'select_data_component.py', 'split_text_component.py', 'text_analysis_component.py']
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.alter_metadata_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.alter_metadata_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.api_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.api_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.combine_text_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.combine_text_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.combine_text_component_new
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.combine_text_component_new
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.conditional_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: ConditionalNode -> ConditionalExecutor
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode']
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.conditional_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.convert_script_data_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.convert_script_data_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.data_to_dataframe_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.data_to_dataframe_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.doc_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.doc_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.dynamic_combine_text_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextExecutor -> CombineTextExecutor
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor']
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.dynamic_combine_text_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.gmail_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor', 'GmailComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.gmail_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.gmail_tracker_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.gmail_tracker_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.id_generator_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.id_generator_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.merge_data_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-05-28 16:44:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.merge_data_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.message_to_data_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.message_to_data_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.select_data_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.select_data_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.split_text_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.split_text_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.text_analysis_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.text_analysis_component
2025-05-28 16:44:55 - ComponentSystem - INFO - [discover_component_modules:1396] Imported 17 component modules: ['app.components.alter_metadata_component', 'app.components.api_component', 'app.components.combine_text_component', 'app.components.combine_text_component_new', 'app.components.conditional_component', 'app.components.convert_script_data_component', 'app.components.data_to_dataframe_component', 'app.components.doc_component', 'app.components.dynamic_combine_text_component', 'app.components.gmail_component', 'app.components.gmail_tracker_component', 'app.components.id_generator_component', 'app.components.merge_data_component', 'app.components.message_to_data_component', 'app.components.select_data_component', 'app.components.split_text_component', 'app.components.text_analysis_component']
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: **************:9092
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-05-28 16:44:55 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-05-28 16:45:02 - ComponentSystem - INFO - [start_component:356] Kafka consumer started successfully for component: ApiRequestNode
2025-05-28 16:45:02 - ComponentSystem - INFO - [start_component:378] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-05-28 16:45:02 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-05-28 16:45:02 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=266, TaskID=ApiRequestNode-node-execution-request-0-266-1748430902.6715815
2025-05-28 16:45:02 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-266-1748430902.6715815, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"social\":\"4\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "marketing": "2"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "fbe24603-f02f-4943-a3c4-a9a2c4e9de7c",
  "correlation_id": "09354c4a-cd67-4939-852e-b7403f21528d"
}
2025-05-28 16:45:02 - ComponentSystem - INFO - [_process_message:713] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Executing tool MergeDataComponent for RequestID=fbe24603-f02f-4943-a3c4-a9a2c4e9de7c, TaskID=ApiRequestNode-node-execution-request-0-266-1748430902.6715815
2025-05-28 16:45:02 - ComponentSystem - INFO - [_process_message:717] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Tool MergeDataComponent executed successfully for RequestID=fbe24603-f02f-4943-a3c4-a9a2c4e9de7c, TaskID=ApiRequestNode-node-execution-request-0-266-1748430902.6715815
2025-05-28 16:45:02 - ComponentSystem - INFO - [_send_result:1005] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Preparing to send result for component ApiRequestNode, RequestID=fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:45:02 - ComponentSystem - INFO - [get_producer:244] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Creating Kafka producer for component ApiRequestNode with configuration:
2025-05-28 16:45:02 - ComponentSystem - INFO - [get_producer:247] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d]   Bootstrap Servers: **************:9092
2025-05-28 16:45:02 - ComponentSystem - INFO - [get_producer:248] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d]   Acks: all (ensuring message is written to all in-sync replicas)
2025-05-28 16:45:02 - ComponentSystem - INFO - [get_producer:252] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d]   Request Timeout: 60000ms
2025-05-28 16:45:02 - ComponentSystem - INFO - [get_producer:255] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-05-28 16:45:02 - ComponentSystem - INFO - [get_producer:259] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Creating new Kafka producer for component: ApiRequestNode with servers: **************:9092
2025-05-28 16:45:04 - ComponentSystem - INFO - [get_producer:266] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Kafka producer started successfully for component: ApiRequestNode
2025-05-28 16:45:04 - ComponentSystem - INFO - [_send_result:1118] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Sending Kafka response: RequestID=fbe24603-f02f-4943-a3c4-a9a2c4e9de7c, Response={
  "request_id": "fbe24603-f02f-4943-a3c4-a9a2c4e9de7c",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "fbe24603-f02f-4943-a3c4-a9a2c4e9de7c",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "value": "{\"social\":\"4\"}",
        "marketing": "2"
      }
    }
  },
  "status": "success",
  "timestamp": 1748430904.5700316
}
2025-05-28 16:45:04 - ComponentSystem - INFO - [_send_result:1127] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Sent result for component ApiRequestNode to topic node_results for RequestID=fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:45:05 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Successfully committed offset 267 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-266-1748430902.6715815
2025-05-28 16:45:05 - ComponentSystem - INFO - [_process_message:936] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=266, TaskID=ApiRequestNode-node-execution-request-0-266-1748430902.6715815
2025-05-28 16:45:49 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=267, TaskID=ApiRequestNode-node-execution-request-0-267-1748430949.333623
2025-05-28 16:45:49 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-267-1748430949.333623, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748429970692-5225765898358",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"body\":\"testing\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "9ecb6624-6ae0-43b4-9ed0-c727dd127b0b",
  "correlation_id": "e22a7320-ed4c-4e62-990a-d3a2e5132645"
}
2025-05-28 16:45:49 - ComponentSystem - INFO - [_process_message:713] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Executing tool ApiRequestNode for RequestID=9ecb6624-6ae0-43b4-9ed0-c727dd127b0b, TaskID=ApiRequestNode-node-execution-request-0-267-1748430949.333623
2025-05-28 16:45:49 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=268, TaskID=ApiRequestNode-node-execution-request-0-268-1748430949.333623
2025-05-28 16:45:49 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-268-1748430949.333623, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"social\":\"4\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "marketing": "2"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1",
  "correlation_id": "e22a7320-ed4c-4e62-990a-d3a2e5132645"
}
2025-05-28 16:45:49 - ComponentSystem - INFO - [_process_message:713] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Executing tool MergeDataComponent for RequestID=553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1, TaskID=ApiRequestNode-node-execution-request-0-268-1748430949.333623
2025-05-28 16:45:49 - ComponentSystem - INFO - [_process_message:717] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Tool MergeDataComponent executed successfully for RequestID=553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1, TaskID=ApiRequestNode-node-execution-request-0-268-1748430949.333623
2025-05-28 16:45:49 - ComponentSystem - INFO - [_send_result:1005] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Preparing to send result for component ApiRequestNode, RequestID=553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:45:49 - ComponentSystem - INFO - [_send_result:1118] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Sending Kafka response: RequestID=553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1, Response={
  "request_id": "553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "value": "{\"social\":\"4\"}",
        "marketing": "2"
      }
    }
  },
  "status": "success",
  "timestamp": 1748430949.3587146
}
2025-05-28 16:45:49 - ComponentSystem - INFO - [_send_result:1127] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Sent result for component ApiRequestNode to topic node_results for RequestID=553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:45:49 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Successfully committed offset 269 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-268-1748430949.333623
2025-05-28 16:45:49 - ComponentSystem - INFO - [_process_message:936] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=268, TaskID=ApiRequestNode-node-execution-request-0-268-1748430949.333623
2025-05-28 16:45:50 - ComponentSystem - INFO - [_process_message:717] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Tool ApiRequestNode executed successfully for RequestID=9ecb6624-6ae0-43b4-9ed0-c727dd127b0b, TaskID=ApiRequestNode-node-execution-request-0-267-1748430949.333623
2025-05-28 16:45:50 - ComponentSystem - INFO - [_send_result:1005] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Preparing to send result for component ApiRequestNode, RequestID=9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:50 - ComponentSystem - INFO - [_send_result:1118] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Sending Kafka response: RequestID=9ecb6624-6ae0-43b4-9ed0-c727dd127b0b, Response={
  "request_id": "9ecb6624-6ae0-43b4-9ed0-c727dd127b0b",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "9ecb6624-6ae0-43b4-9ed0-c727dd127b0b",
    "status": "success",
    "response": {
      "result": "1748430949659-2249077649321",
      "status_code": 200,
      "response_headers": {
        "Date": "Wed, 28 May 2025 11:15:49 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-qoErpg1IcfOTTlnhNFvoCHoIOwM\"",
        "X-Response-Time": "0.72954ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=LxDDrOXIfRUKKq7UwVWEesN0VDjAa4wz%2FUPmf%2BeiVUot4MNvoNoSKa75CzlnNWe9hKR9UuEhHDhNi47EgvO913jzaU9ev7m6R10l7ghImdBYukfKjFNGCQ%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "946d5619ad5ae22f-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748429970692-5225765898358",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748430950.1635716
}
2025-05-28 16:45:50 - ComponentSystem - INFO - [_send_result:1127] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Sent result for component ApiRequestNode to topic node_results for RequestID=9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:50 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Successfully committed offset 268 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-267-1748430949.333623
2025-05-28 16:45:50 - ComponentSystem - INFO - [_process_message:936] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=267, TaskID=ApiRequestNode-node-execution-request-0-267-1748430949.333623
2025-05-28 16:49:01 - ComponentSystem - INFO - [stop_all_components:481] Stopping all running components...
2025-05-28 16:49:01 - ComponentSystem - INFO - [stop_component:413] Stopping component: ApiRequestNode
2025-05-28 16:49:01 - ComponentSystem - INFO - [_consume_messages:601] Consumer task for component ApiRequestNode cancelled
2025-05-28 16:49:01 - ComponentSystem - INFO - [_consume_messages:608] Consumer loop finished for component: ApiRequestNode
