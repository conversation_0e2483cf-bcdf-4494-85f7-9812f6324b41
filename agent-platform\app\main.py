import argparse
import asyncio
import os

from .shared.config.base import get_settings
from .kafka_client.consumer import consume
from .executer.run import run_service
from app.shared.config.logging_config import setup_logging, get_logger

# Determine if we should use JSON logging format
use_json = os.getenv("LOG_FORMAT", "").lower() == "json"

# Set up logging before anything else
setup_logging(
    default_level=os.getenv("LOG_LEVEL", "DEBUG,INFO,ERROR"),
    logs_dir=os.getenv("LOGS_DIR", "logs"),
    use_json=use_json,
)

# Get logger for this module
logger = get_logger(__name__)

# Get configuration
settings = get_settings()

# Log application startup
logger.info(
    "Application starting",
    extra={
        "environment": settings.environment,
        "kafka_servers": settings.kafka.kafka_bootstrap_servers,
    },
)


def start_server():
    try:
        logger.info(
            "Starting Kafka consumer",
            extra={"kafka_servers": settings.kafka.kafka_bootstrap_servers},
        )
        # Make sure consume() is called correctly
        asyncio.run(consume())
    except KeyboardInterrupt:
        logger.warning("Shutting down due to keyboard interrupt")
    except Exception as e:
        logger.error(
            "Error occurred in Kafka consumer", exc_info=True, extra={"error": str(e)}
        )


def start_engine():
    try:
        logger.info("Starting engine service")
        asyncio.run(run_service())
    except KeyboardInterrupt:
        logger.warning("Engine shutting down due to keyboard interrupt")
    except Exception as e:
        logger.error(
            "Error occurred in engine service", exc_info=True, extra={"error": str(e)}
        )
    finally:
        logger.info("Engine service stopped")


def main():
    logger.info("Starting Server")
    parser = argparse.ArgumentParser(
        description="Choose which part of the script to run."
    )
    parser.add_argument(
        "--mode",
        type=str,
        required=True,
        choices=["server", "engine"],
        help="Mode to run the script in.",
    )

    args = parser.parse_args()

    if args.mode == "server":
        start_server()
    elif args.mode == "engine":
        start_engine()


if __name__ == "__main__":

    """
    Usage:
        To run the  kafka server:
        poetry run python -m app.main --mode server

        To run the engine:
        poetry run python -m app.main --mode engine
    """
    main()
