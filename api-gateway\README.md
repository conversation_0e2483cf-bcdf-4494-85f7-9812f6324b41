# API Gateway

FastAPI-based API Gateway that handles client requests and communicates with microservices.

## Features

- RESTful API endpoints
- JWT authentication
- Request validation
- Rate limiting
- CORS support
- Swagger/OpenAPI documentation
- gRPC client communication

## Prerequisites

- Python 3.11 or higher
- Poetry (Python package manager)
- Running User Service

## Setup

1. Install dependencies:

```bash
poetry install
```

2. Set up environment variables:

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# Application Settings
APP_NAME=api-gateway
DEBUG=false
API_V1_STR=/api/v1

# Service endpoints
USER_SERVICE_HOST=localhost
USER_SERVICE_PORT=50052

# JWT Settings
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Settings
CORS_ORIGINS=["http://localhost", "http://localhost:3000"]
CORS_CREDENTIALS=true
CORS_METHODS=["*"]
CORS_HEADERS=["*"]
```

## Running the Service

### Local Development

Use the provided script:

```bash
chmod +x run_local.sh
./run_local.sh
```

Or run manually:

```bash
# Install dependencies
poetry install

# Generate gRPC code
poetry run python -m app.scripts.generate_grpc

# Start the service
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### Docker Deployment

1. Make sure you have created and configured your `.env` file:
```bash
cp .env.example .env
# Edit .env file with your configuration
```

2. Create a Docker network:
```bash
docker network create api-network
```

3. Run Redis container:
```bash
docker run -d \
  --name redis \
  --network api-network \
  -p 6379:6379 \
  redis:latest
```

4. Build the API Gateway image:
```bash
docker build -t api-gateway .
```

5. Run the API Gateway container:
```bash
docker run -d \
  --name api-gateway \
  --network api-network \
  -p 8000:8000 \
  --env-file .env \
  api-gateway
```

Note: Make sure your `.env` file has the correct Redis configuration:
```
REDIS_HOST=redis    # Use the container name as the host
REDIS_PORT=6379
REDIS_DB=0
```

To stop and clean up:
```bash
docker stop api-gateway redis
docker rm api-gateway redis
docker network rm api-network
```

The API Gateway will start on port 8000.

## API Documentation

Once running, access the API documentation at:

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### Available Endpoints

#### Authentication

- POST `/api/v1/auth/register`: Register new user
- POST `/api/v1/auth/login`: Login user
- POST `/api/v1/auth/google`: Google OAuth login
- POST `/api/v1/auth/refresh`: Refresh access token

#### User Management

- GET `/api/v1/users/me`: Get current user profile
- PUT `/api/v1/users/me`: Update current user
- DELETE `/api/v1/users/me`: Delete current user
- GET `/api/v1/users`: List users (admin only)
- GET `/api/v1/users/search`: Search users (admin only)

## Development

### Project Structure

```
api-gateway/
├── app/
│   ├── api/           # API routes and endpoints
│   ├── core/          # Core functionality
│   ├── grpc/          # Generated gRPC code
│   ├── schemas/       # Pydantic models
│   └── services/      # Service clients
├── proto-definitions/ # Proto files
├── tests/            # Test files
├── .env.example      # Example environment variables
├── poetry.lock       # Lock file for dependencies
├── pyproject.toml    # Project configuration
└── README.md         # This file
```

### Testing

Run tests with:

```bash
poetry run pytest
```

### Generating gRPC Code

After modifying proto files:

```bash
poetry run python -m app.scripts.generate_grpc
```
