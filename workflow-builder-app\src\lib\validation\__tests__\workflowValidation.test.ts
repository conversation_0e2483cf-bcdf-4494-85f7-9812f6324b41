import { describe, it, expect, jest } from "jest";
import { validateWorkflow, validateWorkflowStructure } from "../workflowValidation";
import { ValidationErrorCode } from "../types";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";

// Mock the imported validation functions
jest.mock("../nodeValidation", () => ({
  validateNodes: jest.fn().mockReturnValue({ isValid: true, errors: [], warnings: [], infos: [] }),
}));

jest.mock("../edgeValidation", () => ({
  validateEdges: jest.fn().mockReturnValue({ isValid: true, errors: [], warnings: [], infos: [] }),
}));

jest.mock("../connectivityValidation", () => ({
  validateStartNode: jest.fn().mockReturnValue({ 
    isValid: true, 
    errors: [], 
    warnings: [], 
    infos: [], 
    startNodeId: "start-node" 
  }),
  validateConnectivity: jest.fn().mockReturnValue({ 
    isValid: true, 
    errors: [], 
    warnings: [], 
    infos: [], 
    connectedNodes: new Set(["start-node", "node-1"]) 
  }),
  detectCyclesInWorkflow: jest.fn().mockReturnValue({ isValid: true, errors: [], warnings: [], infos: [] }),
}));

jest.mock("../fieldValidation", () => ({
  collectMissingRequiredFields: jest.fn().mockReturnValue([]),
}));

describe("Workflow Validation", () => {
  describe("validateWorkflowStructure", () => {
    it("should validate a valid workflow structure", () => {
      const workflow = {
        nodes: [
          {
            id: "start-node",
            data: {
              originalType: "StartNode",
            },
          },
          {
            id: "node-1",
            data: {
              originalType: "TextNode",
            },
          },
        ],
        edges: [
          { id: "edge-1", source: "start-node", target: "node-1" },
        ],
      };

      const result = validateWorkflowStructure(workflow);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should detect invalid workflow JSON", () => {
      const workflow = null;

      const result = validateWorkflowStructure(workflow);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.WORKFLOW_INVALID_JSON);
    });

    it("should detect missing nodes array", () => {
      const workflow = {
        edges: [],
      };

      const result = validateWorkflowStructure(workflow);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.WORKFLOW_MISSING_NODES);
    });

    it("should detect missing edges array", () => {
      const workflow = {
        nodes: [],
      };

      const result = validateWorkflowStructure(workflow);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.WORKFLOW_MISSING_EDGES);
    });

    it("should detect invalid workflow name", () => {
      const workflow = {
        nodes: [],
        edges: [],
        workflow_name: 123, // Should be a string
      };

      const result = validateWorkflowStructure(workflow);
      expect(result.isValid).toBe(false);
      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0].code).toBe(ValidationErrorCode.WORKFLOW_INVALID_NAME);
    });
  });

  describe("validateWorkflow", () => {
    it("should validate a valid workflow", () => {
      const nodes = [
        {
          id: "start-node",
          data: {
            originalType: "StartNode",
          },
        },
        {
          id: "node-1",
          data: {
            originalType: "TextNode",
          },
        },
      ] as Node<WorkflowNodeData>[];

      const edges = [
        { id: "edge-1", source: "start-node", target: "node-1" },
      ] as Edge[];

      const result = validateWorkflow(nodes, edges);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should respect validation options", () => {
      const nodes = [
        {
          id: "start-node",
          data: {
            originalType: "StartNode",
          },
        },
        {
          id: "node-1",
          data: {
            originalType: "TextNode",
          },
        },
      ] as Node<WorkflowNodeData>[];

      const edges = [
        { id: "edge-1", source: "start-node", target: "node-1" },
      ] as Edge[];

      // Disable connectivity validation
      const result = validateWorkflow(nodes, edges, {
        validateConnectivity: false,
        collectMissingFields: false,
        validateCycles: false,
      });
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      
      // The validateConnectivity function should not have been called
      const { validateConnectivity } = require("../connectivityValidation");
      expect(validateConnectivity).not.toHaveBeenCalled();
    });
  });
});
