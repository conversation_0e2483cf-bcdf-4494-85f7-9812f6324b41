import re
import asyncio
from typing import List, Dict, Any, Optional, Literal

# Assuming these imports point to your framework's definitions
from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    DropdownInput,
    MultilineInput,
    BoolInput,
    IntInput,  # Keep IntInput if needed for other things, not used here now
    InputVisibilityRule,
    HandleInput,
)
from app.models.workflow_builder.components import Output


from app.utils.workflow_builder.input_helpers import create_dual_purpose_input


class ConditionalNode(BaseNode):
    """
    Compares two text inputs and routes a separate input value to either the
    'True' or 'False' output based on the comparison result.
    """

    name = "ConditionalNode"
    display_name = "If-Else Router"
    description = "Compares 'Text Input' and 'Compare Text', then routes 'Input Value' to the 'True' or 'False' output."
    category = "Logic"
    icon = "GitBranch"
    beta = False

    OPERATOR_OPTIONS = ["equals", "not equals", "contains", "starts with", "ends with", "regex"]

    inputs: List[InputBase] = [
        # Text Input - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="input_text",
            display_name="Text Input",
            input_type="multiline",
            info="The text to compare. Can be connected from another node or entered directly.",
            required=True,
            input_types=["string", "Any"],
        ),
        MultilineInput(
            name="match_text",
            display_name="Compare Text",
            info="The text to compare against 'Text Input'. Typically entered directly.",
            required=True,
            is_handle=False,  # Usually, this is typed directly, no handle needed
            value="",
        ),
        DropdownInput(
            name="operator",
            display_name="Operator",
            options=OPERATOR_OPTIONS,
            info="The comparison operator to apply.",
            value="equals",
            real_time_refresh=True,  # For visibility rules
        ),
        BoolInput(
            name="case_sensitive",
            display_name="Case Sensitive",
            info="If true, comparison is case sensitive (ignored for 'regex').",
            value=False,
            visibility_rules=[  # Show unless operator is 'regex'
                InputVisibilityRule(field_name="operator", field_value="equals"),
                InputVisibilityRule(field_name="operator", field_value="not equals"),
                InputVisibilityRule(field_name="operator", field_value="contains"),
                InputVisibilityRule(field_name="operator", field_value="starts with"),
                InputVisibilityRule(field_name="operator", field_value="ends with"),
            ],
        ),
        # Input value to route - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="routed_input",
            display_name="Input Value (to Route)",
            input_type="multiline",
            info="The value/message to pass through the selected output route. Can be connected from another node or entered directly.",
            required=False,
            input_types=["Any"],
        ),
        BoolInput(
            name="route_input_text_if_unconnected",
            display_name="Route 'Text Input' if 'Input Value' is Not Connected?",
            info="If checked and 'Input Value (to Route)' is not connected, the 'Text Input' value will be routed instead.",
            value=False,  # Default to original behavior
            advanced=True,  # Often makes sense to hide this slightly
        ),
    ]

    outputs: List[Output] = [
        Output(
            name="true_result",
            display_name="True",
            output_type="Any",  # Type matches 'routed_input'
            info="Outputs the 'Input Value' if the comparison is true.",
        ),
        Output(
            name="false_result",
            display_name="False",
            output_type="Any",  # Type matches 'routed_input'
            info="Outputs the 'Input Value' if the comparison is false.",
        ),
    ]

    def evaluate_condition(
        self,
        input_text: Any,  # Accept Any initially, then convert
        match_text: Any,  # Accept Any initially, then convert
        operator: str,
        case_sensitive: bool,
    ) -> bool:
        """Performs the comparison logic."""
        is_regex = operator == "regex"

        # --- Input Type Handling ---
        # Convert inputs to string for reliable comparison, handling None/empty cases
        input_str = str(input_text) if input_text is not None else ""
        match_str = str(match_text) if match_text is not None else ""

        # Apply case sensitivity if needed (and not regex)
        if not case_sensitive and not is_regex:
            input_str = input_str.lower()
            match_str = match_str.lower()
        # --- End Input Type Handling ---

        if operator == "equals":
            return input_str == match_str
        if operator == "not equals":
            return input_str != match_str
        if operator == "contains":
            # Ensure match_str is not empty before checking 'in'
            return (
                match_str in input_str if match_str else True
            )  # Empty string is contained in any string
        if operator == "starts with":
            return input_str.startswith(match_str)
        if operator == "ends with":
            return input_str.endswith(match_str)
        if operator == "regex":
            if not match_str:  # Empty regex pattern is often problematic
                return False  # Or True depending on desired behavior for empty pattern
            try:
                # Use re.search for general substring matching
                return bool(re.search(match_str, input_str))
            except re.error:
                print(f"Warning: Invalid regex pattern '{match_str}' in {self.name}")
                return False  # Invalid regex fails the match
        return False  # Default for unknown operator

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the ConditionalNode.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Routes the 'routed_input' value to the appropriate output based on the comparison result.
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        # --- Get Comparison Inputs ---
        input_text = kwargs.get("input_text", "")
        match_text = kwargs.get("match_text", "")
        operator = kwargs.get("operator", "equals")
        # Get case_sensitive, default to False if hidden (e.g., when operator is regex)
        case_sensitive = kwargs.get("case_sensitive", False)

        # --- Get Value to Route ---
        routed_input = kwargs.get("routed_input", "")

        # Check if we should use input_text as the routed value
        route_input_text_if_unconnected = kwargs.get("route_input_text_if_unconnected", False)
        value_to_route = routed_input
        if (value_to_route is None or value_to_route == "") and route_input_text_if_unconnected:
            value_to_route = input_text

        print(f"  - Text Input: '{input_text}' (Type: {type(input_text).__name__})")
        print(f"  - Compare Text: '{match_text}'")
        print(f"  - Operator: '{operator}'")
        if operator != "regex":
            print(f"  - Case Sensitive: {case_sensitive}")

        # --- Perform Evaluation ---
        try:
            condition_met = self.evaluate_condition(
                input_text,
                match_text,
                operator,
                case_sensitive=(operator != "regex" and case_sensitive),
            )
            print(f"  - Condition Result: {condition_met}")
        except Exception as e:
            print(f"Error during condition evaluation: {e}")
            condition_met = False  # Fail the condition if there's an error
            print(f"  - Condition Result (due to error): {condition_met}")

        # --- Prepare Output ---
        output_data = {
            "true_result": None,
            "false_result": None,
        }

        # Use the determined value_to_route for output routing
        if condition_met:
            print(f"  - Routing value to 'true_result'")
            output_data["true_result"] = value_to_route
        else:
            print(f"  - Routing value to 'false_result'")
            output_data["false_result"] = value_to_route

        return output_data
