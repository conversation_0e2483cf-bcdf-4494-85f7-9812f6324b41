import json
import logging
import async<PERSON>
from typing import Dict, Any, List, ClassVar, Optional

# Import BaseNode and definitions
from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    DropdownInput,
    BoolInput,
    StringInput,
    IntInput,
    InputVisibilityRule,
)
from app.models.workflow_builder.components import Output


# Configure logging
logger = logging.getLogger(__name__)


class WebhookComponent(BaseNode):
    """
    Receives data from an external system via an HTTP request trigger.

    This component acts as an entry point for a workflow. The framework
    provides a unique URL. When a request hits that URL, the framework
    triggers this component and provides the request details as inputs.
    """

    # Component metadata
    name: ClassVar[str] = "WebhookComponent"
    display_name: ClassVar[str] = "Webhook Trigger"
    description: ClassVar[str] = "Receives data from an external system via HTTP request."
    category: ClassVar[str] = "Data Interaction"  # Using existing category
    icon: ClassVar[str] = "Globe"
    beta: ClassVar[bool] = True  # Keep if applicable

    # Define inputs - Mostly configuration, plus hidden inputs for received data
    inputs: ClassVar[List[InputBase]] = [
        # --- Configuration Inputs (Used by the FRAMEWORK) ---
        DropdownInput(
            name="method",
            display_name="Allowed Method",
            options=["POST", "GET", "ANY"],  # Consider PUT, DELETE, PATCH?
            value="POST",
            info="HTTP method the webhook endpoint will accept. Set by framework.",
            advanced=True,  # Configuration is often advanced
        ),
        BoolInput(
            name="require_auth",
            display_name="Require Authentication",
            value=False,
            info="If enabled, framework requires requests to include a valid auth token.",
            advanced=True,
        ),
        StringInput(
            name="auth_token",
            display_name="Auth Token",
            required=False,
            is_handle=False,  # Usually False for config values
            # password=True, # Good practice for tokens
            value="",  # Default empty
            info="Secret token framework uses to validate requests if auth is required.",
            visibility_rules=[InputVisibilityRule(field_name="require_auth", field_value=True)],
            advanced=True,
        ),
        # --- Display Inputs (Provided BY the FRAMEWORK) ---
        StringInput(
            name="webhook_url",
            display_name="Webhook URL",
            info="The unique URL for triggering this workflow. Send HTTP requests here.",
            value="FRAMEWORK_GENERATED_URL",  # Placeholder text
            is_handle=False,
        ),
        StringInput(
            name="curl_example",
            display_name="cURL Example",
            value="FRAMEWORK_GENERATED_CURL",  # Placeholder
            is_handle=False,
            advanced=True,
        ),
        # --- Hidden Inputs (Populated BY the FRAMEWORK during trigger) ---
        # These inputs receive the actual data when the webhook is triggered.
        # They might not be visible in the UI graph view but are essential for execution.
        # The framework needs to know to populate these specific input names.
        StringInput(name="received_headers", display_name="Received Headers", advanced=True),
        StringInput(
            name="received_query_params", display_name="Received Query Params", advanced=True
        ),
        StringInput(name="received_body", display_name="Received Body", advanced=True),
        StringInput(
            name="received_method", display_name="Received Method", advanced=True
        ),  # Useful context
    ]

    # Define outputs - Data extracted FROM the triggered request
    outputs: ClassVar[List[Output]] = [
        Output(name="headers", display_name="Request Headers", output_type="dict"),
        Output(name="query_params", display_name="Query Parameters", output_type="dict"),
        Output(
            name="body",
            display_name="Request Body",
            output_type="Any",  # Can be dict (JSON) or string (raw)
        ),
        Output(  # Output the URL for potential downstream use
            name="webhook_url_out",  # Different name from input maybe
            display_name="Webhook URL",
            output_type="string",
        ),
        Output(name="method_out", display_name="Request Method", output_type="string"),
        Output(
            name="trigger_data",  # Combine all for convenience? Like Langflow's Data
            display_name="Trigger Data (Combined)",
            output_type="dict",
        ),
    ]

    # Legacy method for backward compatibility
    async def build(
        self,
        # Configuration inputs might be passed but aren't used IN build
        # method: str,
        # require_auth: bool,
        # auth_token: str,
        webhook_url: str,  # The URL generated by the framework
        # Actual data injected by the framework:
        received_headers: Optional[Dict[str, Any]] = None,
        received_query_params: Optional[Dict[str, Any]] = None,
        received_body: Optional[str] = None,  # Body often comes as raw string
        received_method: Optional[str] = None,
        **kwargs,  # Allow for other framework context if needed
    ) -> Dict[str, Any]:
        """
        Legacy executor for the WebhookComponent.
        Processes the data received from the webhook trigger.

        This method executes *after* the framework has received an HTTP request
        and triggered the workflow. It parses the provided request details.

        Args:
            webhook_url (str): The URL that was triggered.
            received_headers (Optional[Dict]): Headers from the incoming request.
            received_query_params (Optional[Dict]): Query params from the incoming request.
            received_body (Optional[str]): Raw body from the incoming request.
            received_method (Optional[str]): HTTP method used for the request.
            **kwargs: Additional context from the framework.

        Returns:
            A dictionary containing the processed request data for downstream components.
        """
        print(f"Executing {self.name} using legacy build method...")
        headers = received_headers or {}
        query_params = received_query_params or {}
        method = received_method or "UNKNOWN"
        body_output: Any = None
        parsed_body: Optional[Dict[str, Any]] = None
        status_message = "Webhook triggered."  # Add status like Langflow

        if received_body:
            try:
                # Attempt to parse as JSON, like Langflow
                parsed_body = json.loads(received_body)
                body_output = parsed_body
                status_message += "\nParsed JSON body."
                logger.info("Webhook received JSON body.")
            except json.JSONDecodeError:
                # If not JSON, treat as raw string
                body_output = received_body
                status_message += f"\nReceived raw body (not valid JSON):\n{received_body[:100]}..."  # Show snippet
                logger.info("Webhook received non-JSON body.")
            except Exception as e:
                # Catch other potential errors during parsing
                body_output = received_body  # Provide raw body on error too
                status_message += f"\nError processing body: {e}"
                logger.error(f"Error processing webhook body: {e}")
        else:
            status_message += "\nNo body received."
            logger.info("Webhook received no body.")

        # Combine data for the 'trigger_data' output
        trigger_data = {
            "url": webhook_url,
            "method": method,
            "headers": headers,
            "query_params": query_params,
            "body": body_output,  # Use the parsed (or raw) body
            # "parsed_body": parsed_body # Could optionally output parsed JSON separately
        }

        # Set status (if your base class or framework supports it)
        # self.status = status_message

        logger.info(f"Webhook Component Build completed. Method: {method}, URL: {webhook_url}")

        return {
            "headers": headers,
            "query_params": query_params,
            "body": body_output,
            "webhook_url_out": webhook_url,
            "method_out": method,
            "trigger_data": trigger_data,
            # Add error output if needed, based on internal processing
            # "error": ""
        }
