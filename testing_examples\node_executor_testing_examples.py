#!/usr/bin/env python3
"""
Node Executor Service Testing Examples for AlterMetadataComponent
These examples show how to test the component in the Node Executor Service.
"""
import asyncio
import json
import sys
import os

# Add node executor service to path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'node-executor-service'))

# Mock the dependencies for testing
import types
mock_base = types.ModuleType('app.core_.base_component')
mock_base.BaseComponent = type('BaseComponent', (), {
    '__init__': lambda self: None,
    'request_schema': None
})
mock_base.ValidationResult = type('ValidationResult', (), {
    '__init__': lambda self, is_valid=True, error_message="", error_details=None: (
        setattr(self, 'is_valid', is_valid),
        setattr(self, 'error_message', error_message),
        setattr(self, 'error_details', error_details or {})
    )
})

mock_system = types.ModuleType('app.core_.component_system')
mock_system.register_component = lambda name: lambda cls: cls

sys.modules['app.core_.base_component'] = mock_base
sys.modules['app.core_.component_system'] = mock_system

from app.components.alter_metadata_component import AlterMetadataComponent


async def test_node_executor_basic_functionality():
    """Test 1: Basic Node Executor functionality."""
    print("🔧 Node Executor Test 1: Basic Functionality")
    print("=" * 60)
    
    component = AlterMetadataComponent()
    
    # Test payload (as it would come from the API Gateway)
    payload = {
        "request_id": "test_basic_001",
        "tool_parameters": {
            "input_metadata": {
                "document_id": "DOC-001",
                "title": "Test Document",
                "version": "1.0",
                "status": "draft"
            },
            "updates": {
                "version": "2.0",
                "status": "published",
                "last_modified": "2024-01-15"
            },
            "keys_to_remove": []
        }
    }
    
    print("Input payload:")
    print(json.dumps(payload, indent=2))
    
    # Validate
    print("\n--- Validation Phase ---")
    validation_result = await component.validate(payload)
    print(f"Validation result: {validation_result.is_valid}")
    if not validation_result.is_valid:
        print(f"Validation error: {validation_result.error_message}")
        return False
    
    # Process
    print("\n--- Processing Phase ---")
    result = await component.process(payload)
    
    print("Processing result:")
    print(json.dumps(result, indent=2))
    
    # Verify result
    if result.get('status') == 'success':
        output_metadata = result.get('result', {})
        
        success_checks = [
            ("Version updated", output_metadata.get('version') == '2.0'),
            ("Status updated", output_metadata.get('status') == 'published'),
            ("Original data preserved", output_metadata.get('document_id') == 'DOC-001'),
            ("New field added", 'last_modified' in output_metadata)
        ]
        
        print("\nVerification:")
        all_passed = True
        for check_name, passed in success_checks:
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            all_passed = all_passed and passed
        
        return all_passed
    else:
        print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
        return False


async def test_node_executor_direct_payload():
    """Test 2: Direct payload format (without tool_parameters wrapper)."""
    print("\n🔧 Node Executor Test 2: Direct Payload Format")
    print("=" * 60)
    
    component = AlterMetadataComponent()
    
    # Direct payload format
    payload = {
        "request_id": "test_direct_001",
        "input_metadata": {
            "user_id": "USER-123",
            "username": "john_doe",
            "email": "<EMAIL>",
            "temp_session": "session_abc123"
        },
        "updates": {
            "last_login": "2024-01-15T10:30:00Z",
            "login_count": 42
        },
        "keys_to_remove": ["temp_session"]
    }
    
    print("Direct payload:")
    print(json.dumps(payload, indent=2))
    
    # Validate
    validation_result = await component.validate(payload)
    print(f"\nValidation result: {validation_result.is_valid}")
    
    if not validation_result.is_valid:
        print(f"Validation error: {validation_result.error_message}")
        return False
    
    # Process
    result = await component.process(payload)
    
    print("\nProcessing result:")
    print(json.dumps(result, indent=2))
    
    # Verify
    if result.get('status') == 'success':
        output = result.get('result', {})
        
        success = (
            'temp_session' not in output and
            output.get('last_login') == '2024-01-15T10:30:00Z' and
            output.get('user_id') == 'USER-123'
        )
        
        print(f"\n{'✅' if success else '❌'} Direct payload test {'passed' if success else 'failed'}")
        return success
    else:
        print(f"❌ Processing failed: {result.get('error')}")
        return False


async def test_node_executor_validation_errors():
    """Test 3: Validation error scenarios."""
    print("\n🔧 Node Executor Test 3: Validation Errors")
    print("=" * 60)
    
    component = AlterMetadataComponent()
    
    error_scenarios = [
        {
            "name": "Missing input_metadata",
            "payload": {
                "request_id": "error_test_1",
                "tool_parameters": {
                    "updates": {"version": "2.0"},
                    "keys_to_remove": []
                }
            }
        },
        {
            "name": "Invalid input_metadata type",
            "payload": {
                "request_id": "error_test_2",
                "tool_parameters": {
                    "input_metadata": "not_a_dict",
                    "updates": {},
                    "keys_to_remove": []
                }
            }
        },
        {
            "name": "Invalid updates type",
            "payload": {
                "request_id": "error_test_3",
                "tool_parameters": {
                    "input_metadata": {"name": "test"},
                    "updates": "not_a_dict",
                    "keys_to_remove": []
                }
            }
        },
        {
            "name": "Invalid keys_to_remove type",
            "payload": {
                "request_id": "error_test_4",
                "tool_parameters": {
                    "input_metadata": {"name": "test"},
                    "updates": {},
                    "keys_to_remove": "not_a_list"
                }
            }
        }
    ]
    
    all_passed = True
    
    for scenario in error_scenarios:
        print(f"\n--- Testing: {scenario['name']} ---")
        
        validation_result = await component.validate(scenario['payload'])
        
        print(f"Validation result: {validation_result.is_valid}")
        print(f"Error message: {validation_result.error_message}")
        
        # Should fail validation
        if validation_result.is_valid:
            print(f"❌ Expected validation to fail for {scenario['name']}")
            all_passed = False
        else:
            print(f"✅ Validation correctly failed for {scenario['name']}")
    
    return all_passed


async def test_node_executor_large_data():
    """Test 4: Large data handling."""
    print("\n🔧 Node Executor Test 4: Large Data Handling")
    print("=" * 60)
    
    component = AlterMetadataComponent()
    
    # Generate large metadata
    large_metadata = {f"field_{i}": f"value_{i}" for i in range(1000)}
    large_metadata.update({
        "document_id": "LARGE-DOC-001",
        "type": "large_dataset",
        "created_date": "2024-01-15",
        "temp_processing_id": "TEMP-12345"
    })
    
    # Large updates
    large_updates = {f"updated_field_{i}": f"new_value_{i}" for i in range(100)}
    large_updates.update({
        "processed_date": "2024-01-15T12:00:00Z",
        "processing_status": "completed"
    })
    
    payload = {
        "request_id": "large_data_test",
        "tool_parameters": {
            "input_metadata": large_metadata,
            "updates": large_updates,
            "keys_to_remove": ["temp_processing_id"]
        }
    }
    
    print(f"Large data test:")
    print(f"  Input metadata: {len(large_metadata)} fields")
    print(f"  Updates: {len(large_updates)} fields")
    print(f"  Keys to remove: 1 field")
    
    # Measure performance
    import time
    
    # Validation
    start_time = time.time()
    validation_result = await component.validate(payload)
    validation_time = time.time() - start_time
    
    print(f"\nValidation:")
    print(f"  Result: {validation_result.is_valid}")
    print(f"  Time: {validation_time:.4f}s")
    
    if not validation_result.is_valid:
        print(f"  Error: {validation_result.error_message}")
        return False
    
    # Processing
    start_time = time.time()
    result = await component.process(payload)
    processing_time = time.time() - start_time
    
    print(f"\nProcessing:")
    print(f"  Status: {result.get('status')}")
    print(f"  Time: {processing_time:.4f}s")
    
    if result.get('status') == 'success':
        output = result.get('result', {})
        expected_size = len(large_metadata) + len(large_updates) - 1  # -1 for removed key
        actual_size = len(output)
        
        print(f"  Output size: {actual_size} fields")
        print(f"  Expected size: {expected_size} fields")
        
        # Performance check
        performance_ok = processing_time < 1.0  # Should process in under 1 second
        size_correct = actual_size == expected_size
        temp_removed = 'temp_processing_id' not in output
        
        success = performance_ok and size_correct and temp_removed
        
        print(f"\nLarge data verification:")
        print(f"  {'✅' if performance_ok else '❌'} Performance acceptable ({processing_time:.4f}s < 1.0s)")
        print(f"  {'✅' if size_correct else '❌'} Output size correct ({actual_size} == {expected_size})")
        print(f"  {'✅' if temp_removed else '❌'} Temporary field removed")
        
        return success
    else:
        print(f"❌ Large data processing failed: {result.get('error')}")
        return False


async def test_node_executor_concurrent_requests():
    """Test 5: Concurrent request handling."""
    print("\n🔧 Node Executor Test 5: Concurrent Requests")
    print("=" * 60)
    
    component = AlterMetadataComponent()
    
    # Create multiple concurrent requests
    requests = []
    for i in range(10):
        payload = {
            "request_id": f"concurrent_test_{i}",
            "tool_parameters": {
                "input_metadata": {
                    "request_id": f"req_{i}",
                    "data": f"test_data_{i}",
                    "timestamp": "2024-01-15T12:00:00Z",
                    "temp_field": f"temp_{i}"
                },
                "updates": {
                    "processed": True,
                    "batch_id": "BATCH-001"
                },
                "keys_to_remove": ["temp_field"]
            }
        }
        requests.append(payload)
    
    print(f"Testing {len(requests)} concurrent requests...")
    
    # Process all requests concurrently
    import time
    start_time = time.time()
    
    tasks = []
    for payload in requests:
        # Validate and process each request
        async def process_request(p):
            validation = await component.validate(p)
            if validation.is_valid:
                return await component.process(p)
            else:
                return {"status": "error", "error": validation.error_message}
        
        tasks.append(process_request(payload))
    
    results = await asyncio.gather(*tasks)
    total_time = time.time() - start_time
    
    print(f"\nConcurrent processing completed in {total_time:.4f}s")
    
    # Verify results
    successful = sum(1 for r in results if r.get('status') == 'success')
    failed = len(results) - successful
    
    print(f"Results: {successful} successful, {failed} failed")
    
    # Check individual results
    all_correct = True
    for i, result in enumerate(results):
        if result.get('status') == 'success':
            output = result.get('result', {})
            expected_request_id = f"req_{i}"
            actual_request_id = output.get('request_id')
            temp_removed = 'temp_field' not in output
            processed_added = output.get('processed') is True
            
            if actual_request_id != expected_request_id or not temp_removed or not processed_added:
                print(f"❌ Request {i} result incorrect")
                all_correct = False
        else:
            print(f"❌ Request {i} failed: {result.get('error')}")
            all_correct = False
    
    success = successful == len(requests) and all_correct
    
    print(f"\n{'✅' if success else '❌'} Concurrent processing {'passed' if success else 'failed'}")
    print(f"Average time per request: {total_time/len(requests):.4f}s")
    
    return success


async def run_node_executor_tests():
    """Run all Node Executor Service tests."""
    print("🚀 Starting Node Executor Service Testing Suite")
    print("=" * 80)
    
    tests = [
        ("Basic Functionality", test_node_executor_basic_functionality),
        ("Direct Payload Format", test_node_executor_direct_payload),
        ("Validation Errors", test_node_executor_validation_errors),
        ("Large Data Handling", test_node_executor_large_data),
        ("Concurrent Requests", test_node_executor_concurrent_requests)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ EXCEPTION in {test_name}: {str(e)}")
            import traceback
            print(traceback.format_exc())
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 NODE EXECUTOR SERVICE TESTING SUMMARY")
    print(f"{'='*80}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Node Executor Service tests PASSED!")
    else:
        print("⚠️  Some Node Executor Service tests failed.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(run_node_executor_tests())
