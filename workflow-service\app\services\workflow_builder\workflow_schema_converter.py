"""
Module for converting between workflow_capture_schema.json and transition_schema.json formats.

This module has been updated to align with the simplified orchestration engine transition schema
that supports enhanced parameter resolution through explicit field mappings.

Key enhancements:
- Generates explicit 'mapping' arrays in input_data entries for precise field-to-field connections
- Supports nested field path resolution (e.g., 'result.result') for single result resolution
- Maintains backward compatibility with existing field mapping logic
- Removes deprecated connection metadata (priority, required, fallback_value, handle_id)
"""

from collections import defaultdict
from typing import Dict, List, Any, Optional, Set, Tuple
from app.services.workflow_builder.node_combiner import combine_nodes


def build_graph_from_workflow(workflow_data: Dict[str, Any]) -> Tuple[Dict, Dict, Set]:
    """
    Build a directed graph from the workflow edges.

    Args:
        workflow_data: The workflow data in workflow_capture_schema format

    Returns:
        Tuple containing:
        - graph: A dictionary mapping node IDs to lists of connected node IDs
        - edge_map: A dictionary mapping (source, target) tuples to edge data
        - all_nodes: A set of all node IDs in the graph
    """
    graph = defaultdict(list)
    edge_map = {}
    all_nodes = set()

    for edge in workflow_data.get("edges", []):
        source_node = edge["source"]
        target_node = edge["target"]
        graph[source_node].append(target_node)
        edge_map[(source_node, target_node)] = edge
        all_nodes.add(source_node)
        all_nodes.add(target_node)

    return graph, edge_map, all_nodes


def compute_levels(graph: Dict, all_nodes: Set, start_nodes: Optional[List] = None) -> Dict:
    """
    Compute levels for each node using DFS.

    Args:
        graph: A dictionary mapping node IDs to lists of connected node IDs
        all_nodes: A set of all node IDs in the graph
        start_nodes: Optional list of starting node IDs. If None, uses nodes with no incoming edges.

    Returns:
        A dictionary mapping node IDs to their computed level
    """
    levels = {node: 0 for node in all_nodes}

    # If no start nodes provided, find nodes with no incoming edges
    if not start_nodes:
        incoming_edges = defaultdict(int)
        for node in all_nodes:
            for neighbor in graph.get(node, []):
                incoming_edges[neighbor] += 1

        start_nodes = [node for node in all_nodes if incoming_edges.get(node, 0) == 0]

    def dfs(node, current_level, path):
        if levels[node] < current_level:
            levels[node] = current_level
        for neighbor in graph.get(node, []):
            if neighbor in path:  # Avoid cycles
                continue
            if levels.get(neighbor, 0) < current_level + 1:
                levels[neighbor] = current_level + 1
                dfs(neighbor, current_level + 1, path + [neighbor])

    for start_node in start_nodes:
        if start_node in levels:
            dfs(start_node, 0, [start_node])

    return levels


def group_nodes_by_level(levels: Dict) -> Dict:
    """
    Group nodes by their computed level.

    Args:
        levels: A dictionary mapping node IDs to their computed level

    Returns:
        A dictionary mapping levels to lists of node IDs at that level
    """
    level_groups = defaultdict(list)
    for node, level in levels.items():
        level_groups[level].append(node)
    return level_groups


def map_input_type_to_data_type(input_type: str) -> str:
    """
    Map input_type from workflow schema to data_type in transition schema.

    Args:
        input_type: The input type from workflow schema

    Returns:
        The corresponding data type for transition schema
    """
    type_mapping = {
        "string": "string",
        "multiline": "string",
        "int": "number",
        "float": "number",
        "bool": "boolean",
        "dropdown": "string",
        "dict": "object",
        "list": "array",
        "handle": "string",
        "button": "string",
        "code_editor": "string",
    }
    return type_mapping.get(input_type, "string")


def map_output_type_to_data_type(output_type: str) -> str:
    """
    Map output_type from workflow schema to data_type in transition schema.

    Args:
        output_type: The output type from workflow schema

    Returns:
        The corresponding data type for transition schema
    """
    type_mapping = {
        "string": "string",
        "number": "number",
        "boolean": "boolean",
        "array": "array",
        "object": "object",
    }
    return type_mapping.get(output_type, "string")


def determine_mapping_strategy(
    source_node_def: Dict[str, Any],
    source_handle: str,
    target_node_def: Dict[str, Any],
    target_handle: str
) -> Dict[str, str]:
    """
    Determine the appropriate mapping strategy based on node types and handle patterns.

    This function analyzes the source and target nodes to determine the best
    field mapping strategy, supporting various node types and output patterns.
    It's designed to be universal and work with any workflow format.

    Args:
        source_node_def: Source node definition
        source_handle: Source output handle
        target_node_def: Target node definition
        target_handle: Target input handle

    Returns:
        Dictionary with mapping strategy information
    """
    source_node_type = source_node_def.get("type", "")
    target_node_type = target_node_def.get("type", "")
    source_node_name = source_node_def.get("name", "")
    target_node_name = target_node_def.get("name", "")

    # Default strategy
    strategy = {
        "from_field": source_handle,
        "mapping_type": "direct",
        "confidence": "high"
    }

    # MCP nodes typically have direct output mappings
    if source_node_type == "mcp":
        strategy["from_field"] = source_handle
        strategy["mapping_type"] = "direct"
        strategy["confidence"] = "high"
        return strategy

    # API nodes typically output to 'result' or 'response' fields
    if source_node_type == "api" or "API" in source_node_name:
        # API nodes commonly use result/response patterns
        if source_handle in ["result", "response", "data", "output"]:
            strategy["from_field"] = source_handle
            strategy["mapping_type"] = "direct"
            strategy["confidence"] = "high"
        else:
            # Custom API output field
            strategy["from_field"] = source_handle
            strategy["mapping_type"] = "direct"
            strategy["confidence"] = "medium"
        return strategy

    # Component nodes may have nested result structures
    if source_node_type == "component" or "Component" in source_node_name:
        # Check if this is a common component output field
        component_output_fields = [
            "output_data", "output_text", "result", "response",
            "combined_text", "merged_data", "processed_data",
            "selected_data", "api_response", "data", "output"
        ]

        if source_handle in component_output_fields:
            # For component outputs, check if they might be nested
            if source_handle in ["result", "response"]:
                # These might be direct or nested depending on component
                strategy["from_field"] = source_handle
                strategy["mapping_type"] = "direct"
                strategy["confidence"] = "medium"
            else:
                # Other component outputs might be nested under result.result
                strategy["from_field"] = "result.result"
                strategy["mapping_type"] = "nested"
                strategy["confidence"] = "medium"
        else:
            # Unknown output field, use direct mapping
            strategy["from_field"] = source_handle
            strategy["mapping_type"] = "direct"
            strategy["confidence"] = "low"

        return strategy

    # Handle custom node types by analyzing field patterns
    if source_node_type not in ["mcp", "component", "api"]:
        # For custom/unknown node types, analyze field name patterns
        common_output_patterns = [
            "result", "output", "data", "response", "value", "content",
            "processed", "analyzed", "generated", "computed", "transformed"
        ]

        # Check if source_handle matches common patterns
        handle_lower = source_handle.lower()
        is_common_pattern = any(pattern in handle_lower for pattern in common_output_patterns)

        if is_common_pattern:
            strategy["from_field"] = source_handle
            strategy["mapping_type"] = "direct"
            strategy["confidence"] = "medium"
        else:
            # Completely unknown pattern
            strategy["from_field"] = source_handle
            strategy["mapping_type"] = "direct"
            strategy["confidence"] = "low"

        return strategy

    # For any other unknown node types, use direct mapping with low confidence
    strategy["from_field"] = source_handle
    strategy["mapping_type"] = "direct"
    strategy["confidence"] = "low"

    return strategy


def create_enhanced_field_mapping(
    source_handle: str,
    target_handle: str,
    source_node_def: Dict[str, Any],
    target_node_def: Dict[str, Any] = None,
    edge_id: str = "",
) -> Dict[str, str]:
    """
    Create enhanced field mapping that handles various node types and output patterns.

    This function creates generalized mappings that work with any workflow format,
    not just specific examples. It analyzes node types and handle patterns to
    determine the best mapping strategy.

    Args:
        source_handle: The source handle from the edge
        target_handle: The target handle from the edge
        source_node_def: The source node definition
        target_node_def: The target node definition (optional)
        edge_id: The edge ID for traceability

    Returns:
        A mapping dictionary with enhanced metadata
    """
    # Determine the mapping strategy based on node analysis
    strategy = determine_mapping_strategy(
        source_node_def, source_handle,
        target_node_def or {}, target_handle
    )

    return {
        "from_field": strategy["from_field"],
        "to_field": target_handle,
        "source_handle": source_handle,
        "target_handle": target_handle,
        "edge_id": edge_id,
        "mapping_type": strategy["mapping_type"],
        "confidence": strategy["confidence"]
    }


def convert_node_to_transition_node(
    node: Dict[str, Any], mcp_configs: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Convert a node from workflow_capture_schema format to transition_schema node format.

    Args:
        node: A node from workflow_capture_schema
        mcp_configs: MCP configurations from workflow_capture_schema

    Returns:
        A node in transition_schema format
    """
    node_id = node["id"]
    node_data = node["data"]
    node_type = node_data["type"]
    node_definition = node_data["definition"]

    # Find MCP config for this node if it exists
    mcp_config = next((config for config in mcp_configs if config["node_id"] == node_id), None)

    # Determine if this is an MCP node based on node.data.type
    is_mcp_node = node_type == "mcp" or (mcp_config is not None)

    server_tools = []

    if is_mcp_node and mcp_config:
        # Create server tools for MCP node
        tool_name = mcp_config.get("selected_tool_name", "UnknownTool")

        # Check if mcp_info.input_schema is available
        if "mcp_info" in node_definition and "input_schema" in node_definition["mcp_info"]:
            # Convert input_schema from mcp_info to the required format with predefined_fields
            mcp_input_schema = node_definition["mcp_info"]["input_schema"]
            input_schema = {"predefined_fields": []}

            # Extract properties from the mcp_input_schema
            if "properties" in mcp_input_schema:
                properties = mcp_input_schema["properties"]
                required_fields = mcp_input_schema.get("required", [])

                for field_name, field_props in properties.items():
                    # Create data_type object with proper handling of nested objects
                    data_type = {
                        "type": field_props.get("type", "string"),
                        "description": field_props.get("description", ""),
                    }

                    # Handle object properties if present
                    if field_props.get("type") == "object" and "properties" in field_props:
                        data_type["properties"] = field_props["properties"]

                    # Handle array items if present
                    if field_props.get("type") == "array" and "items" in field_props:
                        data_type["items"] = {"type": field_props["items"].get("type", "string")}

                        # Handle object properties in array items
                        if (
                            field_props["items"].get("type") == "object"
                            and "properties" in field_props["items"]
                        ):
                            data_type["items"]["properties"] = field_props["items"]["properties"]

                            # Handle required fields in array items
                            if "required" in field_props["items"]:
                                data_type["items"]["required"] = field_props["items"]["required"]

                    # Add the field to predefined_fields
                    input_schema["predefined_fields"].append(
                        {
                            "field_name": field_name,
                            "data_type": data_type,
                            "required": field_name in required_fields,
                        }
                    )
        else:
            # Create input schema from tool_schema
            tool_schema = mcp_config.get("tool_schema", {})
            input_schema = {"predefined_fields": []}

            # Extract fields from tool schema
            properties = tool_schema.get("properties", {})
            required_fields = tool_schema.get("required", [])

            for field_name, field_props in properties.items():
                field_type = field_props.get("type", "string")
                input_schema["predefined_fields"].append(
                    {
                        "field_name": field_name,
                        "data_type": {
                            "type": field_type,
                            "description": field_props.get("description", ""),
                        },
                        "required": field_name in required_fields,
                    }
                )

        # Check if mcp_info.output_schema is available
        if "mcp_info" in node_definition and "output_schema" in node_definition["mcp_info"]:
            # Convert output_schema from mcp_info to the required format with predefined_fields
            mcp_output_schema = node_definition["mcp_info"]["output_schema"]
            output_schema = {"predefined_fields": []}

            # Extract properties from the mcp_output_schema
            if "properties" in mcp_output_schema:
                properties = mcp_output_schema["properties"]

                for field_name, field_props in properties.items():
                    # Create data_type object with proper handling of nested objects
                    data_type = {
                        "type": field_props.get("type", "string"),
                        "description": field_props.get("description", ""),
                    }

                    # Handle object properties if present
                    if field_props.get("type") == "object" and "properties" in field_props:
                        data_type["properties"] = field_props["properties"]

                    # Handle array items if present
                    if field_props.get("type") == "array" and "items" in field_props:
                        data_type["items"] = {"type": field_props["items"].get("type", "string")}

                        # Handle object properties in array items
                        if (
                            field_props["items"].get("type") == "object"
                            and "properties" in field_props["items"]
                        ):
                            data_type["items"]["properties"] = field_props["items"]["properties"]

                            # Handle required fields in array items
                            if "required" in field_props["items"]:
                                data_type["items"]["required"] = field_props["items"]["required"]

                    # Add the field to predefined_fields
                    output_schema["predefined_fields"].append(
                        {
                            "field_name": field_name,
                            "data_type": data_type,
                        }
                    )
        else:
            # Create output schema (assuming standard response format)
            output_schema = {
                "predefined_fields": [
                    {
                        "field_name": "response",
                        "data_type": {
                            "type": "string",
                            "description": "Response from the MCP tool",
                        },
                    }
                ]
            }

        # Create server tool
        # For MCP nodes, ALWAYS use the tool_name from mcp_info if available
        # According to transition_schema_fields.md line 74: For MCP nodes: nodes.data.definition.mcp_info.tool_name
        if (
            node_type == "mcp"
            and "mcp_info" in node_definition
            and "tool_name" in node_definition["mcp_info"]
        ):
            tool_name = node_definition["mcp_info"]["tool_name"]
        else:
            # Fallback to the node definition name
            tool_name = node_definition["name"]

        # For MCP nodes, ALWAYS use the tool_name from mcp_info.tool_name
        # According to transition_schema_fields.md line 74: For MCP nodes: nodes.data.definition.mcp_info.tool_name
        if (
            node_type == "mcp"
            and "mcp_info" in node_definition
            and "tool_name" in node_definition["mcp_info"]
        ):
            server_tool_name = node_definition["mcp_info"]["tool_name"]
        else:
            server_tool_name = tool_name

        # Debug output
        print(f"Node type: {node_type}")
        print(f"Node definition name: {node_definition['name']}")
        if "mcp_info" in node_definition and "tool_name" in node_definition["mcp_info"]:
            print(f"MCP info tool_name: {node_definition['mcp_info']['tool_name']}")
        print(f"Server tool name: {server_tool_name}")

        server_tools.append(
            {
                "tool_id": 1,  # Assign a default ID
                "tool_name": server_tool_name,
                "input_schema": input_schema,
                "output_schema": output_schema,
            }
        )
    else:
        # Create server tools for API node
        # Check if mcp_info.input_schema is available
        if "mcp_info" in node_definition and "input_schema" in node_definition["mcp_info"]:
            # Convert input_schema from mcp_info to the required format with predefined_fields
            mcp_input_schema = node_definition["mcp_info"]["input_schema"]
            input_schema = {"predefined_fields": []}

            # Extract properties from the mcp_input_schema
            if "properties" in mcp_input_schema:
                properties = mcp_input_schema["properties"]
                required_fields = mcp_input_schema.get("required", [])

                for field_name, field_props in properties.items():
                    # Create data_type object with proper handling of nested objects
                    data_type = {
                        "type": field_props.get("type", "string"),
                        "description": field_props.get("description", ""),
                    }

                    # Handle object properties if present
                    if field_props.get("type") == "object" and "properties" in field_props:
                        data_type["properties"] = field_props["properties"]

                    # Handle array items if present
                    if field_props.get("type") == "array" and "items" in field_props:
                        data_type["items"] = {"type": field_props["items"].get("type", "string")}

                        # Handle object properties in array items
                        if (
                            field_props["items"].get("type") == "object"
                            and "properties" in field_props["items"]
                        ):
                            data_type["items"]["properties"] = field_props["items"]["properties"]

                            # Handle required fields in array items
                            if "required" in field_props["items"]:
                                data_type["items"]["required"] = field_props["items"]["required"]

                    # Add the field to predefined_fields
                    input_schema["predefined_fields"].append(
                        {
                            "field_name": field_name,
                            "data_type": data_type,
                            "required": field_name in required_fields,
                        }
                    )
        else:
            # Map inputs to input schema
            input_schema = {"predefined_fields": []}

            for input_def in node_definition.get("inputs", []):
                input_schema["predefined_fields"].append(
                    {
                        "field_name": input_def["name"],
                        "data_type": {
                            "type": map_input_type_to_data_type(
                                input_def.get("input_type", "string")
                            ),
                            "description": input_def.get("info", ""),
                        },
                        "required": input_def.get("required", False),
                    }
                )

        # Check if mcp_info.output_schema is available
        if "mcp_info" in node_definition and "output_schema" in node_definition["mcp_info"]:
            # Convert output_schema from mcp_info to the required format with predefined_fields
            mcp_output_schema = node_definition["mcp_info"]["output_schema"]
            output_schema = {"predefined_fields": []}

            # Extract properties from the mcp_output_schema
            if "properties" in mcp_output_schema:
                properties = mcp_output_schema["properties"]

                for field_name, field_props in properties.items():
                    # Create data_type object with proper handling of nested objects
                    data_type = {
                        "type": field_props.get("type", "string"),
                        "description": field_props.get("description", ""),
                    }

                    # Handle object properties if present
                    if field_props.get("type") == "object" and "properties" in field_props:
                        data_type["properties"] = field_props["properties"]

                    # Handle array items if present
                    if field_props.get("type") == "array" and "items" in field_props:
                        data_type["items"] = {"type": field_props["items"].get("type", "string")}

                        # Handle object properties in array items
                        if (
                            field_props["items"].get("type") == "object"
                            and "properties" in field_props["items"]
                        ):
                            data_type["items"]["properties"] = field_props["items"]["properties"]

                            # Handle required fields in array items
                            if "required" in field_props["items"]:
                                data_type["items"]["required"] = field_props["items"]["required"]

                    # Add the field to predefined_fields
                    output_schema["predefined_fields"].append(
                        {
                            "field_name": field_name,
                            "data_type": data_type,
                        }
                    )
        else:
            # Map outputs to output schema
            output_schema = {"predefined_fields": []}

            for output_def in node_definition.get("outputs", []):
                output_schema["predefined_fields"].append(
                    {
                        "field_name": output_def["name"],
                        "data_type": {
                            "type": map_output_type_to_data_type(
                                output_def.get("output_type", "string")
                            ),
                            "description": "",
                        },
                    }
                )

        # Create server tool
        server_tools.append(
            {
                "tool_id": 1,  # Assign a default ID
                "tool_name": node_definition["name"],
                "input_schema": input_schema,
                "output_schema": output_schema,
            }
        )

    # Create the transition node
    # Determine server_script_path based on node type
    server_script_path = ""
    if (
        node_type == "mcp"
        and node.get("type") == "CustomNode"
        and "mcp_info" in node_definition
        and "sse_url" in node_definition["mcp_info"]
    ):
        # For MCP nodes: if nodes.data.type is mcp and nodes.type is CustomNode then nodes.data.definition.mcp_info.sse_url
        server_script_path = node_definition["mcp_info"]["sse_url"]
    else:
        # For Components nodes: null (empty string)
        server_script_path = ""

    # Determine the node ID based on node type
    if node_type == "mcp":
        # For MCP nodes, use the server_id from mcp_info if available
        if "mcp_info" in node_definition and "server_id" in node_definition["mcp_info"]:
            # Use the server_id from mcp_info
            node_base_id = node_definition["mcp_info"]["server_id"]
        else:
            # Fallback to the name from the node definition
            node_base_id = node_definition["name"]
    else:
        # For Components nodes, use the definition name
        node_base_id = node_definition["name"]

    transition_node = {
        "id": node_base_id,
        "server_script_path": server_script_path,
        "server_tools": server_tools,
    }

    return transition_node


def create_transition_from_edge(
    edge: Dict[str, Any],
    nodes: List[Dict[str, Any]],
    mcp_configs: List[Dict[str, Any]],
    edges: List[Dict[str, Any]],
    sequence: int,
    is_first: bool = False,
    has_reflection: bool = False,
    is_end: bool = False,
) -> Dict[str, Any]:
    """
    Create a transition from an edge in workflow_capture_schema.

    Args:
        edge: An edge from workflow_capture_schema
        nodes: List of nodes from workflow_capture_schema
        sequence: Sequence number for this transition
        is_first: Whether this is the first transition
        has_reflection: Whether this transition has reflection
        is_end: Whether this is an end transition

    Returns:
        A transition in transition_schema format
    """
    source_id = edge["source"]
    target_id = edge["target"]
    # Note: sourceHandle and targetHandle are used in other parts of the code when processing edges

    # Find the source and target nodes
    source_node = next((node for node in nodes if node["id"] == source_id), None)
    target_node = next((node for node in nodes if node["id"] == target_id), None)

    if not source_node or not target_node:
        raise ValueError(f"Could not find nodes for edge: {edge}")

    # Determine transition type
    transition_type = "standard"
    if is_first:
        transition_type = "initial"
    elif has_reflection:
        transition_type = "reflection"

    # Determine execution type based on node type
    source_node_type = source_node["data"]["type"]
    # execution_type is based directly on node.data.type as per updated transition_schema_fields.md
    execution_type = "MCP" if source_node_type == "mcp" else "Components"

    # Create tools_to_use configuration
    tools_to_use = []
    source_definition = source_node["data"]["definition"]
    source_node_type = source_node["data"]["type"]

    # Find MCP config for this node if it exists
    mcp_config = next((config for config in mcp_configs if config["node_id"] == source_id), None)

    # Determine if this is an MCP node based on node.data.type
    is_mcp_node = source_node_type == "mcp" or (mcp_config is not None)

    if is_mcp_node and mcp_config:
        # Add a tool based on the MCP config
        # Get tool_id from mcp_info if available, otherwise use 1
        tool_id = 1
        if "mcp_info" in source_definition and "tool_id" in source_definition["mcp_info"]:
            tool_id = source_definition["mcp_info"]["tool_id"]

        # According to transition_schema_fields.md:
        # tool_name: nodes.data.definition.mcp_info.tool_name if execution type is MCP Server, otherwise nodes.data.definition.name
        if (
            execution_type == "MCP"
            and "mcp_info" in source_definition
            and "tool_name" in source_definition["mcp_info"]
        ):
            # For MCP nodes, use tool_name from mcp_info
            tool_name = source_definition["mcp_info"]["tool_name"]
        else:
            # For other nodes, use the node definition name
            tool_name = source_definition["name"]

        tool = {
            "tool_id": tool_id,
            "tool_name": tool_name,
            "tool_params": {"items": []},
        }

        # Check if mcp_info.tool_params is available
        if "mcp_info" in source_definition and "tool_params" in source_definition["mcp_info"]:
            # Use tool_params from mcp_info
            tool["tool_params"] = source_definition["mcp_info"]["tool_params"]
        else:
            # Add parameters from tool_args
            tool_args = mcp_config.get("tool_args", {})
            tool_schema = mcp_config.get("tool_schema", {})
            properties = tool_schema.get("properties", {})

            for param_name, param_value in tool_args.items():
                # Get the data type from the tool schema
                field_props = properties.get(param_name, {})
                data_type = field_props.get("type", "string")

                # Handle nested objects in field_value
                if isinstance(param_value, dict):
                    # For nested objects like keywords, use 'object' as the data_type string
                    # The schema requires data_type to be a string, not an object
                    tool["tool_params"]["items"].append(
                        {
                            "field_name": param_name,
                            "data_type": "object",
                            "field_value": param_value,
                        }
                    )
                else:
                    # For simple values, use the data type from the schema
                    tool["tool_params"]["items"].append(
                        {
                            "field_name": param_name,
                            "data_type": data_type,
                            "field_value": param_value,
                        }
                    )
    else:
        # Add a tool based on the node type
        # For non-MCP nodes, tool_id is always 1
        tool_id = 1

        # For non-MCP nodes, use node_definition.name as per updated transition_schema_fields.md
        tool_name = source_definition["name"]

        # If this is an MCP node, check if mcp_info.tool_name is available
        if "mcp_info" in source_definition and "tool_name" in source_definition["mcp_info"]:
            tool_name = source_definition["mcp_info"]["tool_name"]

        tool = {
            "tool_id": tool_id,
            "tool_name": tool_name,
            "tool_params": {"items": []},
        }

        # For non-MCP nodes, use inputs from the node definition
        if "inputs" in source_definition:
            # Create tool_params from inputs
            inputs = source_definition.get("inputs", [])
            source_config = source_node["data"].get("config", {})

            for input_def in inputs:
                # Get field_name from input definition
                field_name = input_def.get("name", "")

                # Get field_value from source_config for matching field value, if not present use null
                field_value = source_config.get(field_name, None)

                # Get data_type from input_type
                data_type = map_input_type_to_data_type(input_def.get("input_type", "string"))

                # Handle nested objects in field_value
                if isinstance(field_value, dict):
                    # For nested objects like keywords, use 'object' as the data_type string
                    # The schema requires data_type to be a string, not an object
                    tool["tool_params"]["items"].append(
                        {
                            "field_name": field_name,
                            "data_type": "object",
                            "field_value": field_value,
                        }
                    )
                else:
                    # For simple values, use the data type from the schema
                    tool["tool_params"]["items"].append(
                        {
                            "field_name": field_name,
                            "data_type": data_type,
                            "field_value": field_value,
                        }
                    )

    tools_to_use.append(tool)

    # Create input_data - connections coming into this node
    input_data = []
    # Find all edges that target this node
    for node in nodes:
        node_id = node["id"]
        if node_id != source_id:  # Don't include self-connections
            # Check if there's an edge from this node to the source node
            for out_edge in edges:
                if out_edge["source"] == node_id and out_edge["target"] == source_id:
                    # Found an incoming edge
                    data_type = "string"  # Default
                    # Try to determine data type from the source handle
                    source_handle_name = out_edge.get("sourceHandle")
                    if source_handle_name:
                        # Find the output definition in the source node
                        source_node_def = node["data"]["definition"]
                        output_def = next(
                            (
                                out
                                for out in source_node_def.get("outputs", [])
                                if out["name"] == source_handle_name.split("_")[0]
                            ),
                            None,
                        )
                        if output_def:
                            data_type = map_output_type_to_data_type(
                                output_def.get("output_type", "string")
                            )

                    source_node_def = node["data"]["definition"]
                    source_server_id = node_id
                    if "mcp_info" in source_node_def and "server_id" in source_node_def["mcp_info"]:
                        source_server_id = source_node_def["mcp_info"]["server_id"]
                    else:
                        source_server_id = source_node_def.get("display_name", node_id)

                    # Get target node definition (the current node)
                    target_node_def = target_node["data"]["definition"]

                    # Get target node ID (the current node's server ID)
                    target_server_id = ""
                    if "mcp_info" in target_node_def and "server_id" in target_node_def["mcp_info"]:
                        target_server_id = target_node_def["mcp_info"]["server_id"]
                    else:
                        target_server_id = target_node_def.get("display_name", target_id)

                    # Create input_data entry with enhanced mapping support
                    input_data_entry = {
                        "from_transition_id": f"transition-{node_id}",
                        "source_node_id": source_server_id,
                        "data_type": data_type,
                    }

                    # Store sourceHandle and targetHandle for later use in setting tool_params
                    source_handle = out_edge.get("sourceHandle")
                    target_handle = out_edge.get("targetHandle")
                    if source_handle and target_handle:
                        # Find the target node definition for enhanced mapping
                        target_node_def = None
                        if target_node and "data" in target_node and "definition" in target_node["data"]:
                            target_node_def = target_node["data"]["definition"]

                        # Create enhanced explicit mapping for better parameter resolution
                        mapping_entry = create_enhanced_field_mapping(
                            source_handle, target_handle, source_node_def, target_node_def, out_edge.get("id", "")
                        )
                        input_data_entry["mapping"] = [mapping_entry]

                        # Store for backward compatibility with existing field mapping logic
                        input_data_entry["_source_handle"] = source_handle
                        input_data_entry["_target_handle"] = target_handle

                        # Check if this is a connection from the start node
                        if (
                            "data" in node
                            and "definition" in node["data"]
                            and "originalType" in node["data"]
                            and node["data"]["originalType"] == "StartNode"
                        ):
                            input_data_entry["_from_start_node"] = True

                    input_data.append(input_data_entry)

    output_data = []
    for out_edge in edges:
        if out_edge["source"] == source_id:
            target_node_id = out_edge["target"]
            data_type = "string"  # Default

            # Try to determine data type from the source handle
            source_handle_name = out_edge.get("sourceHandle")
            if source_handle_name:
                # Find the output definition in the source node
                output_def = next(
                    (
                        out
                        for out in source_definition.get("outputs", [])
                        if out["name"] == source_handle_name.split("_")[0]
                    ),
                    None,
                )
                if output_def:
                    data_type = map_output_type_to_data_type(
                        output_def.get("output_type", "string")
                    )

            # Find the target node to get its server_id from mcp_info
            target_node = next((n for n in nodes if n["id"] == target_node_id), None)
            target_server_id = target_node_id  # Default to ID if node not found

            if target_node and "data" in target_node and "definition" in target_node["data"]:
                target_node_def = target_node["data"]["definition"]
                if "mcp_info" in target_node_def and "server_id" in target_node_def["mcp_info"]:
                    target_server_id = target_node_def["mcp_info"]["server_id"]
                else:
                    target_server_id = target_node_def.get("display_name", target_node_id)

            # Create output_data entry with enhanced metadata
            output_data_entry = {
                "to_transition_id": f"transition-{target_node_id}",
                "target_node_id": target_server_id,  # Using server_id from mcp_info as per updated schema
                "data_type": data_type,
            }

            # Store sourceHandle and targetHandle for later use in setting tool_params
            source_handle = out_edge.get("sourceHandle")
            target_handle = out_edge.get("targetHandle")
            edge_id = out_edge.get("id", "")

            if source_handle and target_handle:
                # Add enhanced output metadata
                expected_output_fields = []
                if source_handle in source_definition.get("outputs", []):
                    expected_output_fields = [source_handle]
                elif source_definition.get("outputs"):
                    expected_output_fields = [output.get("name", "") for output in source_definition["outputs"]]

                output_data_entry["output_metadata"] = {
                    "source_handle": source_handle,
                    "target_handle": target_handle,
                    "edge_id": edge_id,
                    "expected_output_fields": expected_output_fields,
                    "field_mappings": {
                        source_handle: source_handle  # Basic mapping, can be enhanced
                    }
                }

                # Store for backward compatibility
                output_data_entry["_source_handle"] = source_handle
                output_data_entry["_target_handle"] = target_handle

            output_data.append(output_data_entry)

    # Create the transition
    # Get the node_id based on node type
    source_definition = source_node["data"]["definition"]
    source_node_type = source_node["data"]["type"]
    server_id = source_id

    if source_node_type == "mcp":
        # For MCP nodes, use the server_id from mcp_info if available
        if "mcp_info" in source_definition and "server_id" in source_definition["mcp_info"]:
            server_id = source_definition["mcp_info"]["server_id"]
        else:
            # Fallback to the name from the node definition
            server_id = source_definition["name"]
    else:
        # For Components nodes, use the definition name
        server_id = source_definition["name"]

    # Check if requires_approval is present in the node definition
    # For MCP nodes, check if requires_approval is in the definition
    requires_approval = False
    if "requires_approval" in source_definition:
        requires_approval = source_definition.get("requires_approval", False)

    transition = {
        "id": f"transition-{source_id}",
        "sequence": sequence,
        "transition_type": transition_type,
        "execution_type": execution_type,
        "node_info": {
            "node_id": server_id,  # Using server_id from mcp_info as per updated transition_schema_fields.md
            "tools_to_use": tools_to_use,
            "input_data": input_data,
            "output_data": output_data,
        },
        "approval_required": requires_approval,  # Add approval_required field from node definition
        "end": is_end,
    }

    # Add reflection if needed
    if has_reflection:
        transition["reflection"] = {"iteration_count": 0, "max_iterations": 3}

    return transition


def is_conditional_node(node: Dict[str, Any]) -> bool:
    """
    Check if a node is a conditional node.

    Args:
        node: A node from workflow_capture_schema

    Returns:
        True if the node is a conditional node, False otherwise
    """
    if "data" in node and "type" in node["data"]:
        node_type = node["data"]["type"]
        return "Conditional" in node_type or node_type == "ConditionalNode"
    return False


def is_output_node(node: Dict[str, Any]) -> bool:
    """
    Check if a node is an output node.

    Args:
        node: A node from workflow_capture_schema

    Returns:
        True if the node is an output node, False otherwise
    """
    if "data" in node and "type" in node["data"]:
        node_type = node["data"]["type"]
        return "Output" in node_type or node_type == "OutputNode"
    return False


def fix_output_node_input_data(
    transition: Dict[str, Any],
    conditional_node_id: str,
    previous_node_id: str,
    previous_node_server_id: str,
) -> None:
    """
    Fix the input_data of an output node transition to use the previous node as the source instead of the conditional node.

    Args:
        transition: The output node transition to fix
        conditional_node_id: The ID of the conditional node
        previous_node_id: The ID of the previous node (that has the conditional routing)
        previous_node_server_id: The server_id from mcp_info of the previous node
    """
    # Update input_data to use the previous node as the source
    for input_data in transition["node_info"]["input_data"]:
        if input_data["from_transition_id"] == f"transition-{conditional_node_id}":
            input_data["from_transition_id"] = f"transition-{previous_node_id}"
            input_data["source_node_id"] = previous_node_server_id


def create_conditional_routing(node: Dict[str, Any], edges: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Create a conditional routing object for a ConditionalNode (Switch-Case Router).

    Args:
        node: A conditional node from workflow_capture_schema
        edges: List of edges from workflow_capture_schema

    Returns:
        A conditional routing object for the transition schema compatible with orchestration engine
    """
    node_id = node["id"]
    node_data = node.get("data", {})
    node_definition = node_data.get("definition", {})
    
    # Get ConditionalNode configuration from the node
    num_conditions = node_definition.get("num_conditions", 2)
    num_conditions = max(1, min(10, int(num_conditions)))  # Clamp to 1-10
    
    # Create cases array based on actual ConditionalNode configuration
    cases = []
    
    # Find all outgoing edges from this conditional node
    outgoing_edges = [edge for edge in edges if edge["source"] == node_id]
    
    # Create a mapping of output handles to target transitions
    output_to_target = {}
    for edge in outgoing_edges:
        source_handle = edge.get("sourceHandle", "")
        target_id = edge["target"]
        
        # Map condition outputs to targets
        if source_handle.startswith("condition_") and source_handle.endswith("_output"):
            condition_num = source_handle.replace("condition_", "").replace("_output", "")
            try:
                condition_num = int(condition_num)
                output_to_target[f"condition_{condition_num}"] = target_id
            except ValueError:
                continue
        elif source_handle == "default_output":
            output_to_target["default"] = target_id
    
    # Build cases for each configured condition
    for condition_num in range(1, num_conditions + 1):
        condition_key = f"condition_{condition_num}"
        
        # Skip if no target node for this condition
        if condition_key not in output_to_target:
            continue
            
        target_id = output_to_target[condition_key]
        
        # Extract condition configuration from node definition
        source = node_definition.get(f"condition_{condition_num}_source", "node_output")
        operator = node_definition.get(f"condition_{condition_num}_operator", "equals")
        expected_value = node_definition.get(f"condition_{condition_num}_expected_value", "")
        variable_name = node_definition.get(f"condition_{condition_num}_variable", "")
        
        # Create condition object compatible with orchestration engine
        condition = {
            "source": source,
            "operator": operator,
            "expected_value": expected_value
        }
        
        # Add variable_name only for global_context source
        if source == "global_context" and variable_name:
            condition["variable_name"] = variable_name
        
        # Create the case
        case = {
            "condition": condition,
            "next_transition": f"transition-{target_id}"
        }
        
        cases.append(case)
    
    # Collect global context variables used in conditions
    global_variables = []
    for condition_num in range(1, num_conditions + 1):
        source = node_definition.get(f"condition_{condition_num}_source", "node_output")
        if source == "global_context":
            variable_name = node_definition.get(f"condition_{condition_num}_variable", "")
            if variable_name and not any(var["name"] == variable_name for var in global_variables):
                # Add global context variable definition
                global_variables.append({
                    "name": variable_name,
                    "data_type": "string",  # Default to string, can be inferred from usage
                    "value": None  # Will be populated by orchestration engine from StartNode collected_parameters
                })
    
    # Create the conditional routing object
    conditional_routing = {
        "cases": cases
    }
    
    # Add global_context_definitions if any global variables are used
    if global_variables:
        conditional_routing["global_context_definitions"] = {
            "variables": global_variables
        }
    
    # Add default transition if configured
    if "default" in output_to_target:
        conditional_routing["default_transition"] = f"transition-{output_to_target['default']}"
    
    return conditional_routing


def convert_workflow_to_transition_schema(workflow_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert workflow data from workflow_capture_schema format to transition_schema format.

    Args:
        workflow_data: The workflow data in workflow_capture_schema format

    Returns:
        The workflow data in transition_schema format
    """
    # Extract components
    nodes = workflow_data.get("nodes", [])
    edges = workflow_data.get("edges", [])
    mcp_configs = workflow_data.get("mcp_configs", [])

    # Identify the start node
    start_node_id = None
    for node in nodes:
        if (
            "data" in node
            and "definition" in node["data"]
            and "originalType" in node["data"]
            and node["data"]["originalType"] == "StartNode"
        ):
            start_node_id = node["id"]
            break

    # Find nodes connected to the start node - these will be marked as initial
    nodes_connected_to_start = []
    if start_node_id:
        for edge in edges:
            if edge["source"] == start_node_id:
                nodes_connected_to_start.append(edge["target"])

    # Build graph for analysis
    graph, edge_map, all_nodes = build_graph_from_workflow(workflow_data)

    # Remove the start node from the graph and all_nodes if it exists
    if start_node_id:
        if start_node_id in graph:
            del graph[start_node_id]
        all_nodes.discard(start_node_id)

    # Compute node levels
    levels = compute_levels(graph, all_nodes, nodes_connected_to_start)

    # Group nodes by level
    level_groups = group_nodes_by_level(levels)

    # Convert nodes to transition schema format
    transition_nodes = []

    for node in nodes:
        # Skip the start node
        if (
            "data" in node
            and "definition" in node["data"]
            and "originalType" in node["data"]
            and node["data"]["originalType"] == "StartNode"
        ):
            continue

        transition_node = convert_node_to_transition_node(node, mcp_configs)

        # Fix tool_name for MCP nodes in the nodes array
        if "data" in node and "type" in node["data"] and node["data"]["type"] == "mcp":
            if (
                "definition" in node["data"]
                and "mcp_info" in node["data"]["definition"]
                and "tool_name" in node["data"]["definition"]["mcp_info"]
            ):
                # Get the tool_name from mcp_info
                mcp_tool_name = node["data"]["definition"]["mcp_info"]["tool_name"]
                # Set the tool_name in the server_tools array
                if "server_tools" in transition_node and len(transition_node["server_tools"]) > 0:
                    transition_node["server_tools"][0]["tool_name"] = mcp_tool_name
                    # Uncomment for debugging
                    # print(f"Fixed tool_name for node {node['id']} to {mcp_tool_name}")

        transition_nodes.append(transition_node)

    # Combine nodes with the same ID
    transition_nodes = combine_nodes(transition_nodes)

    # Create transitions from edges
    transitions = []
    sequence = 1
    is_first_transition = True
    processed_nodes = set()  # Track nodes we've already processed

    # If we found a start node, add it to processed_nodes to skip it
    if start_node_id:
        processed_nodes.add(start_node_id)

    # Process nodes by level
    for level in sorted(level_groups.keys()):
        nodes_at_level = level_groups[level]
        for node_id in nodes_at_level:
            # Skip if we've already processed this node
            if node_id in processed_nodes:
                continue

            # Find the node
            node = next((n for n in nodes if n["id"] == node_id), None)
            if not node:
                continue

            # Mark this node as processed
            processed_nodes.add(node_id)

            # Check for reflection (if any neighbor is at a lower level)
            has_reflection = any(
                levels.get(neighbor, 0) < levels.get(node_id, 0)
                for neighbor in graph.get(node_id, [])
            )

            # Check if this is a conditional node
            is_conditional = is_conditional_node(node)

            # Check if this is an output node
            is_output = is_output_node(node)

            # Check if this is an end node (no outgoing edges)
            is_end = len(graph.get(node_id, [])) == 0 or is_output

            # Check if this node is connected to the start node
            is_connected_to_start = node_id in nodes_connected_to_start

            # If this is an output node, create a transition for it
            if is_output:

                # Create a dummy edge for the output node
                dummy_edge = {
                    "id": f"dummy-edge-{node_id}",
                    "source": node_id,
                    "target": node_id,  # Self-reference for output nodes
                }
                transition = create_transition_from_edge(
                    dummy_edge,
                    nodes,
                    mcp_configs,
                    edges,
                    sequence,
                    is_first=is_first_transition
                    or is_connected_to_start,  # Mark as initial if connected to start
                    has_reflection=has_reflection,
                    is_end=True,
                )

                # Check if this output node is connected to a conditional node
                # If so, fix the input_data to use the previous node as the source
                for edge in edges:
                    if edge["target"] == node_id and is_conditional_node(
                        next((n for n in nodes if n["id"] == edge["source"]), {})
                    ):
                        conditional_node_id = edge["source"]

                        # Find the previous node that connects to this conditional node
                        for n_id in all_nodes:
                            if conditional_node_id in graph.get(n_id, []):
                                previous_node_id = n_id
                                previous_node = next((n for n in nodes if n["id"] == n_id), None)

                                if (
                                    previous_node
                                    and "data" in previous_node
                                    and "definition" in previous_node["data"]
                                ):
                                    previous_node_def = previous_node["data"]["definition"]
                                    previous_node_type = previous_node["data"]["type"]
                                    previous_server_id = previous_node_id

                                    if previous_node_type == "mcp":
                                        # For MCP nodes, use the server_id from mcp_info if available
                                        if (
                                            "mcp_info" in previous_node_def
                                            and "server_id" in previous_node_def["mcp_info"]
                                        ):
                                            previous_server_id = previous_node_def["mcp_info"][
                                                "server_id"
                                            ]
                                        else:
                                            # Fallback to the name from the node definition
                                            previous_server_id = previous_node_def["name"]
                                    else:
                                        # For Components nodes, use the definition name
                                        previous_server_id = previous_node_def["name"]

                                    # Fix the input_data
                                    fix_output_node_input_data(
                                        transition,
                                        conditional_node_id,
                                        previous_node_id,
                                        previous_server_id,
                                    )
                                break

                transitions.append(transition)
                sequence += 1
            # If this is a conditional node, we need to handle it differently
            elif is_conditional:
                # We no longer mark target nodes as processed
                # This allows output nodes to be processed separately

                # Find the previous node that connects to this conditional node
                previous_node_id = None
                previous_node = None
                for n_id in all_nodes:
                    if node_id in graph.get(n_id, []):
                        previous_node_id = n_id
                        previous_node = next((n for n in nodes if n["id"] == n_id), None)
                        break

                if previous_node_id and previous_node:
                    # Check if we already have a transition for the previous node
                    existing_transition = next(
                        (t for t in transitions if t["id"] == f"transition-{previous_node_id}"),
                        None,
                    )

                    if existing_transition:
                        # Add conditional routing to the existing transition
                        existing_transition["conditional_routing"] = create_conditional_routing(
                            node, edges
                        )

                        # Correct the routing of the previous transition as per special instructions
                        # Remove the conditional node from output_data
                        existing_transition["node_info"]["output_data"] = [
                            output
                            for output in existing_transition["node_info"]["output_data"]
                            if not output["to_transition_id"].endswith(node_id)
                        ]

                        # Add direct connections to the target nodes of the conditional node
                        for edge in edges:
                            if (
                                edge["source"] == node_id
                            ):  # This is an outgoing edge from the conditional node
                                target_id = edge["target"]
                                target_node = next((n for n in nodes if n["id"] == target_id), None)

                                if (
                                    target_node
                                    and "data" in target_node
                                    and "definition" in target_node["data"]
                                ):
                                    target_node_def = target_node["data"]["definition"]
                                    target_node_type = target_node["data"]["type"]
                                    target_server_id = target_id

                                    if target_node_type == "mcp":
                                        # For MCP nodes, use the server_id from mcp_info if available
                                        if (
                                            "mcp_info" in target_node_def
                                            and "server_id" in target_node_def["mcp_info"]
                                        ):
                                            target_server_id = target_node_def["mcp_info"][
                                                "server_id"
                                            ]
                                        else:
                                            # Fallback to the name from the node definition
                                            target_server_id = target_node_def["name"]
                                    else:
                                        # For Components nodes, use the definition name
                                        target_server_id = target_node_def["name"]

                                    # Add a direct connection to the target node
                                    existing_transition["node_info"]["output_data"].append(
                                        {
                                            "to_transition_id": f"transition-{target_id}",
                                            "target_node_id": target_server_id,
                                            "data_type": "string",
                                        }
                                    )
                    else:
                        # Create a transition for the previous node with conditional routing
                        dummy_edge = {
                            "id": f"edge-{previous_node_id}-{node_id}",
                            "source": previous_node_id,
                            "target": node_id,
                        }

                        # Create the transition
                        transition = create_transition_from_edge(
                            dummy_edge,
                            nodes,
                            mcp_configs,
                            edges,
                            sequence,
                            is_first=is_first_transition
                            or is_connected_to_start,  # Mark as initial if connected to start
                            has_reflection=has_reflection,
                            is_end=False,
                        )

                        # Add conditional routing to the transition
                        transition["conditional_routing"] = create_conditional_routing(node, edges)

                        # Correct the routing of the transition as per special instructions
                        # Remove the conditional node from output_data
                        transition["node_info"]["output_data"] = [
                            output
                            for output in transition["node_info"]["output_data"]
                            if not output["to_transition_id"].endswith(node_id)
                        ]

                        # Add direct connections to the target nodes of the conditional node
                        for edge in edges:
                            if (
                                edge["source"] == node_id
                            ):  # This is an outgoing edge from the conditional node
                                target_id = edge["target"]
                                target_node = next((n for n in nodes if n["id"] == target_id), None)

                                if (
                                    target_node
                                    and "data" in target_node
                                    and "definition" in target_node["data"]
                                ):
                                    target_node_def = target_node["data"]["definition"]
                                    target_node_type = target_node["data"]["type"]
                                    target_server_id = target_id

                                    if target_node_type == "mcp":
                                        # For MCP nodes, use the server_id from mcp_info if available
                                        if (
                                            "mcp_info" in target_node_def
                                            and "server_id" in target_node_def["mcp_info"]
                                        ):
                                            target_server_id = target_node_def["mcp_info"][
                                                "server_id"
                                            ]
                                        else:
                                            # Fallback to the name from the node definition
                                            target_server_id = target_node_def["name"]
                                    else:
                                        # For Components nodes, use the definition name
                                        target_server_id = target_node_def["name"]

                                    # Add a direct connection to the target node
                                    transition["node_info"]["output_data"].append(
                                        {
                                            "to_transition_id": f"transition-{target_id}",
                                            "target_node_id": target_server_id,
                                            "data_type": "string",
                                        }
                                    )

                        transitions.append(transition)
                        sequence += 1

                    # Mark only the previous node as processed, not the target nodes
                    processed_nodes.add(previous_node_id)
            # Create a transition for this node
            # If there are no outgoing edges (and it's not an output node), create a dummy edge
            elif is_end and not is_output:
                dummy_edge = {
                    "id": f"dummy-edge-{node_id}",
                    "source": node_id,
                    "target": node_id,  # Self-reference for end nodes
                }
                transition = create_transition_from_edge(
                    dummy_edge,
                    nodes,
                    mcp_configs,
                    edges,
                    sequence,
                    is_first=is_first_transition
                    or is_connected_to_start,  # Mark as initial if connected to start
                    has_reflection=has_reflection,
                    is_end=True,
                )
                transitions.append(transition)
                sequence += 1
            else:
                # Get all outgoing edges for this node
                outgoing_edges = []
                for target_id in graph.get(node_id, []):
                    edge = edge_map.get((node_id, target_id))
                    if edge:
                        outgoing_edges.append(edge)

                # Create a single transition with all outgoing connections
                if outgoing_edges:
                    # Use the first edge for the transition creation
                    transition = create_transition_from_edge(
                        outgoing_edges[0],
                        nodes,
                        mcp_configs,
                        edges,  # Pass all edges for input/output data mapping
                        sequence,
                        is_first=is_first_transition
                        or is_connected_to_start,  # Mark as initial if connected to start
                        has_reflection=has_reflection,
                        is_end=False,
                    )
                    transitions.append(transition)
                    sequence += 1

            # Only the first node processed is the first transition
            if is_first_transition:
                is_first_transition = False

    # Process field mappings to set field_value in tool_params
    # For each transition, look at its input_data to find source_handle and target_handle
    for transition in transitions:
        for input_data in transition["node_info"]["input_data"]:
            # Check if we have source_handle and target_handle information
            if "_source_handle" in input_data and "_target_handle" in input_data:
                source_handle = input_data["_source_handle"]
                target_handle = input_data["_target_handle"]

                # Check if this is a connection from the start node
                is_from_start_node = False
                if "_from_start_node" in input_data:
                    is_from_start_node = input_data["_from_start_node"]

                # Set the field_value in the tool_params
                for tool in transition["node_info"]["tools_to_use"]:
                    for item in tool["tool_params"]["items"]:
                        if item["field_name"] == target_handle:
                            # If the connection is from the start node, set field_value to null
                            if is_from_start_node:
                                item["field_value"] = None
                            else:
                                item["field_value"] = f"${{{source_handle}}}"

                # Remove the temporary fields
                del input_data["_source_handle"]
                del input_data["_target_handle"]
                if "_from_start_node" in input_data:
                    del input_data["_from_start_node"]

    # Clean up any remaining temporary fields in output_data
    for transition in transitions:
        for output_data in transition["node_info"]["output_data"]:
            if "_source_handle" in output_data:
                del output_data["_source_handle"]
            if "_target_handle" in output_data:
                del output_data["_target_handle"]

    # Create the final transition schema
    transition_schema = {"nodes": transition_nodes, "transitions": transitions}

    return transition_schema
