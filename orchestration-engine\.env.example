# Application settings
APP_NAME="Orchestration Engine"
APP_ENV="Production"
APP_DEBUG="False"

# OpenAI API Keys
OPENAI_API_KEY ="YOUR_OPENAI_API_KEY"
MODEL_NAME = "MODEL_NAME" # e.g. gpt-3.5-turbo, gpt-4, etc.

#Commnication ports
KAFKA_BOOTSTRAP_SERVERS="KAFKA_BOOTSTRAP_SERVERS(with port)"

# Redis settings
REDIS_HOST="REDIS_HOST"
REDIS_PORT="REDIS_PORT"
REDIS_RESULTS_DB_INDEX=0
REDIS_STATE_DB_INDEX=1
REDIS_PASSWORD="REDIS_PASSWORD"
# TTL in seconds for Redis keys (default: 5 minutes for results, 10 minutes for state)
REDIS_RESULTS_TTL=300
REDIS_STATE_TTL=600

# PostgreSQL settings
DB_HOST="localhost"
DB_PORT=5432
DB_USER="postgres"
DB_PASSWORD="your_password"
DB_NAME="orchestration_engine"

# GCS connection details
GCS_CRED="GCS_CRED"
BUCKET_NAME="BUCKET_NAME"

# API Gateway Authentication
ORCHESTRATION_SERVER_AUTH_KEY= "ORCHESTRATION_SERVER_AUTH_KEY"
API_GATEWAY_URL="API_GATEWAY_URL"