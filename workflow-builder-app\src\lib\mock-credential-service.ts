/**
 * Mock credential service that uses localStorage for credential management.
 * This is a temporary solution until the backend credential API is implemented.
 */

import {
  Credential,
  CredentialCreate,
  CredentialListResponse,
  CredentialDeleteResponse,
} from "./api";

const STORAGE_KEY = "workflow_builder_credentials";

/**
 * Get all credentials from localStorage
 */
function getStoredCredentials(): Credential[] {
  try {
    const storedData = localStorage.getItem(STORAGE_KEY);
    if (!storedData) return [];
    return JSON.parse(storedData);
  } catch (error) {
    console.error("Error reading credentials from localStorage:", error);
    return [];
  }
}

/**
 * Save credentials to localStorage
 */
function saveCredentials(credentials: Credential[]): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(credentials));
  } catch (error) {
    console.error("Error saving credentials to localStorage:", error);
  }
}

/**
 * Fetch all credentials (mock implementation)
 */
export async function mockFetchCredentials(): Promise<CredentialListResponse> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  const credentials = getStoredCredentials();
  console.log("Mock: Fetched credentials", credentials);

  return { credentials };
}

/**
 * Create a new credential (mock implementation)
 */
export async function mockCreateCredential(credential: CredentialCreate): Promise<Credential> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Generate ID from name
  const id = credential.name.toLowerCase().replace(/\s+/g, "_");

  // Check if credential with this ID already exists
  const credentials = getStoredCredentials();
  if (credentials.some((c) => c.id === id)) {
    throw new Error(`Credential with ID '${id}' already exists`);
  }

  // Create new credential
  const newCredential: Credential = {
    id,
    name: credential.name,
    type: credential.type,
  };

  // Save to localStorage
  saveCredentials([...credentials, newCredential]);

  // Also save the value in a separate item for security (in a real app, this would be on the server)
  try {
    localStorage.setItem(`${STORAGE_KEY}_${id}_value`, credential.value);
  } catch (error) {
    console.error(`Error saving credential value for ${id}:`, error);
  }

  console.log("Mock: Created credential", newCredential);

  return newCredential;
}

/**
 * Delete a credential (mock implementation)
 */
export async function mockDeleteCredential(
  credentialId: string,
): Promise<CredentialDeleteResponse> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 400));

  // Get current credentials
  const credentials = getStoredCredentials();

  // Check if credential exists
  if (!credentials.some((c) => c.id === credentialId)) {
    throw new Error(`Credential with ID '${credentialId}' not found`);
  }

  // Filter out the credential to delete
  const updatedCredentials = credentials.filter((c) => c.id !== credentialId);

  // Save updated list
  saveCredentials(updatedCredentials);

  // Remove the value
  try {
    localStorage.removeItem(`${STORAGE_KEY}_${credentialId}_value`);
  } catch (error) {
    console.error(`Error removing credential value for ${credentialId}:`, error);
  }

  console.log("Mock: Deleted credential", credentialId);

  return {
    success: true,
    message: `Credential '${credentialId}' deleted successfully`,
  };
}

/**
 * Get a credential value (mock implementation)
 * This would be used by the WorkflowContext in a real implementation
 */
export function mockGetCredentialValue(credentialId: string): string | null {
  try {
    return localStorage.getItem(`${STORAGE_KEY}_${credentialId}_value`);
  } catch (error) {
    console.error(`Error getting credential value for ${credentialId}:`, error);
    return null;
  }
}
