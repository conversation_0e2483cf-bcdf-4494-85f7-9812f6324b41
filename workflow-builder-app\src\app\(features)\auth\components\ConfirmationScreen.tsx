"use client";

import { But<PERSON> } from "@/components/ui/button";
import { MailCheck } from "lucide-react";

export interface ConfirmationScreenProps {
  title: string;
  message: string;
  email: string;
  onBackToLogin: () => void;
}

export function ConfirmationScreen({
  title,
  message,
  email,
  onBackToLogin,
}: ConfirmationScreenProps) {
  return (
    <div className="flex flex-col items-center justify-center space-y-6 text-center">
      <div className="bg-primary/10 rounded-full p-3">
        <MailCheck className="text-primary h-6 w-6" />
      </div>

      <div className="space-y-2">
        <h2 className="text-2xl font-semibold">{title}</h2>
        <p className="text-muted-foreground">
          {message}
          <br />
          <span className="text-foreground font-medium">{email}</span>
        </p>
      </div>

      <Button
        variant="link"
        onClick={onBackToLogin}
        className="text-primary hover:text-primary/80 text-sm"
      >
        Back to Login
      </Button>
    </div>
  );
}
