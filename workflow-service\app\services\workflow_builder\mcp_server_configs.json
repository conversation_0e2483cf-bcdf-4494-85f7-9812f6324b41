{"nodes": [{"id": "generate-script", "display_name": "Script Generator", "description": "Generate video scripts based on topics and keywords", "server_script_path": "https://script-generation-mcp-624209391722.us-central1.run.app/sse", "server_tools": [{"tool_id": 1, "tool_name": "script_generate", "description": "Generate a video script based on topic and keywords", "endpoint": "/script", "input_schema": {"predefined_fields": [{"field_name": "topic", "data_type": {"type": "string", "description": "The topic of the video to be covered"}, "required": true}, {"field_name": "script_type", "data_type": {"type": "string", "description": "The type of script"}, "required": true}, {"field_name": "keywords", "data_type": {"type": "object", "description": "Keywords for the script", "properties": {"time": {"type": "string", "description": "Time for the script"}, "objective": {"type": "string", "description": "Objective of the script"}, "audience": {"type": "string", "description": "Audience for the script"}, "gender": {"type": "string", "description": "Gender for the script"}, "tone": {"type": "string", "description": "Tone of the script"}, "speakers": {"type": "string", "description": "Speaker in the script"}}}, "required": true}, {"field_name": "video_type", "data_type": {"type": "string", "description": "The type of video"}, "required": true}, {"field_name": "link", "data_type": {"type": "string", "format": "uri", "description": "Optional link for the script"}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "title", "data_type": {"type": "string", "description": "Title of the generated script"}}, {"field_name": "script", "data_type": {"type": "string", "description": "The generated script"}}, {"field_name": "script_type", "data_type": {"type": "string", "description": "Type of the script"}}, {"field_name": "video_type", "data_type": {"type": "string", "description": "The type of video"}}, {"field_name": "link", "data_type": {"type": "string", "format": "uri", "description": "Optional link for the script"}}]}}]}, {"id": "candidate-interview", "display_name": "Candidate Interview", "description": "Analyze candidate suitability based on job description and resume", "server_script_path": "https://mcp.interview.rapidinnovation.dev/sse", "server_tools": [{"tool_id": 1, "tool_name": "candidate_suitability_pre", "description": "Analyze candidate suitability based on job description and resume", "endpoint": "/interview", "input_schema": {"predefined_fields": [{"field_name": "resume_s3_link", "data_type": {"type": "string", "description": "Candidate's resume"}, "required": true}, {"field_name": "job_description_s3_link", "data_type": {"type": "string", "description": "Job description"}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "suitability_analysis", "data_type": {"type": "string", "description": "Analysis of candidate's suitability for the job"}}, {"field_name": "resume_details", "data_type": {"type": "string", "description": "Candidate's resume"}}, {"field_name": "jd_details", "data_type": {"type": "string", "description": "Interview job description"}}]}}, {"tool_id": 2, "tool_name": "generate_interview_agenda", "description": "Generate interview agenda based on job description and resume", "endpoint": "/interview", "input_schema": {"predefined_fields": [{"field_name": "resume_details", "data_type": {"type": "string", "description": "Candidate's resume"}, "required": true}, {"field_name": "jd_details", "data_type": {"type": "string", "description": "Job description"}, "required": true}, {"field_name": "prompt", "data_type": {"type": "string", "description": "Optional custom prompt to guide the agenda generation"}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "interview_agenda", "data_type": {"type": "string", "description": "Generated interview agenda"}}, {"field_name": "resume_details", "data_type": {"type": "string", "description": "Candidate's resume"}}, {"field_name": "jd_details", "data_type": {"type": "string", "description": "Interview job description"}}]}}, {"tool_id": 3, "tool_name": "generate_questions", "description": "Generate interview questions based on job description and resume for each agenda", "endpoint": "/interview", "input_schema": {"predefined_fields": [{"field_name": "resume_details", "data_type": {"type": "string", "description": "Candidate's resume"}, "required": true}, {"field_name": "jd_details", "data_type": {"type": "string", "description": "Job description"}, "required": true}, {"field_name": "agenda", "data_type": {"type": "string", "description": "Agenda in string format to generate questions from"}, "required": true}, {"field_name": "question_count", "data_type": {"type": "integer", "description": "How many questions to generate for each agenda (max 5)"}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "interview_questions", "data_type": {"type": "string", "description": "Generated interview questions"}}, {"field_name": "resume_details", "data_type": {"type": "string", "description": "Candidate's resume"}}, {"field_name": "jd_details", "data_type": {"type": "string", "description": "Interview job description"}}]}}]}, {"id": "generate-audio", "display_name": "Audio Generator", "description": "Generate and fetch audio from text scripts", "server_script_path": "http://localhost:5002/sse", "server_tools": [{"tool_id": 2, "tool_name": "generate_audio", "description": "Generate audio from a script using text-to-speech", "endpoint": "/audio", "input_schema": {"predefined_fields": [{"field_name": "script", "data_type": {"type": "string", "description": "the script to generate voice"}, "required": true}, {"field_name": "voice_id", "data_type": {"type": "string", "description": "the play ht voice id to generate voice"}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "audio_ids", "data_type": {"type": "array", "description": "List of generated audio IDs", "items": {"type": "string"}}}, {"field_name": "voice_id", "data_type": {"type": "string", "description": "Identifier for the voice used in audio generation"}}, {"field_name": "audio_script", "data_type": {"type": "string", "description": "<PERSON><PERSON><PERSON> used to generate the audio"}}]}}, {"tool_id": 3, "tool_name": "fetch_audio", "description": "Fetch generated audio files by their IDs", "endpoint": "/audio", "input_schema": {"predefined_fields": [{"field_name": "audio_ids", "data_type": {"type": "array", "items": {"type": "string"}, "description": "An array of audio IDs representing the script to generate voice"}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "audio_urls", "data_type": {"type": "array", "items": {"type": "string"}, "description": "generated audio links"}}, {"field_name": "mimetype", "data_type": {"type": "string", "description": "generated audio file mimetype"}}]}}]}, {"id": "generate-subtitle", "display_name": "Subtitle Generator", "description": "Generate subtitles from scripts and audio", "server_script_path": "http://localhost:5003/sse", "server_tools": [{"tool_id": 4, "tool_name": "generate_subtitle", "description": "Generate subtitles from script and audio files", "endpoint": "/content", "input_schema": {"predefined_fields": [{"field_name": "script", "data_type": {"type": "string", "description": "the script to generate subtitle"}, "required": true}, {"field_name": "audio_urls", "data_type": {"type": "array", "items": {"type": "string"}, "description": "An array of audio urls representing the script to generate voice"}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "subtitle", "data_type": {"type": "string", "description": "generated subtitle of the audio"}}]}}]}, {"id": "generate-stock-video", "display_name": "Stock Video Generator", "description": "Generate stock video clips based on script content", "server_script_path": "http://localhost:5004/sse", "server_tools": [{"tool_id": 5, "tool_name": "generate_stock_video", "description": "Generate stock video clips based on script content", "endpoint": "/stock-video", "input_schema": {"predefined_fields": [{"field_name": "script", "data_type": {"type": "string", "description": "the subtitle script of the video"}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "stock_video_clips", "data_type": {"type": "array", "description": "List of stock video clips", "items": {"type": "object", "properties": {"at_time": {"type": "number", "description": "Time at which the video clip starts"}, "url": {"type": "string", "description": "URL of the stock video clip"}, "search_terms": {"type": "array", "items": {"type": "string"}, "description": "list of search terms"}, "mimetype": {"type": "string", "description": "mimetype of the stock video clip"}}}}}]}}]}, {"id": "generate-stock-image", "display_name": "Stock Image Generator", "description": "Generate AI stock images based on script content", "server_script_path": "http://localhost:5005/sse", "server_tools": [{"tool_id": 6, "tool_name": "generate_ai_stock_image", "description": "Generate AI-created stock images based on script content", "endpoint": "/stock-image", "input_schema": {"predefined_fields": [{"field_name": "script", "data_type": {"type": "string", "description": "the subtitle script of the video"}, "required": true}, {"field_name": "view_type", "data_type": {"type": "string", "description": "The view type for the video (enumerated value)"}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "stock_image_clips", "data_type": {"type": "array", "description": "List of stock video clips", "items": {"type": "object", "properties": {"at_time": {"type": "number", "description": "Time at which the image clip starts"}, "url": {"type": "string", "description": "URL of the image"}, "prompt": {"type": "string", "description": "prompt for the image"}, "mimetype": {"type": "string", "description": "mimetype of the image clip"}}}}}]}}]}, {"id": "generate-video", "display_name": "Video Generator", "description": "Generate complete videos from stock clips, images, and audio", "server_script_path": "https://video-generation-mcp-624209391722.us-central1.run.app/sse", "server_tools": [{"tool_id": 7, "tool_name": "generate_video", "description": "Generate a complete video from stock clips, images, audio, and subtitles", "endpoint": "/generate-video", "input_schema": {"predefined_fields": [{"field_name": "view_type", "data_type": {"type": "string", "description": "The view type for the video (enumerated value)"}, "required": true}, {"field_name": "stock_video_clips", "data_type": {"type": "array", "description": "List of stock video clips", "items": {"type": "object", "properties": {"at_time": {"type": "number", "minimum": 0, "description": "Time at which the video clip starts"}, "url": {"type": "string", "format": "uri", "description": "URL of the stock video clip"}}, "required": ["at_time", "url"]}}, "required": true}, {"field_name": "stock_image_clips", "data_type": {"type": "array", "description": "List of stock image clips", "items": {"type": "object", "properties": {"at_time": {"type": "number", "minimum": 0, "description": "Time at which the image clip starts"}, "url": {"type": "string", "format": "uri", "description": "URL of the stock image clip"}}, "required": ["at_time", "url"]}}, "required": true}, {"field_name": "event_stock_clips", "data_type": {"type": "array", "description": "List of event stock clips", "items": {"type": "object", "properties": {"clip": {"type": "number", "minimum": 0, "description": "Index of the event stock clip"}, "at_time": {"type": "number", "minimum": 0, "description": "Time at which the event clip starts"}, "duration": {"type": "number", "exclusiveMinimum": 0, "description": "Duration of the event clip"}}, "required": ["clip", "at_time", "duration"]}}, "required": true}, {"field_name": "audio_urls", "data_type": {"type": "array", "description": "List of audio URLs (must contain at least one URL)", "items": {"type": "string", "format": "uri"}}, "required": true}, {"field_name": "avatar_video_ids", "data_type": {"type": "array", "description": "Optional list of avatar video IDs (alphanumeric values)", "items": {"type": "string"}}, "required": false}, {"field_name": "subtitles", "data_type": {"type": "string", "minLength": 1, "description": "Subtitle script of the video"}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "thumbnail", "data_type": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the thumbnail"}, "mimetype": {"type": "string", "description": "MIME type of the thumbnail"}}}}, {"field_name": "video_link", "data_type": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the video"}, "mimetype": {"type": "string", "description": "MIME type of the video"}}}}, {"field_name": "duration", "data_type": {"type": "number", "description": "Duration of the video"}}]}}]}], "transitions": [{"id": "generate_script_transition", "sequence": 1, "transition_type": "initial", "node_info": {"node_id": "generate-script", "tools_to_use": [{"tool_id": 1, "tool_name": "script_generate", "tool_params": {"items": [{"field_name": "topic", "data_type": "string", "field_value": null}, {"field_name": "video_type", "data_type": "string", "field_value": null}, {"field_name": "script_type", "data_type": "string", "field_value": "TOPIC"}, {"field_name": "keywords", "data_type": "object", "field_value": {"time": "30seconds", "objective": "educational", "audience": "18 to 50 ages", "gender": "neutral", "tone": "Technical", "speakers": "influencer"}}]}}], "input_data": [], "output_data": [{"to_transition_id": "generate_audio_transition", "target_node_id": "generate-audio", "data_type": "object"}, {"to_transition_id": "generate_subtitle_transition", "target_node_id": "generate-subtitle", "data_type": "object"}]}, "approval_required": true, "end": false}, {"id": "generate_audio_transition", "sequence": 2, "transition_type": "standard", "node_info": {"node_id": "generate-audio", "tools_to_use": [{"tool_id": 2, "tool_name": "generate_audio", "tool_params": {"items": [{"field_name": "script", "data_type": "string", "field_value": "${script}"}, {"field_name": "voice_id", "data_type": "string", "field_value": "s3://voice-cloning-zero-shot/b41d1a8c-2c99-4403-8262-5808bc67c3e0/bentonsaad/manifest.json"}]}}], "input_data": [{"from_transition_id": "generate_script_transition", "target_node_id": "generate-script", "data_type": "object"}], "output_data": [{"to_transition_id": "fetch_audio_transition", "target_node_id": "generate-audio", "data_type": "object"}]}, "approval_required": false, "end": false}, {"id": "fetch_audio_transition", "sequence": 3, "transition_type": "standard", "node_info": {"node_id": "generate-audio", "tools_to_use": [{"tool_id": 3, "tool_name": "fetch_audio", "tool_params": {"items": [{"field_name": "audio_ids", "data_type": "array", "field_value": "${audio_ids}"}]}}], "input_data": [{"from_transition_id": "generate_audio_transition", "target_node_id": "generate-audio", "data_type": "object"}], "output_data": [{"to_transition_id": "generate_subtitle_transition", "target_node_id": "generate-subtitle", "data_type": "object"}, {"to_transition_id": "generate_video_transition", "target_node_id": "generate-video", "data_type": "object"}]}, "approval_required": true, "end": false}, {"id": "generate_subtitle_transition", "sequence": 4, "transition_type": "standard", "node_info": {"node_id": "generate-subtitle", "tools_to_use": [{"tool_id": 4, "tool_name": "generate_subtitle", "tool_params": {"items": [{"field_name": "script", "data_type": "string", "field_value": "${script}"}, {"field_name": "audio_urls", "data_type": "array", "field_value": "${audio_urls}"}]}}], "input_data": [{"from_transition_id": "generate_script_transition", "target_node_id": "generate-script", "data_type": "object"}, {"from_transition_id": "fetch_audio_transition", "target_node_id": "generate-audio", "data_type": "object"}], "output_data": [{"to_transition_id": "generate_stock_video_transition", "target_node_id": "generate-stock-video", "data_type": "object"}, {"to_transition_id": "generate_stock_image_transition", "target_node_id": "generate-stock-image", "data_type": "object"}, {"to_transition_id": "generate_video_transition", "target_node_id": "generate-video", "data_type": "object"}]}, "approval_required": false, "end": false}, {"id": "generate_stock_video_transition", "sequence": 5, "transition_type": "standard", "node_info": {"node_id": "generate-stock-video", "tools_to_use": [{"tool_id": 5, "tool_name": "generate_stock_video", "tool_params": {"items": [{"field_name": "script", "data_type": "string", "field_value": "${subtitle}"}]}}], "input_data": [{"from_transition_id": "generate_subtitle_transition", "target_node_id": "generate-subtitle", "data_type": "object"}], "output_data": [{"to_transition_id": "generate_video_transition", "target_node_id": "generate-video", "data_type": "object"}]}, "approval_required": true, "end": false}, {"id": "generate_stock_image_transition", "sequence": 6, "transition_type": "standard", "node_info": {"node_id": "generate-stock-image", "tools_to_use": [{"tool_id": 6, "tool_name": "generate_ai_stock_image", "tool_params": {"items": [{"field_name": "script", "data_type": "string", "field_value": "${subtitle}"}, {"field_name": "view_type", "data_type": "string", "field_value": "PORTRAIT"}]}}], "input_data": [{"from_transition_id": "generate_subtitle_transition", "target_node_id": "generate-subtitle", "data_type": "object"}], "output_data": [{"to_transition_id": "generate_video_transition", "target_node_id": "generate-video", "data_type": "object"}]}, "approval_required": true, "end": false}, {"id": "generate_video_transition", "sequence": 7, "transition_type": "standard", "node_info": {"node_id": "generate-video", "tools_to_use": [{"tool_id": 7, "tool_name": "generate_video", "tool_params": {"items": [{"field_name": "view_type", "data_type": "string", "field_value": "PORTRAIT"}, {"field_name": "stock_video_clips", "data_type": "array", "field_value": "${stock_video_clips}"}, {"field_name": "stock_image_clips", "data_type": "array", "field_value": "${stock_image_clips}"}, {"field_name": "event_stock_clips", "data_type": "array", "field_value": []}, {"field_name": "audio_urls", "data_type": "array", "field_value": "${audio_urls}"}, {"field_name": "avatar_video_ids", "data_type": "array", "field_value": []}, {"field_name": "subtitles", "data_type": "string", "field_value": "${subtitles}"}]}}], "input_data": [{"from_transition_id": "generate_stock_video_transition", "target_node_id": "generate-stock-video", "data_type": "object"}, {"from_transition_id": "generate_stock_image_transition", "target_node_id": "generate-stock-image", "data_type": "object"}, {"from_transition_id": "fetch_audio_transition", "target_node_id": "generate-audio", "data_type": "object"}, {"from_transition_id": "generate_subtitle_transition", "target_node_id": "generate-subtitle", "data_type": "object"}], "output_data": [], "conditional_routing": null}, "approval_required": false, "end": true}]}