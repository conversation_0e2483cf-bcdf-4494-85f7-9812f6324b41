# PostgreSQL Implementation for Persistent Storage

## Overview

This document describes the implementation of PostgreSQL as a persistent storage solution for workflow state and transition results in the Orchestration Engine. The implementation provides long-term persistence for data that would otherwise be lost when deleted from Redis.

## Architecture

The implementation follows a tiered storage approach:

1. **Redis (Primary Storage)**: Fast in-memory storage for active workflows
2. **PostgreSQL (Persistent Storage)**: Durable storage for archiving data when it's removed from Redis
3. **In-Memory (Fallback)**: Used only when both Redis and PostgreSQL are unavailable

## Components

### 1. PostgreSQL Connection Manager

The `PostgresManager` class in `app/services/db_connections/postgres_connections.py` handles:

- Connection management to PostgreSQL
- Table creation and verification
- CRUD operations for workflow state and transition results

### 2. Redis Event Listener

The `RedisEventListener` class in `app/services/db_connections/redis_event_listener.py`:

- Subscribes to Redis keyspace notifications
- Detects when keys are deleted from Redis
- Triggers archiving of data to PostgreSQL before it's permanently lost

### 3. WorkflowStateManager Integration

The `WorkflowStateManager` class has been updated to:

- Initialize PostgreSQL connection
- Store data in both Redis and PostgreSQL
- Retrieve data from PostgreSQL when not found in Redis
- Archive data to PostgreSQL when deleted from Redis

## Database Schema

### Workflow State Table

```sql
CREATE TABLE workflow_state (
    id SERIAL PRIMARY KEY,
    correlation_id VARCHAR(255) UNIQUE NOT NULL,
    workflow_id VARCHAR(255) NOT NULL,
    state_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    archived BOOLEAN DEFAULT FALSE
);
```

### Transition Results Table

```sql
CREATE TABLE transition_results (
    id SERIAL PRIMARY KEY,
    correlation_id VARCHAR(255) NOT NULL,
    transition_id VARCHAR(255) NOT NULL,
    result_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    archived BOOLEAN DEFAULT FALSE
);
```

## Configuration

Add the following PostgreSQL configuration to your `.env` file:

```env
# PostgreSQL settings
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=orchestration_engine
```

## Setup

### 1. Install Dependencies

```bash
poetry add psycopg2-binary
```

### 2. Run Database Migration

```bash
# Create database and tables
poetry run python migrations/run_postgres_migration.py --create-db

# Run migration on existing database
poetry run python migrations/run_postgres_migration.py
```

## Usage

The implementation is designed to be transparent to existing code. The `WorkflowStateManager` will:

1. Always try Redis first for performance
2. Fall back to PostgreSQL if data is not found in Redis
3. Store data in both Redis and PostgreSQL for redundancy

## Data Flow

### Storing Data

1. Data is stored in memory for immediate access
2. Data is stored in Redis for shared access
3. Data is also stored in PostgreSQL for persistence

### Retrieving Data

1. Try to get data from Redis first (fastest)
2. If not found in Redis, try PostgreSQL (slower but persistent)
3. If not found in PostgreSQL, fall back to in-memory (if available)

### Archiving Data

When Redis deletes a key (due to expiration or manual deletion):

1. Redis Event Listener detects the deletion event
2. The listener extracts the key information (workflow ID or transition ID)
3. The data is retrieved from memory if still available
4. The data is archived to PostgreSQL for long-term storage

## Testing

Unit tests have been added for:

1. PostgreSQL connection manager
2. Redis event listener
3. WorkflowStateManager integration with PostgreSQL

Run the tests with:

```bash
poetry run pytest tests/services/db_connections/test_postgres_connections.py
poetry run pytest tests/services/db_connections/test_redis_event_listener.py
poetry run pytest tests/core_/test_state_manager.py
```

## Monitoring

The implementation includes detailed logging for:

- Connection status
- Data storage and retrieval operations
- Archiving events
- Error conditions

Monitor logs to ensure proper operation and troubleshoot any issues.

## Future Improvements

1. Add connection pooling for better performance
2. Implement data retention policies for PostgreSQL
3. Add metrics collection for storage operations
4. Implement data compression for large results
