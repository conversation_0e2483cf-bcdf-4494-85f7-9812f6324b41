import { describe, it, expect } from "jest";
import { validateNode, validateNodes, validateNodeUniqueness } from "../nodeValidation";
import { ValidationErrorCode } from "../types";
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";

describe("Node Validation", () => {
  describe("validateNode", () => {
    it("should validate a valid node", () => {
      const node = {
        id: "node-1",
        type: "default",
        position: { x: 100, y: 100 },
        data: {
          type: "component",
          label: "Test Node",
          definition: {
            name: "TestNode",
            inputs: [],
            outputs: [],
          },
        },
      } as Node<WorkflowNodeData>;

      const errors = validateNode(node, 0);
      expect(errors).toHaveLength(0);
    });

    it("should detect missing id", () => {
      const node = {
        type: "default",
        position: { x: 100, y: 100 },
        data: {
          type: "component",
          label: "Test Node",
          definition: {
            name: "TestNode",
            inputs: [],
            outputs: [],
          },
        },
      } as any;

      const errors = validateNode(node, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.NODE_MISSING_ID);
    });

    it("should detect missing type", () => {
      const node = {
        id: "node-1",
        position: { x: 100, y: 100 },
        data: {
          type: "component",
          label: "Test Node",
          definition: {
            name: "TestNode",
            inputs: [],
            outputs: [],
          },
        },
      } as any;

      const errors = validateNode(node, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.NODE_MISSING_TYPE);
    });

    it("should detect missing position", () => {
      const node = {
        id: "node-1",
        type: "default",
        data: {
          type: "component",
          label: "Test Node",
          definition: {
            name: "TestNode",
            inputs: [],
            outputs: [],
          },
        },
      } as any;

      const errors = validateNode(node, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.NODE_MISSING_POSITION);
    });

    it("should detect invalid position", () => {
      const node = {
        id: "node-1",
        type: "default",
        position: "invalid",
        data: {
          type: "component",
          label: "Test Node",
          definition: {
            name: "TestNode",
            inputs: [],
            outputs: [],
          },
        },
      } as any;

      const errors = validateNode(node, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.NODE_INVALID_POSITION);
    });

    it("should detect missing data", () => {
      const node = {
        id: "node-1",
        type: "default",
        position: { x: 100, y: 100 },
      } as any;

      const errors = validateNode(node, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.NODE_MISSING_DATA);
    });

    it("should detect missing data.type", () => {
      const node = {
        id: "node-1",
        type: "default",
        position: { x: 100, y: 100 },
        data: {
          label: "Test Node",
          definition: {
            name: "TestNode",
            inputs: [],
            outputs: [],
          },
        },
      } as any;

      const errors = validateNode(node, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.NODE_MISSING_DATA_TYPE);
    });

    it("should detect missing data.label", () => {
      const node = {
        id: "node-1",
        type: "default",
        position: { x: 100, y: 100 },
        data: {
          type: "component",
          definition: {
            name: "TestNode",
            inputs: [],
            outputs: [],
          },
        },
      } as any;

      const errors = validateNode(node, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.NODE_MISSING_DATA_LABEL);
    });

    it("should detect missing data.definition", () => {
      const node = {
        id: "node-1",
        type: "default",
        position: { x: 100, y: 100 },
        data: {
          type: "component",
          label: "Test Node",
        },
      } as any;

      const errors = validateNode(node, 0);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.NODE_MISSING_DATA_DEFINITION);
    });
  });

  describe("validateNodeUniqueness", () => {
    it("should validate nodes with unique IDs", () => {
      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
        { id: "node-3" },
      ] as Node<WorkflowNodeData>[];

      const errors = validateNodeUniqueness(nodes);
      expect(errors).toHaveLength(0);
    });

    it("should detect duplicate node IDs", () => {
      const nodes = [
        { id: "node-1" },
        { id: "node-2" },
        { id: "node-1" }, // Duplicate
      ] as Node<WorkflowNodeData>[];

      const errors = validateNodeUniqueness(nodes);
      expect(errors).toHaveLength(1);
      expect(errors[0].code).toBe(ValidationErrorCode.NODE_DUPLICATE_ID);
      expect(errors[0].message).toContain("node-1");
    });
  });

  describe("validateNodes", () => {
    it("should validate an array of valid nodes", () => {
      const nodes = [
        {
          id: "node-1",
          type: "default",
          position: { x: 100, y: 100 },
          data: {
            type: "component",
            label: "Test Node 1",
            definition: {
              name: "TestNode",
              inputs: [],
              outputs: [],
            },
          },
        },
        {
          id: "node-2",
          type: "default",
          position: { x: 200, y: 200 },
          data: {
            type: "component",
            label: "Test Node 2",
            definition: {
              name: "TestNode",
              inputs: [],
              outputs: [],
            },
          },
        },
      ] as Node<WorkflowNodeData>[];

      const result = validateNodes(nodes);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should detect invalid nodes", () => {
      const nodes = [
        {
          id: "node-1",
          type: "default",
          position: { x: 100, y: 100 },
          data: {
            type: "component",
            label: "Test Node 1",
            definition: {
              name: "TestNode",
              inputs: [],
              outputs: [],
            },
          },
        },
        {
          // Missing id
          type: "default",
          position: { x: 200, y: 200 },
          data: {
            type: "component",
            label: "Test Node 2",
            definition: {
              name: "TestNode",
              inputs: [],
              outputs: [],
            },
          },
        },
      ] as any[];

      const result = validateNodes(nodes);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.NODE_MISSING_ID);
    });

    it("should detect duplicate node IDs", () => {
      const nodes = [
        {
          id: "node-1",
          type: "default",
          position: { x: 100, y: 100 },
          data: {
            type: "component",
            label: "Test Node 1",
            definition: {
              name: "TestNode",
              inputs: [],
              outputs: [],
            },
          },
        },
        {
          id: "node-1", // Duplicate ID
          type: "default",
          position: { x: 200, y: 200 },
          data: {
            type: "component",
            label: "Test Node 2",
            definition: {
              name: "TestNode",
              inputs: [],
              outputs: [],
            },
          },
        },
      ] as Node<WorkflowNodeData>[];

      const result = validateNodes(nodes);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].code).toBe(ValidationErrorCode.NODE_DUPLICATE_ID);
    });
  });
});
