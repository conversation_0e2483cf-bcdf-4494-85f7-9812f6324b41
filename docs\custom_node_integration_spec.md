# Custom Node Integration for Distributed Workflow Platform

## 1. Executive Summary

This technical specification outlines a comprehensive solution for enabling end users to define, register, and use custom nodes in our distributed workflow platform without modifying core code. The solution addresses all aspects of the custom node lifecycle, from definition and registration to execution and security.

## 2. System Architecture Context

Our platform consists of four microservices:

1. **Workflow Builder App** (Frontend: Next.js + React Flow)
2. **Workflow Service** (Backend: Python)
3. **Node Executor Service** (Backend: Python)
4. **Orchestration Engine** (Backend: Python)

## 3. Custom Node Integration Solution

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────┐
│                        Workflow Builder App (Frontend)                   │
├─────────────────────────────────────────────────────────────────────────┤
│                                                                         │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────────────┐   │
│  │ Component     │    │ Custom Node   │    │ Workflow Canvas       │   │
│  │ Catalog       │    │ Editor        │    │ (React Flow)          │   │
│  └───────┬───────┘    └───────┬───────┘    └───────────┬───────────┘   │
│          │                    │                        │               │
└──────────┼────────────────────┼────────────────────────┼───────────────┘
           │                    │                        │
           ▼                    ▼                        ▼
┌──────────────────────────────────────────────────────────────────────────┐
│                         API Gateway                                       │
└──────────────────────────┬───────────────────────────────────────────────┘
                           │
                           ▼
┌──────────────────────────────────────────────────────────────────────────┐
│                         Workflow Service                                  │
├──────────────────────────────────────────────────────────────────────────┤
│                                                                          │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────────────┐    │
│  │ Component     │    │ Custom Node   │    │ Workflow Execution    │    │
│  │ Registry      │◄───┤ Service       │    │ Service               │    │
│  └───────┬───────┘    └───────┬───────┘    └───────────┬───────────┘    │
│          │                    │                        │                │
└──────────┼────────────────────┼────────────────────────┼────────────────┘
           │                    │                        │
           │                    ▼                        │
           │    ┌───────────────────────────────┐        │
           │    │        MongoDB                │        │
           │    │  (Custom Node Definitions)    │        │
           │    └───────────────────────────────┘        │
           │                                             │
           ▼                                             ▼
┌──────────────────────────────────────────────────────────────────────────┐
│                         Node Executor Service                             │
├──────────────────────────────────────────────────────────────────────────┤
│                                                                          │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────────────┐    │
│  │ Component     │    │ Custom Node   │    │ Sandboxed Execution   │    │
│  │ Manager       │◄───┤ Component     │◄───┤ Environment           │    │
│  └───────────────┘    └───────────────┘    └───────────────────────┘    │
│                                                                          │
└──────────────────────────────────────────────────────────────────────────┘
                           │
                           ▼
┌──────────────────────────────────────────────────────────────────────────┐
│                         Orchestration Engine                              │
├──────────────────────────────────────────────────────────────────────────┤
│                                                                          │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────────────┐    │
│  │ Workflow      │    │ Transition    │    │ State Manager         │    │
│  │ Engine        │    │ Handler       │    │                       │    │
│  └───────────────┘    └───────────────┘    └───────────────────────┘    │
│                                                                          │
└──────────────────────────────────────────────────────────────────────────┘
```

### A. Definition & Schema

#### Custom Node Definition Format

We will use a JSON schema for custom node definitions with the following structure:

```json
{
  "name": "CustomHttpClient",                  // Required: Unique identifier
  "display_name": "Custom HTTP Client",        // Required: User-friendly name
  "description": "Makes HTTP requests to custom endpoints", // Required
  "category": "Integration",                   // Required: For UI grouping
  "icon": "Globe",                             // Required: From Lucide icon library
  "version": "1.0.0",                          // Required: Semantic versioning
  "author": {                                  // Required
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "beta": false,                               // Optional: Default false
  "requires_approval": false,                  // Optional: Default false
  "inputs": [                                  // Required: Array of input ports
    {
      "name": "url",                           // Required: Unique within node
      "display_name": "URL",                   // Required: User-friendly name
      "input_type": "string",                  // Required: Data type
      "required": true,                        // Optional: Default false
      "default_value": "https://",             // Optional
      "description": "The URL to send the request to", // Optional
      "validation": {                          // Optional: Validation rules
        "pattern": "^https?://.*$",
        "min_length": 5,
        "max_length": 2048
      },
      "visibility_rules": []                   // Optional: Conditional visibility
    }
  ],
  "outputs": [                                 // Required: Array of output ports
    {
      "name": "response",                      // Required: Unique within node
      "display_name": "Response",              // Required: User-friendly name
      "output_type": "object",                 // Required: Data type
      "description": "The HTTP response"       // Optional
    }
  ],
  "execution": {                               // Required: Execution configuration
    "type": "python_code",                     // Required: Execution type
    "code": "import requests\n\nresponse = requests.get(inputs['url'])\nreturn {'response': response.json()}" // Required for python_code type
  },
  "ui": {                                      // Optional: UI customization
    "color": "#4287f5",                        // Optional: Node color
    "width": 180,                              // Optional: Node width
    "height": 100                              // Optional: Node height
  }
}
```

#### Supported Execution Types

1. **`python_code`**: Python code snippet executed in a sandboxed environment
2. **`http_endpoint`**: External HTTP endpoint that receives node inputs and returns outputs
3. **`javascript_code`**: JavaScript code for client-side execution (limited to UI operations)

### B. Registration & Persistence

#### Storage Strategy

Custom node definitions will be stored in a MongoDB collection with the following schema:

```javascript
{
  _id: ObjectId,                // Auto-generated
  name: String,                 // Unique identifier
  namespace: String,            // User or organization namespace
  version: String,              // Semantic version
  definition: Object,           // Full node definition (as per schema)
  created_at: Date,             // Creation timestamp
  updated_at: Date,             // Last update timestamp
  created_by: String,           // User ID
  is_public: Boolean,           // Whether node is publicly available
  status: String,               // "active", "deprecated", "disabled"
  tags: [String],               // Searchable tags
  usage_count: Number,          // Number of workflows using this node
}
```

#### Registration Process

1. **Validation**: Validate the node definition against the JSON schema
2. **Namespace Verification**: Ensure the user has permission to register in the namespace
3. **Duplicate Check**: Prevent name collisions within the same namespace
4. **Versioning**: Support multiple versions of the same node
5. **Indexing**: Index nodes for efficient retrieval

#### API Endpoints

```
POST /api/v1/custom-nodes                  # Create a new custom node
GET /api/v1/custom-nodes                   # List custom nodes (with filtering)
GET /api/v1/custom-nodes/{namespace}/{name} # Get a specific custom node
PUT /api/v1/custom-nodes/{namespace}/{name} # Update a custom node
DELETE /api/v1/custom-nodes/{namespace}/{name} # Delete a custom node
```

### C. Frontend Integration

#### Component Catalog API

Extend the existing component API to include custom nodes:

```typescript
// Extended API response
export interface ComponentsApiResponse {
  [category: string]: {
    [componentName: string]: ComponentDefinition | CustomNodeDefinition;
  };
}

// Add a discriminator field to identify custom nodes
export interface CustomNodeDefinition extends ComponentDefinition {
  is_custom: true;
  namespace: string;
  version: string;
  author: {
    name: string;
    email: string;
  };
}
```

#### Dynamic Node Rendering

1. Extend the `WorkflowNode` component to handle custom nodes
2. Generate configuration forms dynamically based on input definitions
3. Apply custom styling from the UI configuration
4. Add visual indicators for custom nodes (badge, border style)

#### Custom Node Editor

Create a dedicated UI for creating and editing custom nodes:
1. Form-based editor for node metadata and ports
2. Code editor for Python/JavaScript implementation
3. Visual preview of the node appearance
4. Testing interface to validate inputs/outputs

### D. Execution Path

#### Workflow Service Integration

1. Extend `ComponentRegistry` to load custom nodes from MongoDB
2. Create a `CustomNodeAdapter` that wraps custom node definitions as `BaseNode` subclasses
3. Implement the `execute()` method to dispatch to the appropriate executor

```python
class CustomNodeAdapter(BaseNode):
    """Adapter that wraps a custom node definition as a BaseNode."""

    def __init__(self, custom_node_def):
        self.custom_node_def = custom_node_def
        # Map definition fields to BaseNode class variables
        self.name = custom_node_def["name"]
        self.display_name = custom_node_def["display_name"]
        # ... other mappings

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """Execute the custom node based on its execution type."""
        execution_type = self.custom_node_def["execution"]["type"]

        if execution_type == "python_code":
            return await self._execute_python_code(context)
        elif execution_type == "http_endpoint":
            return await self._execute_http_endpoint(context)
        else:
            return NodeResult.error(f"Unsupported execution type: {execution_type}")
```

#### Node Executor Service Integration

1. Create a new `CustomNodeExecutor` component in the Node Executor Service
2. Implement sandboxed execution for Python code using RestrictedPython
3. Add HTTP client for external endpoint execution with security controls
4. Implement resource limiting and timeout mechanisms

### E. Edge Cases & Safeguards

#### Security Measures

1. **Code Sandboxing**: Use RestrictedPython to execute custom Python code in a restricted environment
2. **Resource Limits**:
   - CPU time limits (max 5 seconds per execution)
   - Memory limits (max 128MB per execution)
   - Network access controls (allowlist/blocklist)
   - File system isolation
3. **Input Validation**: Validate all inputs against defined schemas
4. **Rate Limiting**: Limit execution frequency per user
5. **Audit Logging**: Log all custom node executions with detailed context

#### Error Handling

1. **Graceful Degradation**: If a custom node fails, the workflow continues with error status
2. **Detailed Error Messages**: Provide actionable error messages to users
3. **Version Rollback**: Allow reverting to previous working versions
4. **Execution Retry**: Implement automatic retry with backoff for transient errors

## 4. Data Flow & Communication

### Custom Node Registration Flow

1. **User creates a custom node in the frontend editor**
   - The frontend validates the node definition against the schema
   - The editor provides real-time feedback on syntax and validation errors

2. **Frontend sends the node definition to the API Gateway**
   - `POST /api/v1/custom-nodes` with the node definition in the request body
   - The API Gateway forwards the request to the Workflow Service

3. **Workflow Service validates and stores the node**
   - Validates the node definition against the schema
   - Checks for namespace permissions and name collisions
   - Stores the node definition in MongoDB
   - Returns the created node with its ID to the frontend

### Custom Node Discovery Flow

1. **Frontend requests component catalog**
   - `GET /api/v1/components?include_custom=true`
   - The API Gateway forwards the request to the Workflow Service

2. **Workflow Service retrieves components**
   - Loads built-in components from the file system
   - Queries MongoDB for custom node definitions
   - Transforms custom nodes into component-compatible format
   - Returns the combined catalog to the frontend

3. **Frontend renders components in the palette**
   - Displays custom nodes with visual indicators
   - Groups nodes by category
   - Provides filtering and search capabilities

### Custom Node Execution Flow

1. **User adds a custom node to a workflow and configures it**
   - The frontend validates input configurations
   - The workflow is saved with the custom node reference

2. **User executes the workflow**
   - `POST /api/v1/workflows/{id}/execute`
   - The API Gateway forwards the request to the Workflow Service

3. **Workflow Service processes the workflow**
   - Loads the workflow definition
   - Identifies custom nodes in the workflow
   - Creates `CustomNodeAdapter` instances for each custom node

4. **Workflow Service executes nodes in sequence**
   - For custom nodes, the adapter's `execute()` method is called
   - The adapter prepares the execution request for the Node Executor Service

5. **Node Executor Service processes the custom node**
   - Receives the execution request via Kafka
   - The `CustomNodeComponent` handles the request
   - Based on the execution type, it routes to the appropriate executor
   - For Python code, it uses RestrictedPython in a sandboxed environment
   - For HTTP endpoints, it makes secure HTTP requests
   - Returns the execution result via Kafka

6. **Workflow Service continues workflow execution**
   - Receives the execution result
   - Updates the workflow context with the outputs
   - Continues to the next node in the workflow
   - Returns the final workflow result to the frontend

### Security & Monitoring Flow

1. **Audit logging for all custom node operations**
   - Creation, modification, and deletion events
   - Execution attempts and results
   - Security violations and errors

2. **Resource monitoring during execution**
   - CPU and memory usage tracking
   - Execution time monitoring
   - Rate limiting enforcement

3. **Error handling and reporting**
   - Detailed error messages for debugging
   - Sanitized error messages for end users
   - Error aggregation for monitoring dashboards

## 5. Implementation Plan

### Phase 1: Core Infrastructure (Weeks 1-2)

#### Workflow Service
- Create MongoDB schema for custom nodes
- Implement CRUD API endpoints
- Develop `CustomNodeAdapter` class
- Extend component discovery to include custom nodes

#### Node Executor Service
- Implement sandboxed Python execution
- Add resource monitoring and limiting
- Create HTTP endpoint executor with security controls

### Phase 2: Frontend Integration (Weeks 3-4)

#### Workflow Builder App
- Extend component catalog to include custom nodes
- Update node rendering to support custom nodes
- Develop custom node editor UI
- Implement node testing interface

### Phase 3: Security & Monitoring (Weeks 5-6)

- Implement comprehensive security measures
- Add audit logging for custom node operations
- Create monitoring dashboard for custom node usage
- Develop automated testing for custom nodes

### Phase 4: Documentation & Examples (Week 7)

- Create user documentation for custom nodes
- Develop example custom nodes for common use cases
- Create video tutorials for custom node creation
- Implement node sharing marketplace

## 6. Code Examples

### Custom Node Registration API

```python
# workflow-service/app/services/workflow_builder_backend/api/routes/custom_node.py

from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, List, Any, Optional
from app.services.workflow_builder_backend.schemas.custom_nodes import (
    CustomNodeCreate, CustomNodeUpdate, CustomNodeResponse
)
from app.services.workflow_builder_backend.services.custom_node_service import CustomNodeService

router = APIRouter(prefix="/custom-nodes", tags=["custom-nodes"])
custom_node_service = CustomNodeService()

@router.post("", response_model=CustomNodeResponse, status_code=status.HTTP_201_CREATED)
async def create_custom_node(node: CustomNodeCreate, current_user = Depends(get_current_user)):
    """Create a new custom node."""
    try:
        # Set the creator ID
        node.created_by = current_user.id

        # Validate and register the node
        result = await custom_node_service.create_custom_node(node)
        return result
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
```

### Custom Node Adapter

```python
# workflow-service/app/services/workflow_builder_backend/components/custom/custom_node_adapter.py

from app.services.workflow_builder_backend.components.core.base_node import BaseNode
from app.services.workflow_builder_backend.models.context import WorkflowContext
from app.services.workflow_builder_backend.models.node_result import NodeResult, NodeStatus
from app.services.workflow_builder_backend.services.execution.custom_node_executor import CustomNodeExecutor

class CustomNodeAdapter(BaseNode):
    """Adapter that wraps a custom node definition as a BaseNode."""

    def __init__(self, custom_node_def):
        # Store the custom node definition
        self.custom_node_def = custom_node_def

        # Map definition fields to BaseNode class variables
        self.name = custom_node_def["name"]
        self.display_name = custom_node_def["display_name"]
        self.description = custom_node_def["description"]
        self.category = custom_node_def["category"]
        self.icon = custom_node_def["icon"]
        self.beta = custom_node_def.get("beta", False)
        self.requires_approval = custom_node_def.get("requires_approval", False)

        # Create inputs and outputs from the definition
        self.inputs = self._create_inputs_from_definition()
        self.outputs = self._create_outputs_from_definition()

        # Create the executor
        self.executor = CustomNodeExecutor()

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """Execute the custom node based on its execution type."""
        try:
            # Get the inputs from the context
            inputs = context.node_outputs.get(context.current_node_id, {})

            # Execute the node using the appropriate executor
            result = await self.executor.execute(
                self.custom_node_def,
                inputs,
                context
            )

            return result
        except Exception as e:
            return NodeResult(
                status=NodeStatus.ERROR,
                error_message=str(e),
                outputs={}
            )
```

### Sandboxed Python Execution

```python
# node-executor-service/app/components/custom_node_component.py

import asyncio
import traceback
from RestrictedPython import compile_restricted
from RestrictedPython.Guards import safe_globals, limited_builtins
from app.core_.base_component import BaseComponent
from app.core_.component_system import register_component
from app.utils.security import get_security_settings

@register_component("CustomNodeComponent")
class CustomNodeComponent(BaseComponent):
    """Component for executing custom nodes."""

    async def process(self, payload):
        """Process a custom node execution request."""
        try:
            # Extract parameters
            node_def = payload.get("node_definition")
            inputs = payload.get("inputs", {})

            # Validate the payload
            if not node_def or "execution" not in node_def:
                return {"error": "Invalid node definition"}

            # Get execution details
            execution = node_def["execution"]
            execution_type = execution.get("type")

            # Execute based on type
            if execution_type == "python_code":
                return await self._execute_python_code(execution, inputs)
            elif execution_type == "http_endpoint":
                return await self._execute_http_endpoint(execution, inputs)
            else:
                return {"error": f"Unsupported execution type: {execution_type}"}

        except Exception as e:
            return {"error": str(e), "traceback": traceback.format_exc()}

    async def _execute_python_code(self, execution, inputs):
        """Execute Python code in a restricted environment."""
        # Get the code to execute
        code = execution.get("code", "")

        # Set up the restricted environment
        restricted_globals = safe_globals.copy()
        restricted_globals.update({
            "__builtins__": limited_builtins,
            "inputs": inputs,
            # Add safe libraries
            "json": __import__("json"),
            "re": __import__("re"),
            "math": __import__("math"),
            "datetime": __import__("datetime"),
        })

        try:
            # Compile the code
            byte_code = compile_restricted(
                code,
                filename="<custom_node>",
                mode="exec"
            )

            # Create a namespace for execution
            namespace = restricted_globals.copy()

            # Execute with resource limits
            with self._resource_limits():
                exec(byte_code, namespace)

            # Get the result
            if "result" in namespace:
                return namespace["result"]
            else:
                return {"error": "Code did not produce a 'result' variable"}

        except Exception as e:
            return {"error": f"Code execution error: {str(e)}"}
```

### Frontend Custom Node Editor

```typescript
// workflow-builder-app/src/components/custom-nodes/CustomNodeEditor.tsx

import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button, Input, Textarea, Select, Switch } from '@/components/ui';
import { CodeEditor } from '@/components/code-editor';
import { CustomNodePreview } from '@/components/custom-nodes/CustomNodePreview';
import { createCustomNode, updateCustomNode } from '@/lib/api';
import { toast } from '@/components/ui/toast';

// Validation schema for custom node
const customNodeSchema = z.object({
  name: z.string().min(3).max(50).regex(/^[a-zA-Z][a-zA-Z0-9_]*$/),
  display_name: z.string().min(3).max(50),
  description: z.string().min(10).max(500),
  category: z.string().min(2).max(50),
  icon: z.string().min(1),
  version: z.string().regex(/^\d+\.\d+\.\d+$/),
  beta: z.boolean().default(false),
  requires_approval: z.boolean().default(false),
  inputs: z.array(
    z.object({
      name: z.string().min(1).max(50).regex(/^[a-zA-Z][a-zA-Z0-9_]*$/),
      display_name: z.string().min(1).max(50),
      input_type: z.string(),
      required: z.boolean().default(false),
      default_value: z.any().optional(),
      description: z.string().optional(),
    })
  ),
  outputs: z.array(
    z.object({
      name: z.string().min(1).max(50).regex(/^[a-zA-Z][a-zA-Z0-9_]*$/),
      display_name: z.string().min(1).max(50),
      output_type: z.string(),
      description: z.string().optional(),
    })
  ),
  execution: z.object({
    type: z.enum(['python_code', 'http_endpoint', 'javascript_code']),
    code: z.string().min(1).optional(),
    endpoint: z.string().url().optional(),
  }),
});

export function CustomNodeEditor({ initialData, onSave }) {
  // Form setup with react-hook-form and zod validation
  const { control, handleSubmit, watch, formState: { errors } } = useForm({
    resolver: zodResolver(customNodeSchema),
    defaultValues: initialData || {
      name: '',
      display_name: '',
      description: '',
      category: 'Custom',
      icon: 'Cog',
      version: '1.0.0',
      beta: false,
      requires_approval: false,
      inputs: [{ name: 'input', display_name: 'Input', input_type: 'string', required: true }],
      outputs: [{ name: 'output', display_name: 'Output', output_type: 'any' }],
      execution: { type: 'python_code', code: '# Your code here\nresult = {"output": inputs["input"]}' },
    },
  });

  // Watch form values for preview
  const formValues = watch();

  // Handle form submission
  const onSubmit = async (data) => {
    try {
      if (initialData?.id) {
        await updateCustomNode(initialData.id, data);
        toast.success('Custom node updated successfully');
      } else {
        await createCustomNode(data);
        toast.success('Custom node created successfully');
      }
      onSave && onSave(data);
    } catch (error) {
      toast.error(`Error: ${error.message}`);
    }
  };

  return (
    <div className="grid grid-cols-2 gap-6">
      {/* Form section */}
      <div className="space-y-6">
        <form onSubmit={handleSubmit(onSubmit)}>
          {/* Basic information */}
          <div className="space-y-4">
            <h2 className="text-xl font-bold">Basic Information</h2>

            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <Input
                  label="Name"
                  placeholder="my_custom_node"
                  error={errors.name?.message}
                  {...field}
                />
              )}
            />

            {/* Additional form fields... */}

            {/* Code editor for execution */}
            <div className="mt-6">
              <h2 className="text-xl font-bold">Implementation</h2>
              <Controller
                name="execution.type"
                control={control}
                render={({ field }) => (
                  <Select
                    label="Execution Type"
                    options={[
                      { value: 'python_code', label: 'Python Code' },
                      { value: 'http_endpoint', label: 'HTTP Endpoint' },
                      { value: 'javascript_code', label: 'JavaScript Code' },
                    ]}
                    {...field}
                  />
                )}
              />

              {/* Render appropriate editor based on execution type */}
              {watch('execution.type') === 'python_code' && (
                <Controller
                  name="execution.code"
                  control={control}
                  render={({ field }) => (
                    <CodeEditor
                      language="python"
                      height="300px"
                      label="Python Code"
                      placeholder="# Your code here\nresult = {'output': inputs['input']}"
                      error={errors.execution?.code?.message}
                      {...field}
                    />
                  )}
                />
              )}
            </div>

            <Button type="submit" className="mt-6">
              {initialData?.id ? 'Update Node' : 'Create Node'}
            </Button>
          </div>
        </form>
      </div>

      {/* Preview section */}
      <div>
        <h2 className="text-xl font-bold mb-4">Node Preview</h2>
        <CustomNodePreview nodeDefinition={formValues} />

        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">JSON Definition</h3>
          <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-[300px] text-xs">
            {JSON.stringify(formValues, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
```

## 7. Limitations and Constraints

When implementing custom nodes, users should be aware of the following limitations and constraints across each service in our architecture. Understanding these boundaries is essential for developing effective and secure custom nodes.

### 7.1 General Limitations

1. **Version Compatibility**
   - Custom nodes are tied to the platform version they were created in
   - Major platform updates may require custom node updates
   - No automatic migration path for custom nodes during major version upgrades

2. **Performance Impact**
   - Custom nodes typically have higher execution overhead than built-in nodes
   - Complex workflows with many custom nodes may experience performance degradation
   - Execution time may vary based on system load and resource availability

3. **Debugging Capabilities**
   - Limited runtime debugging compared to local development environments
   - Error messages may be sanitized for security reasons
   - No step-through debugging or breakpoint capabilities

### 7.2 Workflow Builder App (Frontend) Limitations

1. **UI Constraints**
   - Custom node visual customization is limited to predefined options (color, icon, size)
   - No support for custom rendering of node content beyond the standard template
   - Maximum of 10 inputs and 10 outputs per custom node

2. **JavaScript Execution**
   - JavaScript code execution is limited to UI operations only
   - No access to browser APIs like `localStorage`, `indexedDB`, or `navigator`
   - No access to DOM outside the node's own container
   - Maximum execution time of 500ms for JavaScript code
   - Maximum memory usage of 10MB for JavaScript execution

3. **Form Validation**
   - Limited to validation rules defined in the schema
   - No custom validation functions for input fields
   - No cross-field validation capabilities

### 7.3 Workflow Service Limitations

1. **Definition Constraints**
   - Maximum node definition size: 100KB
   - Maximum number of custom nodes per user/organization: 100
   - Node names must be unique within a namespace
   - Reserved keywords and names cannot be used

2. **Input/Output Limitations**
   - Maximum payload size: 5MB
   - Supported data types: string, number, boolean, object, array
   - No support for binary data types (must be base64 encoded)
   - No support for streaming data

3. **Versioning Limitations**
   - Maximum 10 versions per custom node
   - No automatic version migration for workflows using older versions
   - No branching or parallel version development

### 7.4 Node Executor Service Limitations

1. **Python Code Execution**
   - Restricted Python environment with limited standard library access
   - Available modules: json, re, math, datetime, collections, copy, itertools
   - No access to file system, network, or system resources
   - No third-party libraries or package imports
   - Maximum execution time: 5 seconds
   - Maximum memory usage: 128MB
   - No persistent state between executions
   - No concurrent or parallel execution within a node

2. **HTTP Endpoint Execution**
   - Restricted to allowlisted domains only
   - Blocked protocols: file://, ftp://, ws://, wss://
   - Maximum request timeout: 30 seconds
   - Maximum response size: 10MB
   - Supported methods: GET, POST, PUT, DELETE
   - Supported content types: application/json, text/plain, application/xml
   - No support for client certificates
   - Rate limiting: 100 requests per hour per user

3. **Security Boundaries**
   - No access to other users' data or nodes
   - No access to platform internals or configuration
   - No ability to modify workflow execution path outside defined edges
   - No privilege escalation capabilities
   - All code execution is logged and audited

### 7.5 Orchestration Engine Limitations

1. **Workflow Execution**
   - Maximum workflow execution time: 30 minutes
   - Maximum nodes in a single workflow: 100
   - Maximum custom nodes in a single workflow: 50
   - Maximum workflow nesting depth: 5
   - No support for dynamic node creation during execution
   - No support for dynamic edge creation during execution

2. **State Management**
   - Maximum context data size: 50MB
   - No persistent state between workflow executions
   - Limited global variable scope
   - No shared state between concurrent workflow executions

3. **Error Handling**
   - Limited retry capabilities for custom nodes (max 3 retries)
   - No custom exception types
   - No transaction support for partial rollback
   - No compensation workflows for failed executions

### 7.6 Resource Quotas and Rate Limits

1. **Execution Quotas**
   - Free tier: 1,000 custom node executions per month
   - Standard tier: 10,000 custom node executions per month
   - Enterprise tier: Customizable limits

2. **Rate Limits**
   - API requests: 100 requests per minute
   - Custom node creation/update: 50 per day
   - Workflow executions with custom nodes: 10 concurrent executions

3. **Storage Limits**
   - Custom node definitions: 10MB total per user/organization
   - Execution logs: 7-day retention for free tier, 30-day for standard, 90-day for enterprise

### 7.7 Compliance and Governance

1. **Code Review Requirements**
   - Public custom nodes require approval before publishing
   - Enterprise environments may require internal approval workflows
   - Code must comply with platform terms of service

2. **Audit Requirements**
   - All custom node executions are logged
   - Logs include creator, executor, inputs (sanitized), and execution time
   - No option to disable audit logging

3. **Data Processing Limitations**
   - Custom nodes must comply with data residency requirements
   - PII processing may be restricted based on compliance settings
   - Custom nodes cannot bypass data governance policies

Understanding these limitations will help users develop custom nodes that work effectively within the platform's constraints while maintaining security and performance standards.

## 8. Conclusion

This custom node integration solution enables end users to extend the platform's capabilities without compromising security or stability. By providing a structured definition format, intuitive UI, and secure execution environment, we empower users to create specialized workflow components while maintaining the integrity of the core platform.

The implementation follows a phased approach, starting with core infrastructure and gradually adding frontend integration, security measures, and documentation. This ensures a robust and user-friendly solution that meets the needs of both technical and non-technical users.

By leveraging existing components like the component registry and node executor service, we minimize the changes required to the core codebase while maximizing the extensibility of the platform. The result is a flexible and powerful system that can adapt to the evolving needs of our users, while maintaining appropriate limitations and constraints to ensure security, performance, and reliability.
