from typing import Dict, Any, List, ClassVar
import time
import logging

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import InputBase, DropdownInput
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

# Set up logging
logger = logging.getLogger(__name__)


class DataToDataFrameComponent(BaseNode):
    """
    Converts data to a Pandas DataFrame.

    This component takes input data (list of dictionaries or dictionary of lists)
    and converts it to a Pandas DataFrame.
    """

    name: ClassVar[str] = "DataToDataFrameComponent"
    display_name: ClassVar[str] = "Data to DataFrame"
    description: ClassVar[str] = "Converts data to a Pandas DataFrame."
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Table"
    beta: ClassVar[bool] = False

    inputs: ClassVar[List[InputBase]] = [
        # Input data - unified dual-purpose input
        create_dual_purpose_input(
            name="input_data",
            display_name="Input Data",
            input_type="dict",
            required=True,
            info="The data to convert to a DataFrame. Can be connected from another node or entered directly.",
            input_types=["list", "dict", "Any"],
        ),
        DropdownInput(
            name="orientation",
            display_name="Data Orientation",
            options=["records", "columns", "auto-detect"],
            value="auto-detect",
            info="The orientation of the input data: 'records' (list of dicts), 'columns' (dict of lists), or 'auto-detect'.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="output_dataframe", display_name="DataFrame", output_type="DataFrame"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get the value of an input from the workflow context.

        This method handles dual-purpose inputs by checking both node outputs and global context.

        Args:
            input_name: The name of the input to get the value for.
            context: The workflow context.
            default: The default value to return if the input is not found.

        Returns:
            The value of the input.
        """
        node_id = context.current_node_id

        # Check if there's a value in the node outputs
        node_outputs = context.node_outputs.get(node_id, {})
        if input_name in node_outputs:
            return node_outputs[input_name]

        # If not found, return the default
        return default

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the DataToDataFrameComponent.

        This method converts data to a Pandas DataFrame according to the specified orientation.

        Args:
            context: The workflow execution context containing input values.

        Returns:
            A NodeResult with the execution results.
        """
        # Start timing for performance measurement
        start_time = time.time()

        # Log execution start
        context.log(f"Executing {self.name}...")

        try:
            # Get inputs using the helper method
            input_data = self.get_input_value("input_data", context)
            orientation = self.get_input_value("orientation", context, "auto-detect")

            # Log the inputs for debugging
            logger.debug(f"Input data type: {type(input_data)}")
            logger.debug(f"Orientation: {orientation}")

            # Validate input data
            if input_data is None:
                error_msg = "Input data is missing. Please connect data to convert."
                context.log(error_msg)
                return NodeResult.error(error_msg, time.time() - start_time)

            # Import pandas
            try:
                import pandas as pd
            except ImportError:
                error_msg = "Pandas library is not installed. Please install it with 'pip install pandas'."
                context.log(error_msg)
                return NodeResult.error(error_msg, time.time() - start_time)

            # Auto-detect orientation if needed
            if orientation == "auto-detect":
                if isinstance(input_data, list) and all(isinstance(item, dict) for item in input_data):
                    orientation = "records"
                    context.log("Auto-detected orientation: records (list of dicts)")
                elif isinstance(input_data, dict) and all(isinstance(value, list) for value in input_data.values()):
                    orientation = "columns"
                    context.log("Auto-detected orientation: columns (dict of lists)")
                elif isinstance(input_data, dict):
                    # Single dictionary - convert to records format (list with one dict)
                    orientation = "records"
                    input_data = [input_data]  # Wrap in list
                    context.log("Auto-detected orientation: records (single dict converted to list)")
                else:
                    error_msg = f"Cannot auto-detect orientation for data type {type(input_data).__name__}. Please specify the orientation explicitly or provide data as list of dicts or dict of lists."
                    context.log(error_msg)
                    return NodeResult.error(error_msg, time.time() - start_time)

            # Convert to DataFrame based on orientation
            if orientation == "records":
                if not isinstance(input_data, list):
                    error_msg = f"Expected a list of dictionaries for 'records' orientation, got {type(input_data).__name__}"
                    context.log(error_msg)
                    return NodeResult.error(error_msg, time.time() - start_time)

                # Check if all items are dictionaries
                if not all(isinstance(item, dict) for item in input_data):
                    error_msg = "Not all items in the list are dictionaries"
                    context.log(error_msg)
                    return NodeResult.error(error_msg, time.time() - start_time)

                # Create DataFrame from records
                df = pd.DataFrame.from_records(input_data)

            elif orientation == "columns":
                if not isinstance(input_data, dict):
                    error_msg = f"Expected a dictionary of lists for 'columns' orientation, got {type(input_data).__name__}"
                    context.log(error_msg)
                    return NodeResult.error(error_msg, time.time() - start_time)

                # Check if all values are lists
                if not all(isinstance(value, list) for value in input_data.values()):
                    error_msg = "Not all values in the dictionary are lists"
                    context.log(error_msg)
                    return NodeResult.error(error_msg, time.time() - start_time)

                # Check if all lists have the same length
                list_lengths = [len(value) for value in input_data.values()]
                if len(set(list_lengths)) > 1:
                    error_msg = "Lists in the dictionary have different lengths"
                    context.log(error_msg)
                    return NodeResult.error(error_msg, time.time() - start_time)

                # Create DataFrame from dict of lists
                df = pd.DataFrame(input_data)

            else:
                error_msg = f"Unsupported orientation: {orientation}"
                context.log(error_msg)
                return NodeResult.error(error_msg, time.time() - start_time)

            # Log success
            execution_time = time.time() - start_time
            context.log(f"Data converted to DataFrame successfully. Shape: {df.shape}. Time: {execution_time:.2f}s")

            # Return success with the DataFrame
            return NodeResult.success(
                outputs={"output_dataframe": df},
                execution_time=execution_time
            )

        except Exception as e:
            error_msg = f"Error converting to DataFrame: {str(e)}"
            context.log(error_msg)
            logger.error(error_msg, exc_info=True)
            return NodeResult.error(error_msg, time.time() - start_time)

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the DataToDataFrameComponent.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Args:
            **kwargs: Contains the input values:
                - input_data: The data to convert (updated from input_data_handle)
                - orientation: The orientation of the data

        Returns:
            A dictionary with either:
                - output_dataframe: The converted DataFrame
                - error: An error message if the operation failed
        """
        logger.warning(f"Using legacy build method for {self.name}. Please update to use execute method.")

        # Get inputs - support both old and new input names for backward compatibility
        input_data = kwargs.get("input_data") or kwargs.get("input_data_handle")
        orientation = kwargs.get("orientation", "auto-detect")

        # Validate input
        if input_data is None:
            return {"error": "Input data is missing. Please connect data to convert."}

        try:
            # Import pandas
            try:
                import pandas as pd
            except ImportError:
                return {
                    "error": "Pandas library is not installed. Please install it with 'pip install pandas'."
                }

            # Normalize orientation values for backward compatibility
            orientation_mapping = {
                "Auto-Detect": "auto-detect",
                "Records (List of Dicts)": "records",
                "Columns (Dict of Lists)": "columns"
            }
            orientation = orientation_mapping.get(orientation, orientation)

            # Determine orientation if auto-detect
            if orientation == "auto-detect":
                if isinstance(input_data, list) and all(
                    isinstance(item, dict) for item in input_data
                ):
                    orientation = "records"
                elif isinstance(input_data, dict) and all(
                    isinstance(value, list) for value in input_data.values()
                ):
                    orientation = "columns"
                elif isinstance(input_data, dict):
                    # Single dictionary - convert to records format (list with one dict)
                    orientation = "records"
                    input_data = [input_data]  # Wrap in list
                else:
                    return {
                        "error": f"Cannot auto-detect orientation for data type {type(input_data).__name__}. Please specify the orientation explicitly or provide data as list of dicts or dict of lists."
                    }

            # Convert to DataFrame based on orientation
            if orientation == "records":
                if not isinstance(input_data, list):
                    return {
                        "error": f"Expected a list of dictionaries, got {type(input_data).__name__}"
                    }

                # Check if all items are dictionaries
                if not all(isinstance(item, dict) for item in input_data):
                    return {"error": "Not all items in the list are dictionaries"}

                # Create DataFrame from records
                df = pd.DataFrame.from_records(input_data)

            elif orientation == "columns":
                if not isinstance(input_data, dict):
                    return {
                        "error": f"Expected a dictionary of lists, got {type(input_data).__name__}"
                    }

                # Check if all values are lists
                if not all(isinstance(value, list) for value in input_data.values()):
                    return {"error": "Not all values in the dictionary are lists"}

                # Check if all lists have the same length
                list_lengths = [len(value) for value in input_data.values()]
                if len(set(list_lengths)) > 1:
                    return {"error": "Lists in the dictionary have different lengths"}

                # Create DataFrame from dict of lists
                df = pd.DataFrame(input_data)

            else:
                return {"error": f"Unsupported orientation: {orientation}"}

            print(f"  Data converted to DataFrame successfully. Shape: {df.shape}")
            return {"output_dataframe": df}

        except Exception as e:
            error_msg = f"Error converting to DataFrame: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
