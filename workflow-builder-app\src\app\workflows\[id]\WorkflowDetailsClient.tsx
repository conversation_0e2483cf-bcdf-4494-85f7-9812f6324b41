"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { getClientAccessToken } from "@/lib/clientCookies";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import {
  Workflow,
  ArrowLeft,
  Edit,
  Clock,
  Calendar,
  Info,
  Loader2,
  AlertCircle,
  Play,
} from "lucide-react";
// import { formatDistanceToNow } from "date-fns"; // Uncomment if needed for relative dates
import { WorkflowSummary, WorkflowDetails } from "@/app/(features)/workflows/api";

// Format relative date for display (not currently used but kept for future use)
// const formatDate = (dateString: string) => {
//   try {
//     return formatDistanceToNow(new Date(dateString), { addSuffix: true });
//   } catch (e) {
//     return "Unknown date";
//   }
// };

// Format full date for display
const formatFullDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleString();
  } catch (e) {
    return "Unknown date";
  }
};

interface WorkflowDetailsClientProps {
  id: string;
}

export default function WorkflowDetailsClient({ id }: WorkflowDetailsClientProps) {
  const router = useRouter();

  const [workflow, setWorkflow] = useState<WorkflowSummary | WorkflowDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to get workflow data regardless of structure
  const getWorkflowData = (data: WorkflowSummary | WorkflowDetails): WorkflowDetails => {
    // If it's a WorkflowSummary (has workflow property), return the nested workflow
    if ("workflow" in data && data.workflow) {
      return data.workflow;
    }
    // Otherwise, it's already a WorkflowDetails
    return data as WorkflowDetails;
  };

  useEffect(() => {
    const fetchWorkflowDetails = async () => {
      // Get token from cookie
      const accessToken = getClientAccessToken();

      try {
        setIsLoading(true);
        setError(null);

        // Use the API URL from environment variables
        // Note: NEXT_PUBLIC_API_URL already includes '/api/v1' from the environment variable
        const apiUrl = process.env.NEXT_PUBLIC_API_URL;
        console.log("Using API URL:", apiUrl);

        // Fetch workflow details from the API
        const response = await fetch(`${apiUrl}/workflows/${id}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
          },
        });

        if (!response.ok) {
          throw new Error(
            `Failed to fetch workflow details: ${response.status} ${response.statusText}`,
          );
        }

        const data = await response.json();
        console.log("Fetched workflow details:", data);
        setWorkflow(data);
      } catch (err) {
        console.error("Failed to fetch workflow details:", err);
        if (err instanceof Error) {
          setError(`Failed to load workflow details: ${err.message}`);
        } else {
          setError("Failed to load workflow details. Please try again later.");
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchWorkflowDetails();
    }
  }, [id]);

  // Handle opening the workflow in the editor
  const handleOpenInEditor = () => {
    router.push(`/?workflow_id=${id}`);
  };

  // Handle executing the workflow
  const handleExecuteWorkflow = async () => {
    try {
      // Use the API URL from environment variables
      // Note: NEXT_PUBLIC_API_URL already includes '/api/v1' from the environment variable
      const apiUrl = process.env.NEXT_PUBLIC_API_URL;

      // Execute the workflow
      const response = await fetch(`${apiUrl}/workflows/${id}/execute`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to execute workflow: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log("Execution result:", result);

      // Show success message
      alert("Workflow execution started successfully!");
    } catch (err) {
      console.error("Failed to execute workflow:", err);
      alert(`Failed to execute workflow: ${err instanceof Error ? err.message : "Unknown error"}`);
    }
  };

  return (
    <main className="bg-background min-h-screen">
      {/* Header */}
      <div className="brand-gradient-indicator text-brand-white-text p-6 shadow-md">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="rounded-md bg-white p-2 shadow-md">
                <Workflow className="text-brand-primary h-6 w-6" />
              </div>
              <h1 className="font-primary text-2xl font-bold">Workflow Details</h1>
            </div>
            <Link href="/workflows">
              <Button
                variant="outline"
                className="text-brand-primary hover:bg-brand-card-hover border-brand-stroke bg-white"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Workflows
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="container mx-auto px-4 py-8">
        {isLoading ? (
          <div className="flex h-64 items-center justify-center">
            <Loader2 className="text-brand-primary h-8 w-8 animate-spin" />
            <span className="font-primary ml-2 text-lg">Loading workflow details...</span>
          </div>
        ) : error ? (
          <div className="flex h-64 flex-col items-center justify-center text-center">
            <AlertCircle className="text-brand-unpublish mb-4 h-12 w-12" />
            <h3 className="font-primary mb-2 text-xl font-semibold">
              Failed to Load Workflow Details
            </h3>
            <p className="text-brand-secondary-font mb-4">{error}</p>
            <div className="flex gap-4">
              <Button
                onClick={() => window.location.reload()}
                className="brand-gradient-indicator text-brand-white-text"
              >
                Try Again
              </Button>
              <Link href="/workflows">
                <Button
                  variant="outline"
                  className="border-brand-stroke text-brand-primary hover:bg-brand-clicked"
                >
                  Back to Workflows
                </Button>
              </Link>
            </div>
          </div>
        ) : workflow ? (
          (() => {
            // Get the actual workflow data regardless of structure
            const workflowData = getWorkflowData(workflow);

            return (
              <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                {/* Main workflow info */}
                <Card className="border-brand-stroke bg-brand-card lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="font-primary text-brand-primary-font text-2xl">
                      {workflowData.name || "Untitled Workflow"}
                    </CardTitle>
                    <CardDescription className="font-secondary text-brand-secondary-font">
                      {workflowData.description || "No description provided"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Info className="text-brand-secondary h-5 w-5" />
                        <span className="font-primary font-medium">ID:</span>
                        <span className="text-brand-secondary-font">{workflowData.id}</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Calendar className="text-brand-secondary h-5 w-5" />
                        <span className="font-primary font-medium">Created:</span>
                        <span className="text-brand-secondary-font">
                          {workflowData.created_at
                            ? formatFullDate(workflowData.created_at)
                            : "Unknown"}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Clock className="text-brand-secondary h-5 w-5" />
                        <span className="font-primary font-medium">Last Updated:</span>
                        <span className="text-brand-secondary-font">
                          {workflowData.updated_at
                            ? formatFullDate(workflowData.updated_at)
                            : "Unknown"}
                        </span>
                      </div>

                      {workflowData.execution_count !== undefined && (
                        <div className="flex items-center gap-2">
                          <Play className="text-brand-secondary h-5 w-5" />
                          <span className="font-primary font-medium">Executions:</span>
                          <span className="text-brand-secondary-font">
                            {workflowData.execution_count}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={handleOpenInEditor}
                      className="border-brand-stroke text-brand-primary hover:bg-brand-clicked flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Open in Editor
                    </Button>
                    <Button
                      onClick={handleExecuteWorkflow}
                      className="brand-gradient-indicator text-brand-white-text flex items-center gap-2"
                    >
                      <Play className="h-4 w-4" />
                      Execute Workflow
                    </Button>
                  </CardFooter>
                </Card>

                {/* Workflow metadata */}
                <Card className="border-brand-stroke bg-brand-card">
                  <CardHeader>
                    <CardTitle className="font-primary text-brand-primary-font">
                      Workflow Metadata
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {workflowData.builder_url && (
                        <div>
                          <h3 className="font-primary mb-1 text-sm font-medium">Builder URL</h3>
                          <p className="text-brand-secondary-font text-xs break-all">
                            {workflowData.builder_url}
                          </p>
                        </div>
                      )}

                      {workflowData.user_id && (
                        <div>
                          <h3 className="font-primary mb-1 text-sm font-medium">Owner</h3>
                          <p className="text-brand-secondary-font text-sm">
                            {workflowData.user_id}
                          </p>
                        </div>
                      )}

                      {workflowData.status && (
                        <div>
                          <h3 className="font-primary mb-1 text-sm font-medium">Status</h3>
                          <div
                            className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                              workflowData.status === "active"
                                ? "bg-brand-tick/20 text-brand-tick"
                                : "bg-brand-unpublish/20 text-brand-unpublish"
                            }`}
                          >
                            {workflowData.status}
                          </div>
                        </div>
                      )}

                      {workflowData.tags &&
                        Array.isArray(workflowData.tags) &&
                        workflowData.tags.length > 0 && (
                          <div>
                            <h3 className="font-primary mb-1 text-sm font-medium">Tags</h3>
                            <div className="flex flex-wrap gap-1">
                              {workflowData.tags.map((tag: string, index: number) => (
                                <span
                                  key={index}
                                  className="bg-brand-primary/10 text-brand-primary inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            );
          })()
        ) : (
          <div className="flex h-64 flex-col items-center justify-center text-center">
            <AlertCircle className="text-brand-unpublish mb-4 h-12 w-12" />
            <h3 className="font-primary mb-2 text-xl font-semibold">Workflow Not Found</h3>
            <p className="text-brand-secondary-font mb-4">
              The workflow you&apos;re looking for doesn&apos;t exist or you don&apos;t have
              permission to view it.
            </p>
            <Link href="/workflows">
              <Button className="brand-gradient-indicator text-brand-white-text">
                Back to Workflows
              </Button>
            </Link>
          </div>
        )}
      </div>
    </main>
  );
}
