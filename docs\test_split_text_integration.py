#!/usr/bin/env python3
"""
Integration testing for Split Text Component across services.
This simulates the full workflow execution path.
"""

import asyncio
import json
import sys
import os

async def test_integration():
    """Test the complete integration flow."""
    print("🧪 Split Text Component - Integration Test")
    print("=" * 60)
    
    # Test data that simulates what would come from the frontend
    workflow_request = {
        "workflow_id": "test_workflow_789",
        "nodes": [
            {
                "id": "start_node",
                "type": "StartNode",
                "outputs": {
                    "flow": "split_node"
                }
            },
            {
                "id": "split_node", 
                "type": "SplitTextComponent",
                "inputs": {
                    "input_text": "apple,banana,cherry,date,elderberry",
                    "delimiter": ",",
                    "max_splits": 3,
                    "include_delimiter": False
                },
                "outputs": {
                    "output_list": "end_node"
                }
            },
            {
                "id": "end_node",
                "type": "EndNode",
                "inputs": {
                    "result": "split_node.output_list"
                }
            }
        ]
    }
    
    print("📋 Simulated Workflow Request:")
    print(json.dumps(workflow_request, indent=2))
    
    # Simulate the payload that would be sent to Node Executor Service
    node_executor_payload = {
        "request_id": "req_12345",
        "workflow_id": "test_workflow_789",
        "node_id": "split_node",
        "component_type": "SplitTextComponent",
        "tool_parameters": {
            "input_text": "apple,banana,cherry,date,elderberry",
            "delimiter": ",",
            "max_splits": 3,
            "include_delimiter": False
        }
    }
    
    print(f"\n📋 Node Executor Payload:")
    print(json.dumps(node_executor_payload, indent=2))
    
    # Test the Node Executor Service component
    print(f"\n🔧 Testing Node Executor Service...")
    try:
        # Add node-executor-service to path
        node_executor_path = os.path.join(os.path.dirname(__file__), 'node-executor-service')
        if os.path.exists(node_executor_path):
            sys.path.insert(0, node_executor_path)
        
        from app.components.split_text_component import SplitTextComponent
        
        component = SplitTextComponent()
        
        # Validate
        validation = await component.validate(node_executor_payload)
        print(f"Validation: {'✅ Passed' if validation.is_valid else '❌ Failed'}")
        
        if validation.is_valid:
            # Process
            result = await component.process(node_executor_payload)
            print(f"Processing Status: {result.get('status', 'unknown')}")
            
            if result.get("status") == "success":
                output_list = result.get("output_list", [])
                print(f"Split Result: {output_list}")
                
                # Verify the result
                expected = ["apple", "banana", "cherry", "date,elderberry"]
                if output_list == expected:
                    print("✅ Integration test PASSED")
                    
                    # Simulate what would be returned to the orchestration engine
                    orchestration_response = {
                        "request_id": "req_12345",
                        "node_id": "split_node",
                        "status": "success",
                        "outputs": {
                            "output_list": output_list
                        },
                        "execution_time_ms": 45
                    }
                    
                    print(f"\n📋 Response to Orchestration Engine:")
                    print(json.dumps(orchestration_response, indent=2))
                    
                else:
                    print(f"❌ Expected {expected}, got {output_list}")
            else:
                print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ Validation failed: {validation.error_message}")
            
    except ImportError as e:
        print(f"⚠️  Could not import Node Executor Service component: {e}")
        print("Make sure you're running this from the correct directory or adjust the path")
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_real_world_scenarios():
    """Test real-world use cases."""
    print(f"\n🌍 Real-World Scenario Tests")
    print("=" * 40)
    
    scenarios = [
        {
            "name": "Processing CSV data",
            "input": "Name,Age,City,Country\nJohn,25,New York,USA\nJane,30,London,UK",
            "delimiter": "\n",
            "description": "Split CSV rows"
        },
        {
            "name": "Processing log entries", 
            "input": "ERROR|2024-01-15|Database connection failed|retry_count=3",
            "delimiter": "|",
            "description": "Parse structured log entry"
        },
        {
            "name": "Processing email addresses",
            "input": "<EMAIL>;<EMAIL>;<EMAIL>",
            "delimiter": ";",
            "description": "Split email list"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 Scenario: {scenario['name']}")
        print(f"Description: {scenario['description']}")
        print(f"Input: {scenario['input'][:50]}{'...' if len(scenario['input']) > 50 else ''}")
        print(f"Delimiter: '{scenario['delimiter']}'")
        
        # Create payload
        payload = {
            "request_id": f"scenario_{scenario['name'].replace(' ', '_')}",
            "tool_parameters": {
                "input_text": scenario["input"],
                "delimiter": scenario["delimiter"],
                "max_splits": -1,
                "include_delimiter": False
            }
        }
        
        try:
            # Test if we can import the component
            from app.components.split_text_component import SplitTextComponent
            component = SplitTextComponent()
            
            result = await component.process(payload)
            if result.get("status") == "success":
                output = result.get("output_list", [])
                print(f"Result: {len(output)} parts")
                for i, part in enumerate(output[:3]):  # Show first 3 parts
                    print(f"  [{i}]: {part[:30]}{'...' if len(part) > 30 else ''}")
                if len(output) > 3:
                    print(f"  ... and {len(output) - 3} more parts")
                print("✅ Scenario PASSED")
            else:
                print(f"❌ Scenario FAILED: {result.get('error', 'Unknown error')}")
                
        except ImportError:
            print("⚠️  Skipping scenario test (component not available)")
        except Exception as e:
            print(f"❌ Scenario failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_integration())
    asyncio.run(test_real_world_scenarios())
