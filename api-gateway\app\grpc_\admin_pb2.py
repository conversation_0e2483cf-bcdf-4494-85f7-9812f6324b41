# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: admin.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'admin.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0b\x61\x64min.proto\x12\x05\x61\x64min\"/\n\x0cLoginRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\"}\n\rLoginResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x13\n\x0b\x61\x63\x63\x65ssToken\x18\x03 \x01(\t\x12\x14\n\x0crefreshToken\x18\x04 \x01(\t\x12\x1f\n\x05\x61\x64min\x18\x05 \x01(\x0b\x32\x10.admin.AdminInfo\"*\n\x12\x41\x63\x63\x65ssTokenRequest\x12\x14\n\x0crefreshToken\x18\x01 \x01(\t\"b\n\x13\x41\x63\x63\x65ssTokenResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x13\n\x0b\x61\x63\x63\x65ssToken\x18\x03 \x01(\t\x12\x14\n\x0crefreshToken\x18\x04 \x01(\t\"V\n\x12\x43reateAdminRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x10\n\x08\x66ullName\x18\x03 \x01(\t\x12\r\n\x05roles\x18\x04 \x03(\t\"\"\n\x0fGetAdminRequest\x12\x0f\n\x07\x61\x64minId\x18\x01 \x01(\t\"\x9a\x01\n\x12UpdateAdminRequest\x12\x0f\n\x07\x61\x64minId\x18\x01 \x01(\t\x12\x15\n\x08\x66ullName\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x12\n\x05\x65mail\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x15\n\x08password\x18\x04 \x01(\tH\x02\x88\x01\x01\x12\r\n\x05roles\x18\x05 \x03(\tB\x0b\n\t_fullNameB\x08\n\x06_emailB\x0b\n\t_password\"%\n\x12\x44\x65leteAdminRequest\x12\x0f\n\x07\x61\x64minId\x18\x01 \x01(\t\"7\n\x13\x44\x65leteAdminResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"3\n\x11ListAdminsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x10\n\x08pageSize\x18\x02 \x01(\x05\"g\n\x12ListAdminsResponse\x12 \n\x06\x61\x64mins\x18\x01 \x03(\x0b\x32\x10.admin.AdminInfo\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x12\n\ntotalPages\x18\x04 \x01(\x05\"R\n\rAdminResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1f\n\x05\x61\x64min\x18\x03 \x01(\x0b\x32\x10.admin.AdminInfo\"\x83\x01\n\tAdminInfo\x12\x0f\n\x07\x61\x64minId\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x10\n\x08\x66ullName\x18\x03 \x01(\t\x12\x1e\n\x05roles\x18\x04 \x03(\x0b\x32\x0f.admin.RoleInfo\x12\x11\n\tcreatedAt\x18\x05 \x01(\t\x12\x11\n\tupdatedAt\x18\x06 \x01(\t\"x\n\x08RoleInfo\x12\x0e\n\x06roleId\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x13\n\x0bpermissions\x18\x04 \x03(\t\x12\x11\n\tcreatedAt\x18\x05 \x01(\t\x12\x11\n\tupdatedAt\x18\x06 \x01(\t\"K\n\x11\x43reateRoleRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x13\n\x0bpermissions\x18\x03 \x03(\t\" \n\x0eGetRoleRequest\x12\x0e\n\x06roleId\x18\x01 \x01(\t\"~\n\x11UpdateRoleRequest\x12\x0e\n\x06roleId\x18\x01 \x01(\t\x12\x11\n\x04name\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x18\n\x0b\x64\x65scription\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x13\n\x0bpermissions\x18\x04 \x03(\tB\x07\n\x05_nameB\x0e\n\x0c_description\"#\n\x11\x44\x65leteRoleRequest\x12\x0e\n\x06roleId\x18\x01 \x01(\t\"6\n\x12\x44\x65leteRoleResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"2\n\x10ListRolesRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x10\n\x08pageSize\x18\x02 \x01(\x05\"d\n\x11ListRolesResponse\x12\x1e\n\x05roles\x18\x01 \x03(\x0b\x32\x0f.admin.RoleInfo\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x12\n\ntotalPages\x18\x04 \x01(\x05\"O\n\x0cRoleResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1d\n\x04role\x18\x03 \x01(\x0b\x32\x0f.admin.RoleInfo\"5\n\x11\x41ssignRoleRequest\x12\x0f\n\x07\x61\x64minId\x18\x01 \x01(\t\x12\x0f\n\x07roleIds\x18\x02 \x03(\t2\xbd\x06\n\x0c\x41\x64minService\x12\x32\n\x05login\x12\x13.admin.LoginRequest\x1a\x14.admin.LoginResponse\x12\x44\n\x0b\x61\x63\x63\x65ssToken\x12\x19.admin.AccessTokenRequest\x1a\x1a.admin.AccessTokenResponse\x12>\n\x0b\x63reateAdmin\x12\x19.admin.CreateAdminRequest\x1a\x14.admin.AdminResponse\x12\x38\n\x08getAdmin\x12\x16.admin.GetAdminRequest\x1a\x14.admin.AdminResponse\x12>\n\x0bupdateAdmin\x12\x19.admin.UpdateAdminRequest\x1a\x14.admin.AdminResponse\x12\x44\n\x0b\x64\x65leteAdmin\x12\x19.admin.DeleteAdminRequest\x1a\x1a.admin.DeleteAdminResponse\x12\x41\n\nlistAdmins\x12\x18.admin.ListAdminsRequest\x1a\x19.admin.ListAdminsResponse\x12;\n\ncreateRole\x12\x18.admin.CreateRoleRequest\x1a\x13.admin.RoleResponse\x12\x35\n\x07getRole\x12\x15.admin.GetRoleRequest\x1a\x13.admin.RoleResponse\x12;\n\nupdateRole\x12\x18.admin.UpdateRoleRequest\x1a\x13.admin.RoleResponse\x12\x41\n\ndeleteRole\x12\x18.admin.DeleteRoleRequest\x1a\x19.admin.DeleteRoleResponse\x12>\n\tlistRoles\x12\x17.admin.ListRolesRequest\x1a\x18.admin.ListRolesResponse\x12<\n\nassignRole\x12\x18.admin.AssignRoleRequest\x1a\x14.admin.AdminResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'admin_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_LOGINREQUEST']._serialized_start=22
  _globals['_LOGINREQUEST']._serialized_end=69
  _globals['_LOGINRESPONSE']._serialized_start=71
  _globals['_LOGINRESPONSE']._serialized_end=196
  _globals['_ACCESSTOKENREQUEST']._serialized_start=198
  _globals['_ACCESSTOKENREQUEST']._serialized_end=240
  _globals['_ACCESSTOKENRESPONSE']._serialized_start=242
  _globals['_ACCESSTOKENRESPONSE']._serialized_end=340
  _globals['_CREATEADMINREQUEST']._serialized_start=342
  _globals['_CREATEADMINREQUEST']._serialized_end=428
  _globals['_GETADMINREQUEST']._serialized_start=430
  _globals['_GETADMINREQUEST']._serialized_end=464
  _globals['_UPDATEADMINREQUEST']._serialized_start=467
  _globals['_UPDATEADMINREQUEST']._serialized_end=621
  _globals['_DELETEADMINREQUEST']._serialized_start=623
  _globals['_DELETEADMINREQUEST']._serialized_end=660
  _globals['_DELETEADMINRESPONSE']._serialized_start=662
  _globals['_DELETEADMINRESPONSE']._serialized_end=717
  _globals['_LISTADMINSREQUEST']._serialized_start=719
  _globals['_LISTADMINSREQUEST']._serialized_end=770
  _globals['_LISTADMINSRESPONSE']._serialized_start=772
  _globals['_LISTADMINSRESPONSE']._serialized_end=875
  _globals['_ADMINRESPONSE']._serialized_start=877
  _globals['_ADMINRESPONSE']._serialized_end=959
  _globals['_ADMININFO']._serialized_start=962
  _globals['_ADMININFO']._serialized_end=1093
  _globals['_ROLEINFO']._serialized_start=1095
  _globals['_ROLEINFO']._serialized_end=1215
  _globals['_CREATEROLEREQUEST']._serialized_start=1217
  _globals['_CREATEROLEREQUEST']._serialized_end=1292
  _globals['_GETROLEREQUEST']._serialized_start=1294
  _globals['_GETROLEREQUEST']._serialized_end=1326
  _globals['_UPDATEROLEREQUEST']._serialized_start=1328
  _globals['_UPDATEROLEREQUEST']._serialized_end=1454
  _globals['_DELETEROLEREQUEST']._serialized_start=1456
  _globals['_DELETEROLEREQUEST']._serialized_end=1491
  _globals['_DELETEROLERESPONSE']._serialized_start=1493
  _globals['_DELETEROLERESPONSE']._serialized_end=1547
  _globals['_LISTROLESREQUEST']._serialized_start=1549
  _globals['_LISTROLESREQUEST']._serialized_end=1599
  _globals['_LISTROLESRESPONSE']._serialized_start=1601
  _globals['_LISTROLESRESPONSE']._serialized_end=1701
  _globals['_ROLERESPONSE']._serialized_start=1703
  _globals['_ROLERESPONSE']._serialized_end=1782
  _globals['_ASSIGNROLEREQUEST']._serialized_start=1784
  _globals['_ASSIGNROLEREQUEST']._serialized_end=1837
  _globals['_ADMINSERVICE']._serialized_start=1840
  _globals['_ADMINSERVICE']._serialized_end=2669
# @@protoc_insertion_point(module_scope)
