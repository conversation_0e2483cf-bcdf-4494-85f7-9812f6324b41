"""
Gmail Tracker Component - Tracks email responses and forwards them.
"""

import os
import logging
import imaplib
import email
import smtplib
import ssl
from email.message import EmailMessage
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from app.core_.base_component import BaseComponent
from app.core_.component_system import register_component
from app.utils.db_handler import create_db_handler

# Setup logger
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("GmailTrackerComponent")
except ImportError:
    # Fallback to basic logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logger = logging.getLogger(__name__)


# Request schema for tracking emails
class TrackEmailsRequest(BaseModel):
    """Schema for email tracking requests."""
    forward_to: List[str] = Field(..., description="Email addresses to forward replies to")
    db_type: str = Field("sqlite", description="Database type (sqlite or mongodb)")
    db_config: Dict[str, Any] = Field(default_factory=dict, description="Database configuration")
    imap_server: Optional[str] = Field(None, description="IMAP server address")
    smtp_server: Optional[str] = Field(None, description="SMTP server address")
    smtp_port: int = Field(587, description="SMTP server port")
    sender_email: Optional[str] = Field(None, description="Sender email address")
    sender_password: Optional[str] = Field(None, description="Sender email password")
    excluded_subject_terms: List[str] = Field(
        default_factory=lambda: [
            "automated", "out of office", "Automatic", "Automa",
            "Failure", "Address not found", "Away from my desk",
            "On Vacation", "away until", "No Reply", "ticket has"
        ],
        description="Terms to exclude in subject lines"
    )
    excluded_domains: List[str] = Field(
        default_factory=lambda: ["@sentry.wixpress.com", "sentry.io", 'gmail'],
        description="Domains to exclude from forwarding"
    )
    organization: Optional[str] = Field(None, description="Organization filter")
    user_id: Optional[str] = Field(None, description="User ID filter")


@register_component("GmailTrackerComponent")
class GmailTrackerComponent(BaseComponent):
    """
    Component for tracking email responses and forwarding them.
    """

    def __init__(self):
        """Initialize the Gmail Tracker component."""
        logger.info("Initializing Gmail Tracker Component")
        super().__init__()

        # Set the request schema for automatic validation
        self.request_schema = TrackEmailsRequest

        logger.info("Gmail Tracker Component initialized successfully")

    def _get_email_content(self, msg):
        """Extract email content (plain text and HTML) from a multipart message."""
        text_content = ""
        html_content = ""

        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                if content_type == "text/plain" and not text_content:
                    text_content = part.get_payload(decode=True).decode(errors='ignore')
                elif content_type == "text/html" and not html_content:
                    html_content = part.get_payload(decode=True).decode(errors='ignore')
        else:
            content_type = msg.get_content_type()
            if content_type == "text/plain":
                text_content = msg.get_payload(decode=True).decode(errors='ignore')
            elif content_type == "text/html":
                html_content = msg.get_payload(decode=True).decode(errors='ignore')

        return text_content or "No plain text content", html_content or "No HTML content"

    def _should_forward_email(self, msg, excluded_subject_terms, excluded_domains):
        """
        Check if the email should be forwarded based on exclusion criteria.
        Returns True if email should be forwarded, False otherwise.
        """
        # Check subject for excluded terms
        subject = msg.get('subject', '').lower()
        for term in excluded_subject_terms:
            if term.lower() in subject:
                logger.info(f"Skipping email with excluded term in subject: {subject}")
                return False

        # Check sender domain
        from_address = msg.get('from', '').lower()
        for domain in excluded_domains:
            if domain.lower() in from_address:
                logger.info(f"Skipping email from excluded domain: {from_address}")
                return False

        return True

    def _forward_reply(self, original_msg, forward_to, smtp_server, smtp_port, sender_email, sender_password, excluded_subject_terms, excluded_domains):
        """Forward a reply to specified addresses."""
        try:
            # Check if email should be forwarded
            if not self._should_forward_email(original_msg, excluded_subject_terms, excluded_domains):
                return False

            forward_msg = EmailMessage()
            forward_msg["From"] = sender_email
            forward_msg["To"] = ", ".join(forward_to)

            # Sanitize the subject to remove linefeed and carriage return characters
            original_subject = original_msg.get("Subject", "No Subject")
            sanitized_subject = original_subject.replace('\n', ' ').replace('\r', ' ')
            forward_msg["Subject"] = f"REPLY: {sanitized_subject}"

            text_content, html_content = self._get_email_content(original_msg)
            forward_msg.set_content(text_content)
            forward_msg.add_alternative(html_content, subtype='html')

            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls(context=ssl.create_default_context())
                server.login(sender_email, sender_password)
                server.send_message(forward_msg)

            logger.info(f"Forwarded reply for subject: {original_subject}")
            return True
        except Exception as e:
            logger.error(f"Forwarding error: {str(e)}")
            return False

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an email tracking request.

        Args:
            payload: The request payload

        Returns:
            A dictionary with the processing result
        """
        try:
            # Extract parameters
            forward_to = payload.get("forward_to", [])
            db_type = payload.get("db_type", "sqlite")
            db_config = payload.get("db_config", {})

            # Get IMAP/SMTP configuration
            imap_server = payload.get("imap_server") or os.getenv("IMAP_SERVER")
            smtp_server = payload.get("smtp_server") or os.getenv("SMTP_SERVER")
            smtp_port = payload.get("smtp_port", 587)
            sender_email = payload.get("sender_email") or os.getenv("SENDER_EMAIL")
            sender_password = payload.get("sender_password") or os.getenv("SENDER_PASSWORD")

            # Get exclusion lists
            excluded_subject_terms = payload.get("excluded_subject_terms", [
                "automated", "out of office", "Automatic", "Automa",
                "Failure", "Address not found", "Away from my desk",
                "On Vacation", "away until", "No Reply", "ticket has"
            ])
            excluded_domains = payload.get("excluded_domains", ["@sentry.wixpress.com", "sentry.io", 'gmail'])

            # Get organization and user filters
            organization = payload.get("organization")
            user_id = payload.get("user_id")

            # Validate required parameters
            if not forward_to:
                return {
                    "success": False,
                    "error": "No forward_to email addresses provided"
                }

            if not imap_server or not smtp_server or not sender_email or not sender_password:
                return {
                    "success": False,
                    "error": "Missing email server configuration. Provide imap_server, smtp_server, sender_email, and sender_password in payload or as environment variables."
                }

            # Create database handler
            db_handler = create_db_handler(db_type, **db_config)

            # Get unforwarded emails
            unforwarded_emails = db_handler.get_unforwarded_emails()

            # Apply organization and user filters if provided
            if organization or user_id:
                filtered_emails = []
                for email_data in unforwarded_emails:
                    if organization and email_data.get('organization') != organization:
                        continue
                    if user_id and email_data.get('user_id') != user_id:
                        continue
                    filtered_emails.append(email_data)
                unforwarded_emails = filtered_emails

            total_forwarded = 0
            try:
                with imaplib.IMAP4_SSL(imap_server) as imap:
                    imap.login(sender_email, sender_password)
                    imap.select("inbox")

                    for email_data in unforwarded_emails:
                        try:
                            message_id = email_data.get('message_id')
                            recipient = email_data.get('recipient')

                            search_query = f'(HEADER "In-Reply-To" "{message_id}")'
                            status, data = imap.search(None, search_query)

                            if status == "OK" and data[0]:
                                for e_id in data[0].split():
                                    status, msg_data = imap.fetch(e_id, "(RFC822)")
                                    if status == "OK":
                                        raw_email = msg_data[0][1]
                                        original_msg = email.message_from_bytes(raw_email)

                                        if self._forward_reply(
                                            original_msg,
                                            forward_to,
                                            smtp_server,
                                            smtp_port,
                                            sender_email,
                                            sender_password,
                                            excluded_subject_terms,
                                            excluded_domains
                                        ):
                                            # Mark the email as seen after forwarding
                                            imap.store(e_id, "+FLAGS", "\\Seen")
                                            total_forwarded += 1
                                            # Update the flag in the database to avoid future forwarding
                                            db_handler.update_email_status(message_id)
                        except Exception as e:
                            logger.error(f"Error processing {recipient}: {str(e)}")
            except Exception as e:
                logger.error(f"IMAP error: {str(e)}")
                return {
                    "success": False,
                    "error": f"IMAP error: {str(e)}"
                }

            # Close database connection
            db_handler.close()

            logger.info(f"Forwarded {total_forwarded} replies")
            return {
                "success": True,
                "total_forwarded": total_forwarded,
                "total_checked": len(unforwarded_emails)
            }

        except Exception as e:
            logger.error(f"Error tracking emails: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
