"""
Script to run all PostgreSQL implementation tests.

Usage:
    poetry run python run_postgres_tests.py
"""

import sys
import logging
import subprocess
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("PostgresTests")

def run_test(test_script, description):
    """
    Run a test script and log the results.
    
    Args:
        test_script (str): The name of the test script to run.
        description (str): A description of the test.
        
    Returns:
        bool: True if the test passed, False otherwise.
    """
    logger.info(f"Running test: {description}")
    logger.info(f"Executing: poetry run python {test_script}")
    
    try:
        result = subprocess.run(
            ["poetry", "run", "python", test_script],
            capture_output=True,
            text=True,
            check=False
        )
        
        # Log the output
        if result.stdout:
            for line in result.stdout.splitlines():
                logger.info(f"[{test_script}] {line}")
        
        if result.stderr:
            for line in result.stderr.splitlines():
                logger.error(f"[{test_script}] {line}")
        
        if result.returncode == 0:
            logger.info(f"✅ Test passed: {description}")
            return True
        else:
            logger.error(f"❌ Test failed: {description}")
            return False
    except Exception as e:
        logger.exception(f"Error running test {test_script}: {e}")
        return False

def run_all_tests():
    """
    Run all PostgreSQL implementation tests.
    """
    tests = [
        ("test_postgres_implementation.py", "Redis to PostgreSQL archiving"),
        ("test_postgres_retrieval.py", "PostgreSQL retrieval"),
        ("test_redis_expiration.py", "Redis expiration and PostgreSQL fallback"),
        ("test_manual_deletion.py", "Manual deletion and PostgreSQL archiving")
    ]
    
    results = {}
    all_passed = True
    
    for test_script, description in tests:
        # Run the test
        passed = run_test(test_script, description)
        results[description] = passed
        
        if not passed:
            all_passed = False
        
        # Wait a moment between tests
        time.sleep(1)
    
    # Print summary
    logger.info("\n=== Test Results Summary ===")
    for description, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        logger.info(f"{status} - {description}")
    
    if all_passed:
        logger.info("\n🎉 All tests passed! The PostgreSQL implementation is working correctly.")
        return 0
    else:
        logger.error("\n❌ Some tests failed. Please check the logs for details.")
        return 1

if __name__ == "__main__":
    logger.info("Starting PostgreSQL implementation tests...")
    sys.exit(run_all_tests())
