#!/bin/bash

# API Testing Script for Split Text Component
# Make sure your API Gateway and services are running

echo "🧪 Split Text Component - API Testing"
echo "======================================"

# Configuration
API_BASE_URL="http://localhost:8000"  # Adjust to your API Gateway URL
WORKFLOW_ENDPOINT="$API_BASE_URL/api/v1/workflows/execute"

# Test Case 1: Basic CSV splitting
echo ""
echo "📋 Test 1: Basic CSV splitting"
echo "------------------------------"

curl -X POST "$WORKFLOW_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_id": "test_split_csv",
    "nodes": [
      {
        "id": "split_node",
        "type": "SplitTextComponent",
        "inputs": {
          "input_text": "apple,banana,cherry,date",
          "delimiter": ",",
          "max_splits": -1,
          "include_delimiter": false
        }
      }
    ]
  }' | jq '.'

echo ""
echo "------------------------------"

# Test Case 2: Limited splits
echo ""
echo "📋 Test 2: Limited splits"
echo "------------------------------"

curl -X POST "$WORKFLOW_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_id": "test_split_limited",
    "nodes": [
      {
        "id": "split_node",
        "type": "SplitTextComponent", 
        "inputs": {
          "input_text": "one|two|three|four|five",
          "delimiter": "|",
          "max_splits": 2,
          "include_delimiter": false
        }
      }
    ]
  }' | jq '.'

echo ""
echo "------------------------------"

# Test Case 3: Include delimiter
echo ""
echo "📋 Test 3: Include delimiter"
echo "------------------------------"

curl -X POST "$WORKFLOW_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_id": "test_split_delimiter",
    "nodes": [
      {
        "id": "split_node",
        "type": "SplitTextComponent",
        "inputs": {
          "input_text": "hello;world;test",
          "delimiter": ";",
          "max_splits": -1,
          "include_delimiter": true
        }
      }
    ]
  }' | jq '.'

echo ""
echo "------------------------------"

# Test Case 4: Error case - missing input
echo ""
echo "📋 Test 4: Error case - missing input"
echo "------------------------------"

curl -X POST "$WORKFLOW_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_id": "test_split_error",
    "nodes": [
      {
        "id": "split_node",
        "type": "SplitTextComponent",
        "inputs": {
          "delimiter": ",",
          "max_splits": -1,
          "include_delimiter": false
        }
      }
    ]
  }' | jq '.'

echo ""
echo "======================================"
echo "🎉 API tests completed!"
echo ""
echo "Note: Make sure jq is installed for JSON formatting"
echo "Install with: sudo apt-get install jq (Ubuntu) or brew install jq (Mac)"
