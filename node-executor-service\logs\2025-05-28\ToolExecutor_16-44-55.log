2025-05-28 16:44:55 - ToolExecutor - INFO - [setup_logger:467] Logger ToolExecutor configured with log file: logs\2025-05-28\ToolExecutor_16-44-55.log
2025-05-28 16:44:55 - ToolExecutor - INFO - [setup_tool_executor_logger:97] <PERSON><PERSON><PERSON> logging enabled for ToolExecutor, sending to dedicated topic: tool_executor_logs
2025-05-28 16:44:55 - ToolExecutor - INFO - [get_tool_executor:281] Creating new global ToolExecutor instance
2025-05-28 16:44:55 - ToolExecutor - INFO - [__init__:76] Initializing ToolExecutor
2025-05-28 16:44:55 - ToolExecutor - INFO - [__init__:78] ToolExecutor initialized successfully
2025-05-28 16:45:02 - ToolExecutor - INFO - [execute_tool:94] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Executing tool for request_id: fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:45:02 - ToolExecutor - INFO - [execute_tool:97] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"social\":\"4\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "marketing": "2"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "fbe24603-f02f-4943-a3c4-a9a2c4e9de7c",
  "correlation_id": "09354c4a-cd67-4939-852e-b7403f21528d"
}
2025-05-28 16:45:02 - ToolExecutor - INFO - [execute_tool:116] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Tool name: MergeDataComponent for request_id: fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:45:02 - ToolExecutor - INFO - [execute_tool:151] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Processing payload with component MergeDataComponent for request_id: fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:45:02 - ToolExecutor - INFO - [execute_tool:155] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Component MergeDataComponent processed payload successfully for request_id: fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:45:02 - ToolExecutor - INFO - [execute_tool:225] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] ToolExecutor returning success: {
  "request_id": "fbe24603-f02f-4943-a3c4-a9a2c4e9de7c",
  "status": "success",
  "result": {
    "status": "success",
    "result": {
      "value": "{\"social\":\"4\"}",
      "marketing": "2"
    }
  }
}
2025-05-28 16:45:49 - ToolExecutor - INFO - [execute_tool:94] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Executing tool for request_id: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:49 - ToolExecutor - INFO - [execute_tool:97] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748429970692-5225765898358",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"body\":\"testing\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "9ecb6624-6ae0-43b4-9ed0-c727dd127b0b",
  "correlation_id": "e22a7320-ed4c-4e62-990a-d3a2e5132645"
}
2025-05-28 16:45:49 - ToolExecutor - INFO - [execute_tool:116] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Tool name: ApiRequestNode for request_id: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:49 - ToolExecutor - INFO - [execute_tool:151] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Processing payload with component ApiRequestNode for request_id: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:49 - ToolExecutor - INFO - [execute_tool:94] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Executing tool for request_id: 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:45:49 - ToolExecutor - INFO - [execute_tool:97] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"social\":\"4\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "marketing": "2"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1",
  "correlation_id": "e22a7320-ed4c-4e62-990a-d3a2e5132645"
}
2025-05-28 16:45:49 - ToolExecutor - INFO - [execute_tool:116] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Tool name: MergeDataComponent for request_id: 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:45:49 - ToolExecutor - INFO - [execute_tool:151] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Processing payload with component MergeDataComponent for request_id: 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:45:49 - ToolExecutor - INFO - [execute_tool:155] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Component MergeDataComponent processed payload successfully for request_id: 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:45:49 - ToolExecutor - INFO - [execute_tool:225] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] ToolExecutor returning success: {
  "request_id": "553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1",
  "status": "success",
  "result": {
    "status": "success",
    "result": {
      "value": "{\"social\":\"4\"}",
      "marketing": "2"
    }
  }
}
2025-05-28 16:45:50 - ToolExecutor - INFO - [execute_tool:155] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Component ApiRequestNode processed payload successfully for request_id: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:50 - ToolExecutor - INFO - [execute_tool:225] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "9ecb6624-6ae0-43b4-9ed0-c727dd127b0b",
  "status": "success",
  "response": {
    "result": "1748430949659-2249077649321",
    "status_code": 200,
    "response_headers": {
      "Date": "Wed, 28 May 2025 11:15:49 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-qoErpg1IcfOTTlnhNFvoCHoIOwM\"",
      "X-Response-Time": "0.72954ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=LxDDrOXIfRUKKq7UwVWEesN0VDjAa4wz%2FUPmf%2BeiVUot4MNvoNoSKa75CzlnNWe9hKR9UuEhHDhNi47EgvO913jzaU9ev7m6R10l7ghImdBYukfKjFNGCQ%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "946d5619ad5ae22f-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748429970692-5225765898358",
    "method": "POST"
  }
}
