syntax = "proto3";

package communication;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/any.proto";

// Communication service definition
service CommunicationService {
    rpc createConversation(CreateConversationRequest) returns (Conversation);
    rpc getConversation(GetConversationRequest) returns (Conversation);
    rpc deleteConversation(DeleteConversationRequest) returns (google.protobuf.Empty);
    rpc listConversations(ListConversationsRequest) returns (ListConversationsResponse);

    rpc createMessage(CreateMessageRequest) returns (Message);
    rpc deleteMessage(DeleteMessageRequest) returns (google.protobuf.Empty);
    rpc listMessages(ListMessagesRequest) returns (ListMessagesResponse);
}

// Conversation Related Enums & Messages

// Enum for channel types
enum ChannelType {
    CHANNEL_TYPE_UNSPECIFIED = 0;
    CHANNEL_TYPE_WEB = 1;
}

// Enum for chat types
enum ChatType {
    CHAT_TYPE_UNSPECIFIED = 0;
    CHAT_TYPE_SINGLE = 1;
    CHAT_TYPE_MULTI = 2;
}

// Conversation model
message Conversation {
    string id = 1;
    string userId = 2;
    string agentId = 3;
    string title = 4;
    ChannelType channel = 5;
    ChatType chatType = 6;
    string summary = 7;
    google.protobuf.Timestamp createdAt = 8;
    google.protobuf.Timestamp updatedAt = 9;
}

// Request messages for conversation operations
message CreateConversationRequest {
    string userId = 1;
    string agentId = 2;
    string title = 3;
    ChannelType channel = 4;
    ChatType chatType = 5;
    string summary = 6;
}

message GetConversationRequest {
    string conversationId = 1;
    string userId = 2;
}

message DeleteConversationRequest {
    string conversationId = 1;
    string userId = 2;
}

message ListConversationsRequest {
    string userId = 1;
    ChannelType channel = 2;
    ChatType chatType = 3;
    int32 page = 4;
    int32 limit = 5;
    string agentId = 6;
}

message PaginationMetadata {
    int32 total = 1;
    int32 totalPages = 2;
    int32 currentPage = 3;
    int32 pageSize = 4;
    bool hasNextPage = 5;
    bool hasPreviousPage = 6;
}

message ListConversationsResponse {
    repeated Conversation data = 1;
    PaginationMetadata metadata = 2;
}

// Message Related Enums & Messages

// Enum for sender types
enum SenderType {
    SENDER_TYPE_UNSPECIFIED = 0;
    SENDER_TYPE_USER = 1;
    SENDER_TYPE_ASSISTANT = 2;
}

// Message model
message Message {
    string id = 1;
    string conversationId = 2;
    SenderType senderType = 3;
    optional string content = 4;
    optional string workflowId = 5;
    map<string, google.protobuf.Any> workflowResponse = 6;
    google.protobuf.Timestamp createdAt = 7;
    google.protobuf.Timestamp updatedAt = 8;
}

// Request messages for message operations
message CreateMessageRequest {
    string conversationId = 1;
    SenderType senderType = 2;
    optional string content = 3;
    optional string workflowId = 4;
    map<string, google.protobuf.Any> workflowResponse = 5;
    string userId = 6;
}

message DeleteMessageRequest {
    string messageId = 1;
    string userId = 2;
}

message ListMessagesRequest {
    string conversationId = 1;
    int32 page = 2;
    int32 limit = 3;
    string userId = 4;
}

message ListMessagesResponse {
    repeated Message data = 1;
    PaginationMetadata metadata = 2;
}

