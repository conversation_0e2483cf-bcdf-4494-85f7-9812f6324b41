2025-05-28 16:17:37 - MergeDataComponent - INFO - [setup_logger:467] Logger MergeDataComponent configured with log file: logs\2025-05-28\MergeDataComponent_16-17-37.log
2025-05-28 16:20:20 - MergeDataComponent - INFO - [__init__:96] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] MergeDataExecutor initialized
2025-05-28 16:20:20 - MergeDataComponent - INFO - [process:241] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Processing merge data request for request_id: 38089d77-a0e5-4549-8eb1-93945a27cab0
2025-05-28 16:20:20 - MergeDataComponent - INFO - [process:243] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-28 16:20:20 - MergeDataComponent - INFO - [process:257] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-28 16:20:20 - MergeDataComponent - INFO - [process:277] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Merging data for request_id 38089d77-a0e5-4549-8eb1-93945a27cab0. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-28 16:20:20 - MergeDataComponent - INFO - [process:341] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Dictionary input_1 merged with deep merge strategy for request_id 38089d77-a0e5-4549-8eb1-93945a27cab0. Current keys: ['interview_questions', 'agenda', 'questions']
2025-05-28 16:20:20 - MergeDataComponent - INFO - [process:380] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] All data merged successfully for request_id 38089d77-a0e5-4549-8eb1-93945a27cab0
2025-05-28 16:33:45 - MergeDataComponent - INFO - [process:241] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Processing merge data request for request_id: fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:33:45 - MergeDataComponent - INFO - [process:243] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-28 16:33:45 - MergeDataComponent - INFO - [process:257] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-28 16:33:45 - MergeDataComponent - INFO - [process:277] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Merging data for request_id fbe24603-f02f-4943-a3c4-a9a2c4e9de7c. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-28 16:33:45 - MergeDataComponent - INFO - [process:341] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Dictionary input_1 merged with deep merge strategy for request_id fbe24603-f02f-4943-a3c4-a9a2c4e9de7c. Current keys: ['value', 'marketing']
2025-05-28 16:33:45 - MergeDataComponent - INFO - [process:380] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] All data merged successfully for request_id fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
