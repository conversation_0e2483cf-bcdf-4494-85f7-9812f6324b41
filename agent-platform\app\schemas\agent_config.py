from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from autogen_core.memory import ListMemory


class ToolConfig(BaseModel):
    name: str = Field(..., description="Name of the tool")
    description: str = Field(..., description="Description of the tool")
    parameters: Dict[str, Any] = Field(
        default_factory=dict, description="Tool parameters"
    )

    model_config = {"arbitrary_types_allowed": True}


class AgentTool(BaseModel):
    tool_type: str = Field(..., description="Type of the tool (e.g., workflow)")
    url: str = Field(..., description="URL to execute the tool")
    workflow: Dict[str, Any] = Field(
        ...,
        description="Workflow details including ID, approval, description, and payload",
    )


class AgentConfig(BaseModel):
    name: str
    description: str
    agent_type: str
    ai_model_config: Dict[str, Any] = Field(
        ...,  # Makes the field required
        description="Model configuration containing model name and API key",
        example={"model": "gpt-4", "api_key": "your-api-key-here"},
    )
    system_message: str
    tools: List[AgentTool] = Field(
        default_factory=list,
        description="List of tools available to the agent",
        example=[
            {
                "tool_type": "workflow",
                "url": "http://localhost:5000/execute-by-name",
                "workflow": {
                    "workflow_id": "data_processing_test_workflow",
                    "approval": "False",
                    "description": "Generate a script, audio, and images for a marketing video",
                    "payload": {
                        "user_dependent_fields": ["topic", "video_type", "keywords"],
                        "user_payload_template": {
                            "topic": "",
                            "video_type": "",
                            "keywords": "",
                        },
                    },
                },
            }
        ],
    )
    memory_enabled: bool = False
    memory_config: Optional[Dict[str, Any]] = None

    model_config = {
        "arbitrary_types_allowed": True,
        "json_schema_extra": {
            "examples": [
                {
                    "name": "Assistant",
                    "description": "A helpful AI assistant",
                    "agent_type": "autogen",
                    "ai_model_config": {
                        "model": "gpt-4",
                        "api_key": "your-api-key-here",
                    },
                    "system_message": "You are a helpful AI assistant",
                    "tools": [
                        {
                            "tool_type": "workflow",
                            "url": "http://localhost:5000/execute-by-name",
                            "workflow": {
                                "workflow_id": "data_processing_test_workflow",
                                "approval": "False",
                                "description": "Generate a script, audio, and images for a marketing video",
                                "payload": {
                                    "user_dependent_fields": [
                                        "topic",
                                        "video_type",
                                        "keywords",
                                    ],
                                    "user_payload_template": {
                                        "topic": "",
                                        "video_type": "",
                                        "keywords": "",
                                    },
                                },
                            },
                        }
                    ],
                    "memory_enabled": True,
                    "memory_config": {"initial_entries": []},
                }
            ]
        },
    }


class ChatMessage(BaseModel):
    role: str = Field(
        ..., description="Role of the message sender (user/assistant/system)"
    )
    content: str = Field(..., description="Content of the message")
    timestamp: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

    model_config = {"arbitrary_types_allowed": True}


class ChatRequest(BaseModel):
    message: str = Field(..., description="Message content")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )

    model_config = {"arbitrary_types_allowed": True}


class ChatResponse(BaseModel):
    response: str = Field(..., description="Response from the agent")
    session_id: str = Field(..., description="Session identifier")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Response metadata"
    )

    model_config = {"arbitrary_types_allowed": True}


class ChatSession(BaseModel):
    session_id: str
    agent_id: str
    # agent_config_json: AgentConfig
    memory: Optional[ListMemory] = None
    # created_at: Optional[str] = None

    model_config = {
        "arbitrary_types_allowed": True,  # Allow arbitrary types like ListMemory
        "json_schema_extra": {
            "examples": [
                {
                    "session_id": "123e4567-e89b-12d3-a456-426614174000",
                    "agent_id": "assistant_1",
                    "memory": None,
                }
            ]
        },
    }
