from enum import Enum


# Enum for channel types
class ChannelType(str, Enum):
    CHANNEL_TYPE_UNSPECIFIED = "CHANNEL_TYPE_UNSPECIFIED"
    CHANNEL_TYPE_WEB = "CHANNEL_TYPE_WEB"


# Mapping dictionaries for conversion between string and int
CHANNEL_TYPE_TO_INT = {
    ChannelType.CHANNEL_TYPE_UNSPECIFIED: 0,
    ChannelType.CHANNEL_TYPE_WEB: 1,
}

INT_TO_CHANNEL_TYPE = {
    0: ChannelType.CHANNEL_TYPE_UNSPECIFIED,
    1: ChannelType.CHANNEL_TYPE_WEB,
}


# Enum for sender type
class SenderType(str, Enum):
    SENDER_TYPE_UNSPECIFIED = "SENDER_TYPE_UNSPECIFIED"
    SENDER_TYPE_USER = "SENDER_TYPE_USER"
    SENDER_TYPE_ASSISTANT = "SENDER_TYPE_ASSISTANT"


# Mapping dictionaries for sender type
SENDER_TYPE_TO_INT = {
    SenderType.SENDER_TYPE_UNSPECIFIED: 0,
    SenderType.SENDER_TYPE_USER: 1,
    SenderType.SENDER_TYPE_ASSISTANT: 2,
}

INT_TO_SENDER_TYPE = {
    0: SenderType.SENDER_TYPE_UNSPECIFIED,
    1: SenderType.SENDER_TYPE_USER,
    2: SenderType.SENDER_TYPE_ASSISTANT,
}


# Enum for chat types
class ChatType(str, Enum):
    CHAT_TYPE_UNSPECIFIED = "CHAT_TYPE_UNSPECIFIED"
    CHAT_TYPE_SINGLE = "CHAT_TYPE_SINGLE"
    CHAT_TYPE_MULTI = "CHAT_TYPE_MULTI"


# Mapping dictionaries for chat type
CHAT_TYPE_TO_INT = {
    ChatType.CHAT_TYPE_UNSPECIFIED: 0,
    ChatType.CHAT_TYPE_SINGLE: 1,
    ChatType.CHAT_TYPE_MULTI: 2,
}

INT_TO_CHAT_TYPE = {
    0: ChatType.CHAT_TYPE_UNSPECIFIED,
    1: ChatType.CHAT_TYPE_SINGLE,
    2: ChatType.CHAT_TYPE_MULTI,
}
