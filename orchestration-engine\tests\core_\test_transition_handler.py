import pytest
import as<PERSON><PERSON>
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from app.core_.transition_handler import TransitionHandler
from app.utils.enhanced_logger import get_logger
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.services.node_executor import NodeExecutor


class TestTransitionHandler:
    """
    Test suite for TransitionHandler class.
    Covers transition execution, routing, and workflow control.
    """

    @pytest.fixture
    def mock_state_manager(self):
        """
        Provides a mock StateManager for testing.
        """
        mock = Mock()
        mock.completed_transitions = set()
        mock.workflow_paused = False
        mock.mark_transition_completed = Mock()
        return mock

    @pytest.fixture
    def mock_tool_executor(self):
        """
        Provides a mock ToolExecutor (KafkaToolExecutor) for testing.
        """
        mock = AsyncMock(spec=KafkaToolExecutor)
        mock.execute_tool = AsyncMock(
            return_value={"status": "success", "result": "test result"}
        )
        return mock

    @pytest.fixture
    def mock_node_executor(self):
        """
        Provides a mock NodeExecutor for testing.
        """
        mock = AsyncMock(spec=NodeExecutor)
        mock.execute_tool = AsyncMock(
            return_value={"status": "success", "result": "node execution result"}
        )
        return mock

    @pytest.fixture
    def mock_workflow_utils(self):
        """
        Provides a mock WorkflowUtils for testing.
        """
        mock = Mock()
        mock._evaluate_switch_case = AsyncMock()
        return mock

    @pytest.fixture
    def basic_transition_handler(
        self,
        mock_state_manager,
        mock_tool_executor,
        mock_node_executor,
        mock_workflow_utils,
    ):
        """
        Provides a basic TransitionHandler instance for testing.
        """
        transitions_by_id = {
            "trans1": {
                "id": "trans1",
                "transition_type": "initial",
                "sequence": 1,
                "node_info": {
                    "node_id": "node1",
                    "server_tools": [{"tool_name": "test_tool"}],
                },
            }
        }
        nodes = {
            "node1": {
                "id": "node1",
                "server_script_path": "test/path",
                "server_tools": [{"tool_name": "test_tool"}],
            }
        }
        dependency_map = {}

        # Create a mock result_callback
        mock_result_callback = AsyncMock()

        return TransitionHandler(
            state_manager=mock_state_manager,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            dependency_map=dependency_map,
            workflow_utils=mock_workflow_utils,
            tool_executor=mock_tool_executor,
            node_executor=mock_node_executor,
            result_callback=mock_result_callback,
        )

    def test_initialization(self, basic_transition_handler):
        """
        Test successful initialization of TransitionHandler.
        """
        assert basic_transition_handler.current_transition_id is None
        assert basic_transition_handler.approval is False
        assert basic_transition_handler.workflow_paused is False
        assert isinstance(basic_transition_handler._pause_event, asyncio.Event)

    def test_find_initial_transition_with_initial_type(self, basic_transition_handler):
        """
        Test finding initial transition when transition_type='initial' is present.
        """
        # Arrange
        basic_transition_handler.transitions_by_id = {
            "trans1": {"id": "trans1", "transition_type": "initial", "sequence": 2},
            "trans2": {"id": "trans2", "transition_type": "standard", "sequence": 1},
        }

        # Act
        initial_transition = basic_transition_handler._find_initial_transition()

        # Assert
        assert initial_transition["id"] == "trans1"
        assert initial_transition["transition_type"] == "initial"

    def test_find_initial_transition_by_sequence(self, basic_transition_handler):
        """
        Test finding initial transition by sequence when no initial type is present.
        """
        # Arrange
        basic_transition_handler.transitions_by_id = {
            "trans1": {"id": "trans1", "transition_type": "standard", "sequence": 2},
            "trans2": {"id": "trans2", "transition_type": "standard", "sequence": 1},
        }

        # Act
        initial_transition = basic_transition_handler._find_initial_transition()

        # Assert
        assert initial_transition["id"] == "trans2"
        assert initial_transition["sequence"] == 1

    def test_find_initial_transition_empty_transitions(self, basic_transition_handler):
        """
        Test finding initial transition with empty transitions dictionary.
        """
        # Arrange
        basic_transition_handler.transitions_by_id = {}

        # Act & Assert
        with pytest.raises(Exception, match="No initial transition found"):
            basic_transition_handler._find_initial_transition()

    @pytest.mark.asyncio
    async def test_execute_transition_with_tracking_success(
        self, basic_transition_handler
    ):
        """
        Test successful execution of transition with tracking.
        """
        # Arrange
        transition = {
            "id": "trans1",
            "transition_type": "standard",
            "node_info": {
                "node_id": "node1",
                "server_tools": [{"tool_name": "test_tool"}],
            },
        }
        basic_transition_handler._execute_standard_or_reflection_transition = AsyncMock(
            return_value=["next_trans"]
        )

        # Act
        result = await basic_transition_handler._execute_transition_with_tracking(
            transition
        )

        # Assert
        assert result == ["next_trans"]
        basic_transition_handler._execute_standard_or_reflection_transition.assert_called_once_with(
            transition
        )

    @pytest.mark.asyncio
    async def test_execute_transition_with_tracking_error(
        self, basic_transition_handler
    ):
        """
        Test transition execution with tracking when an error occurs.
        """
        # Arrange
        transition = {
            "id": "trans1",
            "transition_type": "standard",
            "node_info": {
                "node_id": "node1",
                "server_tools": [{"tool_name": "test_tool"}],
            },
        }
        error_msg = "Test execution error"
        basic_transition_handler._execute_standard_or_reflection_transition = AsyncMock(
            side_effect=Exception(error_msg)
        )

        # Act & Assert
        with pytest.raises(Exception, match=error_msg):
            await basic_transition_handler._execute_transition_with_tracking(transition)

    @pytest.mark.asyncio
    async def test_execute_standard_transition(self, basic_transition_handler):
        """
        Test execution of a standard transition.
        """
        # Arrange
        transition = {
            "id": "trans1",
            "transition_type": "standard",
            "execution_type": "MCP",  # Explicitly set execution_type to MCP
            "node_info": {
                "node_id": "node1",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
            },
        }
        basic_transition_handler.tool_executor.execute_tool.return_value = {
            "status": "success",
            "result": "test result",
        }
        # Mock the formatting method
        basic_transition_handler.workflow_utils._format_tool_parameters = AsyncMock(
            return_value={"topic": "test topic", "keywords": {"time": "30seconds"}}
        )

        # Act
        result = (
            await basic_transition_handler._execute_standard_or_reflection_transition(
                transition
            )
        )

        # Assert
        assert isinstance(result, list)
        basic_transition_handler.state_manager.mark_transition_completed.assert_called_once()
        # Verify that the format method was called
        basic_transition_handler.workflow_utils._format_tool_parameters.assert_called_once()
        # Verify that the tool executor was called with the correct parameters
        basic_transition_handler.tool_executor.execute_tool.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_reflection_logic(self, basic_transition_handler):
        """
        Test handling of reflection logic in transitions.
        """
        # Arrange
        transition = {
            "id": "trans1",
            "transition_type": "reflection",
            "reflection": {"iteration_count": 0, "max_iterations": 2},
            "node_info": {
                "node_id": "node1",
                "server_tools": [{"tool_name": "test_tool"}],
            },
        }
        basic_transition_handler._execute_standard_or_reflection_transition = AsyncMock(
            return_value=["next_trans"]
        )

        # Act
        result = await basic_transition_handler._handle_reflection_logic(transition)

        # Assert
        assert result == ["next_trans"]
        assert transition["reflection"]["iteration_count"] == 1

    def test_resolve_next_transition_with_reflection(self, basic_transition_handler):
        """
        Test resolution of next transitions with reflection transitions present.
        """
        # Arrange
        basic_transition_handler.transitions_by_id = {
            "trans1": {"id": "trans1", "transition_type": "reflection", "sequence": 2},
            "trans2": {"id": "trans2", "transition_type": "standard", "sequence": 1},
        }
        next_transition_candidates = ["trans1", "trans2"]

        # Act
        result = basic_transition_handler._resolve_next_transition(
            next_transition_candidates
        )

        # Assert
        assert result == ["trans1", "trans2"]

    @pytest.mark.asyncio
    async def test_regenerate_transition(self, basic_transition_handler):
        """
        Test regeneration of a specific transition.
        """
        # Arrange
        transition_id = "trans1"
        basic_transition_handler.state_manager.reset_to_transition.return_value = True
        basic_transition_handler._execute_standard_or_reflection_transition = AsyncMock(
            return_value=[]
        )

        # Act
        result = await basic_transition_handler.regenerate_transition(
            transition_id, "re-execute"
        )

        # Assert
        assert result is True
        basic_transition_handler.state_manager.reset_to_transition.assert_called_once()
        basic_transition_handler._execute_standard_or_reflection_transition.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_executor_for_type_mcp_server(self, basic_transition_handler):
        """
        Test getting the correct executor for MCP server execution type.
        """
        # Act
        executor = basic_transition_handler._get_executor_for_type("MCP server")

        # Assert
        assert executor == basic_transition_handler.tool_executor

    @pytest.mark.asyncio
    async def test_get_executor_for_type_api_request(self, basic_transition_handler):
        """
        Test getting the correct executor for API request execution type.
        """
        # Act
        executor = basic_transition_handler._get_executor_for_type("API request")

        # Assert
        assert executor == basic_transition_handler.node_executor

    @pytest.mark.asyncio
    async def test_get_executor_for_type_database_operation(
        self, basic_transition_handler
    ):
        """
        Test getting the correct executor for Database Operation execution type.
        """
        # Act
        executor = basic_transition_handler._get_executor_for_type("Database Operation")

        # Assert
        assert executor == basic_transition_handler.node_executor

    @pytest.mark.asyncio
    async def test_get_executor_for_type_function_call(self, basic_transition_handler):
        """
        Test getting the correct executor for Function Call execution type.
        """
        # Act
        executor = basic_transition_handler._get_executor_for_type("Function Call")

        # Assert
        assert executor == basic_transition_handler.node_executor

    @pytest.mark.asyncio
    async def test_get_executor_for_type_unknown(self, basic_transition_handler):
        """
        Test getting the fallback executor for unknown execution type.
        """
        # Act
        executor = basic_transition_handler._get_executor_for_type("Unknown Type")

        # Assert
        assert executor == basic_transition_handler.tool_executor

    @pytest.mark.asyncio
    async def test_get_executor_for_type_no_node_executor(self):
        """
        Test getting the fallback executor when node executor is not available.
        """
        # Arrange
        mock_state_manager = Mock()
        mock_tool_executor = AsyncMock()
        mock_workflow_utils = Mock()

        # Create a handler without node_executor
        handler = TransitionHandler(
            state_manager=mock_state_manager,
            transitions_by_id={},
            nodes={},
            dependency_map={},
            workflow_utils=mock_workflow_utils,
            tool_executor=mock_tool_executor,
        )

        # Act
        executor = handler._get_executor_for_type("API request")

        # Assert
        assert executor == mock_tool_executor

    @pytest.mark.asyncio
    async def test_execute_standard_transition_with_node_executor(
        self, basic_transition_handler
    ):
        """
        Test execution of a standard transition using the node executor.
        """
        # Arrange
        transition = {
            "id": "trans1",
            "transition_type": "standard",
            "execution_type": "API request",
            "node_info": {
                "node_id": "node1",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
            },
        }
        basic_transition_handler.node_executor.execute_tool.return_value = {
            "status": "success",
            "result": "node execution result",
        }
        # Mock the formatting method
        basic_transition_handler.workflow_utils._format_tool_parameters = AsyncMock(
            return_value={"param1": "value1"}
        )

        # Act
        result = (
            await basic_transition_handler._execute_standard_or_reflection_transition(
                transition
            )
        )

        # Assert
        assert isinstance(result, list)
        basic_transition_handler.state_manager.mark_transition_completed.assert_called_once()
        # Verify that the regular format method was called
        basic_transition_handler.workflow_utils._format_tool_parameters.assert_called_once()
        # Verify that the MCP executor method was not called
        basic_transition_handler.workflow_utils._format_tool_parameters_for_mcp_executor.assert_not_called()
        # Verify that the node executor was called with the correct parameters
        basic_transition_handler.node_executor.execute_tool.assert_called_once_with(
            "test/path", "test_tool", {"param1": "value1"}
        )

    @pytest.mark.asyncio
    async def test_execute_standard_transition_with_conditional_routing(
        self, basic_transition_handler
    ):
        """
        Test execution of a standard transition with conditional routing.
        """
        # Arrange
        transition = {
            "id": "trans1",
            "transition_type": "standard",
            "execution_type": "MCP",  # Explicitly set execution_type to MCP
            "node_info": {
                "node_id": "node1",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
                "output_data": [{"to_transition_id": "trans2"}],
            },
            "conditional_routing": {"cases": [{"next_transition": "trans3"}]},
        }
        basic_transition_handler.tool_executor.execute_tool.return_value = {
            "status": "success",
            "result": "test result",
        }
        # Mock the formatting method
        basic_transition_handler.workflow_utils._format_tool_parameters = AsyncMock(
            return_value={"topic": "test topic", "keywords": {"time": "30seconds"}}
        )
        basic_transition_handler.workflow_utils._evaluate_switch_case = AsyncMock(
            return_value=["trans3"]
        )

        # Act
        result = (
            await basic_transition_handler._execute_standard_or_reflection_transition(
                transition
            )
        )

        # Assert
        assert "trans3" in result
        assert (
            "trans2" in result
        )  # Output transitions are now included regardless of conditional nodes
        basic_transition_handler.workflow_utils._evaluate_switch_case.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_standard_transition_with_multiple_matching_conditions(
        self, basic_transition_handler
    ):
        """
        Test execution of a standard transition with multiple matching conditions in conditional routing.
        """
        # Arrange
        transition = {
            "id": "trans1",
            "transition_type": "standard",
            "execution_type": "MCP",  # Explicitly set execution_type to MCP
            "node_info": {
                "node_id": "node1",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
                "output_data": [{"to_transition_id": "trans4"}],
            },
            "conditional_routing": {
                "cases": [{"next_transition": "trans2"}, {"next_transition": "trans3"}]
            },
        }
        basic_transition_handler.tool_executor.execute_tool.return_value = {
            "status": "success",
            "result": "test result",
        }
        # Mock the formatting method
        basic_transition_handler.workflow_utils._format_tool_parameters = AsyncMock(
            return_value={"topic": "test topic", "keywords": {"time": "30seconds"}}
        )
        # Return multiple matching transitions
        basic_transition_handler.workflow_utils._evaluate_switch_case = AsyncMock(
            return_value=["trans2", "trans3"]
        )

        # Act
        result = (
            await basic_transition_handler._execute_standard_or_reflection_transition(
                transition
            )
        )

        # Assert
        assert "trans2" in result
        assert "trans3" in result
        assert "trans4" in result  # Should be included as it's not in conditional nodes
        basic_transition_handler.workflow_utils._evaluate_switch_case.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_standard_transition_with_error(
        self, basic_transition_handler
    ):
        """
        Test execution of a standard transition when the tool execution fails.
        """
        # Arrange
        transition = {
            "id": "trans1",
            "transition_type": "standard",
            "execution_type": "MCP",  # Explicitly set execution_type to MCP
            "node_info": {
                "node_id": "node1",
                "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
            },
        }
        basic_transition_handler.tool_executor.execute_tool = AsyncMock(
            return_value={"status": "error", "error": "execution failed"}
        )
        # Mock the formatting method
        basic_transition_handler.workflow_utils._format_tool_parameters = AsyncMock(
            return_value={"topic": "test topic", "keywords": {"time": "30seconds"}}
        )

        # Act & Assert
        with pytest.raises(Exception, match="Tool execution error: execution failed"):
            await basic_transition_handler._execute_standard_or_reflection_transition(
                transition
            )

    @pytest.mark.asyncio
    async def test_handle_reflection_logic_max_iterations(
        self, basic_transition_handler
    ):
        """
        Test handling of reflection logic when max iterations is reached.
        """
        # Arrange
        transition = {
            "id": "trans1",
            "transition_type": "reflection",
            "reflection": {"iteration_count": 2, "max_iterations": 2},
            "node_info": {
                "node_id": "node1",
                "server_tools": [{"tool_name": "test_tool"}],
            },
        }
        basic_transition_handler._execute_standard_or_reflection_transition = AsyncMock(
            return_value=["next_trans"]
        )

        # Act
        result = await basic_transition_handler._handle_reflection_logic(transition)

        # Assert
        assert result == ["next_trans"]
        assert transition["reflection"]["iteration_count"] == 3
        basic_transition_handler._execute_standard_or_reflection_transition.assert_called_once_with(
            transition
        )

    @pytest.mark.asyncio
    async def test_resolve_next_transition_no_candidates(
        self, basic_transition_handler
    ):
        """
        Test resolution of next transitions with no candidates.
        """
        # Act
        result = basic_transition_handler._resolve_next_transition([])

        # Assert
        assert result == []

    @pytest.mark.asyncio
    async def test_resolve_next_transition_invalid_id(self, basic_transition_handler):
        """
        Test resolution of next transitions with invalid transition ID.
        """
        # Arrange
        basic_transition_handler.transitions_by_id = {
            "trans1": {"id": "trans1", "transition_type": "standard", "sequence": 1},
        }
        next_transition_candidates = ["trans1", "invalid_id"]

        # Act
        result = basic_transition_handler._resolve_next_transition(
            next_transition_candidates
        )

        # Assert
        assert result == ["trans1"]

    @pytest.mark.asyncio
    async def test_regenerate_transition_invalid_id(self, basic_transition_handler):
        """
        Test regeneration of a transition with invalid ID.
        """
        # Act
        result = await basic_transition_handler.regenerate_transition(
            "invalid_id", "re-execute"
        )

        # Assert
        assert result is False
