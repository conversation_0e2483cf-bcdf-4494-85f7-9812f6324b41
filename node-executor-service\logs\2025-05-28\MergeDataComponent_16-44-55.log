2025-05-28 16:44:55 - MergeDataComponent - INFO - [setup_logger:467] Logger MergeDataComponent configured with log file: logs\2025-05-28\MergeDataComponent_16-44-55.log
2025-05-28 16:45:02 - MergeDataComponent - INFO - [__init__:96] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] MergeDataExecutor initialized
2025-05-28 16:45:02 - MergeDataComponent - INFO - [process:241] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Processing merge data request for request_id: fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:45:02 - MergeDataComponent - INFO - [process:243] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-28 16:45:02 - MergeDataComponent - INFO - [process:257] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-28 16:45:02 - MergeDataComponent - INFO - [process:277] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Merging data for request_id fbe24603-f02f-4943-a3c4-a9a2c4e9de7c. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-28 16:45:02 - MergeDataComponent - INFO - [process:341] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Dictionary input_1 merged with deep merge strategy for request_id fbe24603-f02f-4943-a3c4-a9a2c4e9de7c. Current keys: ['value', 'marketing']
2025-05-28 16:45:02 - MergeDataComponent - INFO - [process:380] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] All data merged successfully for request_id fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:45:49 - MergeDataComponent - INFO - [process:241] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Processing merge data request for request_id: 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
2025-05-28 16:45:49 - MergeDataComponent - INFO - [process:243] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-28 16:45:49 - MergeDataComponent - INFO - [process:257] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-28 16:45:49 - MergeDataComponent - INFO - [process:277] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Merging data for request_id 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-28 16:45:49 - MergeDataComponent - INFO - [process:341] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Dictionary input_1 merged with deep merge strategy for request_id 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1. Current keys: ['value', 'marketing']
2025-05-28 16:45:49 - MergeDataComponent - INFO - [process:380] [ReqID:553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] All data merged successfully for request_id 553e6bdc-a7bc-44bb-be05-bd8b16e6c8e1
