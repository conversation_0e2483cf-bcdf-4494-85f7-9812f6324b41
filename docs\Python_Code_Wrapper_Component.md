# Python Code Wrapper Component - Implementation Plan

## Current State Assessment

### Existing Architecture Patterns

#### Workflow Service (BaseNode Pattern)
- **Component Structure**: All components inherit from `BaseNode` with ClassVar attributes
- **Input System**: Uses `create_dual_purpose_input()` for flexible input handling
- **Execution Pattern**: Modern `execute()` method returns `NodeResult` with `tool_parameters`
- **Context Handling**: `WorkflowContext` provides input access via `get_input_value()`
- **Registration**: Automatic discovery and registration via component service

#### Node Executor Service (BaseComponent Pattern)  
- **Component Structure**: Components inherit from `BaseComponent` with `@register_component` decorator
- **Processing Pattern**: `process()` method handles `tool_parameters` from payload
- **Validation**: Optional `validate()` method with `ValidationResult`
- **Communication**: Kafka-based messaging with request/response pattern
- **Error Handling**: Structured error responses with logging

#### Data Flow Pattern
1. **Workflow Service**: Validates inputs → packages `tool_parameters` → returns `NodeResult`
2. **Orchestration Engine**: Receives `tool_parameters` → adds `request_id` → sends via Kafka
3. **Node Executor Service**: Receives payload → extracts `tool_parameters` → executes → returns result

### Current Dependencies
- **Workflow Service**: Standard Python libraries, no RestrictedPython
- **Node Executor Service**: Basic dependencies, no RestrictedPython or psutil

## Implementation Strategy

### Phase 1: Basic RestrictedPython Execution (TDD Approach)

#### Test-First Development Plan
1. **Write Tests First**: Create comprehensive test suites before implementation
2. **Minimal Implementation**: Write just enough code to make tests pass
3. **Refactor**: Improve code quality while maintaining test coverage
4. **Iterate**: Add features incrementally with tests

#### Security Approach
- **RestrictedPython**: Primary sandboxing mechanism
- **Resource Limits**: Basic timeout and memory monitoring
- **Safe Globals**: Curated set of allowed functions and modules
- **No Docker**: Phase 1 focuses on RestrictedPython-only security

## Technical Specifications

### Component Interface

#### Workflow Service Component
```python
class PythonCodeWrapperComponent(BaseNode):
    name: ClassVar[str] = "PythonCodeWrapperComponent"
    display_name: ClassVar[str] = "Python Code Wrapper"
    category: ClassVar[str] = "Processing"
    
    inputs: ClassVar[List[InputBase]] = [
        create_dual_purpose_input(
            name="python_code",
            display_name="Python Code",
            input_type="code",
            required=True,
            info="Python code to execute. Use 'inputs' dict for input data and assign results to 'result' variable."
        ),
        create_dual_purpose_input(
            name="input_data", 
            display_name="Input Data",
            input_type="dict",
            required=False,
            info="Data to pass to the Python code as 'inputs' variable."
        ),
        IntInput(
            name="timeout_seconds",
            display_name="Timeout (seconds)", 
            value=5,
            info="Maximum execution time (1-30 seconds)."
        )
    ]
    
    outputs: ClassVar[List[Output]] = [
        Output(name="result", display_name="Execution Result", output_type="Any"),
        Output(name="output_logs", display_name="Output Logs", output_type="string"),
        Output(name="execution_time", display_name="Execution Time (s)", output_type="float"),
        Output(name="error", display_name="Error", output_type="string")
    ]
```

#### Node Executor Service Component
```python
@register_component("PythonCodeWrapperComponent")
class PythonCodeWrapperExecutor(BaseComponent):
    def __init__(self):
        super().__init__()
        self._setup_restricted_environment()
    
    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        # Extract tool_parameters
        # Execute code with RestrictedPython
        # Return structured result
```

### Security Architecture

#### RestrictedPython Configuration
- **Compilation**: Use `compile_restricted()` for safe code compilation
- **Execution Environment**: Curated `safe_globals` with limited builtins
- **Allowed Modules**: Pre-imported safe modules (json, re, math, datetime, collections)
- **Resource Monitoring**: Basic timeout and memory tracking with psutil

#### Execution Sandbox
```python
# Safe execution environment
safe_globals = {
    '__builtins__': limited_builtins,
    'inputs': user_input_data,
    'result': None,
    'json': json_module,
    'math': math_module,
    # ... other safe modules
}
```

## Implementation Phases

### Phase 1: Core Functionality (Week 1)
- [ ] Add RestrictedPython and psutil dependencies
- [ ] Implement basic workflow service component
- [ ] Implement node executor with RestrictedPython
- [ ] Basic error handling and logging
- [ ] Comprehensive test suite

### Phase 2: Enhanced Security (Week 2)  
- [ ] Advanced resource monitoring
- [ ] Enhanced error categorization
- [ ] Security policy configuration
- [ ] Performance optimizations

### Phase 3: Advanced Features (Week 3)
- [ ] Multiple Python version support
- [ ] Enhanced debugging capabilities
- [ ] Caching mechanisms
- [ ] Metrics and monitoring

### Phase 4: Docker Integration (Future)
- [ ] Container-based execution
- [ ] Advanced isolation
- [ ] Resource quotas
- [ ] Image management

## Dependencies & Setup

### Required Dependencies
```toml
# workflow-service/pyproject.toml - No changes needed

# node-executor-service/pyproject.toml
[tool.poetry.dependencies]
RestrictedPython = "^6.0"
psutil = "^5.9.8"
```

### Component Registration
- **Workflow Service**: Add to `processing/__init__.py`
- **Node Executor Service**: Add to `components/__init__.py`

## Testing Plan

### Test Structure
```
workflow-service/tests/components/
├── test_python_code_wrapper_component.py
└── processing/
    └── test_python_code_wrapper.py

node-executor-service/tests/
├── test_python_code_wrapper_executor.py
└── components/
    └── test_python_code_wrapper_component.py
```

### Test Categories
1. **Unit Tests**: Component initialization, input validation, basic execution
2. **Integration Tests**: End-to-end workflow execution
3. **Security Tests**: Sandboxing, resource limits, malicious code handling
4. **Performance Tests**: Execution time, memory usage, timeout handling
5. **Error Tests**: Invalid code, runtime errors, timeout scenarios

### Test Data Examples
```python
# Valid code examples
VALID_CODE_EXAMPLES = [
    "result = inputs.get('value', 0) * 2",
    "result = {'processed': True, 'data': inputs}",
    "result = [x * 2 for x in inputs.get('numbers', [])]"
]

# Invalid/malicious code examples  
INVALID_CODE_EXAMPLES = [
    "import os; os.system('rm -rf /')",  # System access
    "open('/etc/passwd', 'r')",         # File access
    "while True: pass",                 # Infinite loop
    "__import__('subprocess')",         # Import restriction
]
```

## Integration Points

### Orchestration Engine Integration
- **Request Format**: Standard tool_parameters structure
- **Response Format**: Consistent with other components
- **Error Handling**: Structured error responses
- **Timeout Management**: Coordinated timeout handling

### Kafka Communication
- **Topics**: Uses existing component topic structure
- **Message Format**: Standard payload with tool_parameters
- **Error Propagation**: Structured error responses via Kafka

## Success Criteria

### Functional Requirements
- [ ] Execute Python code safely using RestrictedPython
- [ ] Handle various input data types (dict, list, string, numbers)
- [ ] Provide structured output (result, logs, execution_time, error)
- [ ] Enforce timeout limits (1-30 seconds)
- [ ] Integrate seamlessly with existing workflow patterns

### Security Requirements
- [ ] Prevent file system access
- [ ] Prevent network access
- [ ] Prevent dangerous imports
- [ ] Enforce resource limits
- [ ] Handle malicious code gracefully

### Performance Requirements
- [ ] Execute simple code within 100ms overhead
- [ ] Handle concurrent executions
- [ ] Respect memory limits
- [ ] Provide accurate timing metrics

### Quality Requirements
- [ ] 90%+ test coverage
- [ ] Comprehensive error handling
- [ ] Clear logging and debugging
- [ ] Documentation and examples
- [ ] Consistent with platform patterns

## Risk Mitigation

### Security Risks
- **Sandbox Escape**: Multiple layers (RestrictedPython + resource limits)
- **Resource Exhaustion**: Timeout and memory monitoring
- **Malicious Code**: Comprehensive testing with attack vectors

### Performance Risks
- **Memory Leaks**: Proper cleanup and monitoring
- **Blocking Operations**: Async execution with timeouts
- **Scalability**: Resource pooling and limits

### Integration Risks
- **Breaking Changes**: Comprehensive integration tests
- **Dependency Conflicts**: Careful dependency management
- **Version Compatibility**: Testing across Python versions

## Next Steps

1. **Setup Dependencies**: Add RestrictedPython and psutil to node-executor-service
2. **Create Test Files**: Implement comprehensive test suites first (TDD)
3. **Implement Components**: Build minimal implementations to pass tests
4. **Integration Testing**: Test end-to-end workflow execution
5. **Security Validation**: Test with malicious code examples
6. **Performance Optimization**: Profile and optimize execution
7. **Documentation**: Create user guides and examples
