from fastapi import APIRouter
from app.services.health_check_service import HealthService
from typing import Dict, Any, List

health_router = APIRouter(tags=["health"])

@health_router.get(
    "/health",
    summary="Get basic health status",
    description="Returns a simple health status of the API Gateway",
    response_model=Dict[str, str]
)
async def health_check() -> Dict[str, str]:
    """Basic health check endpoint."""
    return {"status": "healthy"}

@health_router.get(
    "/health/services",
    summary="Get health status of all services",
    description="Checks the health of all microservices and returns their status",
    response_model=Dict[str, Any]
)
async def check_all_services() -> Dict[str, Any]:
    """Check the health of all microservices."""
    return HealthService.check_all_services()

@health_router.get(
    "/health/services/{service_name}",
    summary="Get health status of a specific service",
    description="Checks the health of a specific microservice and returns its status",
    response_model=Dict[str, Any]
)
async def check_specific_service(service_name: str) -> Dict[str, Any]:
    """
    Check the health of a specific microservice.
    
    Args:
        service_name: Name of the service to check (user, admin, notification, etc.)
        
    Returns:
        Dict[str, Any]: Health status of the specified service
    """
    service_checks = {
        "user": HealthService.check_user_service,
        "admin": HealthService.check_admin_service,
        "notification": HealthService.check_notification_service,
        "communication": HealthService.check_communication_service,
        "workflow": HealthService.check_workflow_service,
        "agent": HealthService.check_agent_service,
        "mcp": HealthService.check_mcp_service,
    }
    
    if service_name not in service_checks:
        return {
            "service": service_name,
            "status": "unknown",
            "error": "Service not found"
        }
    
    return service_checks[service_name]()
