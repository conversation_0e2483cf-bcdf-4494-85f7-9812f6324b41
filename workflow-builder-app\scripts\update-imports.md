# Import Pattern Update Guide

This guide provides steps to manually update import patterns in the codebase to follow the new conventions defined in `docs/import-conventions.md`.

## Steps to Update Imports

1. **Install ESLint Extension** in your IDE (if not already installed)
   - For VS Code: [ESLint Extension](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)

2. **Configure Auto-Fix on Save** (VS Code)
   - Add to settings.json:
   ```json
   "editor.codeActionsOnSave": {
     "source.fixAll.eslint": true
   }
   ```

3. **Run ESLint Fix Command** to automatically fix import order issues:
   ```bash
   npm run lint:fix
   ```

4. **Manual Updates Required**:
   - Replace relative imports with alias imports where appropriate
   - Update imports to use the new path aliases
   - Group imports according to the conventions
   - Add blank lines between import groups

## Common Import Patterns to Update

### Types Imports

**Before**:
```tsx
import { User } from "../../../types";
```

**After**:
```tsx
import type { User } from "@/types";
```

### Component Imports

**Before**:
```tsx
import { Button } from "../../components/ui/button";
```

**After**:
```tsx
import { Button } from "@/components/ui/button";
```

### Utility Imports

**Before**:
```tsx
import { cn } from "../../lib/utils";
```

**After**:
```tsx
import { cn } from "@/lib/utils";
```

### API Imports

**Before**:
```tsx
import { fetchData } from "../../lib/api";
```

**After**:
```tsx
import { fetchData } from "@/lib/api";
```

## Feature-Specific Imports

For imports within a feature directory, use relative imports:

**Example**:
```tsx
// When importing from within the same feature
import { WorkflowCard } from "./components/WorkflowCard";
```

## Checking Your Progress

After updating imports in a file, verify:

1. ESLint shows no import-related warnings
2. Imports are grouped correctly with blank lines between groups
3. Path aliases are used consistently
4. The application still builds and functions correctly

## Troubleshooting

If you encounter issues after updating imports:

1. Check for typos in import paths
2. Verify that the imported module exists at the specified path
3. Restart your TypeScript server (VS Code: Ctrl+Shift+P > "TypeScript: Restart TS Server")
4. Clear Next.js cache: `npm run dev -- --clear-cache`
