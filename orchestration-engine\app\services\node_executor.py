# node_executor.py

import asyncio
import json
import uuid
from typing import Any, Dict, Optional

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer  # type: ignore
from aiokafka.errors import KafkaError  # type: ignore
from app.config.config import settings
from app.utils.enhanced_logger import get_logger

logger = get_logger("NodeExecutor")


class NodeExecutionError(Exception):
    pass


class NodeExecutor:
    def __init__(self, producer: AIOKafkaProducer):
        self.logger = logger
        if producer is None:
            raise ValueError("A running AIOKafkaProducer instance must be provided.")
        if not getattr(producer._sender, "_running", True):
            self.logger.warning("The provided Kafka Producer may not be running.")
        self.producer = producer
        self._bootstrap_servers = settings.kafka_bootstrap_servers
        self._request_topic = settings.kafka_node_execution_request_topic
        self._results_topic = settings.kafka_node_execution_result_topic

        self._consumer: Optional[AIOKafkaConsumer] = None
        self._consumer_task: Optional[asyncio.Task] = None
        self._pending_requests: Dict[str, asyncio.Future] = {}
        self._consumer_group_id = f"node-executor-consumer"
        self._current_correlation_id: Optional[str] = None
        self.logger.info("NodeExecutor initialized.")

    async def _start_internal_consumer(self):
        if self._consumer is not None:
            self.logger.warning("Internal consumer already started.")
            return

        self.logger.info("Starting NodeExecutor internal consumer...")
        try:
            self._consumer = AIOKafkaConsumer(
                self._results_topic,
                bootstrap_servers=self._bootstrap_servers,
                group_id=self._consumer_group_id,
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )
            await self._consumer.start()
            self.logger.info(
                f"Internal consumer started. Listening for results on: '{self._results_topic}', Group: '{self._consumer_group_id}'"
            )

            self._consumer_task = asyncio.create_task(
                self._consume_loop(),
                name=f"NodeExecutorConsumer-{self._consumer_group_id[:8]}",
            )
            self.logger.info("Background result consumer loop started.")

        except KafkaError as e:
            self.logger.error(f"Failed to start internal consumer: {e}", exc_info=True)
            await self._stop_internal_consumer()
            raise

    async def _stop_internal_consumer(self):
        self.logger.info("Stopping NodeExecutor internal consumer components...")

        if self._consumer_task and not self._consumer_task.done():
            self.logger.debug("Cancelling background consumer task...")
            self._consumer_task.cancel()
            try:
                await self._consumer_task
            except asyncio.CancelledError:
                self.logger.debug("Consumer task successfully cancelled.")
            except Exception as e:
                self.logger.error(
                    f"Error during consumer task cancellation: {e}", exc_info=True
                )
        self._consumer_task = None

        if self._consumer:
            self.logger.debug("Stopping internal Kafka consumer...")
            try:
                await self._consumer.stop()
                self.logger.info("Internal Kafka consumer stopped.")
            except Exception as e:
                self.logger.error(f"Error stopping consumer: {e}", exc_info=True)
            self._consumer = None

        if self._pending_requests:
            self.logger.warning(
                f"Stopping internal consumer with {len(self._pending_requests)} pending requests."
            )
            for request_id, future in self._pending_requests.items():
                if not future.done():
                    future.set_exception(
                        NodeExecutionError(
                            f"Executor stopped before result received. Cancelled request_id:{request_id}"
                        )
                    )
            self._pending_requests.clear()

        self.logger.info("NodeExecutor internal consumer stopped.")

    async def start(self):
        await self._start_internal_consumer()

    async def stop(self):
        await self._stop_internal_consumer()

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()

    def set_correlation_id(self, correlation_id: str):
        """
        Set the current correlation ID for this executor instance.
        This ID will be included in all tool execution requests.

        Args:
            correlation_id: The correlation ID to use for subsequent requests
        """
        self._current_correlation_id = correlation_id
        self.logger.debug(f"Set correlation ID to: {correlation_id}")

    async def _consume_loop(self):
        if not self._consumer:
            self.logger.error("Consumer not initialized in _consume_loop.")
            return

        try:
            while True:
                try:
                    async for msg in self._consumer:
                        self.logger.debug(
                            f"Result consumer received message: Offset={msg.offset}"
                        )
                        try:
                            result_payload = json.loads(msg.value.decode("utf-8"))
                            request_id = result_payload.get("request_id")
                            result_data = result_payload.get("result")
                            error_data = result_payload.get("error")

                            if not request_id:
                                self.logger.warning(
                                    f"Received result message without 'request_id': {result_payload}"
                                )
                                continue

                            future = self._pending_requests.pop(request_id, None)

                            if future and not future.done():
                                # Check for direct error field
                                if error_data:
                                    self.logger.warning(
                                        f"Received error response for request_id {request_id}: {error_data}"
                                    )
                                    future.set_exception(
                                        NodeExecutionError(
                                            f"Node execution failed: {error_data}"
                                        )
                                    )
                                # Check for error status
                                elif result_payload.get("status") == "error":
                                    self.logger.warning(
                                        f"Received response with error status for request_id {request_id}: {result_payload}"
                                    )
                                    # Extract error message from result if available
                                    error_msg = result_payload.get(
                                        "error", "Unknown error"
                                    )

                                    # Check for error in result
                                    if isinstance(result_data, dict):
                                        # Check for direct error field in result
                                        if "error" in result_data:
                                            error_msg = result_data["error"]
                                            # Include additional details if available
                                            if "data" in result_data and isinstance(
                                                result_data["data"], dict
                                            ):
                                                detail = result_data["data"].get(
                                                    "detail"
                                                )
                                                if detail:
                                                    error_msg = (
                                                        f"{error_msg} - {detail}"
                                                    )

                                        # Check for error in nested response field (common in API responses)
                                        elif "response" in result_data and isinstance(
                                            result_data["response"], dict
                                        ):
                                            if "error" in result_data["response"]:
                                                error_msg = result_data["response"][
                                                    "error"
                                                ]

                                            # Include status code if available
                                            if "status_code" in result_data["response"]:
                                                status_code = result_data["response"][
                                                    "status_code"
                                                ]
                                                if error_msg == "Unknown error":
                                                    error_msg = f"HTTP {status_code}"
                                                else:
                                                    error_msg = f"{error_msg} (HTTP {status_code})"

                                            # Include URL if available
                                            if "url" in result_data["response"]:
                                                url = result_data["response"]["url"]
                                                error_msg = f"{error_msg} - URL: {url}"

                                            # Include method if available
                                            if "method" in result_data["response"]:
                                                method = result_data["response"][
                                                    "method"
                                                ]
                                                error_msg = (
                                                    f"{error_msg} - Method: {method}"
                                                )

                                    future.set_exception(
                                        NodeExecutionError(
                                            f"Node execution failed: {error_msg}"
                                        )
                                    )
                                # Check for error in result
                                elif (
                                    isinstance(result_data, dict)
                                    and "error" in result_data
                                    and result_data["error"]
                                ):
                                    error_msg = result_data["error"]
                                    self.logger.warning(
                                        f"Received result with error field for request_id {request_id}: {error_msg}"
                                    )
                                    # Include additional details if available
                                    if "data" in result_data and isinstance(
                                        result_data["data"], dict
                                    ):
                                        detail = result_data["data"].get("detail")
                                        if detail:
                                            error_msg = f"{error_msg} - {detail}"
                                    future.set_exception(
                                        NodeExecutionError(
                                            f"Node execution failed: {error_msg}"
                                        )
                                    )
                                else:
                                    self.logger.debug(
                                        f"Received valid result for request_id {request_id}"
                                    )
                                    future.set_result(result_data)
                            elif future and future.done():
                                self.logger.warning(
                                    f"Received result for already completed/cancelled request_id {request_id}"
                                )
                            else:
                                self.logger.warning(
                                    f"Received result for unknown or timed-out request_id: {request_id}"
                                )

                        except json.JSONDecodeError:
                            self.logger.warning(
                                f"Could not decode JSON from results topic: {msg.value.decode('utf-8', errors='ignore')}"
                            )
                        except Exception as e:
                            self.logger.error(
                                f"Error processing result message: {e}", exc_info=True
                            )

                except asyncio.CancelledError:
                    self.logger.info("Result consumer loop cancelled.")
                    break
        except Exception as e:
            self.logger.error(
                f"Result consumer loop unexpectedly terminated: {e}", exc_info=True
            )
            if self._pending_requests:
                err = NodeExecutionError(f"Consumer loop failed: {e}")
                for req_id, fut in self._pending_requests.items():
                    if not fut.done():
                        fut.set_exception(err)

    async def execute_tool(
        self, server_script_path: str, tool_name: str, tool_parameters: dict
    ) -> Any:
        if not self._consumer or not self._consumer_task or self._consumer_task.done():
            raise RuntimeError(
                "NodeExecutor's internal consumer is not running. Call start() or use 'async with'."
            )

        if not self.producer or not getattr(self.producer._sender, "_running", True):
            raise RuntimeError(
                "The provided Kafka Producer is not running or not available."
            )

        request_id = str(uuid.uuid4())
        self.logger.info(
            f"Executing tool '{tool_name}' via Kafka (request_id: {request_id}) using provided producer."
        )

        payload = {
            "tool_name": tool_name,
            "tool_parameters": tool_parameters,
            "request_id": request_id,
        }

        # Add correlation_id to the payload if it's set
        if self._current_correlation_id:
            payload["correlation_id"] = self._current_correlation_id
            self.logger.debug(
                f"Added correlation_id {self._current_correlation_id} to payload"
            )

        future = asyncio.Future()
        self._pending_requests[request_id] = future

        try:
            self.logger.debug(
                f"Sending request to topic '{self._request_topic}': {payload}"
            )
            await self.producer.send(self._request_topic, value=payload)
            self.logger.debug(
                f"Request {request_id} sent successfully using provided producer."
            )

            self.logger.debug(
                f"Waiting indefinitely for result for request {request_id}..."
            )
            result = await future
            self.logger.info(f"Result received for request {request_id}.")
            return result

        except KafkaError as e:
            self.logger.error(
                f"Kafka error during node execution {request_id}: {e}", exc_info=True
            )
            self._pending_requests.pop(request_id, None)
            raise NodeExecutionError(
                f"Kafka error executing request {request_id}: {e}"
            ) from e
        except Exception as e:
            self.logger.error(
                f"Error during node execution {request_id}: {e}", exc_info=True
            )
            self._pending_requests.pop(request_id, None)
            raise e
