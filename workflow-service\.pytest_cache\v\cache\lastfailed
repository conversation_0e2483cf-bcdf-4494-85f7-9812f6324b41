{"app/services/workflow_builder_backend/tests/components/control_flow/test_switch_node_dynamic.py": true, "tests/test_node_combination.py": true, "tests/test_workflow_schema_converter.py": true, "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_1_initial_state": true, "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_2_adding_additional_conditions": true, "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_3_source_selection_single_change": true, "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_4_source_selection_multiple_changes": true, "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_5_change_back_to_node_output": true, "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_6_all_global_context": true}