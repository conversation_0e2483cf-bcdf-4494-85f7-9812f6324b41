from fastapi import APIRouter, Depends, Query, HTTPException
from app.core.auth_guard import role_required
from app.services.notification_service import NotificationsServiceClient
from app.schemas.notification import MarkAsSeenResponse, NotificationInfo, PaginationMetadata, PaginatedNotificationResponse
from google.protobuf.json_format import MessageToDict
import traceback

notification_router = APIRouter(prefix="/notifications", tags=["notifications"])
notifications_service = NotificationsServiceClient()

@notification_router.get("", response_model=PaginatedNotificationResponse)
async def get_user_notifications(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await notifications_service.get_user_notifications(
            user_id=current_user["user_id"],
            page=page,
            page_size=page_size
        )

        notifications = []
        for notification in response.notifications:
            notification_dict = MessageToDict(notification, preserving_proto_field_name=True)
            # Ensure seen field exists with default False
            if 'seen' not in notification_dict:
                notification_dict['seen'] = False
            notifications.append(NotificationInfo.parse_obj(notification_dict))

        metadata = PaginationMetadata(
            total=response.total,
            totalPages=response.total_pages,
            currentPage=response.page,
            pageSize=page_size,
            hasNextPage=response.page < response.total_pages,
            hasPreviousPage=response.page > 1
        )

        return PaginatedNotificationResponse(
            data=notifications,
            metadata=metadata
        )

    except Exception as e:
        print(f"[EXCEPTION] {e}")
        raise HTTPException(status_code=500, detail=str(e))

@notification_router.post("/{notification_id}/seen", response_model=MarkAsSeenResponse)
async def mark_notification_as_seen(
    notification_id: str,
    current_user: dict = Depends(role_required(["user"]))  # Fixed: using parentheses instead of square brackets
):
    """
    Mark a notification as seen for the current user.
    """
    try:
        print(f"[INFO] Marking notification as seen - ID: {notification_id}, User: {current_user['user_id']}")
        response = await notifications_service.mark_notification_as_seen(
            notification_id=notification_id,
            user_id=current_user["user_id"]
        )
        print(f"[INFO] Successfully marked notification as seen: {response}")
        return response
    except HTTPException as http_exc:
        print(f"[ERROR] HTTP exception in mark_notification_as_seen: {str(http_exc)}")
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in mark_notification_as_seen endpoint:")
        print(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}")

@notification_router.get("/{notification_id}", response_model=NotificationInfo)
async def get_notification_by_id(
    notification_id: str,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Get a specific notification by its ID.
    Only returns the notification if it belongs to the requesting user.
    """
    try:
        print(f"[INFO] Getting notification - ID: {notification_id}, User: {current_user['user_id']}")
        response = await notifications_service.get_notification_by_id(
            notification_id=notification_id,
            user_id=current_user["user_id"]
        )
        
        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)
            
        notification_dict = MessageToDict(
            response.notification, 
            preserving_proto_field_name=True
        )
        
        # Ensure seen field exists with default False
        if 'seen' not in notification_dict:
            notification_dict['seen'] = False
            
        return NotificationInfo.parse_obj(notification_dict)
        
    except HTTPException as http_exc:
        print(f"[ERROR] HTTP exception in get_notification_by_id: {str(http_exc)}")
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in get_notification_by_id endpoint:")
        print(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}")
