#!/usr/bin/env python3
"""
Manual Testing Guide for AlterMetadataComponent
This script provides interactive examples for testing the component manually.
"""
import asyncio
import json
import sys
import os

# Add workflow service to path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'workflow-service'))

from app.components.processing.alter_metadata import AlterMetadataComponent
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeStatus


def print_test_header(test_name: str):
    """Print a formatted test header."""
    print(f"\n{'='*60}")
    print(f"🧪 {test_name}")
    print(f"{'='*60}")


def print_test_result(test_name: str, success: bool, details: str = ""):
    """Print test results."""
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"\n{status}: {test_name}")
    if details:
        print(f"Details: {details}")


async def test_basic_metadata_update():
    """Test 1: Basic metadata update functionality."""
    print_test_header("Test 1: Basic Metadata Update")
    
    component = AlterMetadataComponent()
    context = WorkflowContext(workflow_id="test_basic", execution_id="exec_1")
    context.current_node_id = "node_1"
    
    # Test data
    test_data = {
        "input_metadata": {
            "document_id": "DOC-001",
            "title": "Test Document",
            "version": "1.0",
            "status": "draft"
        },
        "updates": {
            "version": "2.0",
            "status": "published",
            "last_modified": "2024-01-15"
        },
        "keys_to_remove": []
    }
    
    context.node_outputs["node_1"] = test_data
    
    print("Input:")
    print(f"  Original metadata: {test_data['input_metadata']}")
    print(f"  Updates: {test_data['updates']}")
    print(f"  Keys to remove: {test_data['keys_to_remove']}")
    
    # Execute
    result = await component.execute(context)
    
    print(f"\nExecution Status: {result.status}")
    print(f"Execution Time: {result.execution_time:.4f}s")
    
    if result.status == NodeStatus.SUCCESS:
        output = result.outputs.get('output_metadata', {})
        print(f"Output metadata: {output}")
        
        # Verify updates were applied
        expected_version = test_data['updates']['version']
        actual_version = output.get('version')
        
        success = (
            actual_version == expected_version and
            output.get('status') == 'published' and
            output.get('last_modified') == '2024-01-15' and
            output.get('document_id') == 'DOC-001'  # Original data preserved
        )
        
        print_test_result("Basic Metadata Update", success, 
                         f"Version updated from 1.0 to {actual_version}")
    else:
        print_test_result("Basic Metadata Update", False, result.error_message)
    
    return result.status == NodeStatus.SUCCESS


async def test_key_removal():
    """Test 2: Key removal functionality."""
    print_test_header("Test 2: Key Removal")
    
    component = AlterMetadataComponent()
    context = WorkflowContext(workflow_id="test_removal", execution_id="exec_2")
    context.current_node_id = "node_2"
    
    # Test data with keys to remove
    test_data = {
        "input_metadata": {
            "name": "Product A",
            "price": 99.99,
            "deprecated_field": "old_value",
            "temp_processing_id": "TEMP-123",
            "category": "electronics",
            "internal_notes": "remove this"
        },
        "updates": {},
        "keys_to_remove": ["deprecated_field", "temp_processing_id", "internal_notes"]
    }
    
    context.node_outputs["node_2"] = test_data
    
    print("Input:")
    print(f"  Original metadata: {test_data['input_metadata']}")
    print(f"  Keys to remove: {test_data['keys_to_remove']}")
    
    # Execute
    result = await component.execute(context)
    
    print(f"\nExecution Status: {result.status}")
    
    if result.status == NodeStatus.SUCCESS:
        output = result.outputs.get('output_metadata', {})
        print(f"Output metadata: {output}")
        
        # Verify keys were removed
        removed_keys_present = any(key in output for key in test_data['keys_to_remove'])
        expected_keys = {"name", "price", "category"}
        actual_keys = set(output.keys())
        
        success = not removed_keys_present and expected_keys == actual_keys
        
        print_test_result("Key Removal", success,
                         f"Removed keys: {test_data['keys_to_remove']}, Remaining: {list(actual_keys)}")
    else:
        print_test_result("Key Removal", False, result.error_message)
    
    return result.status == NodeStatus.SUCCESS


async def test_combined_operations():
    """Test 3: Combined update and removal operations."""
    print_test_header("Test 3: Combined Update and Removal")
    
    component = AlterMetadataComponent()
    context = WorkflowContext(workflow_id="test_combined", execution_id="exec_3")
    context.current_node_id = "node_3"
    
    # Test data with both updates and removals
    test_data = {
        "input_metadata": {
            "user_id": "USER-123",
            "username": "john_doe",
            "email": "<EMAIL>",
            "old_password_hash": "old_hash_123",
            "temp_session": "session_456",
            "status": "inactive",
            "created_date": "2023-01-01"
        },
        "updates": {
            "status": "active",
            "last_login": "2024-01-15",
            "password_hash": "new_hash_789",
            "updated_date": "2024-01-15"
        },
        "keys_to_remove": ["old_password_hash", "temp_session"]
    }
    
    context.node_outputs["node_3"] = test_data
    
    print("Input:")
    print(f"  Original metadata: {test_data['input_metadata']}")
    print(f"  Updates: {test_data['updates']}")
    print(f"  Keys to remove: {test_data['keys_to_remove']}")
    
    # Execute
    result = await component.execute(context)
    
    print(f"\nExecution Status: {result.status}")
    
    if result.status == NodeStatus.SUCCESS:
        output = result.outputs.get('output_metadata', {})
        print(f"Output metadata: {output}")
        
        # Verify combined operations
        updates_applied = all(
            output.get(key) == value 
            for key, value in test_data['updates'].items()
        )
        keys_removed = all(
            key not in output 
            for key in test_data['keys_to_remove']
        )
        original_preserved = (
            output.get('user_id') == 'USER-123' and
            output.get('username') == 'john_doe'
        )
        
        success = updates_applied and keys_removed and original_preserved
        
        print_test_result("Combined Operations", success,
                         f"Updates applied: {updates_applied}, Keys removed: {keys_removed}")
    else:
        print_test_result("Combined Operations", False, result.error_message)
    
    return result.status == NodeStatus.SUCCESS


async def test_error_scenarios():
    """Test 4: Error handling scenarios."""
    print_test_header("Test 4: Error Handling")
    
    component = AlterMetadataComponent()
    
    error_tests = [
        {
            "name": "Missing Input Metadata",
            "data": {
                "updates": {"version": "2.0"},
                "keys_to_remove": []
            },
            "expected_error": "missing"
        },
        {
            "name": "Invalid Input Type",
            "data": {
                "input_metadata": "not_a_dict",
                "updates": {},
                "keys_to_remove": []
            },
            "expected_error": "dictionary"
        },
        {
            "name": "Invalid Updates Type",
            "data": {
                "input_metadata": {"name": "test"},
                "updates": "not_a_dict",
                "keys_to_remove": []
            },
            "expected_error": "dictionary"
        },
        {
            "name": "Invalid Keys Type",
            "data": {
                "input_metadata": {"name": "test"},
                "updates": {},
                "keys_to_remove": "not_a_list"
            },
            "expected_error": "list"
        }
    ]
    
    all_passed = True
    
    for i, test in enumerate(error_tests, 1):
        print(f"\n--- Error Test {i}: {test['name']} ---")
        
        context = WorkflowContext(workflow_id=f"error_test_{i}", execution_id=f"exec_error_{i}")
        context.current_node_id = f"error_node_{i}"
        context.node_outputs[f"error_node_{i}"] = test['data']
        
        print(f"Input: {test['data']}")
        
        result = await component.execute(context)
        
        print(f"Status: {result.status}")
        print(f"Error message: {result.error_message}")
        
        # Check if error was detected and contains expected keyword
        error_detected = result.status == NodeStatus.ERROR
        expected_keyword_found = test['expected_error'].lower() in result.error_message.lower()
        
        test_passed = error_detected and expected_keyword_found
        all_passed = all_passed and test_passed
        
        status = "✅ PASSED" if test_passed else "❌ FAILED"
        print(f"{status}: {test['name']}")
    
    print_test_result("Error Handling", all_passed, "All error scenarios handled correctly")
    return all_passed


async def test_legacy_compatibility():
    """Test 5: Legacy build method compatibility."""
    print_test_header("Test 5: Legacy Build Method Compatibility")
    
    component = AlterMetadataComponent()
    
    # Test data
    test_data = {
        "input_metadata": {
            "name": "Legacy Test",
            "version": "1.0"
        },
        "updates": {
            "version": "2.0",
            "tested_with": "legacy_method"
        },
        "keys_to_remove": []
    }
    
    print("Input:")
    print(f"  Test data: {test_data}")
    
    # Execute using legacy build method
    result = component.build(**test_data)
    
    print(f"\nLegacy build result: {result}")
    
    if "output_metadata" in result:
        output = result["output_metadata"]
        
        # Verify legacy method works correctly
        success = (
            output.get('version') == '2.0' and
            output.get('tested_with') == 'legacy_method' and
            output.get('name') == 'Legacy Test'
        )
        
        print_test_result("Legacy Compatibility", success,
                         f"Legacy method produced correct output: {output}")
    else:
        print_test_result("Legacy Compatibility", False, 
                         f"Legacy method failed: {result.get('error', 'Unknown error')}")
        success = False
    
    return success


async def run_all_manual_tests():
    """Run all manual tests."""
    print("🚀 Starting Manual Testing Suite for AlterMetadataComponent")
    print("=" * 80)
    
    tests = [
        ("Basic Metadata Update", test_basic_metadata_update),
        ("Key Removal", test_key_removal),
        ("Combined Operations", test_combined_operations),
        ("Error Handling", test_error_scenarios),
        ("Legacy Compatibility", test_legacy_compatibility)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ EXCEPTION in {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 MANUAL TESTING SUMMARY")
    print(f"{'='*80}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All manual tests PASSED! Component is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the results above.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(run_all_manual_tests())
