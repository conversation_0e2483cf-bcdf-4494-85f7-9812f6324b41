# Python Code Wrapper Component Implementation Guide

## 1. Current System State Analysis

### 1.1 Distributed Workflow Platform Architecture

Our platform consists of five microservices that work together to provide a comprehensive workflow automation solution:

1. **Workflow Builder App** (Frontend: Next.js + React Flow)
   - Canvas-based workflow designer
   - Component sidebar with drag-and-drop functionality
   - Real-time workflow visualization and editing

2. **Workflow Service** (Backend: Python + gRPC)
   - Component definition and discovery
   - Workflow validation and management
   - Component registration and metadata

3. **API Gateway** (Backend: FastAPI + gRPC)
   - HTTP/REST API endpoints
   - Authentication and authorization
   - Request routing to appropriate services

4. **Orchestration Engine** (Backend: Python + Kafka)
   - Workflow execution orchestration
   - State management and persistence (Redis)
   - Transition handling and dependency resolution

5. **Node Executor Service** (Backend: Python + Kafka)
   - Component execution engine
   - Security sandboxing and validation
   - Result processing and error handling

### 1.2 Communication Architecture

```mermaid
graph TB
    WBA[Workflow Builder App] -->|HTTP/REST| AG[API Gateway]
    AG -->|gRPC| WS[Workflow Service]
    AG -->|Kafka| OE[Orchestration Engine]
    OE -->|Kafka| NES[Node Executor Service]
    OE -->|Redis| STATE[(State Storage)]
    WS -->|Component Discovery| WBA
    NES -->|Results| OE
```

**Key Communication Patterns:**
- **gRPC**: API Gateway ↔ Workflow Service (component discovery, workflow management)
- **Kafka**: Orchestration Engine ↔ Node Executor Service (execution requests/results)
- **Redis**: State persistence and result storage
- **HTTP/REST**: Frontend ↔ API Gateway (user interactions)

### 1.3 Existing Component Patterns

#### 1.3.1 Component Structure (Workflow Service)
```python
class ComponentName(BaseNode):
    name: ClassVar[str] = "ComponentName"
    display_name: ClassVar[str] = "Display Name"
    description: ClassVar[str] = "Component description"
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Icon"

    inputs: ClassVar[List[InputBase]] = [
        create_dual_purpose_input(
            name="input_name",
            display_name="Input Display Name",
            input_type="string",
            required=True,
            info="Input description",
            input_types=["string", "Any"]
        )
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="output_name", display_name="Output Name", output_type="string"),
        Output(name="error", display_name="Error", output_type="str")
    ]

    async def execute(self, context: WorkflowContext) -> NodeResult:
        # Modern execution method
        pass
```

#### 1.3.2 Executor Structure (Node Executor Service)
```python
@register_component("ComponentName")
class ComponentExecutor(BaseComponent):
    def __init__(self):
        super().__init__()
        self.request_schema = ComponentRequest

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        # Validation logic
        pass

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        # Execution logic
        pass
```

### 1.4 Security Infrastructure

The platform implements comprehensive security measures:

#### 1.4.1 Existing Security Features
- **API Security**: URL allowlists/blocklists, timeout controls, content type validation
- **File System Security**: Path restrictions, file size limits
- **Content Security**: Malicious content scanning, input sanitization
- **Audit Logging**: Comprehensive logging with sensitive data masking
- **Network Security**: Network isolation capabilities
- **Rate Limiting**: Request frequency controls

#### 1.4.2 Security Configuration
```bash
# API Component Security
SECURITY_API_URL_ALLOWLIST=["example.com", "api.example.com"]
SECURITY_API_URL_BLOCKLIST=["localhost", "127.0.0.1"]
SECURITY_API_MAX_TIMEOUT=60
SECURITY_API_MAX_CONTENT_SIZE=10485760

# Content Security
SECURITY_SCAN_FOR_MALICIOUS_CONTENT=true
SECURITY_MALICIOUS_CONTENT_PATTERNS=["eval(", "exec(", "import os"]

# Audit Logging
SECURITY_ENABLE_AUDIT_LOGGING=true
SECURITY_LOG_SENSITIVE_FIELDS=["password", "token", "secret"]
```

## 2. Python Code Wrapper Component Specification

### 2.1 Purpose and Functionality

The Python Code Wrapper component enables users to execute custom Python code snippets within workflows while maintaining security and isolation. It provides:

- **Safe Code Execution**: Sandboxed Python environment using RestrictedPython
- **Input/Output Integration**: Seamless data flow with other workflow components
- **Error Handling**: Comprehensive error reporting and debugging information
- **Resource Management**: CPU, memory, and execution time limits
- **Library Access**: Controlled access to safe Python libraries

### 2.2 Input/Output Requirements

#### 2.2.1 Inputs
```python
inputs: ClassVar[List[InputBase]] = [
    create_dual_purpose_input(
        name="python_code",
        display_name="Python Code",
        input_type="code",
        required=True,
        info="Python code to execute. Use 'inputs' dict for input data and assign results to 'result' variable.",
        input_types=["string"]
    ),
    create_dual_purpose_input(
        name="input_data",
        display_name="Input Data",
        input_type="dict",
        required=False,
        info="Data to pass to the Python code as 'inputs' variable.",
        input_types=["dict", "list", "string", "Any"]
    ),
    IntInput(
        name="timeout_seconds",
        display_name="Timeout (seconds)",
        value=5,
        info="Maximum execution time in seconds (1-30)."
    ),
    BoolInput(
        name="enable_debugging",
        display_name="Enable Debugging",
        value=False,
        info="Include detailed execution information in output."
    )
]
```

#### 2.2.2 Outputs
```python
outputs: ClassVar[List[Output]] = [
    Output(name="result", display_name="Execution Result", output_type="Any"),
    Output(name="output_logs", display_name="Output Logs", output_type="string"),
    Output(name="execution_time", display_name="Execution Time", output_type="float"),
    Output(name="error", display_name="Error", output_type="str")
]
```

### 2.3 Security Considerations

#### 2.3.1 Sandboxing Requirements
- **RestrictedPython**: Compile and execute code in restricted environment
- **Limited Builtins**: Only safe built-in functions available
- **Import Restrictions**: Controlled library access (json, re, math, datetime, collections)
- **No File System Access**: Prevent file operations
- **No Network Access**: Block network operations
- **No System Calls**: Prevent system command execution

#### 2.3.2 Resource Limits
```python
SECURITY_PYTHON_MAX_EXECUTION_TIME=30  # seconds
SECURITY_PYTHON_MAX_MEMORY_MB=128      # megabytes
SECURITY_PYTHON_ALLOWED_LIBRARIES=["json", "re", "math", "datetime", "collections", "copy", "itertools"]
SECURITY_PYTHON_BLOCKED_PATTERNS=["import os", "import sys", "exec(", "eval(", "__import__"]
```

## 3. Implementation Requirements

### 3.1 Dependencies

#### 3.1.1 Workflow Service Dependencies
```toml
# Add to workflow-service/pyproject.toml
[tool.poetry.dependencies]
# Existing dependencies...
RestrictedPython = "^6.0"
```

#### 3.1.2 Node Executor Service Dependencies
```toml
# Add to node-executor-service/pyproject.toml
[tool.poetry.dependencies]
# Existing dependencies...
RestrictedPython = "^6.0"
psutil = "^5.9.8"  # For resource monitoring
```

### 3.2 Workflow Service Implementation

#### 3.2.1 Component Definition
```python
# workflow-service/app/components/processing/python_code_wrapper.py

import time
import json
from typing import Dict, Any, List, ClassVar

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase, Output, StringInput, IntInput, BoolInput
)
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

class PythonCodeWrapperComponent(BaseNode):
    """
    Executes Python code in a secure sandboxed environment.

    This component allows users to run custom Python code snippets within workflows
    while maintaining security through RestrictedPython sandboxing.
    """

    name: ClassVar[str] = "PythonCodeWrapperComponent"
    display_name: ClassVar[str] = "Python Code Wrapper"
    description: ClassVar[str] = (
        "Execute custom Python code in a secure sandboxed environment. "
        "Access input data via 'inputs' variable and assign results to 'result' variable."
    )
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Code"

    inputs: ClassVar[List[InputBase]] = [
        create_dual_purpose_input(
            name="python_code",
            display_name="Python Code",
            input_type="code",
            required=True,
            info="Python code to execute. Use 'inputs' dict for input data and assign results to 'result' variable.",
            input_types=["string"]
        ),
        create_dual_purpose_input(
            name="input_data",
            display_name="Input Data",
            input_type="dict",
            required=False,
            info="Data to pass to the Python code as 'inputs' variable.",
            input_types=["dict", "list", "string", "Any"]
        ),
        IntInput(
            name="timeout_seconds",
            display_name="Timeout (seconds)",
            value=5,
            info="Maximum execution time in seconds (1-30)."
        ),
        BoolInput(
            name="enable_debugging",
            display_name="Enable Debugging",
            value=False,
            info="Include detailed execution information in output."
        )
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="result", display_name="Execution Result", output_type="Any"),
        Output(name="output_logs", display_name="Output Logs", output_type="string"),
        Output(name="execution_time", display_name="Execution Time", output_type="float"),
        Output(name="error", display_name="Error", output_type="str")
    ]

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """Execute the Python code wrapper component."""
        start_time = time.time()
        context.log(f"Executing {self.name}...")

        try:
            # Get inputs
            python_code = self.get_input_value("python_code", context, "")
            input_data = self.get_input_value("input_data", context, {})
            timeout_seconds = min(max(int(self.get_input_value("timeout_seconds", context, 5)), 1), 30)
            enable_debugging = self.get_input_value("enable_debugging", context, False)

            # Validate inputs
            if not python_code or not python_code.strip():
                error_msg = "Python code is required and cannot be empty."
                context.log(error_msg)
                return NodeResult.error(error_message=error_msg)

            # Prepare tool parameters for node-executor-service
            tool_parameters = {
                "python_code": python_code,
                "input_data": input_data,
                "timeout_seconds": timeout_seconds,
                "enable_debugging": enable_debugging
            }

            context.log(f"Prepared Python code execution request. Timeout: {timeout_seconds}s")

            # The actual execution will be handled by the orchestration engine
            # which will forward the request to the node-executor-service

            # For now, return a placeholder response
            # In production, this would be handled by the orchestration flow
            execution_time = time.time() - start_time
            context.log(f"Python code wrapper prepared successfully. Time: {execution_time:.2f}s")

            return NodeResult.success(
                outputs={
                    "result": "Execution prepared - will be handled by orchestration engine",
                    "output_logs": "",
                    "execution_time": execution_time
                },
                execution_time=execution_time
            )

        except Exception as e:
            error_msg = f"Error in Python code wrapper: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(
                error_message=error_msg,
                execution_time=time.time() - start_time
            )
```

#### 3.2.2 Component Registration
```python
# Add to workflow-service/app/components/processing/__init__.py
from app.components.processing.python_code_wrapper import PythonCodeWrapperComponent

__all__ = [
    # ... existing components
    "PythonCodeWrapperComponent",
]
```

### 3.3 Node Executor Service Implementation

#### 3.3.1 Request Schema
```python
# node-executor-service/app/components/python_code_wrapper_component.py

import asyncio
import logging
import traceback
import time
import signal
import psutil
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, field_validator
from RestrictedPython import compile_restricted
from RestrictedPython.Guards import safe_globals, limited_builtins

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component
from app.utils.security import get_security_settings

logger = logging.getLogger(__name__)

class PythonCodeRequest(BaseModel):
    """Schema for Python code execution requests."""
    python_code: str = Field(..., description="Python code to execute")
    input_data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Input data for the code")
    timeout_seconds: int = Field(default=5, ge=1, le=30, description="Execution timeout in seconds")
    enable_debugging: bool = Field(default=False, description="Enable debugging output")

    @field_validator('python_code')
    def validate_python_code(cls, v):
        if not v or not v.strip():
            raise ValueError("Python code cannot be empty")

        # Check for dangerous patterns
        dangerous_patterns = [
            'import os', 'import sys', 'import subprocess', 'import socket',
            'exec(', 'eval(', '__import__', 'open(', 'file(',
            'input(', 'raw_input(', 'compile(', 'globals(', 'locals(',
            'vars(', 'dir(', 'help(', 'reload(', 'delattr(', 'setattr(',
            'getattr(', 'hasattr('
        ]

        code_lower = v.lower()
        for pattern in dangerous_patterns:
            if pattern in code_lower:
                raise ValueError(f"Dangerous pattern detected: {pattern}")

        return v

@register_component("PythonCodeWrapperComponent")
class PythonCodeWrapperExecutor(BaseComponent):
    """
    Component for executing Python code in a secure sandboxed environment.

    This component uses RestrictedPython to safely execute user-provided Python code
    with controlled access to libraries and system resources.
    """

    def __init__(self):
        super().__init__()
        self.request_schema = PythonCodeRequest
        self._setup_restricted_environment()
        logger.info("Python Code Wrapper Executor initialized")

    def _setup_restricted_environment(self):
        """Set up the restricted Python execution environment."""
        self.restricted_globals = safe_globals.copy()
        self.restricted_globals.update({
            "__builtins__": limited_builtins,
            # Safe libraries
            "json": __import__("json"),
            "re": __import__("re"),
            "math": __import__("math"),
            "datetime": __import__("datetime"),
            "collections": __import__("collections"),
            "copy": __import__("copy"),
            "itertools": __import__("itertools"),
        })

        # Remove dangerous builtins
        dangerous_builtins = [
            'open', 'file', 'input', 'raw_input', 'compile', 'exec', 'eval',
            'execfile', 'reload', '__import__', 'apply', 'buffer', 'coerce',
            'intern', 'vars', 'dir', 'globals', 'locals', 'help'
        ]

        for builtin in dangerous_builtins:
            if builtin in self.restricted_globals.get("__builtins__", {}):
                del self.restricted_globals["__builtins__"][builtin]

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """Validate Python code execution payload."""
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating Python code execution payload for request_id: {request_id}")

        try:
            # Extract parameters
            if "tool_parameters" in payload:
                parameters = payload["tool_parameters"]
                parameters["request_id"] = request_id
            else:
                parameters = payload

            # Schema validation
            try:
                PythonCodeRequest(**parameters)
            except Exception as e:
                error_msg = f"Schema validation failed: {str(e)}"
                logger.error(f"{error_msg} for request_id {request_id}")
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"validation_error": str(e)}
                )

            logger.info(f"Python code execution payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during validation: {str(e)}"
            logger.error(f"{error_msg} for request_id {request_id}")
            return ValidationResult(
                is_valid=False,
                error_message=error_msg,
                error_details={"validation_error": str(e)}
            )

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute Python code in a restricted environment."""
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing Python code execution for request_id: {request_id}")

        try:
            # Extract parameters
            if "tool_parameters" in payload:
                parameters = payload["tool_parameters"]
            else:
                parameters = payload

            python_code = parameters.get("python_code", "")
            input_data = parameters.get("input_data", {})
            timeout_seconds = parameters.get("timeout_seconds", 5)
            enable_debugging = parameters.get("enable_debugging", False)

            logger.info(f"Executing Python code with timeout {timeout_seconds}s for request_id {request_id}")

            # Execute the code
            result = await self._execute_restricted_code(
                code=python_code,
                inputs=input_data,
                timeout=timeout_seconds,
                debug=enable_debugging,
                request_id=request_id
            )

            return {
                "status": "success",
                "result": result.get("result"),
                "output_logs": result.get("output_logs", ""),
                "execution_time": result.get("execution_time", 0.0)
            }

        except Exception as e:
            error_msg = f"Error executing Python code: {str(e)}"
            logger.error(f"{error_msg} for request_id {request_id}")
            logger.debug(f"Exception details: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg
            }

    async def _execute_restricted_code(self, code: str, inputs: Dict[str, Any],
                                     timeout: int, debug: bool, request_id: str) -> Dict[str, Any]:
        """Execute code in a restricted environment with resource limits."""
        start_time = time.time()
        output_logs = []

        try:
            # Create execution namespace
            namespace = self.restricted_globals.copy()
            namespace["inputs"] = inputs
            namespace["result"] = None

            # Add a custom print function to capture output
            def safe_print(*args, **kwargs):
                output_logs.append(" ".join(str(arg) for arg in args))

            namespace["print"] = safe_print

            # Compile the code
            try:
                byte_code = compile_restricted(
                    code,
                    filename=f"<python-code-wrapper-{request_id}>",
                    mode="exec"
                )

                if byte_code is None:
                    raise ValueError("Code compilation failed - code may contain restricted operations")

            except SyntaxError as e:
                raise ValueError(f"Syntax error in Python code: {str(e)}")

            # Execute with timeout and resource monitoring
            result = await asyncio.wait_for(
                self._run_with_monitoring(byte_code, namespace, request_id),
                timeout=timeout
            )

            execution_time = time.time() - start_time

            # Get the result
            final_result = namespace.get("result")
            if final_result is None and not output_logs:
                logger.warning(f"No result produced by code execution for request_id {request_id}")

            return {
                "result": final_result,
                "output_logs": "\n".join(output_logs) if output_logs else "",
                "execution_time": execution_time,
                "debug_info": {
                    "namespace_keys": list(namespace.keys()),
                    "execution_time": execution_time
                } if debug else None
            }

        except asyncio.TimeoutError:
            raise ValueError(f"Code execution timed out after {timeout} seconds")
        except Exception as e:
            raise ValueError(f"Code execution error: {str(e)}")

    async def _run_with_monitoring(self, byte_code, namespace: Dict[str, Any], request_id: str):
        """Run code with resource monitoring."""
        def execute_code():
            try:
                # Monitor memory usage
                process = psutil.Process()
                initial_memory = process.memory_info().rss / 1024 / 1024  # MB

                # Execute the code
                exec(byte_code, namespace)

                # Check memory usage after execution
                final_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_used = final_memory - initial_memory

                if memory_used > 128:  # 128MB limit
                    raise ValueError(f"Memory limit exceeded: {memory_used:.2f}MB used")

                return namespace

            except Exception as e:
                logger.error(f"Error during code execution for request_id {request_id}: {str(e)}")
                raise

        # Run in thread pool to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, execute_code)
```

#### 3.3.2 Component Registration
```python
# Add to node-executor-service/app/components/__init__.py
from . import python_code_wrapper_component
```

## 4. Use Cases and Examples

### 4.1 Data Transformation
```python
# Example: Transform JSON data structure
inputs = {
    "users": [
        {"name": "John", "age": 30, "city": "New York"},
        {"name": "Jane", "age": 25, "city": "Boston"}
    ]
}

# Python code:
result = []
for user in inputs["users"]:
    if user["age"] >= 25:
        result.append({
            "full_name": user["name"],
            "location": user["city"],
            "category": "adult"
        })
```

### 4.2 Mathematical Calculations
```python
# Example: Statistical analysis
import math

data = inputs.get("numbers", [1, 2, 3, 4, 5])
result = {
    "count": len(data),
    "sum": sum(data),
    "mean": sum(data) / len(data),
    "std_dev": math.sqrt(sum((x - sum(data)/len(data))**2 for x in data) / len(data))
}
```

### 4.3 Text Processing
```python
# Example: Text analysis and formatting
import re

text = inputs.get("text", "")
words = re.findall(r'\w+', text.lower())
word_count = {}
for word in words:
    word_count[word] = word_count.get(word, 0) + 1

result = {
    "word_count": len(words),
    "unique_words": len(word_count),
    "most_common": max(word_count.items(), key=lambda x: x[1]) if word_count else None,
    "processed_text": text.upper()
}
```

### 4.4 Workflow Integration Example
```mermaid
graph LR
    A[API Request] --> B[Parse JSON]
    B --> C[Python Code Wrapper]
    C --> D[Format Output]
    D --> E[Send Response]

    C --> |Transform Data| C1[Custom Logic]
    C1 --> |Calculations| C2[Math Operations]
    C2 --> |Validation| C3[Data Validation]
```

## 5. Technical Architecture Context

### 5.1 Component Lifecycle

1. **Component Discovery** (Workflow Service)
   - Component class registered in `__init__.py`
   - Discovered by component service
   - Metadata sent to frontend via gRPC

2. **Workflow Creation** (Workflow Builder App)
   - User drags component to canvas
   - Configures inputs and connections
   - Workflow saved via API Gateway

3. **Workflow Execution** (Orchestration Engine)
   - Receives execution request via Kafka
   - Processes workflow transitions
   - Sends component execution requests to Node Executor Service

4. **Component Execution** (Node Executor Service)
   - Receives execution request via Kafka
   - Validates payload using Pydantic schema
   - Executes component logic in sandboxed environment
   - Returns results via Kafka

### 5.2 Security Boundaries

```mermaid
graph TB
    subgraph "Trusted Zone"
        WS[Workflow Service]
        OE[Orchestration Engine]
    end

    subgraph "Execution Zone"
        NES[Node Executor Service]
        subgraph "Sandbox"
            RP[RestrictedPython]
            SC[Safe Code]
        end
    end

    subgraph "User Zone"
        WBA[Workflow Builder App]
        UC[User Code]
    end

    UC -->|Validated| WS
    WS -->|Orchestrated| OE
    OE -->|Secured| NES
    NES -->|Sandboxed| RP
```

### 5.3 Error Handling Strategy

1. **Validation Errors**: Caught at schema validation level
2. **Compilation Errors**: Handled by RestrictedPython compiler
3. **Runtime Errors**: Caught and wrapped with context
4. **Timeout Errors**: Handled by asyncio timeout mechanism
5. **Resource Errors**: Monitored via psutil and enforced limits

## 6. Agent Onboarding Guide

### 6.1 Project Structure Conventions

```
workflow-service/
├── app/
│   ├── components/
│   │   ├── processing/           # Data processing components
│   │   │   ├── __init__.py      # Component registration
│   │   │   └── component_name.py
│   │   ├── ai/                  # AI/ML components
│   │   └── data_interaction/    # External data components
│   ├── models/                  # Pydantic models
│   ├── utils/                   # Utility functions
│   └── services/               # Business logic

node-executor-service/
├── app/
│   ├── components/             # Executor implementations
│   │   ├── __init__.py        # Component imports
│   │   └── component_executor.py
│   ├── core_/                 # Core system components
│   └── utils/                 # Utility functions
```

### 6.2 Implementation Patterns

#### 6.2.1 Test-Driven Development
```python
# Always write tests first
def test_python_code_wrapper_basic_execution():
    """Test basic Python code execution."""
    executor = PythonCodeWrapperExecutor()
    payload = {
        "python_code": "result = inputs['x'] + inputs['y']",
        "input_data": {"x": 5, "y": 3},
        "timeout_seconds": 5
    }

    result = await executor.process(payload)
    assert result["status"] == "success"
    assert result["result"] == 8
```

#### 6.2.2 Dual-Purpose Inputs
```python
# Always use create_dual_purpose_input for user inputs
create_dual_purpose_input(
    name="input_name",
    display_name="Display Name",
    input_type="string",  # or dict, list, code, etc.
    required=True,
    info="Description for users",
    input_types=["string", "Any"]  # Types this input accepts
)
```

#### 6.2.3 Modern Execute Methods
```python
# Use async execute method, not legacy build method
async def execute(self, context: WorkflowContext) -> NodeResult:
    start_time = time.time()
    context.log(f"Executing {self.name}...")

    try:
        # Component logic here
        result = process_data()

        execution_time = time.time() - start_time
        return NodeResult.success(
            outputs={"output_name": result},
            execution_time=execution_time
        )
    except Exception as e:
        return NodeResult.error(
            error_message=str(e),
            execution_time=time.time() - start_time
        )
```

### 6.3 Security Guidelines

1. **Always validate inputs** using Pydantic schemas
2. **Use RestrictedPython** for any code execution
3. **Implement resource limits** (CPU, memory, time)
4. **Log security events** for audit trails
5. **Sanitize sensitive data** in logs
6. **Follow principle of least privilege**

### 6.4 Component Registration Checklist

- [ ] Component class inherits from BaseNode (Workflow Service)
- [ ] Executor class inherits from BaseComponent (Node Executor Service)
- [ ] Component registered with @register_component decorator
- [ ] Added to __init__.py imports
- [ ] Pydantic schema defined for validation
- [ ] Tests written and passing
- [ ] Documentation updated
- [ ] Security review completed

This comprehensive guide provides all the context and implementation details needed to successfully create the Python Code Wrapper component within our distributed workflow platform architecture.
```
