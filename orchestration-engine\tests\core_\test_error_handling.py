import pytest
import async<PERSON>
from unittest.mock import Mock, AsyncMock, patch
from app.core_.transition_handler import TransitionHandler
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.services.node_executor import NodeExecutor


class TestErrorHandling:
    """
    Test suite for error handling in TransitionHandler.
    Covers different error formats from different executors.
    """

    @pytest.fixture
    def mock_state_manager(self):
        """
        Provides a mock StateManager for testing.
        """
        mock = Mock()
        mock.completed_transitions = set()
        mock.workflow_paused = False
        mock.mark_transition_completed = Mock()
        return mock

    @pytest.fixture
    def mock_tool_executor(self):
        """
        Provides a mock ToolExecutor (KafkaToolExecutor) for testing.
        """
        mock = AsyncMock(spec=KafkaToolExecutor)
        mock.execute_tool = AsyncMock(
            return_value={"status": "success", "result": "test result"}
        )
        return mock

    @pytest.fixture
    def mock_node_executor(self):
        """
        Provides a mock NodeExecutor for testing.
        """
        mock = AsyncMock(spec=NodeExecutor)
        mock.execute_tool = AsyncMock(
            return_value={"status": "success", "result": "test result"}
        )
        return mock

    @pytest.fixture
    def mock_workflow_utils(self):
        """
        Provides a mock WorkflowUtils for testing.
        """
        mock = Mock()
        mock._format_tool_parameters = AsyncMock(return_value={"param1": "value1"})
        mock._evaluate_switch_case = AsyncMock(return_value=None)
        return mock

    @pytest.fixture
    def error_handling_transition_handler(
        self,
        mock_state_manager,
        mock_tool_executor,
        mock_node_executor,
        mock_workflow_utils,
    ):
        """
        Provides a TransitionHandler instance for testing error handling.
        """
        transitions_by_id = {
            "trans1": {
                "id": "trans1",
                "transition_type": "standard",
                "sequence": 1,
                "node_info": {
                    "node_id": "node1",
                    "tools_to_use": [{"tool_name": "test_tool", "tool_id": "test_id"}],
                },
            },
            "trans2": {
                "id": "trans2",
                "transition_type": "standard",
                "execution_type": "Components",
                "sequence": 2,
                "node_info": {
                    "node_id": "node2",
                    "tools_to_use": [{"tool_name": "api_tool", "tool_id": "api_id"}],
                },
            },
        }
        nodes = {
            "node1": {
                "id": "node1",
                "server_script_path": "test/path",
                "server_tools": [{"tool_name": "test_tool", "tool_id": "test_id"}],
            },
            "node2": {
                "id": "node2",
                "server_script_path": "test/api/path",
                "server_tools": [{"tool_name": "api_tool", "tool_id": "api_id"}],
            },
        }
        dependency_map = {}

        return TransitionHandler(
            state_manager=mock_state_manager,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            dependency_map=dependency_map,
            workflow_utils=mock_workflow_utils,
            tool_executor=mock_tool_executor,
            node_executor=mock_node_executor,
        )

    @pytest.mark.asyncio
    async def test_kafka_tool_executor_error_handling(
        self, error_handling_transition_handler
    ):
        """
        Test handling of errors from KafkaToolExecutor (Format 1).
        """
        # Arrange
        transition = error_handling_transition_handler.transitions_by_id["trans1"]
        error_message = "Failed to execute tool script_generate after 3 attempts"
        error_response = {
            "request_id": "01c4333d-bcb4-49dc-ac3b-6dcdf5b23a0e",
            "error": error_message,
            "status": "error",
        }

        error_handling_transition_handler.tool_executor.execute_tool.return_value = (
            error_response
        )

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            await error_handling_transition_handler._execute_standard_or_reflection_transition(
                transition
            )

        assert f"Tool execution error: {error_message}" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_node_executor_error_handling(
        self, error_handling_transition_handler
    ):
        """
        Test handling of errors from NodeExecutor (Format 2).
        """
        # Arrange
        transition = error_handling_transition_handler.transitions_by_id["trans2"]
        error_message = "API request failed with status 500 (Internal Server Error)"
        error_response = {
            "status": "error",
            "result": {
                "error": error_message,
                "request_id": "a805afb6-b172-404a-8bb8-1e65ac358fd6",
                "status_code": 500,
                "data": {"detail": "This endpoint always returns a 500 error"},
                "tool_name": "ApiRequestNode",
            },
            "request_id": "a805afb6-b172-404a-8bb8-1e65ac358fd6",
            "tool_name": "ApiRequestNode",
        }

        error_handling_transition_handler.node_executor.execute_tool.return_value = (
            error_response
        )

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            await error_handling_transition_handler._execute_standard_or_reflection_transition(
                transition
            )

        # The error is wrapped in another error message by the exception handler
        inner_error = f"Tool execution error: {error_message} - This endpoint always returns a 500 error"
        expected_error = f"Tool execution error: [ERROR] Tool Execution Failed with error: {inner_error}"
        assert expected_error in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_node_executor_new_format_error_handling(
        self, error_handling_transition_handler
    ):
        """
        Test handling of errors from NodeExecutor with the new format.
        """
        # Arrange
        transition = error_handling_transition_handler.transitions_by_id["trans2"]
        error_message = "API request failed with status 500 (Internal Server Error)"
        error_response = {
            "request_id": "a805afb6-b172-404a-8bb8-1e65ac358fd6",
            "status": "error",
            "result": {
                "error": error_message,
                "status_code": 500,
                "data": {"detail": "This endpoint always returns a 500 error"},
            },
        }

        error_handling_transition_handler.node_executor.execute_tool.return_value = (
            error_response
        )

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            await error_handling_transition_handler._execute_standard_or_reflection_transition(
                transition
            )

        # The error is wrapped in another error message by the exception handler
        inner_error = f"Tool execution error: {error_message} - This endpoint always returns a 500 error"
        expected_error = f"Tool execution error: [ERROR] Tool Execution Failed with error: {inner_error}"
        assert expected_error in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_components_error_without_error_status(
        self, error_handling_transition_handler
    ):
        """
        Test handling of errors from Components execution type where status is not 'error'
        but there's an error field in the result.
        """
        # Arrange
        transition = error_handling_transition_handler.transitions_by_id["trans2"]
        # Explicitly set execution_type to Components
        transition["execution_type"] = "Components"

        error_message = "API request failed with status 500 (Internal Server Error)"
        # Note: status is not 'error' here
        error_response = {
            "request_id": "68054818-1fc5-4de1-97f5-00beea284c40",
            "status": "success",  # Status is not error
            "result": {
                "error": error_message,
                "status_code": 500,
                "data": {"detail": "This endpoint always returns a 500 error"},
            },
        }

        error_handling_transition_handler.node_executor.execute_tool.return_value = (
            error_response
        )

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            await error_handling_transition_handler._execute_standard_or_reflection_transition(
                transition
            )

        # The error is wrapped in another error message by the exception handler
        inner_error = f"Tool execution error: {error_message} - This endpoint always returns a 500 error"
        expected_error = f"Tool execution error: [ERROR] Tool Execution Failed with error: {inner_error}"
        assert expected_error in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_fallback_error_handling(self, error_handling_transition_handler):
        """
        Test handling of errors with fallback format.
        """
        # Arrange
        transition = error_handling_transition_handler.transitions_by_id["trans1"]
        error_response = {
            "status": "error",
            "result": "Unknown error format",
        }

        error_handling_transition_handler.tool_executor.execute_tool.return_value = (
            error_response
        )

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            await error_handling_transition_handler._execute_standard_or_reflection_transition(
                transition
            )

        assert "Tool execution error: Unknown error format" in str(exc_info.value)
