import React from "react";
import { useInspector } from "./InspectorContext";
import {
  <PERSON>erHeader,
  DrawerTitle,
  DrawerDescription,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

/**
 * Component for the header section of the inspector panel
 */
export function InspectorHeader() {
  const { selectedNode, onClose } = useInspector();

  // If no node is selected, don't render anything
  if (!selectedNode?.data?.definition) return null;

  return (
    <DrawerHeader className="flex flex-shrink-0 items-start justify-between border-b p-4">
      <div>
        <DrawerTitle className="flex items-center gap-2">
          {selectedNode.data.definition.display_name}
          <Badge variant="outline" className="bg-muted/50 text-[10px]">
            {selectedNode.data.definition.category}
          </Badge>
        </DrawerTitle>
        <DrawerDescription className="mt-1 text-xs">
          {selectedNode.data.definition.description}
        </DrawerDescription>
      </div>
      <Button
        variant="ghost"
        size="icon"
        className="-mt-1 -mr-2 h-8 w-8"
        onClick={onClose}
      >
        <X className="h-4 w-4" />
      </Button>
    </DrawerHeader>
  );
}
