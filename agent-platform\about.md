# Agent Platform Technical Documentation

## Overview

The Agent Platform is a sophisticated system designed to facilitate interactions between users and AI agents. It leverages a modular architecture to enable dynamic agent configuration, tool integration, and seamless conversation management. This document provides a comprehensive overview of the system's architecture, components, and operational flow.

## System Architecture

The Agent Platform follows a service-oriented architecture with the following key components:

1. **FastAPI Application**: Serves as the entry point and API layer for client interactions
2. **Agent Runtime**: Manages agent lifecycle and message routing
3. **Agent System**: Includes specialized agents for different purposes
4. **Tool Integration**: Provides extensible functionality through tool definitions
5. **Session Management**: Maintains conversation state across user interactions

### High-Level Architecture Diagram

```
┌─────────────┐     ┌─────────────────┐     ┌───────────────┐
│  Client     │────▶│  FastAPI Server  │────▶│ Agent Runtime │
└─────────────┘     └─────────────────┘     └───────┬───────┘
                                                    │
                                                    ▼
┌─────────────┐     ┌─────────────────┐     ┌───────────────┐
│  Session    │◀───▶│  Head Agent     │◀───▶│ Specialized   │
│  Manager    │     │  (Router)       │     │ Agents        │
└─────────────┘     └─────────────────┘     └───────┬───────┘
                                                    │
                                                    ▼
                                            ┌───────────────┐
                                            │ Tool Loader   │
                                            │ & Tools       │
                                            └───────────────┘
```

## System Flow Diagrams

### 1. Overall System Architecture

```mermaid
graph TB
    Client[Client Application] -->|HTTP/SSE| API[FastAPI Server]
    API -->|Session Management| SM[Session Manager]
    API -->|Task Routing| HA[Head Agent]
    
    subgraph "Agent Layer"
        HA -->|Delegates| MA[Marketing Agent]
        HA -->|Delegates| SA[Sales Agent]
        HA -->|Delegates| CA[Content Agent]
        
        MA & SA & CA -->|Uses| TL[Tool Loader]
    end
    
    subgraph "Tool Layer"
        TL -->|Loads| MT[MCP Tools]
        TL -->|Loads| WF[Workflow Tools]
        TL -->|Loads| FT[Function Tools]
    end
    
    subgraph "External Services"
        MT -->|Connects| MCP[MCP Service]
        WF -->|Executes| WFA[Workflow API]
        FT -->|Calls| EXT[External APIs]
    end
```

### 2. Message Processing Flow

```mermaid
flowchart TD
    A[Client Message] -->|HTTP POST| B[FastAPI Endpoint]
    B -->|Create| C[UserTask]
    C -->|Publish| D[Head Agent]
    
    D -->|Analyze| E{Needs Delegation?}
    E -->|Yes| F[Select Agent]
    E -->|No| G[Direct Response]
    
    F -->|Delegate| H[Specialized Agent]
    H -->|Process| I[Tool Execution]
    I -->|Return| J[Response]
    
    G -->|Stream| K[SSE Response]
    J -->|Stream| K
    
    K -->|Send| L[Client]
```

### 3. Tool Loading and Execution

```mermaid
flowchart LR
    A[Tool Definition] -->|Parse| B[Tool Loader]
    
    subgraph "Tool Types"
        C[MCP SSE]
        D[Workflow]
        E[Function]
        F[Streaming]
    end
    
    B -->|Create| C & D & E & F
    
    C -->|Execute| G[MCP Service]
    D -->|Execute| H[Workflow API]
    E -->|Execute| I[Function Call]
    F -->|Stream| J[Real-time Data]
```

### 4. Session Management Flow

```mermaid
stateDiagram-v2
    [*] --> Initialized: Create Session
    Initialized --> Active: First Message
    Active --> Processing: Message Received
    Processing --> Active: Response Sent
    Active --> Terminated: Session End
    Terminated --> [*]
    
    Processing --> Error: Exception
    Error --> Active: Recovery
    Error --> Terminated: Fatal Error
```

### 5. Agent Registration Process

```mermaid
sequenceDiagram
    participant Admin
    participant API
    participant HeadAgent
    participant Registry
    
    Admin->>API: POST /register_agent
    API->>Registry: Validate Config
    Registry->>Registry: Create Instance
    Registry->>HeadAgent: Update Tools
    HeadAgent->>HeadAgent: Update Delegation Schema
    HeadAgent-->>API: Confirmation
    API-->>Admin: Success Response
```

### 6. Tool Integration Architecture

```mermaid
graph TB
    subgraph "Tool Definition Layer"
        TD[Tool Definitions] -->|Parse| TL[Tool Loader]
        TC[Tool Configs] -->|Load| TL
    end
    
    subgraph "Tool Implementation Layer"
        TL -->|Create| MT[MCP Tools]
        TL -->|Create| WT[Workflow Tools]
        TL -->|Create| FT[Function Tools]
    end
    
    subgraph "Execution Layer"
        MT & WT & FT -->|Execute| EX[Executor]
        EX -->|Stream| RS[Result Stream]
    end
    
    subgraph "Integration Layer"
        RS -->|Format| RM[Response Manager]
        RM -->|Send| CL[Client]
    end
```

These diagrams provide a comprehensive view of:
1. System architecture and component relationships
2. Message processing and routing
3. Tool loading and execution flow
4. Session state management
5. Agent registration process
6. Tool integration architecture

Each diagram focuses on a specific aspect of the system, making it easier to understand the different layers and interactions within the platform.

## Initialization Flow

### Application Startup

1. The application starts in `main.py` with the FastAPI framework
2. During the `lifespan` context manager execution:
   - A `SingleThreadedAgentRuntime` is initialized
   - A `ToolLoader` instance is created
   - An OpenAI model client is configured with API key and model name
   - The Head Agent is registered via `register_head_agent()`
   - The runtime is started

```python
@asynccontextmanager
async def lifespan(app: FastAPI):
    runtime = SingleThreadedAgentRuntime()
    app.state.runtime = runtime
    tool_loader = ToolLoader()
    app.state.tool_loader = tool_loader

    model_client = OpenAIChatCompletionClient(
        model=LLM_MODEL,
        api_key=OPENAI_API_KEY
    )

    head_agent = await register_head_agent(runtime, tool_loader)
    registered_agent_types["head_agent"] = head_agent
    runtime.start()

    yield

    # Cleanup on shutdown
    registered_agent_types.clear()
    await runtime.stop()
```

### Agent Configuration and Initialization

#### Head Agent Registration

1. The `register_head_agent()` function in `agent_fetching.py` is called during startup
2. It fetches the Head Agent configuration (currently hardcoded)
3. The `AgentConfigParser` parses the configuration and registers the Head Agent with the runtime
4. The Head Agent is returned and stored in the `registered_agent_types` dictionary

```python
async def register_head_agent(runtime: SingleThreadedAgentRuntime, tool_loader: ToolLoader):
    head_agent_config = await fetch_agent_config_from_db('head_agent')
    config_parser = AgentConfigParser(runtime, tool_loader)
    registered_head_agent = await config_parser.parse_and_register_agent_from_config(
        head_agent_config, registered_agent_types=registered_agent_types
    )
    return registered_head_agent
```

#### Specialized Agent Registration

1. Specialized agents are registered on-demand when a user interacts with the system
2. The `/register_agent` endpoint triggers the registration process
3. `register_agent_for_user()` fetches the agent configuration for the user token
4. The `AgentConfigParser` parses the configuration and registers the agent with the runtime
5. The registered agent is stored in the `registered_agent_types` dictionary

## Agent Configuration Parsing

The `AgentConfigParser` class is responsible for parsing agent configurations and registering agents with the runtime:

1. It accepts a JSON configuration that defines:

   - Agent type/category (e.g., "AIAgent", "HeadAgent")
   - System message content
   - Description
   - Tools configuration
   - Model configuration

2. It dynamically creates agent instances based on the configuration:
   - Maps agent categories to concrete classes
   - Loads and configures tools for the agent
   - Sets up delegation tools if needed
   - Creates an agent factory function
   - Registers the agent with the runtime

```python
async def parse_and_register_agent_from_config(self, agent_config, registered_agent_types=None):
    # Extract configuration parameters
    agent_category = agent_config.get("agent_category")
    agent_class = self.agent_category_to_class.get(agent_category)

    # Load agent tools
    agent_tools = await self._load_agent_tools(agent_tools_configs)

    # Create agent factory
    def agent_factory():
        agent_kwargs = {
            "description": description,
            "system_message": SystemMessage(content=system_message_content),
            "model_client": model_client,
            # Additional parameters...
        }

        if agent_category in ["AIAgent", "Assistant"]:
            agent_kwargs["tools"] = agent_tools

        return agent_class(**agent_kwargs)

    # Register agent with runtime
    agent_type = self.runtime.register_agent_type(
        agent_topic_type,
        agent_factory,
        TypeSubscription(user_topic_type)
    )

    return agent_type
```

## Agent Hierarchy and Relationships

The platform implements a hierarchical agent structure:

### Head Agent (Router)

- Acts as the central coordinator for all user interactions
- Receives user messages and determines which specialized agent should handle them
- Maintains delegation tools for each registered specialized agent
- Routes messages to appropriate agents and collects responses
- Handles streaming responses back to the user

```python
class HeadAgent(RoutedAgent):
    def __init__(self, description, system_message, model_client, registered_agent_types=None,
                 agent_topic_type="HeadAgent", user_topic_type="User", delegate_tools=None):
        super().__init__(description)
        self._system_message = system_message
        self._model_client = model_client
        self.registered_agent_types = registered_agent_types or {}
        self._agent_topic_type = agent_topic_type
        self._user_topic_type = user_topic_type
        self._delegate_tools = {}
        self._delegate_tool_schema = []
        self._update_delegation_tools()
```

The Head Agent dynamically updates its delegation tools based on the currently registered specialized agents:

```python
def _update_delegation_tools(self):
    self._delegate_tools.clear()
    self._delegate_tool_schema.clear()

    delegate_candidates = self.registered_agent_types.copy()

    for agent_type_str in delegate_candidates.keys():
        tool_name = f"delegate_to_{agent_type_str}"
        tool_description = f"Delegate task to {agent_type_str} agent for tasks related to {agent_type_str}"

        def create_delegation_func(target_agent_type: str):
            async def _delegate(user_message: str) -> str:
                return target_agent_type
            return _delegate

        tool = FunctionTool(
            func=create_delegation_func(agent_type_str),
            name=tool_name,
            description=tool_description,
        )

        self._delegate_tools[tool_name] = tool
        self._delegate_tool_schema.append(tool.schema)
```

### Specialized Agents (AIAgent)

- Handle specific domains or tasks (e.g., Marketing, Sales)
- Receive delegated tasks from the Head Agent
- Use tools to perform actions or retrieve information
- Can further delegate subtasks to other specialized agents
- Return responses to the Head Agent

```python
class AIAgent(RoutedAgent):
    def __init__(self, description, system_message, model_client, tools, delegate_tools,
                 agent_topic_type, user_topic_type, registered_agent_types=None):
        super().__init__(description)
        self._system_message = system_message
        self._model_client = model_client
        self._tools = {tool.name: tool for tool in tools}
        self._tool_schema = [tool.schema for tool in tools]
        self._delegate_tools = {tool.name: tool for tool in delegate_tools}
        self._delegate_tool_schema = [tool.schema for tool in delegate_tools]
        self._agent_topic_type = agent_topic_type
        self._user_topic_type = user_topic_type
        self.registered_agent_types = registered_agent_types or {}
```

## Tool Integration

The platform implements a flexible tool integration system through the `ToolLoader` class:

1. Tools are defined in JSON configurations
2. The `ToolLoader` creates tool instances based on these configurations
3. Tools can be of different types:
   - MCP SSE tools
   - Workflow tools
   - Function tools
   - Streaming function tools

### Tool Creation Process

1. The `create_tool_from_definition()` method analyzes the tool definition
2. Based on the `tool_type`, it calls the appropriate creation method
3. For workflow tools, it creates a specific tool bound to a generic executor
4. The tool is then made available to agents

```python
async def create_tool_from_definition(self, tool_definition: Dict[str, Any]) -> Optional[Tool]:
    tool_type = tool_definition.get("tool_type")

    if tool_type == "mcp_sse":
        return await self._create_mcp_sse_tool(tool_definition.get("mcp_tool", {}))

    elif tool_type == "workflow":
        url = "https://22a9-2405-201-6814-39a3-dcfe-b54d-555b-bcec.ngrok-free.app/api/v1/kafka/workflow-requests"
        workflow_config = tool_definition.get("workflow")
        return self._create_specific_workflow_tool(workflow_config, url)

    else:
        logger.warning(f"Tool type '{tool_type}' not supported yet.")
        return None
```

### Streaming Tools

The platform supports streaming tools that can provide incremental results:

1. `StreamingFunctionTool` extends `FunctionTool` to mark tools as streaming
2. Streaming tools yield chunks of data as they become available
3. The AIAgent detects streaming tools and handles their output accordingly
4. Each chunk is immediately sent back to the user via the Head Agent

## API Routing and Request Handling

The API layer is implemented using FastAPI with the following key endpoints:

### Chat Endpoint (`/chat`)

1. Receives user messages and session information
2. Creates a unique task ID and request queue
3. Builds conversation history from the session
4. Publishes a `UserTask` message to the Head Agent
5. Returns a `StreamingResponse` that streams responses back to the client

```python
@router.post("/chat")
async def chat_endpoint(request: TaskRequest, req: Request):
    runtime = req.app.state.runtime
    session_id = request.session_id or str(uuid.uuid4())
    user_token = request.user_token

    task_id = str(uuid.uuid4())
    request_queue = asyncio.Queue()
    request_queues[task_id] = request_queue

    # Build conversation history
    conversation_history = []
    stored_history = session_manager.get_session(session_id)
    for msg in stored_history:
        # Convert stored messages to appropriate format
        conversation_history.append(msg)

    # Add current user message
    conversation_history.append(UserMessage(content=request.message, source="User"))

    # Add message to session manager
    session_manager.add_message(
        session_id=session_id,
        message={"role": "User", "content": request.message}
    )

    # Publish task to HeadAgent
    await runtime.publish_message(
        UserTask(
            context=conversation_history,
            registered_agent_types=registered_agent_types,
            task_id=task_id
        ),
        topic_id=TopicId(head_agent_topic_type, source="User"),
    )

    # Return streaming response
    return StreamingResponse(
        stream_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache, no-transform",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        }
    )
```

### Register Agent Endpoint (`/register_agent`)

1. Receives a user token
2. Checks if an agent is already registered for the token
3. Calls `register_agent_for_user()` to register the agent
4. Stores the registered agent in the `registered_agent_types` dictionary

### End Session Endpoint (`/chat/{session_id}`)

1. Receives a session ID
2. Calls `session_manager.end_session()` to clear the session data
3. Returns a success message if the session was found and removed

## Session Management and Conversation State

The `SessionManager` class maintains conversation state across user interactions:

1. It stores conversations in a dictionary keyed by session ID
2. Each session contains a list of messages
3. Messages can be added, retrieved, and updated
4. Sessions can be ended, which removes them from the manager

```python
class SessionManager:
    def __init__(self):
        self.sessions: Dict[str, List[LLMMessage]] = {}

    def add_message(self, session_id: str, message: Dict[str, Any]) -> None:
        if session_id not in self.sessions:
            self.sessions[session_id] = []
        self.sessions[session_id].append(message)

    def get_session(self, session_id: str) -> List[LLMMessage]:
        return self.sessions.get(session_id, [])

    def update_session(self, session_id: str, conversation_history: List[LLMMessage]) -> None:
        self.sessions[session_id] = conversation_history

    def end_session(self, session_id: str) -> bool:
        if session_id in self.sessions:
            del self.sessions[session_id]
            return True
        return False
```

## Message Flow

The complete message flow in the system follows this pattern:

1. User sends a message to the `/chat` endpoint
2. The API layer creates a `UserTask` and publishes it to the Head Agent
3. The Head Agent:
   - Analyzes the message using its LLM
   - Decides which specialized agent should handle the task
   - Delegates the task to the chosen agent
4. The specialized agent:
   - Processes the task using its LLM
   - Uses tools to perform actions or retrieve information
   - May delegate subtasks to other agents
   - Returns a response to the Head Agent
5. The Head Agent:
   - Collects responses from specialized agents
   - Streams responses back to the user via the request queue
6. The API layer:
   - Reads from the request queue
   - Formats responses as Server-Sent Events
   - Streams them back to the client
   - Stores final responses in the session manager

## Function Calling Flow

The following diagram illustrates how function calling works in the API endpoints:

```mermaid
sequenceDiagram
    participant Client
    participant FastAPI
    participant HeadAgent
    participant AIAgent
    participant ToolLoader
    participant ExternalService

    Client->>FastAPI: POST /chat
    Note over FastAPI: Creates UserTask & Queue

    FastAPI->>HeadAgent: Publish UserTask
    
    rect rgb(200, 220, 255)
        Note over HeadAgent: LLM Analysis Phase
        HeadAgent->>HeadAgent: Analyze with LLM
        HeadAgent->>HeadAgent: Check Delegation Tools
    end

    alt Needs Delegation
        HeadAgent->>AIAgent: Delegate Task
        
        rect rgb(220, 240, 220)
            Note over AIAgent: Tool Execution Phase
            AIAgent->>AIAgent: Process with LLM
            AIAgent->>ToolLoader: Request Tool
            ToolLoader->>ExternalService: Execute Tool
            ExternalService-->>ToolLoader: Tool Result
            ToolLoader-->>AIAgent: Return Result
        end
        
        AIAgent-->>HeadAgent: Return Response
    else Direct Response
        HeadAgent-->>FastAPI: Stream Response
    end

    FastAPI-->>Client: SSE Stream Response
```

### Function Call Process Flow

1. **Initial Request Processing**
   - Client sends request to FastAPI endpoint
   - FastAPI creates a UserTask and request queue
   - Task is published to HeadAgent

2. **Head Agent Processing**
   - Analyzes message using LLM
   - Checks available delegation tools
   - Decides whether to delegate or respond directly

3. **Tool Execution Flow**
   - If delegation needed:
     - Task delegated to specialized AIAgent
     - AIAgent processes with its own LLM
     - Uses ToolLoader to execute required tools
     - Results returned through tool execution chain
   - If direct response:
     - HeadAgent streams response back

4. **Response Handling**
   - Responses streamed back via SSE
   - Session state updated
   - Client receives real-time updates

## Conclusion

The Agent Platform implements a sophisticated architecture for AI agent interactions. Its key strengths include:

1. **Modularity**: Components are well-separated with clear responsibilities
2. **Extensibility**: New agents and tools can be easily added
3. **Dynamic Configuration**: Agents can be configured and registered at runtime
4. **Streaming Responses**: Real-time streaming of agent responses
5. **Session Management**: Persistent conversation state across interactions

This architecture enables the platform to handle complex user interactions while maintaining flexibility and scalability.
