import React from "react";
import { InputDefinition } from "@/types";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface HandleInputProps {
  inputDef: InputDefinition;
  value: any;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  connectionInfo?: {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };
  nodeId?: string;
}

/**
 * Component for rendering handle inputs (connection points)
 */
export function HandleInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  connectionInfo,
  nodeId,
}: HandleInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;
  
  // Display connection status for handle inputs
  const handleIsConnected = isConnected;
  const sourceNodeInfo = connectionInfo?.sourceNodeLabel
    ? `from ${connectionInfo.sourceNodeLabel}`
    : connectionInfo?.sourceNodeId
    ? `from ${connectionInfo.sourceNodeId}`
    : "";

  return (
    <div className="relative">
      <div className="mt-1 flex items-center space-x-2">
        <div className="flex-grow">
          <Input
            id={inputId}
            type="text"
            value={
              handleIsConnected
                ? `Connected ${sourceNodeInfo}`
                : "Connection handle (not connected)"
            }
            className={cn(
              "bg-background/50 h-8 text-xs",
              handleIsConnected ? "border-blue-500/30 opacity-50" : "opacity-50"
            )}
            disabled={true}
            readOnly={true}
          />
        </div>
        <div className="flex-shrink-0">
          <Badge
            variant={handleIsConnected ? "success" : "outline"}
            className="h-5 px-2 text-[9px]"
          >
            {handleIsConnected ? "Connected" : "Not Connected"}
          </Badge>
        </div>
      </div>
      {inputDef.info && <p className="text-muted-foreground mt-1 text-xs">{inputDef.info}</p>}
    </div>
  );
}
