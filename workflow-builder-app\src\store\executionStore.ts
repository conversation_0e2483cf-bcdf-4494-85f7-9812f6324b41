import { create } from "zustand";
import { persist } from "zustand/middleware";


export interface MissingField {
  nodeId: string;
  nodeName: string;
  name: string;
  displayName: string;
  info?: string;
  inputType: string;
  connected_to_start?: boolean;
  directly_connected_to_start?: boolean;
  required?: boolean;
  isEmpty?: boolean;
  currentValue?: any;
  options?: Array<string | { value: string; label: string }> | null;
  // Handle connection information
  is_handle?: boolean;
  is_connected?: boolean;
  // Schema information for JSON objects
  schema?: {
    type?: string;
    properties?: Record<string, any>;
    required?: string[];
  };
}

interface ExecutionState {
  // Dialog state
  isDialogOpen: boolean;
  setDialogOpen: (isOpen: boolean) => void;

  // Missing fields
  missingFields: MissingField[];
  setMissingFields: (fields: MissingField[]) => void;

  // Tab state
  activeTab: string;
  setActiveTab: (tab: string) => void;

  // Parameters state
  fieldValues: Record<string, any>;
  setFieldValues: (values: Record<string, any>) => void;
  updateFieldValue: (fieldId: string, value: any) => void;

  // Form validation
  errors: Record<string, string>;
  setErrors: (errors: Record<string, string>) => void;
  isFormValid: boolean;
  setIsFormValid: (isValid: boolean) => void;

  // Logs state
  logs: string[];
  setLogs: (logs: string[]) => void;
  addLog: (log: string) => void;
  clearLogs: () => void;

  // Execution state
  isExecuting: boolean;
  setIsExecuting: (isExecuting: boolean) => void;

  // Correlation ID for SSE streaming
  correlationId: string | null;
  setCorrelationId: (id: string | null) => void;

  // SSE connection state
  isStreaming: boolean;
  setIsStreaming: (isStreaming: boolean) => void;

  // Track if execution is ongoing but dialog is closed
  hasActiveExecution: boolean;
  setHasActiveExecution: (hasActive: boolean) => void;

  // Stop execution
  stopExecution: () => void;

  // Reopen dialog to view execution
  viewExecution: () => void;

  // Process values for execution
  processFieldValues: () => Record<string, any>;

  // Reset state
  resetState: () => void;
}

export const useExecutionStore = create<ExecutionState>()(
  persist(
    (set, get) => ({
      // Dialog state
      isDialogOpen: false,
      setDialogOpen: (isOpen) =>
        set((state) => ({
          isDialogOpen: isOpen,
          // If dialog is being opened and there's an active execution, update the state
          hasActiveExecution: isOpen ? false : state.hasActiveExecution || state.isStreaming,
        })),

      // Missing fields
      missingFields: [],
      setMissingFields: (fields) => set({ missingFields: fields }),

      // Tab state - default to parameters tab
      activeTab: "parameters",
      setActiveTab: (tab) => set({ activeTab: tab }),

      // Parameters state
      fieldValues: {},
      setFieldValues: (values) => set({ fieldValues: values }),
      updateFieldValue: (fieldId, value) =>
        set((state) => ({
          fieldValues: { ...state.fieldValues, [fieldId]: value },
        })),

      // Form validation
      errors: {},
      setErrors: (errors) => set({ errors }),
      isFormValid: false,
      setIsFormValid: (isValid) => set({ isFormValid: isValid }),

      // Logs state
      logs: [],
      setLogs: (logs) => set({ logs }),
      addLog: (log) => set((state) => ({ logs: [...state.logs, log] })),
      clearLogs: () => set({ logs: [] }),

      // Execution state
      isExecuting: false,
      setIsExecuting: (isExecuting) => set({ isExecuting }),

      // Correlation ID for SSE streaming
      correlationId: null,
      setCorrelationId: (id) => set({ correlationId: id }),

      // SSE connection state
      isStreaming: false,
      setIsStreaming: (isStreaming) =>
        set((state) => ({
          isStreaming,
          // Update hasActiveExecution based on streaming state
          hasActiveExecution:
            !state.isDialogOpen && isStreaming
              ? true
              : state.hasActiveExecution && !isStreaming
                ? false
                : state.hasActiveExecution,
        })),

      // Track if execution is ongoing but dialog is closed
      hasActiveExecution: false,
      setHasActiveExecution: (hasActive) => set({ hasActiveExecution: hasActive }),

      // Stop execution function - this will be called by the Stop Execution button
      stopExecution: () => {
        const state = get();
        // Add log entries
        const timestamp = new Date().toISOString().substring(11, 19); // Extract time HH:MM:SS

        // Add correlation ID to the log if available
        const correlationIdInfo = state.correlationId
          ? ` (Correlation ID: ${state.correlationId})`
          : '';

        const logEntry = `[${timestamp}] ⚠️ Workflow execution manually stopped by user${correlationIdInfo}`;
        const backendLogEntry = `[${timestamp}] 📡 Stop request sent to backend server`;

        // Update state
        set((state) => ({
          logs: [...state.logs, logEntry, backendLogEntry],
          isStreaming: false,
          hasActiveExecution: false,
        }));

        // Note: The actual SSE connection closing and backend API call are handled in the component
      },

      // View execution function - reopen dialog to view ongoing execution
      viewExecution: () => {
        console.log("Reopening execution dialog and resetting field values");
        // Reset field values to ensure fresh input values
        set((state) => ({
          isDialogOpen: true,
          activeTab: "logs",
          hasActiveExecution: false,
          fieldValues: {}, // Reset field values to ensure fresh input
          errors: {}, // Reset errors
          isFormValid: false, // Reset form validation
        }));
      },

      // Process values for execution
      processFieldValues: () => {
        const state = get();
        const processedValues: Record<string, any> = {};

        console.log("Processing field values from store:", state.fieldValues);

        state.missingFields.forEach((field) => {
          const fieldId = `${field.nodeId}_${field.name}`;

          // Use the current value from the node's configuration if available
          // Otherwise, use the value from the form
          let value;
          if (field.isEmpty === false && field.currentValue !== undefined) {
            value = field.currentValue;
            console.log(`Using pre-configured value for field ${fieldId}:`, value);
          } else {
            value = state.fieldValues[fieldId];
            console.log(`Using form value for field ${fieldId}:`, value);
          }

          console.log(`Processing field ${fieldId} with value:`, value);

          // Parse JSON strings for object and array types
          if (
            (field.inputType === "object" ||
              field.inputType === "dict" ||
              field.inputType === "json" ||
              field.inputType === "array" ||
              field.inputType === "list") &&
            typeof value === "string"
          ) {
            try {
              value = JSON.parse(value);
              console.log(`Successfully parsed JSON for field ${fieldId}:`, value);
            } catch (e) {
              console.error(`Failed to parse JSON for field ${fieldId}:`, e);
            }
          }

          // Convert string numbers to actual numbers
          if (
            (field.inputType === "number" ||
              field.inputType === "int" ||
              field.inputType === "float") &&
            typeof value === "string"
          ) {
            value = Number(value);
            console.log(`Converted number field ${fieldId} to:`, value);
          }

          // Group values by node
          if (!processedValues[field.nodeId]) {
            processedValues[field.nodeId] = {};
          }

          processedValues[field.nodeId][field.name] = value;
        });

        console.log("Final processed values for execution:", processedValues);
        return processedValues;
      },

      // Reset state
      resetState: () => {
        // Clear field values to ensure fresh input values each time
        console.log("Resetting execution store state - clearing all field values");
        set({
          activeTab: "parameters",
          fieldValues: {}, // Always reset to empty object
          errors: {},
          isFormValid: false,
          logs: [],
          isExecuting: false,
          missingFields: [],
          correlationId: null,
          isStreaming: false,
          hasActiveExecution: false,
        });
      },
    }),
    {
      name: "execution-store",
      partialize: (state) => ({
        // Don't persist fieldValues to ensure fresh input values each time
        logs: state.logs,
      }),
    },
  ),
);
