"""
add average rating to workflow templates

Revision ID: 003
Revises: 002
Create Date: 2025-05-13 17:40:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql import table, column

# revision identifiers, used by Alembic.
revision = "003"
down_revision = "002"
branch_labels = None
depends_on = None

# Define table references for use in migrations
workflow_templates = table(
    "workflow-templates",
    column("id", sa.String),
    column("name", sa.String),
    column("description", sa.String),
    column("tags", postgresql.JSON),
    column("owner_name", sa.String),
    column("average_rating", sa.Integer),
    column("visibility", sa.String),
    # Add other columns as needed
)

workflows = table(
    "workflows",
    column("id", sa.String),
    column("owner_name", sa.String),
    column("user_ids", postgresql.ARRAY(sa.String)),
    column("user_id", sa.String),
    # Add other columns as needed
)


def upgrade() -> None:
    """
    Apply all changes from old model to new model:
    1. Add average_rating column to workflow-templates
    2. Add visibility column to workflow-templates
    3. Remove owner_name column from workflow-templates
    4. Replace user_ids with user_id in workflows
    5. Remove owner_name column from workflows
    """
    # 1. Add average_rating column to workflow-templates as nullable
    op.add_column(
        "workflow-templates",
        sa.Column("average_rating", sa.Integer(), nullable=True),
    )

    # 2. Add visibility column to workflow-templates if it doesn't exist
    # First check if the column exists to avoid errors
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col["name"] for col in inspector.get_columns("workflow-templates")]

    if "visibility" not in columns:
        op.add_column(
            "workflow-templates",
            sa.Column(
                "visibility",
                sa.Enum("private", "public", name="workflowvisibilityenum"),
                nullable=True,
                server_default="private",
            ),
        )
        # Set default value for existing rows
        op.execute(workflow_templates.update().values(visibility="private"))

    # 3. Remove owner_name column from workflow-templates
    # First check if the column exists to avoid errors
    if "owner_name" in columns:
        op.drop_column("workflow-templates", "owner_name")

    # 4 & 5. Handle workflow table changes
    # Check if columns exist in workflows table
    workflow_columns = [col["name"] for col in inspector.get_columns("workflows")]

    # 4. Add user_id column if it doesn't exist
    if "user_id" not in workflow_columns and "user_ids" in workflow_columns:
        op.add_column(
            "workflows",
            sa.Column("user_id", sa.String(), nullable=True),
        )

        # Copy first user_id from user_ids array to user_id column
        # This is a best-effort migration - you may need to adjust this logic
        op.execute(
            """
            UPDATE "workflows" 
            SET user_id = (user_ids[1])
            WHERE user_ids IS NOT NULL AND array_length(user_ids, 1) > 0
            """
        )

        # Drop user_ids column
        op.drop_column("workflows", "user_ids")

    # 5. Remove owner_name column from workflows if it exists
    if "owner_name" in workflow_columns:
        op.drop_column("workflows", "owner_name")


def downgrade() -> None:
    """
    Revert all changes:
    1. Remove average_rating column from workflow-templates
    2. Remove visibility column from workflow-templates
    3. Add back owner_name column to workflow-templates
    4. Replace user_id with user_ids in workflows
    5. Add back owner_name column to workflows
    """
    # 1. Remove average_rating column
    op.drop_column("workflow-templates", "average_rating")

    # 2. Remove visibility column if it exists
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col["name"] for col in inspector.get_columns("workflow-templates")]

    if "visibility" in columns:
        op.drop_column("workflow-templates", "visibility")

    # 3. Add back owner_name column to workflow-templates
    op.add_column(
        "workflow-templates",
        sa.Column("owner_name", sa.String(), nullable=True),
    )

    # 4. Replace user_id with user_ids in workflows
    workflow_columns = [col["name"] for col in inspector.get_columns("workflows")]

    if "user_id" in workflow_columns and "user_ids" not in workflow_columns:
        # Add user_ids column
        op.add_column(
            "workflows",
            sa.Column("user_ids", postgresql.ARRAY(sa.String()), nullable=True),
        )

        # Copy user_id to user_ids array
        op.execute(
            """
            UPDATE "workflows" 
            SET user_ids = ARRAY[user_id]
            WHERE user_id IS NOT NULL
            """
        )

        # Drop user_id column
        op.drop_column("workflows", "user_id")

    # 5. Add back owner_name column to workflows
    op.add_column(
        "workflows",
        sa.Column("owner_name", sa.String(), nullable=True),
    )
