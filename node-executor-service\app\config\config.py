import logging
from pydantic import Field, validator # Import validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    kafka_bootstrap_servers: str = Field(
        default="localhost:9092", alias="KAFKA_BOOTSTRAP_SERVERS"
    )
    kafka_consumer_topic: str = Field(
        default="node-execution-request", alias="KAFKA_CONSUMER_TOPIC"
    )
    kafka_consumer_group_id: str = Field(
        default="node_executor_service", alias="KAFKA_CONSUMER_GROUP_ID"
    )
    kafka_results_topic: str = Field(default="node_results", alias="KAFKA_RESULTS_TOPIC")
    kafka_tool_executor_logs_topic: str = Field(
        default="tool_executor_logs", alias="KAFKA_TOOL_EXECUTOR_LOGS_TOPIC"
    )
    default_node_retries: int = Field(default=3, alias="DEFAULT_NODE_RETRIES")
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")
    max_concurrent_tasks: int = Field(default=10, alias="MAX_CONCURRENT_TASKS")

    # New settings for hardcoded values
    component_task_timeout: int = Field(default=60, alias="COMPONENT_TASK_TIMEOUT")
    component_worker_threads: int = Field(default=4, alias="COMPONENT_WORKER_THREADS")

    kafka_producer_request_timeout_ms: int = Field(default=60000, alias="KAFKA_PRODUCER_REQUEST_TIMEOUT_MS")
    kafka_consumer_fetch_min_bytes: int = Field(default=100, alias="KAFKA_CONSUMER_FETCH_MIN_BYTES")
    kafka_consumer_fetch_max_wait_ms: int = Field(default=500, alias="KAFKA_CONSUMER_FETCH_MAX_WAIT_MS")
    kafka_consumer_session_timeout_ms: int = Field(default=10000, alias="KAFKA_CONSUMER_SESSION_TIMEOUT_MS")
    kafka_consumer_heartbeat_interval_ms: int = Field(default=3000, alias="KAFKA_CONSUMER_HEARTBEAT_INTERVAL_MS")

    api_default_timeout: int = Field(default=30, alias="API_DEFAULT_TIMEOUT")
    api_default_max_content_length: int = Field(default=1000000, alias="API_DEFAULT_MAX_CONTENT_LENGTH")

    doc_default_max_content_length: int = Field(default=1000000, alias="DOC_DEFAULT_MAX_CONTENT_LENGTH")

    text_analysis_default_max_results: int = Field(default=10, alias="TEXT_ANALYSIS_DEFAULT_MAX_RESULTS")
    text_analysis_max_results_limit: int = Field(default=100, alias="TEXT_ANALYSIS_MAX_RESULTS_LIMIT")

    split_text_default_delimiter: str = Field(default=",", alias="SPLIT_TEXT_DEFAULT_DELIMITER")
    split_text_default_max_splits: int = Field(default=-1, alias="SPLIT_TEXT_DEFAULT_MAX_SPLITS")
    split_text_default_include_delimiter: bool = Field(default=False, alias="SPLIT_TEXT_DEFAULT_INCLUDE_DELIMITER")


    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}. Must be one of {valid_levels}")
        return v.upper()

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        populate_by_name=True,
        extra="ignore",
    )


try:
    settings = Settings()
except Exception as e:
    print(f"FATAL: Error loading Node Executor Service configuration settings: {e}")
    raise

log_level_int = getattr(logging, settings.log_level.upper(), logging.INFO)

# Configure the root logger initially
logging.basicConfig(
    level=log_level_int,
    format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s] %(message)s",  # Using consistent format
    datefmt="%Y-%m-%d %H:%M:%S",
)

# Re-configure loggers that might have been created before settings were loaded
# This is a common pattern when using a global settings object
for name in logging.root.manager.loggerDict:
    logger = logging.getLogger(name)
    logger.setLevel(log_level_int)
    # Ensure handlers are configured correctly if using custom logging_config
    # If using basicConfig, this might not be strictly necessary for existing loggers,
    # but ensures consistency for newly created ones or if handlers were added manually.
    # For this codebase, the custom setup_logger handles handlers, so this part
    # primarily ensures the level is correct.
    # If using setup_logger, the level is set there, but setting it here again
    # ensures the root logger's level is correct and propagates.
    pass # Handlers are managed by setup_logger in utils/logging_config.py

config_logger = logging.getLogger(__name__)
config_logger.info("Node Executor Service configuration loaded successfully.")
config_logger.debug(f"Node Executor Service loaded settings: {settings.model_dump()}")
