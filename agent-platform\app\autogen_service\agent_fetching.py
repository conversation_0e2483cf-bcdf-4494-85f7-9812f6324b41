import requests
import json
import os
from typing import Dict, Any, Optional, List

# Import necessary components (assuming these are correctly located relative to this file)
from autogen_core import SingleThreadedAgentRuntime, AgentType
from .agent_config_parser import AgentConfigParser
from ..tools.tool_loader import <PERSON><PERSON><PERSON><PERSON>der


# --- Cache for Agent Configurations ---
agent_config_cache: Dict[str, Dict[str, Any]] = {}

USE_API_FETCHING = False  # Default to using API fetching


def fetch_agents_from_api(user_id: str) -> Optional[Dict[Any, Any]]:
    """
    Fetch agent configurations for a specific user_id from the API.

    This function retrieves a list of agent records from the API, then iterates over
    each record to fetch its JSON configuration from the provided GCS URL. It also normalizes
    the configuration so that an 'agent_id' is always present.

    Args:
        user_id (str): The ID of the user whose agents to retrieve.

    Returns:
        dict: A dictionary mapping each agent record's ID (from the API response) to its
              normalized JSON configuration, or None if an error occurs.
    """
    # --- Configuration for API Fetching ---
    API_BASE_URL = "https://full-horses-arrive.loca.lt"
    API_AUTH_KEY = os.getenv("AGENT_API_AUTH_KEY")

    # Optionally override user_id from environment if needed
    user_id = os.getenv("Test_User_Id")
    url = f"{API_BASE_URL}/api/v1/agents/getAgentsByUserId/{user_id}"
    headers = {"X-Agent-Platform-Auth-Key": API_AUTH_KEY}

    print(f"[DEBUG] Sending GET request to: {url} for user_id: {user_id}")
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"[DEBUG] Received response with status code: {response.status_code}")
        response.raise_for_status()  # Raises HTTPError for bad responses

        response_json = response.json()
        print(f"[DEBUG] Parsed JSON response: {json.dumps(response_json, indent=2)}")

        # Get the list of agent records from the API response
        agents_data = response_json.get("data", [])
        if not isinstance(agents_data, list):
            print(
                f"[ERROR] Expected a list of agents in 'data', got {type(agents_data)}"
            )
            return None

        configs = {}
        for agent in agents_data:
            print(f"[DEBUG] Processing agent of type: {type(agent)}")
            record_id = agent.get("id")
            if record_id != 7:
                continue
            config_url = agent.get("config_url")
            if config_url:
                try:
                    print(
                        f"[DEBUG] Fetching config for agent record ID {record_id} from {config_url}"
                    )
                    config_response = requests.get(config_url, timeout=10)

                    # Save raw response to a file for debugging
                    with open(f"agent_config_{record_id}_raw.json", "w") as f:
                        f.write(config_response.text)

                    config_response.raise_for_status()

                    # Validate JSON before parsing
                    try:
                        # Get the raw configuration from the GCS URL
                        raw_text = config_response.text
                        config = json.loads(raw_text)

                        # Basic validation
                        required_fields = [
                            "id",
                            "agent_name",
                            "agent_category",
                            "system_message",
                        ]
                        missing_fields = [
                            field for field in required_fields if field not in config
                        ]
                        if missing_fields:
                            print(
                                f"[WARNING] Config for agent {record_id} is missing required fields: {missing_fields}"
                            )

                        # Normalize the configuration: ensure that 'agent_id' is always set.
                        if not config.get("agent_id"):
                            config["agent_id"] = config.get("id") or config.get(
                                "agent_name"
                            )
                        config["url"] = "testing/new/workflow/testing"
                        config["agent_category"] = "AIAgent"
                        configs[record_id] = config
                    except json.JSONDecodeError as je:
                        print(f"[ERROR] Invalid JSON for agent {record_id}: {je}")
                        print(
                            f"[DEBUG] First 100 chars of response: {raw_text[:100]}..."
                        )
                        configs[record_id] = None
                except requests.RequestException as e:
                    print(
                        f"[ERROR] Error fetching config for agent record ID {record_id} from {config_url}: {e}"
                    )
                    configs[record_id] = None
            else:
                print(f"[DEBUG] No config_url found for agent record ID {record_id}")
                configs[record_id] = None

    except requests.exceptions.RequestException as e:
        print(f"[ERROR] API request failed for user_id {user_id}: {e}")
        return None

    except json.JSONDecodeError:
        print(
            f"[ERROR] Failed to parse API response JSON for user_id {user_id}: {response.text}"
        )
        return None
    except Exception as e:
        print(
            f"[ERROR] An unexpected error occurred during API fetch for {user_id}: {e}"
        )
        return None

    print(f"Fetched {len(configs)} agent configurations")
    return configs


# --- Hardcoded Agent Configuration Function ---
def _fetch_agent_config_hardcoded(user_token_or_id: str) -> Optional[Dict[str, Any]]:
    """
    Provides hardcoded agent configurations for testing by reading from a JSON file.
    """
    print(f"Fetching *HARDCODED* agent config for token/id: {user_token_or_id}")

    config_file_path = "./config/agents.json"

    try:
        with open(config_file_path, "r") as file:
            agent_configs = json.load(file)
            print(f"Loaded agent configurations from {config_file_path}")
    except FileNotFoundError:
        print(f"[ERROR] Configuration file not found: {config_file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"[ERROR] Failed to parse JSON from {config_file_path}: {e}")
        return None

    agent_config = agent_configs.get(user_token_or_id)

    if agent_config:
        print(f"Hardcoded agent config found for: {user_token_or_id}")
        return agent_config
    else:
        print(f"No hardcoded agent config found for: {user_token_or_id}")
        return None


async def fetch_agent_config_from_db(user_token: str) -> Optional[Any]:
    """
    Fetches agent configuration(s) from cache or source (API/hardcoded).

    Returns:
        For API fetching: a dict mapping record IDs to agent configurations.
        For hardcoded: a single agent configuration dict.
    """
    # Check Cache First
    if user_token in agent_config_cache:
        print(f"Fetching agent config from cache for user token: {user_token}")
        return agent_config_cache[user_token]

    # Fetch from Source if not in Cache
    if user_token == "head_agent":
        print("Fetching Head Agent config (using hardcoded config)...")
        agent_config = _fetch_agent_config_hardcoded("head_agent_config_id")

    elif USE_API_FETCHING:
        print(f"Attempting to fetch agent config via API for user token: {user_token}")
        agent_config = fetch_agents_from_api(user_id=user_token)

        if not agent_config:
            print(f"Failed to fetch agent config from API for user token: {user_token}")
    else:
        agent_config = _fetch_agent_config_hardcoded(user_token)

    if agent_config:
        # Store in Cache
        agent_config_cache[user_token] = agent_config
        print(f"Agent config stored in cache for user token: {user_token}")
    else:
        print(
            f"No agent config found for user token: {user_token} (using {'API' if USE_API_FETCHING else 'Hardcoded'})"
        )

    return agent_config


# Global registry for registered agents
registered_agent_types: Dict[str, AgentType] = {}


async def register_agent_for_user(
    runtime: SingleThreadedAgentRuntime, tool_loader: ToolLoader, user_token: str
) -> Optional[List[AgentType]]:
    """
    Fetches agent configuration(s) (using fetch_agent_config_from_db) and registers
    the agent(s) for a specific user. For API fetching, it registers all agents returned.
    Excludes Head Agent registration, which is handled separately.

    Returns:
        A list of registered AgentType objects, or None if registration fails.
    """
    if user_token == "head_agent":
        return None

    agent_config_data = await fetch_agent_config_from_db(user_token)

    if not agent_config_data:
        print(
            f"Failed to fetch agent config for user token: {user_token} (using {'API' if USE_API_FETCHING else 'Hardcoded'})"
        )
        return None

    config_parser = AgentConfigParser(runtime, tool_loader)

    registered_agents: List[AgentType] = []

    # If using API fetching, agent_config_data is expected to be a dict mapping record IDs to configs.
    # Otherwise, it is a single configuration dict.
    if USE_API_FETCHING and isinstance(agent_config_data, dict):
        for record_id, config in agent_config_data.items():
            if config is None:
                print(f"[ERROR] No configuration for record ID {record_id}")
                continue
            # Normalize config if needed
            if not config.get("agent_id"):
                config["agent_id"] = config.get("id") or config.get("agent_name")
            print(
                f"Parsing and registering agent (record ID {record_id}) from API config for user token: {user_token}"
            )
            registered = await config_parser.parse_and_register_agent_from_config(
                config, registered_agent_types=registered_agent_types
            )
            if registered:
                registered_agents.append(registered)
            else:
                print(f"Agent registration failed for configuration: {config}")
    else:
        # Hardcoded config case
        print(
            f"Parsing and registering agent from hardcoded config for user token: {user_token}"
        )
        # Normalize config if needed
        if not agent_config_data.get("agent_id"):
            agent_config_data["agent_id"] = agent_config_data.get(
                "id"
            ) or agent_config_data.get("agent_name")

        registered = await config_parser.parse_and_register_agent_from_config(
            agent_config_data, registered_agent_types=registered_agent_types
        )
        if registered:
            registered_agents.append(registered)
        else:
            print(f"Agent registration failed for user token: {user_token}")

    if registered_agents:
        print(f"Agent(s) registered successfully for user token: {user_token}")
        return registered_agents
    else:
        return None


async def register_head_agent(
    runtime: SingleThreadedAgentRuntime, tool_loader: ToolLoader
) -> Optional[AgentType]:
    """
    Registers the Head Agent at application startup.
    """
    head_agent_config = await fetch_agent_config_from_db("head_agent")

    if not head_agent_config:
        print("Failed to fetch Head Agent config.")
        return None

    print("Parsing and registering Head Agent...")
    config_parser = AgentConfigParser(runtime, tool_loader)
    registered_head_agent = await config_parser.parse_and_register_agent_from_config(
        head_agent_config,
        registered_agent_types=registered_agent_types,
    )
    if not registered_head_agent:
        print("Head Agent registration failed.")
        return None

    print("Head Agent registered successfully.")
    return registered_head_agent
