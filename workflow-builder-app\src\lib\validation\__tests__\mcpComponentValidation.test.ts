import { describe, it, expect, beforeEach } from 'vitest';
import { validateWorkflow } from '../workflowValidation';
import { Node, Edge } from 'reactflow';
import { WorkflowNodeData } from '@/types';

// Mock the internal functions
jest.mock('../fieldValidation', () => {
  const original = jest.requireActual('../fieldValidation');
  return {
    ...original,
    // Expose the internal function for testing
    isMCPMarketplaceComponent: (node: Node<WorkflowNodeData>) => {
      if (!node || !node.data) return false;
    
      // Check various indicators that this is an MCP component
      return (
        node.data.type === "mcp" ||
        node.data.originalType === "MCPMarketplaceComponent" ||
        node.data.type === "MCPMarketplaceComponent" ||
        (node.data.definition && node.data.definition.type === "MCPMarketplaceComponent") ||
        (node.data.definition && node.data.definition.mcp_info) ||
        (node.data.definition && node.data.definition.path &&
         (node.data.definition.path.includes("mcp_marketplace") ||
          node.data.definition.path.includes("components.mcp")))
      );
    }
  };
});

describe('MCP Component Validation', () => {
  // Helper function to create a basic workflow with a Start node
  const createBasicWorkflow = () => {
    const nodes: Node<WorkflowNodeData>[] = [
      {
        id: 'start-node',
        type: 'default',
        position: { x: 0, y: 0 },
        data: {
          label: 'Start Node',
          originalType: 'StartNode',
          type: 'StartNode',
          definition: {
            name: 'StartNode',
            inputs: []
          },
          config: {}
        }
      }
    ];
    
    const edges: Edge[] = [];
    
    return { nodes, edges };
  };
  
  // Helper function to create an MCP Script Generator component
  const createScriptGenerator = (id: string, config: any = {}) => {
    return {
      id,
      type: 'default',
      position: { x: 100, y: 0 },
      data: {
        label: 'Script Generator',
        type: 'mcp',
        originalType: 'MCPMarketplaceComponent',
        definition: {
          name: 'ScriptGenerator',
          display_name: 'Script Generator',
          inputs: [
            {
              name: 'topic',
              display_name: 'Topic',
              input_type: 'string',
              info: 'The topic of the video to be covered'
            },
            {
              name: 'script_type',
              display_name: 'Script Type',
              input_type: 'string',
              info: 'The type of script'
            },
            {
              name: 'keywords',
              display_name: 'Keywords',
              input_type: 'object',
              info: 'Keywords for the script'
            },
            {
              name: 'video_type',
              display_name: 'Video Type',
              input_type: 'string',
              info: 'The type of video'
            },
            {
              name: 'api_key',
              display_name: 'API Key',
              input_type: 'string',
              info: 'Optional API key'
            }
          ],
          mcp_info: {
            tool_name: 'ScriptGenerator'
          }
        },
        config
      }
    } as Node<WorkflowNodeData>;
  };
  
  // Helper function to create an edge between nodes
  const createEdge = (source: string, target: string) => {
    return {
      id: `${source}-${target}`,
      source,
      target,
      type: 'default'
    } as Edge;
  };

  it('should identify Script Generator as an MCP component', () => {
    const { nodes } = createBasicWorkflow();
    const scriptGenerator = createScriptGenerator('script-gen');
    nodes.push(scriptGenerator);
    
    const result = validateWorkflow(nodes, [], { collectMissingFields: false });
    
    // The validation should recognize the MCP component
    expect(result.isValid).toBe(true);
  });

  it('should collect all missing required fields from Script Generator', () => {
    const { nodes, edges } = createBasicWorkflow();
    const scriptGenerator = createScriptGenerator('script-gen');
    nodes.push(scriptGenerator);
    edges.push(createEdge('start-node', 'script-gen'));
    
    const result = validateWorkflow(nodes, edges, { 
      validateConnectivity: true,
      collectMissingFields: true
    });
    
    expect(result.isValid).toBe(true);
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(4); // All fields except api_key should be required
    
    // Check that each required field is in the missing fields list
    expect(result.missingFields!.some(f => f.name === 'topic')).toBe(true);
    expect(result.missingFields!.some(f => f.name === 'script_type')).toBe(true);
    expect(result.missingFields!.some(f => f.name === 'keywords')).toBe(true);
    expect(result.missingFields!.some(f => f.name === 'video_type')).toBe(true);
    
    // api_key should not be in the missing fields list
    expect(result.missingFields!.some(f => f.name === 'api_key')).toBe(false);
  });

  it('should not collect fields that already have values', () => {
    const { nodes, edges } = createBasicWorkflow();
    const scriptGenerator = createScriptGenerator('script-gen', {
      topic: 'AI in Healthcare',
      script_type: 'Educational'
      // keywords and video_type are still missing
    });
    nodes.push(scriptGenerator);
    edges.push(createEdge('start-node', 'script-gen'));
    
    const result = validateWorkflow(nodes, edges, { 
      validateConnectivity: true,
      collectMissingFields: true
    });
    
    expect(result.isValid).toBe(true);
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(2); // Only keywords and video_type should be missing
    
    // Check that only the missing fields are in the list
    expect(result.missingFields!.some(f => f.name === 'keywords')).toBe(true);
    expect(result.missingFields!.some(f => f.name === 'video_type')).toBe(true);
    
    // Fields that have values should not be in the missing fields list
    expect(result.missingFields!.some(f => f.name === 'topic')).toBe(false);
    expect(result.missingFields!.some(f => f.name === 'script_type')).toBe(false);
  });

  it('should handle empty object values correctly', () => {
    const { nodes, edges } = createBasicWorkflow();
    const scriptGenerator = createScriptGenerator('script-gen', {
      topic: 'AI in Healthcare',
      script_type: 'Educational',
      keywords: {}, // Empty object should be considered empty
      video_type: 'Tutorial'
    });
    nodes.push(scriptGenerator);
    edges.push(createEdge('start-node', 'script-gen'));
    
    const result = validateWorkflow(nodes, edges, { 
      validateConnectivity: true,
      collectMissingFields: true
    });
    
    expect(result.isValid).toBe(true);
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(1); // Only keywords should be missing
    expect(result.missingFields![0].name).toBe('keywords');
  });

  it('should handle string representation of objects', () => {
    const { nodes, edges } = createBasicWorkflow();
    const scriptGenerator = createScriptGenerator('script-gen', {
      topic: 'AI in Healthcare',
      script_type: 'Educational',
      keywords: '{}', // Empty object as string should be considered empty
      video_type: 'Tutorial'
    });
    nodes.push(scriptGenerator);
    edges.push(createEdge('start-node', 'script-gen'));
    
    const result = validateWorkflow(nodes, edges, { 
      validateConnectivity: true,
      collectMissingFields: true
    });
    
    expect(result.isValid).toBe(true);
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(1); // Only keywords should be missing
    expect(result.missingFields![0].name).toBe('keywords');
  });

  it('should not consider non-empty object as missing', () => {
    const { nodes, edges } = createBasicWorkflow();
    const scriptGenerator = createScriptGenerator('script-gen', {
      topic: 'AI in Healthcare',
      script_type: 'Educational',
      keywords: { time: '10 minutes', audience: 'technical' }, // Non-empty object
      video_type: 'Tutorial'
    });
    nodes.push(scriptGenerator);
    edges.push(createEdge('start-node', 'script-gen'));
    
    const result = validateWorkflow(nodes, edges, { 
      validateConnectivity: true,
      collectMissingFields: true
    });
    
    expect(result.isValid).toBe(true);
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(0); // No missing fields
  });

  it('should handle multiple MCP components in the same workflow', () => {
    const { nodes, edges } = createBasicWorkflow();
    
    // Add two Script Generator components with different configurations
    const scriptGen1 = createScriptGenerator('script-gen-1', {
      topic: 'AI in Healthcare',
      // Other fields missing
    });
    
    const scriptGen2 = createScriptGenerator('script-gen-2', {
      topic: 'Machine Learning',
      script_type: 'Tutorial',
      // Other fields missing
    });
    
    nodes.push(scriptGen1, scriptGen2);
    edges.push(
      createEdge('start-node', 'script-gen-1'),
      createEdge('start-node', 'script-gen-2')
    );
    
    const result = validateWorkflow(nodes, edges, { 
      validateConnectivity: true,
      collectMissingFields: true
    });
    
    expect(result.isValid).toBe(true);
    expect(result.missingFields).toBeDefined();
    expect(result.missingFields!.length).toBe(5); // 3 from scriptGen1, 2 from scriptGen2
    
    // Count missing fields by node
    const scriptGen1MissingFields = result.missingFields!.filter(f => f.nodeId === 'script-gen-1');
    const scriptGen2MissingFields = result.missingFields!.filter(f => f.nodeId === 'script-gen-2');
    
    expect(scriptGen1MissingFields.length).toBe(3); // script_type, keywords, video_type
    expect(scriptGen2MissingFields.length).toBe(2); // keywords, video_type
  });
});
