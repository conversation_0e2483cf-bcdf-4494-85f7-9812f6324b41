import json
from pathlib import Path
from typing import List, Dict, Any, Optional

# Define path relative to this file or use an absolute path/env variable
# Assuming mcp_server_configs.json is in the project root (one level up from components)
CONFIG_FILE_PATH = Path(__file__).parent.parent.parent.parent / "mcp_server_configs.json"

_mcp_definitions: Optional[List[Dict[str, Any]]] = None

def load_mcp_definitions() -> List[Dict[str, Any]]:
    """Loads MCP server definitions from the JSON file."""
    global _mcp_definitions
    if _mcp_definitions is not None:
        return _mcp_definitions

    if not CONFIG_FILE_PATH.exists():
        print(f"Warning: MCP config file not found at {CONFIG_FILE_PATH}")
        _mcp_definitions = []
        return _mcp_definitions

    try:
        with open(CONFIG_FILE_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # Validate basic structure
            if isinstance(data, dict) and "nodes" in data and isinstance(data["nodes"], list):
                 _mcp_definitions = data["nodes"]
                 print(f"Successfully loaded {len(_mcp_definitions)} MCP definitions.")
            else:
                 print(f"Warning: Invalid format in {CONFIG_FILE_PATH}. Expected root object with 'nodes' list.")
                 _mcp_definitions = []

    except json.JSONDecodeError as e:
        
        _mcp_definitions = []
    except Exception as e:
        
        _mcp_definitions = []

    return _mcp_definitions

def get_mcp_definition_options() -> List[Dict[str, str]]:
    """Returns a list of {'value': id, 'label': display_name} for dropdowns."""
    definitions = load_mcp_definitions()
    options = [
        {"value": definition.get("id", ""), "label": definition.get("display_name", definition.get("id", "Unknown"))}
        for definition in definitions
        if definition.get("id") # Ensure ID exists
    ]
    # Sort options alphabetically by label
    return sorted(options, key=lambda x: x["label"])

def get_mcp_definition_by_id(definition_id: str) -> Optional[Dict[str, Any]]:
     """Finds a specific MCP definition by its ID."""
     definitions = load_mcp_definitions()
     for definition in definitions:
         if definition.get("id") == definition_id:
             return definition
     return None

def get_tool_options_for_definition(definition_id: str) -> List[Dict[str, str]]:
    """Returns tool options {'value': tool_name, 'label': tool_name} for a given definition ID."""
    definition = get_mcp_definition_by_id(definition_id)
    if not definition or "server_tools" not in definition:
        return []

    options = [
        {"value": tool.get("tool_name", ""), "label": tool.get("tool_name", "Unknown Tool")}
        for tool in definition["server_tools"]
        if tool.get("tool_name")
    ]
    return sorted(options, key=lambda x: x["label"])

def map_mcp_type_to_node_type(mcp_type: str) -> str:
    """Maps MCP schema types to workflow node output/input types."""
    # Basic mapping, expand as needed
    if mcp_type == "string":
        return "string"
    if mcp_type == "number":
        return "float" # Or "int" depending on context, default to float
    if mcp_type == "integer":
         return "int"
    if mcp_type == "boolean":
        return "bool"
    if mcp_type == "array":
        return "list"
    if mcp_type == "object":
        return "dict"
    return "Any" # Default fallback

def get_json_schema_from_mcp_schema(input_schema):
    """Convert MCP schema format to JSON schema format."""
    if not input_schema or not isinstance(input_schema, dict):
        return {"type": "object", "properties": {}}

    predefined_fields = input_schema.get("predefined_fields", [])
    properties = {}
    required = []

    for field in predefined_fields:
        field_name = field.get("field_name")
        if not field_name:
            continue

        data_type = field.get("data_type", {})
        field_type = data_type.get("type", "string")
        description = data_type.get("description", "")

        # Handle different types
        if field_type == "array":
            items = data_type.get("items", {"type": "string"})
            properties[field_name] = {
                "type": field_type,
                "description": description,
                "items": items
            }
        elif field_type == "object":
            nested_properties = data_type.get("properties", {})
            properties[field_name] = {
                "type": field_type,
                "description": description,
                "properties": nested_properties
            }
        else:
            properties[field_name] = {
                "type": field_type,
                "description": description
            }

        # Add to required list if needed
        if field.get("required", False):
            required.append(field_name)

    return {
        "type": "object",
        "properties": properties,
        "required": required
    }

# Load definitions on module import
load_mcp_definitions()
