/**
 * Utility functions for handling workflow approval events
 * This centralizes the approval event dispatching and tracking to prevent duplicate events
 */

// Add global type definitions for our debugging variables
declare global {
  interface Window {
    _lastApprovalTimestamp?: number;
    _pendingApproval?: {
      correlationId: string;
      nodeId: string;
      nodeName: string;
      timestamp: number;
    };
    _approvalEventHistory?: Array<{
      correlationId: string;
      nodeId: string;
      nodeName: string;
      timestamp: number;
      status: string;
    }>;
  }
}

// Initialize the approval event history array
if (typeof window !== 'undefined') {
  window._approvalEventHistory = window._approvalEventHistory || [];
}

// Track already dispatched approval events to prevent duplicates
const dispatchedApprovalEvents = new Set<string>();

/**
 * Dispatches a workflow approval needed event, with smarter deduplication
 *
 * @param correlationId The correlation ID of the workflow execution
 * @param nodeId The ID of the node requiring approval
 * @param nodeName The name of the node requiring approval
 */
export function dispatchApprovalNeededEvent(correlationId: string, nodeId: string, nodeName: string): void {
  // Skip if any parameter is missing or invalid
  if (!correlationId || !nodeId || nodeId === "unknown") {
    console.log(`Skipping approval event due to invalid parameters: correlationId=${correlationId}, nodeId=${nodeId}`);
    return;
  }

  // Create a unique key for this approval request
  const approvalKey = `${correlationId}_${nodeId}`;

  // Get the current timestamp
  const currentTimestamp = Date.now();

  // Add to approval history for debugging
  if (window._approvalEventHistory) {
    window._approvalEventHistory.push({
      correlationId,
      nodeId,
      nodeName: nodeName || nodeId,
      timestamp: currentTimestamp,
      status: 'requested'
    });

    // Keep only the last 10 events
    if (window._approvalEventHistory.length > 10) {
      window._approvalEventHistory = window._approvalEventHistory.slice(-10);
    }
  }

  // Check if we've already dispatched this approval
  // Only dispatch if:
  // 1. We haven't seen this exact approval key before, OR
  // 2. It's been more than 5 seconds since the last dispatch (to handle UI issues)
  const shouldDispatch = !dispatchedApprovalEvents.has(approvalKey) ||
                         (currentTimestamp - (window._lastApprovalTimestamp || 0) > 5000);

  if (shouldDispatch) {
    console.log(`Dispatching approval needed event for node ${nodeName || nodeId} (${nodeId})`);

    // Add to tracking set
    dispatchedApprovalEvents.add(approvalKey);

    // Store the last approval timestamp in a global variable for debugging
    window._lastApprovalTimestamp = currentTimestamp;

    // Dispatch the event with additional metadata
    const approvalEvent = new CustomEvent("workflow-approval-needed", {
      detail: {
        correlationId,
        nodeId,
        nodeName: nodeName || nodeId,
        timestamp: currentTimestamp,
        approvalKey
      }
    });

    window.dispatchEvent(approvalEvent);

    // Also set a flag on the window object that can be checked for debugging
    window._pendingApproval = {
      correlationId,
      nodeId,
      nodeName: nodeName || nodeId,
      timestamp: currentTimestamp
    };

    // Force a UI update
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('approval-ui-update'));
    }, 200);
  } else {
    console.log(`Skipping duplicate approval event for node ${nodeName || nodeId} (${nodeId})`);
  }
}

/**
 * Clears a specific approval event from the tracking system
 *
 * @param correlationId The correlation ID of the workflow execution
 * @param nodeId The ID of the node requiring approval
 */
export function clearApprovalEvent(correlationId: string, nodeId: string): void {
  const approvalKey = `${correlationId}_${nodeId}`;
  dispatchedApprovalEvents.delete(approvalKey);

  // Add to approval history for debugging
  if (window._approvalEventHistory) {
    window._approvalEventHistory.push({
      correlationId,
      nodeId,
      nodeName: nodeId,
      timestamp: Date.now(),
      status: 'cleared'
    });

    // Keep only the last 10 events
    if (window._approvalEventHistory.length > 10) {
      window._approvalEventHistory = window._approvalEventHistory.slice(-10);
    }
  }

  console.log(`Cleared approval event for node ${nodeId} with correlation ID ${correlationId}`);
}

/**
 * Clears all tracked approval events
 * Useful when components unmount or when resetting state
 */
export function clearAllApprovalEvents(): void {
  dispatchedApprovalEvents.clear();

  // Add to approval history for debugging
  if (window._approvalEventHistory) {
    window._approvalEventHistory.push({
      correlationId: 'all',
      nodeId: 'all',
      nodeName: 'all',
      timestamp: Date.now(),
      status: 'cleared_all'
    });

    // Keep only the last 10 events
    if (window._approvalEventHistory.length > 10) {
      window._approvalEventHistory = window._approvalEventHistory.slice(-10);
    }
  }

  // Clear the pending approval flag
  window._pendingApproval = undefined;

  console.log("Cleared all approval events");
}

/**
 * Checks if an approval event has already been dispatched
 *
 * @param correlationId The correlation ID of the workflow execution
 * @param nodeId The ID of the node requiring approval
 * @returns True if the approval event has already been dispatched
 */
export function hasApprovalEventBeenDispatched(correlationId: string, nodeId: string): boolean {
  const approvalKey = `${correlationId}_${nodeId}`;
  return dispatchedApprovalEvents.has(approvalKey);
}
