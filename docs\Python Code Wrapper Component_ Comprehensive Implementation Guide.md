# **Python Code Wrapper Component: Comprehensive Implementation Guide**

**Version:** 1.0 **Date:** October 26, 2023 **Status:** Final Design

## **Table of Contents:**

1. Introduction 1.1 Purpose 1.2 Scope  
2. System Architecture Context 2.1 Microservice Overview 2.2 Communication Patterns  
3. Component Specification (Workflow Service) 3.1 PythonCodeWrapperComponent Definition 3.2 Inputs 3.3 Outputs 3.4 execute Method (Parameter Preparation)  
4. Node Executor Service Implementation 4.1 PythonCodeWrapperExecutor 4.2 ContainerManager 4.2.1 Responsibilities and Features 4.2.2 Core Workflow 4.2.3 ContainerExecutionResult (Conceptual) 4.3 Docker Image Design 4.3.1 Dockerfile 4.3.2 requirements.txt 4.3.3 executor.py (In-Container Execution Script)  
5. Security Architecture 5.1 Multi-Layered Defense Strategy 5.2 Container Isolation Details 5.3 RestrictedPython Sandboxing (In-Container) 5.4 OS-Level Resource Limits (In-Container)  
6. Data Flow 6.1 Overview 6.2 Container Communication (Host ↔ Container) 6.3 Internal Container Data Flow (executor.py)  
7. Error Handling and Reporting 7.1 Error Categorization (ExecutionErrorType) 7.2 Propagation to Workflow 7.3 Container Recovery  
8. Deployment and Configuration 8.1 Dependencies 8.2 Environment Variables  
9. Docker Image Maintenance and Updates 9.1 Security Scanning 9.2 Update Strategy  
10. Use Cases and Examples 10.1 Data Transformation 10.2 Mathematical Calculations 10.3 Text Processing  
11. Implementation Checklist  
12. Python Function-Based Custom Node Architecture \- Feasibility Analysis & Implementation Strategy 12.1 Executive Summary 12.2 Feasibility Analysis 12.3 Recommended Implementation Strategy

## **1\. Introduction**

### **1.1 Purpose**

The Python Code Wrapper component enables users to execute custom Python code snippets within workflows. It provides a secure, isolated, and resource-controlled environment for arbitrary Python execution, integrated seamlessly with the platform's data flow and orchestration.

### **1.2 Scope**

This document details the design and implementation of the Python Code Wrapper, covering its definition in the Workflow Service, its execution logic in the Node Executor Service (including containerization), security measures, data handling, and error reporting.

## **2\. System Architecture Context**

### **2.1 Microservice Overview**

The component interacts with several microservices:

* **Workflow Builder App:** Frontend for designing workflows.  
* **Workflow Service:** Manages component definitions and workflow logic. The `PythonCodeWrapperComponent` is defined here.  
* **API Gateway:** Routes requests.  
* **Orchestration Engine:** Manages workflow execution flow.  
* **Node Executor Service:** Executes individual workflow nodes. The `PythonCodeWrapperExecutor` and `ContainerManager` reside here.

### **2.2 Communication Patterns**

graph TB  
    WBA\[Workflow Builder App\] \--\>|HTTP/REST| AG\[API Gateway\]  
    AG \--\>|gRPC| WS\[Workflow Service\]  
    AG \--\>|Kafka| OE\[Orchestration Engine\]  
    OE \--\>|Kafka| NES\[Node Executor Service\]  
    OE \--\>|Redis| STATE\[(State Storage)\]  
    WS \--\>|Component Discovery| WBA  
    NES \--\>|Results via Kafka| OE

## **3\. Component Specification (Workflow Service)**

### **3.1 PythonCodeWrapperComponent Definition**

Located in `workflow-service/app/components/processing/python_code_wrapper.py`.

\# workflow-service/app/components/processing/python\_code\_wrapper.py  
import time  
from typing import List, ClassVar, Dict, Any

from app.components.core.base\_node import BaseNode  
from app.models.workflow\_builder.components import (  
    InputBase, Output, IntInput, BoolInput  
)  
from app.models.workflow\_builder.context import WorkflowContext  
from app.models.workflow\_builder.node\_result import NodeResult  
from app.utils.workflow\_builder.input\_helpers import create\_dual\_purpose\_input

class PythonCodeWrapperComponent(BaseNode):  
    name: ClassVar\[str\] \= "PythonCodeWrapperComponent"  
    display\_name: ClassVar\[str\] \= "Python Code Wrapper"  
    description: ClassVar\[str\] \= (  
        "Execute custom Python code in a secure sandboxed environment. "  
        "Access input data via 'inputs' variable and assign results to 'result' variable."  
    )  
    category: ClassVar\[str\] \= "Processing"  
    icon: ClassVar\[str\] \= "Code" \# Or a more specific icon like 'Terminal' or 'Python'

    \# Inputs and Outputs defined below  
    \# Execute method defined below

### **3.2 Inputs**

inputs: ClassVar\[List\[InputBase\]\] \= \[  
        create\_dual\_purpose\_input(  
            name="python\_code",  
            display\_name="Python Code",  
            input\_type="code", \# Special UI rendering for code  
            required=True,  
            info="Python code to execute. Use 'inputs' dict for input data and assign results to 'result' variable.",  
            input\_types=\["string"\]  
        ),  
        create\_dual\_purpose\_input(  
            name="input\_data",  
            display\_name="Input Data",  
            input\_type="dict", \# Default type  
            required=False,  
            info="Data to pass to the Python code as 'inputs' variable (accessible as a dictionary).",  
            input\_types=\["dict", "list", "string", "Any"\] \# Accepts various structures  
        ),  
        IntInput(  
            name="timeout\_seconds",  
            display\_name="Timeout (seconds)",  
            value=5, \# Default value  
            info="Maximum execution time for the Python code in seconds (1-30). Actual container timeout might be slightly longer."  
            \# Validation (e.g., min=1, max=30) can be added to IntInput or handled in execute  
        ),  
        \# Potentially: Dropdown for Python version if multiple supported  
        \# StringInput(  
        \#     name="python\_version",  
        \#     display\_name="Python Version",  
        \#     value="3.11", \# Default  
        \#     options=\["3.10", "3.11", "3.12"\], \# Example  
        \#     info="Select the Python interpreter version."  
        \# ),  
        BoolInput(  
            name="enable\_debugging",  
            display\_name="Enable Debugging",  
            value=False,  
            info="Include detailed execution information in output logs (if supported by executor)."  
        )  
    \]

### **3.3 Outputs**

outputs: ClassVar\[List\[Output\]\] \= \[  
        Output(name="result", display\_name="Execution Result", output\_type="Any"),  
        Output(name="output\_logs", display\_name="Output Logs", output\_type="string"),  
        Output(name="execution\_time", display\_name="Execution Time (s)", output\_type="float"),  
        Output(name="error", display\_name="Error", output\_type="string")  
    \]

### **3.4 execute Method (Parameter Preparation)**

This method validates inputs and prepares parameters for the Orchestration Engine, which then forwards them to the Node Executor Service.

async def execute(self, context: WorkflowContext) \-\> NodeResult:  
        """Prepare parameters for Python code execution by the Node Executor Service."""  
        start\_time \= time.time()  
        context.log(f"Preparing {self.name} for execution...")

        try:  
            python\_code \= self.get\_input\_value("python\_code", context, "")  
            input\_data \= self.get\_input\_value("input\_data", context, {})  
            timeout\_seconds \= int(self.get\_input\_value("timeout\_seconds", context, 5))  
            \# python\_version \= self.get\_input\_value("python\_version", context, "3.11") \# If version selection is added  
            enable\_debugging \= self.get\_input\_value("enable\_debugging", context, False)

            \# Basic validation at Workflow Service level  
            if not python\_code or not python\_code.strip():  
                error\_msg \= "Python code is required and cannot be empty."  
                context.log(error\_msg, level="ERROR")  
                return NodeResult.error(error\_message=error\_msg, execution\_time=(time.time() \- start\_time))

            if not (1 \<= timeout\_seconds \<= 30): \# Enforce platform limits  
                 error\_msg \= "Timeout must be between 1 and 30 seconds."  
                 context.log(error\_msg, level="ERROR")  
                 return NodeResult.error(error\_message=error\_msg, execution\_time=(time.time() \- start\_time))

            \# Prepare tool\_parameters for the Orchestration Engine / Node Executor Service  
            tool\_parameters \= {  
                "python\_code": python\_code,  
                "input\_data": input\_data,  
                "timeout\_seconds": timeout\_seconds,  
                \# "python\_version": python\_version, \# If version selection is added  
                "enable\_debugging": enable\_debugging,  
                \# request\_id will be added by the executor or orchestration  
            }

            \# The actual execution is handled by NES.  
            \# This node's success means parameters are ready for dispatch.  
            prep\_time \= time.time() \- start\_time  
            context.log(f"{self.name} parameters prepared successfully in {prep\_time:.2f}s. Forwarding for execution.")

            \# Return success with tool\_parameters. The NodeResult outputs here are placeholders  
            \# as the actual outputs will come from the NES after execution.  
            \# The Orchestrator will use tool\_parameters to call the NES.  
            return NodeResult.success(  
                outputs={}, \# Actual outputs will be populated by NES result  
                tool\_parameters=tool\_parameters, \# Key for orchestrator  
                execution\_time=prep\_time  
            )

        except Exception as e:  
            error\_msg \= f"Error preparing {self.name}: {str(e)}"  
            context.log(error\_msg, level="ERROR")  
            return NodeResult.error(  
                error\_message=error\_msg,  
                execution\_time=(time.time() \- start\_time)  
            )

## **4\. Node Executor Service Implementation**

### **4.1 PythonCodeWrapperExecutor**

Located in `node-executor-service/app/components/python_code_wrapper_component.py`. This class uses the `ContainerManager` to execute code.

\# node-executor-service/app/components/python\_code\_wrapper\_component.py  
import logging  
import time \# Added missing import  
from typing import Dict, Any

from app.core\_.base\_component import BaseComponent, ValidationResult \# Assuming ValidationResult exists  
from app.core\_.component\_system import register\_component  
\# Assuming Pydantic model for request validation  
\# from app.models.components.python\_code\_wrapper import PythonCodeRequest  
from .container\_manager import ContainerManager, ContainerExecutionResult \# Assuming this structure

logger \= logging.getLogger(\_\_name\_\_)

@register\_component("PythonCodeWrapperComponent")  
class PythonCodeWrapperExecutor(BaseComponent):  
    def \_\_init\_\_(self):  
        super().\_\_init\_\_()  
        \# self.request\_schema \= PythonCodeRequest \# For input validation if needed here  
        self.container\_manager \= ContainerManager(  
            max\_concurrent\_containers=10 \# Configurable  
        )  
        logger.info("PythonCodeWrapperExecutor initialized with ContainerManager.")

    async def start(self):  
        """Start the component and its dependencies (e.g., ContainerManager)."""  
        await self.container\_manager.start()  
        logger.info("PythonCodeWrapperExecutor started.")

    async def stop(self):  
        """Stop the component and its dependencies."""  
        await self.container\_manager.stop()  
        logger.info("PythonCodeWrapperExecutor stopped.")

    \# Optional: Implement validate method if pre-execution validation beyond Pydantic is needed  
    \# async def validate(self, payload: Dict\[str, Any\]) \-\> ValidationResult:  
    \#     \# Use self.request\_schema for Pydantic validation  
    \#     \# Add any other specific validation logic  
    \#     return ValidationResult(is\_valid=True)

    async def process(self, payload: Dict\[str, Any\]) \-\> Dict\[str, Any\]:  
        """Execute Python code using the ContainerManager and format the response."""  
        request\_id \= payload.get("request\_id", "node\_exec\_unknown\_req\_id") \# Ensure request\_id  
        start\_process\_time \= time.time()

        try:  
            \# Extract parameters, preferring 'tool\_parameters' if available (from WorkflowService)  
            if "tool\_parameters" in payload and isinstance(payload\["tool\_parameters"\], dict):  
                parameters \= payload\["tool\_parameters"\]  
            else:  
                parameters \= payload  
              
            \# Ensure request\_id is part of parameters for container\_manager  
            parameters\["request\_id"\] \= request\_id

            python\_code \= parameters.get("python\_code", "")  
            if not python\_code.strip():  
                return {  
                    "status": "error", "error": "Python code cannot be empty.",  
                    "output\_logs": "", "execution\_time": 0.0  
                }

            \# Default Python version if not specified  
            python\_version \= parameters.get("python\_version", "3.11")

            container\_exec\_result: ContainerExecutionResult \= await self.container\_manager.execute\_code(  
                code=python\_code,  
                inputs=parameters.get("input\_data", {}),  
                timeout=parameters.get("timeout\_seconds", 5), \# User's desired code execution timeout  
                python\_version=python\_version,  
                request\_id=request\_id  
            )

            total\_node\_exec\_time \= time.time() \- start\_process\_time

            if container\_exec\_result.success:  
                return {  
                    "status": "success",  
                    "result": container\_exec\_result.result\_data,  
                    "output\_logs": container\_exec\_result.user\_output\_logs,  
                    "execution\_time": container\_exec\_result.total\_execution\_time, \# Time reported by ContainerManager  
                    \# "node\_processing\_time": total\_node\_exec\_time \# Optional: for internal metrics  
                }  
            else:  
                return {  
                    "status": "error",  
                    "error": container\_exec\_result.error\_message,  
                    "output\_logs": container\_exec\_result.user\_output\_logs,  
                    "execution\_time": container\_exec\_result.total\_execution\_time,  
                    "result": None,  
                    \# "node\_processing\_time": total\_node\_exec\_time \# Optional: for internal metrics  
                }

        except ValueError as ve: \# Specific, known configuration/validation errors  
            logger.error(f"Executor error for request {request\_id}: {str(ve)}")  
            return {"status": "error", "error": f"Executor error: {str(ve)}", "execution\_time": time.time() \- start\_process\_time}  
        except Exception as e: \# Unexpected errors within the executor  
            logger.exception(f"Critical unexpected error in PythonCodeWrapperExecutor for request {request\_id}: {e}")  
            return {"status": "error", "error": "A critical internal error occurred.", "execution\_time": time.time() \- start\_process\_time}

### **4.2 ContainerManager**

Located in `node-executor-service/app/components/container_manager.py`. This class is responsible for the entire lifecycle of Docker containers used for code execution. (The full code for `ContainerManager` as designed in the previous interactions is extensive. Key aspects are summarized here. Refer to the detailed design for the full implementation.)

#### **4.2.1 Responsibilities and Features:**

* Integrates with Docker using `docker-py`.  
* Executes each code snippet in a fresh, isolated Docker container. No container pooling.  
* Manages concurrency with `max_concurrent_containers`.  
* Handles Docker image availability (pulls if missing).  
* Sets resource limits on containers (memory, CPU, PIDs).  
* Configures containers for security (network disabled, read-only root, non-root user).  
* Manages data transfer via volume mounts (code, input, output).  
* Implements multi-level timeout handling (application-level monitoring, Docker wait timeouts).  
* Captures container logs (stdout/stderr).  
* Provides robust error handling for container operations.  
* Includes background tasks for cleaning up completed and orphaned containers.  
* Uses a `ThreadPoolExecutor` for blocking Docker SDK calls to keep the `asyncio` event loop unblocked.

#### **4.2.2 Core Workflow (Conceptual for `execute_code`):**

1. Check concurrency limits.  
2. Create a unique execution ID and prepare tracking (`ContainerExecution` object).  
3. Create a temporary host directory for volume mounts.  
4. Write user code, input data JSON, and config JSON to the temporary directory.  
5. Select the appropriate pre-built Docker image based on `python_version`.  
6. Create Docker container with specified resource limits, volume mounts, and security settings.  
7. Start the container.  
8. Monitor the container for completion or timeout.  
   * An `asyncio` task monitors the user-specified `timeout_seconds`. If exceeded, it attempts to kill the container.  
   * `container.wait()` is called with a slightly longer timeout as a fallback.  
9. Once completed (or timed out/failed):  
   * Retrieve container exit code and logs.  
   * Read `result.json` (and potentially `error.json`) from the output volume.  
   * Categorize errors.  
   * Schedule the container and temporary host directory for cleanup.  
10. Return a `ContainerExecutionResult` object.

#### **4.2.3 ContainerExecutionResult (Conceptual Data Class)**

This structure is returned by `ContainerManager.execute_code`.

from dataclasses import dataclass, field  
from typing import Optional, Any, Dict  
\# from .error\_handling import ExecutionErrorType \# Assuming ExecutionErrorType enum

\# This would be part of container\_manager.py  
@dataclass  
class ContainerExecutionResult:  
    success: bool  
    request\_id: str  
    execution\_id: str \# Internal ID from ContainerManager  
    result\_data: Optional\[Any\] \= None  
    user\_output\_logs: str \= ""  
    error\_message: Optional\[str\] \= None  
    \# error\_type: Optional\[ExecutionErrorType\] \= None \# For structured error reporting  
    \# system\_logs: str \= "" \# For internal debugging, not usually sent to user  
    code\_execution\_time: float \= 0.0 \# Time measured by executor.py inside container  
    total\_execution\_time: float \= 0.0 \# Wall-clock time managed by ContainerManager  
    exit\_code: Optional\[int\] \= None  
    \# Other metrics like memory\_used\_mb: Optional\[float\] \= None

### **4.3 Docker Image Design**

#### **4.3.1 Dockerfile**

A multi-stage Dockerfile is used to create a secure and minimal execution environment.

\# \=== Builder Stage \===  
FROM python:3.11-slim-bookworm as builder

ARG DEBIAN\_FRONTEND=noninteractive

\# Install build dependencies (e.g., for packages that compile C extensions)  
RUN apt-get update && apt-get install \-y \--no-install-recommends \\  
    gcc \\  
    g++ \\  
    && rm \-rf /var/lib/apt/lists/\*

\# Copy requirements and install Python packages as non-root user's local site-packages  
COPY requirements.txt /tmp/requirements.txt  
\# Create a temporary user for pip install \--user to mimic final user structure  
RUN useradd \--uid 1000 tempuser  
USER tempuser  
RUN pip install \--no-cache-dir \--user \-r /tmp/requirements.txt  
USER root

\# \=== Production Stage \===  
FROM python:3.11-slim-bookworm as production

ARG DEBIAN\_FRONTEND=noninteractive

\# Essential runtime dependencies & security updates  
RUN apt-get update && apt-get upgrade \-y && \\  
    apt-get install \-y \--no-install-recommends \\  
    ca-certificates \\  
    \# Add any other absolutely essential runtime libs (e.g., libgomp1 if numpy/scipy need it)  
    && apt-get clean && rm \-rf /var/lib/apt/lists/\*

\# Security: Remove package manager to prevent runtime package installation  
RUN rm \-rf /usr/bin/apt /usr/bin/apt-get /usr/bin/dpkg \\  
    && rm \-rf /var/lib/dpkg /var/lib/apt/extended\_states

\# Create a non-root user 'executor' (UID 1000, GID 1000\)  
RUN groupadd \-r executor \--gid=1000 && \\  
    useradd \-r \-g executor \--uid=1000 \--home-dir=/home/<USER>/bin/false executor && \\  
    mkdir \-p /home/<USER>
    chown \-R executor:executor /home/<USER>

\# Application directories  
RUN mkdir \-p /app/input /app/output /app/code /app/tmp && \\  
    chown \-R executor:executor /app && \\  
    \# /app itself might need to be writable by root for Docker to mount into,  
    \# but subdirectories should be owned by executor if executor.py writes to them directly (e.g. tmp)  
    \# However, output volume is typically mounted by Docker as writable.  
    chmod 755 /app \# /app/input, /app/code are read-only mounts  
                    \# /app/output is a read-write mount  
                    \# /app/tmp is a tmpfs mount

\# Copy Python packages from builder stage (maintaining the \--user install path)  
COPY \--from=builder \--chown=executor:executor /home/<USER>/.local /home/<USER>/.local

\# Copy the executor script  
COPY \--chown=executor:executor executor.py /app/executor.py  
RUN chmod 555 /app/executor.py \# Execute only

\# Security: Remove unnecessary SUID/SGID binaries and common tools  
RUN find / \-perm /6000 \-type f \-exec rm \-f {} \\; || true  
RUN rm \-f /bin/su /bin/mount /bin/umount \\  
    /usr/bin/ पिंक /usr/bin/chsh /usr/bin/wall \\  
    /usr/bin/wget /usr/bin/curl /usr/bin/ssh /usr/bin/scp \\  
    /usr/bin/telnet /usr/bin/ftp \\  
    /usr/bin/find /usr/bin/xargs \# If not needed by executor.py or its libs

\# Environment variables  
ENV PYTHONUNBUFFERED=1 \\  
    PYTHONDONTWRITEBYTECODE=1 \\  
    PYTHONHASHSEED=random \\  
    PATH="/home/<USER>/.local/bin:${PATH}" \\  
    HOME=/home/<USER>
    USER=executor

USER executor  
WORKDIR /app

\# Healthcheck (optional, but good practice if image is run long-lived, less critical for short-lived execs)  
\# HEALTHCHECK \--interval=30s \--timeout=3s \--start-period=5s \--retries=3 \\  
\# CMD python \-c "import sys; sys.exit(0)"

ENTRYPOINT \["python", "/app/executor.py"\]

(Note: Multiple Dockerfiles would be needed if supporting multiple Python versions, e.g., `Dockerfile.py310`, `Dockerfile.py311`, etc., or use build ARGs extensively.)

#### **4.3.2 requirements.txt (for the Docker image)**

\# Pinned versions for security and stability  
RestrictedPython==6.0  
psutil==5.9.8

\# Allowed safe libraries (pre-installed in the container)  
\# Ensure these do not have capabilities that bypass sandboxing (e.g., arbitrary network/file access)  
\# json \# (built-in)  
\# re \# (built-in)  
\# math \# (built-in)  
\# datetime \# (built-in)  
\# collections \# (built-in)  
\# copy \# (built-in)  
\# itertools \# (built-in)

\# Add other safe, commonly used libraries if desired, after careful vetting:  
\# Example: (ensure they are truly safe in this context)  
\# aniso8601==9.0.1 \# For date parsing, if datetime is not enough

#### **4.3.3 executor.py (In-Container Execution Script)**

This script runs inside the Docker container. It sets up the final sandboxing layers, executes the user's code, and writes results/errors to output files.

\# /app/executor.py  
import json  
import os  
import sys  
import time  
import traceback  
import resource \# For OS-level resource limits  
import signal   \# For alarm-based timeout

from RestrictedPython import compile\_restricted, safe\_globals  
from RestrictedPython.Guards import limited\_builtins \# Make sure this is the right import

\# \--- Configuration \---  
INPUT\_DATA\_PATH \= "/app/input/data.json"  
INPUT\_CODE\_PATH \= "/app/code/user\_code.py"  
INPUT\_CONFIG\_PATH \= "/app/input/config.json" \# For timeout, debug flags

OUTPUT\_RESULT\_PATH \= "/app/output/result.json"  
OUTPUT\_ERROR\_PATH \= "/app/output/error.json" \# For structured errors  
OUTPUT\_LOGS\_STDOUT\_PATH \= "/app/output/stdout.log" \# If redirecting stdout/stderr to files  
OUTPUT\_LOGS\_STDERR\_PATH \= "/app/output/stderr.log"

\# \--- Resource Limiting & Timeout \---  
def set\_resource\_limits(timeout\_seconds, memory\_mb):  
    \# Memory limit (Virtual Memory Size \- RLIMIT\_AS)  
    \# Docker's cgroup limit is the primary enforcer. This is a secondary defense.  
    \# Convert MB to bytes  
    mem\_bytes \= memory\_mb \* 1024 \* 1024  
    try:  
        resource.setrlimit(resource.RLIMIT\_AS, (mem\_bytes, mem\_bytes))  
    except Exception as e:  
        print(f"Warning: Could not set RLIMIT\_AS: {e}", file=sys.stderr)

    \# CPU time limit (seconds) \- RLIMIT\_CPU  
    \# This is for CPU time, not wall-clock time.  
    \# signal.alarm is better for wall-clock.  
    try:  
        \# Soft limit, Hard limit  
        resource.setrlimit(resource.RLIMIT\_CPU, (timeout\_seconds, timeout\_seconds \+ 5))  
    except Exception as e:  
        print(f"Warning: Could not set RLIMIT\_CPU: {e}", file=sys.stderr)

    \# Limit number of open files  
    try:  
        resource.setrlimit(resource.RLIMIT\_NOFILE, (64, 64)) \# Example: 64 files  
    except Exception as e:  
        print(f"Warning: Could not set RLIMIT\_NOFILE: {e}", file=sys.stderr)  
      
    \# Limit number of processes (should be 1 as we don't want fork bombs)  
    \# This might conflict if any allowed library legitimately uses subprocesses (which they shouldn't)  
    try:  
        resource.setrlimit(resource.RLIMIT\_NPROC, (1, 1))  
    except Exception as e:  
        \# Some systems might not allow setting NPROC to 1 if current process count is higher (e.g. due to threads)  
        \# Or if user is not privileged enough even within container for this specific rlimit  
        print(f"Warning: Could not set RLIMIT\_NPROC: {e}", file=sys.stderr)

class TimeoutException(Exception):  
    pass

def timeout\_handler(signum, frame):  
    raise TimeoutException("Execution timed out via SIGALRM")

\# \--- Execution Environment \---  
def get\_restricted\_globals(input\_values):  
    \# Start with safe globals  
    env\_globals \= safe\_globals.copy()

    \# Add carefully selected built-ins (limited\_builtins is usually very restrictive)  
    \# Customize this based on what 'limited\_builtins' provides and what's safe & needed  
    safe\_builtins\_dict \= limited\_builtins.copy() \# Or start with {} and add selectively  
      
    \# Example: Ensure basic types and functions are available  
    \# This might already be in limited\_builtins, check its contents  
    safe\_builtins\_dict.update({  
        'abs': abs, 'all': all, 'any': any, 'bool': bool, 'bytes': bytes,  
        'dict': dict, 'divmod': divmod, 'enumerate': enumerate, 'filter': filter,  
        'float': float, 'int': int, 'isinstance': isinstance, 'issubclass': issubclass,  
        'len': len, 'list': list, 'map': map, 'max': max, 'min': min,  
        'pow': pow, 'range': range, 'repr': repr, 'reversed': reversed,  
        'round': round, 'set': set, 'slice': slice, 'sorted': sorted,  
        'str': str, 'sum': sum, 'tuple': tuple, 'type': type, 'zip': zip,  
        \# 'print': \_safe\_print, \# Capture print statements  
    })

    \# Remove known dangerous built-ins explicitly, even if limited\_builtins should omit them  
    dangerous\_builtins\_to\_remove \= \[  
        'open', 'file', 'exec', 'eval', '\_\_import\_\_', 'compile', 'input',  
        'memoryview', 'object', 'property', 'classmethod', 'staticmethod', 'super'  
    \]  
    for b\_name in dangerous\_builtins\_to\_remove:  
        safe\_builtins\_dict.pop(b\_name, None)  
      
    env\_globals\['\_\_builtins\_\_'\] \= safe\_builtins\_dict

    \# Provide input data  
    env\_globals\['inputs'\] \= input\_values  
    env\_globals\['result'\] \= None \# User code is expected to set this

    \# Allowed modules (must be in requirements.txt and vetted)  
    \# These are imported here and passed, not allowing user \_\_import\_\_  
    try:  
        import json as \_json  
        env\_globals\['json'\] \= \_json  
        import re as \_re  
        env\_globals\['re'\] \= \_re  
        import math as \_math  
        env\_globals\['math'\] \= \_math  
        import datetime as \_datetime  
        env\_globals\['datetime'\] \= \_datetime  
        import collections as \_collections  
        env\_globals\['collections'\] \= \_collections  
        import copy as \_copy  
        env\_globals\['copy'\] \= \_copy  
        import itertools as \_itertools  
        env\_globals\['itertools'\] \= \_itertools  
    except ImportError as e:  
        print(f"Warning: Could not import an allowed module: {e}", file=sys.stderr)  
        \# Decide if this is a fatal error for the executor script

    return env\_globals

\# \--- Main Execution Logic \---  
def main():  
    start\_wall\_time \= time.monotonic()  
    user\_code\_str \= ""  
    input\_data \= {}  
    config \= {"timeout\_seconds": 30, "memory\_mb": 128} \# Defaults

    \# Redirect stdout/stderr to capture user prints  
    \# This should be done carefully if also writing JSON to stdout for results  
    \# For simplicity, we'll let logs go to Docker's stdout/stderr directly,  
    \# and NES ContainerManager will retrieve them.

    try:  
        \# Load config  
        if os.path.exists(INPUT\_CONFIG\_PATH):  
            with open(INPUT\_CONFIG\_PATH, 'r') as f:  
                config.update(json.load(f))  
          
        timeout\_seconds \= int(config.get("timeout\_seconds", 30))  
        memory\_mb \= int(config.get("memory\_mb", 128))

        \# Set resource limits first  
        set\_resource\_limits(timeout\_seconds, memory\_mb)

        \# Setup SIGALRM for wall-clock timeout  
        signal.signal(signal.SIGALRM, timeout\_handler)  
        signal.alarm(timeout\_seconds) \# Start the timer

        \# Load code and input data  
        with open(INPUT\_CODE\_PATH, 'r') as f:  
            user\_code\_str \= f.read()  
        if os.path.exists(INPUT\_DATA\_PATH):  
            with open(INPUT\_DATA\_PATH, 'r') as f:  
                input\_data \= json.load(f)

        \# Prepare execution environment  
        exec\_globals \= get\_restricted\_globals(input\_data)

        \# Compile and execute  
        \# RestrictedPython requires a filename for error reporting  
        byte\_code \= compile\_restricted(user\_code\_str, filename='\<user\_code\>', mode='exec')  
          
        exec\_start\_time \= time.monotonic()  
        exec(byte\_code, exec\_globals)  
        exec\_end\_time \= time.monotonic()  
          
        signal.alarm(0) \# Disable the alarm

        \# Get result  
        final\_result \= exec\_globals.get('result')  
        output \= {  
            "success": True,  
            "result\_data": final\_result,  
            "code\_execution\_time": exec\_end\_time \- exec\_start\_time,  
            "total\_script\_time": time.monotonic() \- start\_wall\_time  
        }  
        with open(OUTPUT\_RESULT\_PATH, 'w') as f:  
            json.dump(output, f)

    except TimeoutException as te:  
        signal.alarm(0) \# Ensure alarm is off  
        error\_output \= {  
            "success": False, "error\_type": "TIMEOUT", "error\_message": str(te),  
            "code\_execution\_time": time.monotonic() \- exec\_start\_time if 'exec\_start\_time' in locals() else 0,  
            "total\_script\_time": time.monotonic() \- start\_wall\_time  
        }  
        with open(OUTPUT\_ERROR\_PATH, 'w') as f:  
            json.dump(error\_output, f)  
        print(f"Execution failed: {str(te)}", file=sys.stderr) \# Also to container logs  
        sys.exit(1) \# Indicate error

    except MemoryError as me: \# Might be caught if RLIMIT\_AS is exceeded before OOM killer  
        signal.alarm(0)  
        error\_output \= {  
            "success": False, "error\_type": "MEMORY\_ERROR", "error\_message": "Memory limit exceeded during execution.",  
            "code\_execution\_time": time.monotonic() \- exec\_start\_time if 'exec\_start\_time' in locals() else 0,  
            "total\_script\_time": time.monotonic() \- start\_wall\_time  
        }  
        with open(OUTPUT\_ERROR\_PATH, 'w') as f:  
            json.dump(error\_output, f)  
        print(f"Execution failed: Memory Error", file=sys.stderr)  
        sys.exit(1)

    except SyntaxError as se:  
        signal.alarm(0)  
        error\_output \= {  
            "success": False, "error\_type": "COMPILATION\_ERROR",  
            "error\_message": f"Syntax error in Python code: {se.msg} on line {se.lineno}",  
            "error\_details": {"filename": se.filename, "lineno": se.lineno, "offset": se.offset, "text": se.text},  
            "total\_script\_time": time.monotonic() \- start\_wall\_time  
        }  
        with open(OUTPUT\_ERROR\_PATH, 'w') as f:  
            json.dump(error\_output, f)  
        print(f"Execution failed: {error\_output\['error\_message'\]}", file=sys.stderr)  
        sys.exit(1)  
          
    except Exception as e:  
        signal.alarm(0)  
        tb\_str \= traceback.format\_exc()  
        error\_output \= {  
            "success": False, "error\_type": "RUNTIME\_ERROR",  
            "error\_message": f"Runtime error: {type(e).\_\_name\_\_}", \# Truncated here as per user input  
            "traceback": tb\_str \# Added traceback for better debugging  
        }  
        with open(OUTPUT\_ERROR\_PATH, 'w') as f:  
            json.dump(error\_output, f)  
        print(f"Execution failed: {error\_output\['error\_message'\]}\\n{tb\_str}", file=sys.stderr)  
        sys.exit(1)

if \_\_name\_\_ \== "\_\_main\_\_":  
    \# Check if all required mount points exist  
    \# This is a basic check; more robust checks might be needed.  
    if not all(os.path.exists(p) for p in \[os.path.dirname(INPUT\_DATA\_PATH),   
                                           os.path.dirname(INPUT\_CODE\_PATH),   
                                           os.path.dirname(OUTPUT\_RESULT\_PATH)\]):  
        print("Error: Required mount directories (/app/input, /app/code, /app/output) not found.", file=sys.stderr)  
        sys.exit(2) \# Specific exit code for setup issues

    \# Ensure output directories are writable (though Docker volumes should handle this)  
    \# For files, we just need parent directory to exist.  
    \# os.makedirs(os.path.dirname(OUTPUT\_RESULT\_PATH), exist\_ok=True)  
    \# os.makedirs(os.path.dirname(OUTPUT\_ERROR\_PATH), exist\_ok=True)  
      
    main()

## **5\. Security Architecture**

### **5.1 Multi-Layered Defense Strategy**

* **Containerization (Docker):** Primary isolation mechanism.  
* **RestrictedPython:** Sandboxing within the container to limit Python capabilities.  
* **OS-Level Resource Limits:** `rlimit` inside the container for CPU, memory, file descriptors, processes.  
* **Docker Security Options:**  
  * `--cap-drop=ALL`: Drop all Linux capabilities.  
  * `--security-opt=no-new-privileges`: Prevent privilege escalation.  
  * `--read-only` root filesystem (except for necessary mounts).  
  * Non-root user inside the container.  
  * Network isolation (`--network=none`).  
  * PID limiting (`--pids-limit`).  
* **Minimal Base Image:** Reduces attack surface.  
* **Static Analysis & Linting:** On user code (potential future enhancement).  
* **Input Validation:** At multiple stages.

### **5.2 Container Isolation Details**

* Each execution gets a new, ephemeral container.  
* No container reuse or pooling to prevent state leakage.  
* Strict volume mount permissions (read-only for code/input, specific path for output).  
* `tmpfs` for `/app/tmp` for non-persistent temporary data within the container.

### **5.3 RestrictedPython Sandboxing (In-Container)**

* `compile_restricted` is used to compile user code.  
* `safe_globals` provides a limited set of global functions and objects.  
* `limited_builtins` (or a custom curated dictionary) restricts available built-in functions.  
* Dangerous modules (`os`, `sys` direct access, `subprocess`, `socket`, file I/O via `open`) are disallowed or heavily restricted.  
* `__import__` is disabled. Allowed modules are pre-imported by `executor.py` and passed into the execution scope.

### **5.4 OS-Level Resource Limits (In-Container)**

Applied by `executor.py` using the `resource` module:

* `RLIMIT_CPU`: Max CPU time.  
* `RLIMIT_AS`: Max virtual memory. (Docker's memory limit is the primary enforcer).  
* `RLIMIT_NOFILE`: Max open file descriptors.  
* `RLIMIT_NPROC`: Max number of processes (ideally 1).  
* `signal.alarm()` for wall-clock timeout, triggering `TimeoutException`.

## **6\. Data Flow**

### **6.1 Overview**

1. **Workflow Service (`PythonCodeWrapperComponent.execute`):**  
   * Receives `python_code`, `input_data`, `timeout_seconds` from workflow context.  
   * Validates parameters.  
   * Packages them into `tool_parameters`.  
   * Returns `NodeResult.success` with `tool_parameters` for the Orchestration Engine.  
2. **Orchestration Engine:**  
   * Receives `tool_parameters`.  
   * Adds `request_id`.  
   * Sends a job/message (e.g., via Kafka) to the Node Executor Service.  
3. **Node Executor Service (`PythonCodeWrapperExecutor.process`):**  
   * Receives the payload.  
   * Calls `ContainerManager.execute_code` with extracted parameters.  
4. **`ContainerManager.execute_code`:**  
   * Manages Docker container lifecycle (see 4.2.2).  
   * Writes `user_code.py`, `data.json` (inputs), `config.json` (timeout) to a host-mounted volume.  
   * Runs the container.  
5. **Inside Docker Container (`executor.py`):**  
   * Reads code, data, config from mounted `/app/code`, `/app/input`.  
   * Executes code using `RestrictedPython`.  
   * Writes `result.json` or `error.json` to mounted `/app/output`.  
   * Prints logs to stdout/stderr.  
6. **`ContainerManager` (Post-Execution):**  
   * Reads `result.json`/`error.json` from the host volume.  
   * Collects container logs.  
   * Returns `ContainerExecutionResult` to `PythonCodeWrapperExecutor`.  
7. **`PythonCodeWrapperExecutor`:**  
   * Formats the result into a dictionary.  
   * Returns it to the Orchestration Engine (e.g., via Kafka reply).  
8. **Orchestration Engine:**  
   * Receives execution result.  
   * Updates workflow state.  
   * Propagates outputs (`result`, `output_logs`, `error`) to subsequent workflow nodes.

### **6.2 Container Communication (Host ↔ Container)**

* **Input to Container:**  
  * Host creates a temporary directory (e.g., `/tmp/exec_abc123`).  
  * `code/user_code.py`: User's Python script.  
  * `input/data.json`: User's input data, serialized to JSON.  
  * `input/config.json`: Execution parameters like timeout, memory limits for `executor.py`.  
  * This directory is volume-mounted into `/app` in the container.  
    * `/tmp/exec_abc123/code:/app/code:ro`  
    * `/tmp/exec_abc123/input:/app/input:ro`  
    * `/tmp/exec_abc123/output:/app/output:rw` (Container writes here)  
* **Output from Container:**  
  * `output/result.json`: Contains `{"success": true, "result_data": ..., "code_execution_time": ...}`.  
  * `output/error.json`: Contains `{"success": false, "error_type": ..., "error_message": ...}`.  
  * Container `stdout`/`stderr`: Captured by Docker daemon, retrieved by `ContainerManager`.

### **6.3 Internal Container Data Flow (`executor.py`)**

1. Load `config.json` to get `timeout_seconds`, `memory_mb`.  
2. Apply resource limits (`set_resource_limits`).  
3. Set `signal.alarm(timeout_seconds)`.  
4. Load `user_code.py` into a string.  
5. Load `data.json` into a Python dictionary (`inputs_dict`).  
6. Prepare `safe_globals` and `safe_builtins` for `RestrictedPython`.  
7. `compile_restricted(user_code_str, ...)`  
8. `exec(byte_code, exec_globals)` where `exec_globals` contains `inputs` (the `inputs_dict`) and an empty `result` variable. User code is expected to assign to `result`.  
9. After execution (or on exception):  
   * Retrieve `exec_globals['result']`.  
   * Write to `result.json` or `error.json`.

## **7\. Error Handling and Reporting**

### **7.1 Error Categorization (`ExecutionErrorType` \- Conceptual Enum)**

To be defined in `container_manager.py` or a shared error module.

* `TIMEOUT`: Execution exceeded `timeout_seconds` (either `SIGALRM` in `executor.py` or `ContainerManager`'s own monitoring).  
* `MEMORY_ERROR`: Code exceeded memory limits (caught by `executor.py` via `MemoryError` or detected by `ContainerManager` if container OOM-killed).  
* `COMPILATION_ERROR`: Syntax error in user code, caught by `compile_restricted`.  
* `RUNTIME_ERROR`: Exception during user code execution within `RestrictedPython`.  
* `SANDBOX_VIOLATION`: Attempt to use disallowed features (if `RestrictedPython` raises specific errors for this).  
* `CONTAINER_SETUP_FAILURE`: Docker errors (image not found, volume mount issues, etc.).  
* `CONTAINER_INTERNAL_ERROR`: `executor.py` script itself failed unexpectedly.  
* `OUTPUT_SERIALIZATION_ERROR`: Result data could not be JSON serialized.  
* `UNKNOWN_ERROR`: Catch-all for unexpected issues.

### **7.2 Propagation to Workflow**

* `executor.py` writes structured error to `error.json`.  
* `ContainerManager` reads this, populates `ContainerExecutionResult` (e.g., `error_message`, `error_type`).  
* `PythonCodeWrapperExecutor` maps this to its output structure (e.g., `{"status": "error", "error": ..., "output_logs": ...}`).  
* Orchestration Engine receives this and makes the `error` string and `output_logs` available in the `PythonCodeWrapperComponent`'s output pins in the workflow.  
* The `result` pin would typically be `null` or empty on error.

### **7.3 Container Recovery**

* Containers are ephemeral; no recovery is attempted for a failed execution's container.  
* `ContainerManager` ensures cleanup of failed/timed-out containers and their associated host volumes.  
* The focus is on reporting the error accurately to the user/workflow.  
* A "retry" mechanism could be implemented at the Orchestration Engine level if desired for certain error types (e.g., transient `CONTAINER_SETUP_FAILURE`s, though less likely for this component).

## **8\. Deployment and Configuration**

### **8.1 Dependencies**

* **Workflow Service:** Standard Python libraries, `BaseNode` framework.  
* **Node Executor Service:**  
  * `docker-py` (Python client for Docker API).  
  * `aiohttp` (if using for async HTTP, though not directly in this component's core logic).  
  * Logging framework.  
* **Docker Image (`executor.py`):**  
  * `RestrictedPython`  
  * `psutil` (optional, if used for more detailed metrics, but `resource` is primary for limits).  
* **System:**  
  * Docker daemon accessible to Node Executor Service.  
  * Pre-built Docker images (e.g., `python-executor:3.11`) available in a registry or locally on executor nodes.

### **8.2 Environment Variables**

* **Node Executor Service / ContainerManager:**  
  * `MAX_CONCURRENT_CONTAINERS`: Default 10\.  
  * `DOCKER_IMAGE_NAME_TEMPLATE`: e.g., "python-executor" (version added dynamically).  
  * `DOCKER_IMAGE_TAG_DEFAULT`: e.g., "3.11".  
  * `CONTAINER_BASE_TMP_DIR`: Host path for temporary execution directories.  
  * `CONTAINER_CPU_LIMIT`: Default CPU shares/quota for Docker.  
  * `CONTAINER_MEMORY_LIMIT_MB`: Default memory limit for Docker (e.g., "256m").  
  * `CONTAINER_PID_LIMIT`: Default PID limit for Docker.  
  * `CONTAINER_WAIT_TIMEOUT_BUFFER_SECONDS`: Extra time for `container.wait()` beyond user timeout.  
  * `CLEANUP_INTERVAL_SECONDS`: How often background cleanup task runs.  
  * `ORPHAN_CONTAINER_TIMEOUT_SECONDS`: How long before an untracked container is considered orphaned.  
* **Docker Image (`executor.py` \- baked in or via `config.json`):**  
  * Default timeout/memory if not in `config.json`.

## **9\. Docker Image Maintenance and Updates**

### **9.1 Security Scanning**

* Regularly scan base images (e.g., `python:3.11-slim-bookworm`) for vulnerabilities using tools like Trivy, Clair, or integrated registry scanners.  
* Scan the custom `python-executor` image after builds.  
* Monitor vulnerabilities in Python packages listed in `requirements.txt`.

### **9.2 Update Strategy**

* **Base Image:** Update to the latest patched versions of `python-slim-bookworm` as they are released. Rebuild and test `python-executor` image.  
* **Python Packages (`requirements.txt`):**  
  * Periodically review and update pinned versions, especially for security fixes.  
  * Test thoroughly after updates, as `RestrictedPython` behavior might be sensitive.  
* **`executor.py`:** Update as needed for bug fixes or feature enhancements.  
* Versioning scheme for Docker images (e.g., `python-executor:3.11-v1.2.0`).  
* Deployment strategy for updated images (e.g., rolling updates for Node Executor Service if it pulls images dynamically, or pre-distribute images).

## **10\. Use Cases and Examples**

### **10.1 Data Transformation**

\# User code example:  
\# inputs \= {"data": \[{"id": 1, "value": " abc "}, {"id": 2, "value": " def "}\]}  
\# result \= None

transformed\_data \= \[\]  
for item in inputs.get("data", \[\]):  
    new\_item \= item.copy()  
    new\_item\["value"\] \= new\_item.get("value", "").strip().upper()  
    new\_item\["processed"\] \= True  
    transformed\_data.append(new\_item)

result \= {"transformed": transformed\_data}

### **10.2 Mathematical Calculations**

\# User code example:  
\# inputs \= {"a": 10, "b": 5, "operation": "add"}  
\# result \= None

a \= inputs.get("a", 0\)  
b \= inputs.get("b", 0\)  
operation \= inputs.get("operation", "add")

if operation \== "add":  
    calc\_result \= a \+ b  
elif operation \== "subtract":  
    calc\_result \= a \- b  
elif operation \== "multiply":  
    calc\_result \= a \* b  
elif operation \== "divide":  
    if b \== 0:  
        \# Cannot raise custom exceptions easily that get nicely caught  
        \# Best to return an error structure if possible, or let it be a runtime error  
        \# For now, let's assume b is not zero for simplicity of example  
        calc\_result \= "Error: Division by zero" \# Or handle as error  
    else:  
        calc\_result \= a / b  
else:  
    calc\_result \= "Error: Unknown operation"

result \= {"calculation\_result": calc\_result}

### **10.3 Text Processing**

\# User code example:  
\# inputs \= {"text": "Hello world, world of Python."}  
\# result \= None

import re \# Assuming 're' is an allowed pre-imported module

text\_to\_process \= inputs.get("text", "")  
word\_count \= len(re.findall(r'\\w+', text\_to\_process))  
char\_count \= len(text\_to\_process)  
\# Convert to lowercase and remove punctuation (simple example)  
processed\_text \= re.sub(r'\[^\\w\\s\]', '', text\_to\_process.lower())

result \= {  
    "word\_count": word\_count,  
    "char\_count": char\_count,  
    "processed\_text": processed\_text  
}

## **11\. Implementation Checklist**

* \[ \] **Workflow Service:**  
  * \[ \] `PythonCodeWrapperComponent` class defined.  
  * \[ \] Inputs (`python_code`, `input_data`, `timeout_seconds`, `enable_debugging`) defined.  
  * \[ \] Outputs (`result`, `output_logs`, `execution_time`, `error`) defined.  
  * \[ \] `execute` method implemented for parameter validation and packaging.  
  * \[ \] Unit tests for `execute` method.  
* \[ \] **Node Executor Service:**  
  * \[ \] `PythonCodeWrapperExecutor` class defined and registered.  
  * \[ \] `process` method implemented.  
  * \[ \] Integration with `ContainerManager`.  
  * \[ \] Input validation (basic, or Pydantic schema).  
  * \[ \] `ContainerManager` class implemented:  
    * \[ \] Docker client integration (`docker-py`).  
    * \[ \] `execute_code` method core logic.  
    * \[ \] Concurrency management (`asyncio.Semaphore`).  
    * \[ \] Temporary directory and file management for I/O.  
    * \[ \] Docker image selection logic.  
    * \[ \] Container creation with security options (non-root, no-new-privileges, cap-drop, net=none, resource limits).  
    * \[ \] Volume mount setup.  
    * \[ \] Multi-level timeout handling (async task \+ `container.wait()`).  
    * \[ \] Container log capture.  
    * \[ \] Result/error file parsing from output volume.  
    * \[ \] `ContainerExecutionResult` data class.  
    * \[ \] Background cleanup task for containers and volumes.  
    * \[ \] Robust error handling for Docker operations.  
    * \[ \] ThreadPoolExecutor for blocking Docker calls.  
    * \[ \] Unit tests for `ContainerManager` (mocking Docker SDK).  
* \[ \] **Docker Image:**  
  * \[ \] `Dockerfile` (multi-stage, non-root user, minimal packages, security hardening).  
  * \[ \] `requirements.txt` (pinned `RestrictedPython`, `psutil`).  
  * \[ \] `executor.py` script:  
    * \[ \] Load code, input data, config from mounted files.  
    * \[ \] `set_resource_limits` (CPU, memory, files, processes) using `resource`.  
    * \[ \] `signal.alarm` for wall-clock timeout.  
    * \[ \] `get_restricted_globals` (curated `safe_globals`, `limited_builtins`, pre-imported safe modules).  
    * \[ \] `compile_restricted` and `exec`.  
    * \[ \] Capture result from `result` variable.  
    * \[ \] Write `result.json` or `error.json` to output mount.  
    * \[ \] Handle `TimeoutException`, `MemoryError`, `SyntaxError`, general `Exception`.  
    * \[ \] Exit codes for `executor.py` (0 for success, \>0 for errors).  
  * \[ \] Test Docker image build.  
  * \[ \] Manual test execution within the built image.  
* \[ \] **Security:**  
  * \[ \] Review all security layers and configurations.  
  * \[ \] Test disallowed operations (file I/O, network, subprocess) in user code.  
  * \[ \] Test resource limit enforcement (timeout, memory).  
* \[ \] **Error Handling:**  
  * \[ \] `ExecutionErrorType` enum (or equivalent) defined.  
  * \[ \] Consistent error propagation from `executor.py` to workflow outputs.  
* \[ \] **Configuration:**  
  * \[ \] Define all relevant environment variables for NES/ContainerManager.  
* \[ \] **Documentation:**  
  * \[ \] User documentation for `PythonCodeWrapperComponent` (how to use, limitations).  
  * \[ \] This Implementation Guide reviewed and finalized.  
* \[ \] **Testing:**  
  * \[ \] End-to-end workflow test with `PythonCodeWrapperComponent`.  
  * \[ \] Test various valid and invalid user code snippets.  
  * \[ \] Test edge cases (empty code, large inputs, max timeout).

## **12\. Python Function-Based Custom Node Architecture \- Feasibility Analysis & Implementation Strategy**

### **12.1 Executive Summary**

After analyzing the existing codebase patterns and architecture, this function-based custom node system is highly feasible and aligns well with the established distributed workflow platform. The key insight is leveraging the existing Python Code Wrapper infrastructure while adding a metadata-driven component generation layer.

### **12.2 Feasibility Analysis**

#### **✅ Strong Alignment Points**

* **Existing Security Infrastructure:** Can directly reuse Python Code Wrapper's Docker containerization and RestrictedPython sandboxing.  
* **Component Patterns:** Well-established `BaseNode`/`BaseComponent` patterns provide clear templates for auto-generation.  
* **Registration System:** `@register_component` decorator system easily supports dynamic registration.  
* **Input System:** `create_dual_purpose_input()` helper provides consistent input handling.  
* **Response Format:** Standard `{status, result/error, execution_time}` format is well-defined.

#### **⚠️ Potential Integration Challenges**

* **Dynamic Component Discovery:** Need to extend component service to handle runtime-generated components.  
* **UI State Management:** Frontend needs to handle dynamically added components.  
* **Function Versioning:** Managing function updates without breaking existing workflows.  
* **Type System Complexity:** Mapping Python types to workflow input/output types.

#### **🔧 Required Infrastructure Extensions**

* Function metadata storage and versioning.  
* Dynamic component registration service.  
* Function parsing and validation pipeline.  
* UI component generation API.

### **12.3 Recommended Implementation Strategy**

#### **Phase 1: Core Function Parser with Strategy Pattern**

\# workflow-service/app/services/function\_parser/base\_parser.py

from abc import ABC, abstractmethod  
from typing import Dict, Any, List, Union \# Added Union  
from dataclasses import dataclass  
import inspect  
from enum import Enum  
from datetime import datetime \# Added datetime for metadata

class ParsingStrategy(Enum):  
    TYPE\_HINTS\_BASIC \= "type\_hints\_basic"  \# Phase 1 MVP  
    STRUCTURED\_DOCSTRING \= "structured\_docstring"  \# Phase 2  
    DECORATOR\_METADATA \= "decorator\_metadata"  \# Phase 3

@dataclass  
class FunctionInputSpec:  
    name: str  
    display\_name: str  
    input\_type: str  \# Maps to workflow input types  
    required: bool  
    default\_value: Any \= None  
    description: str \= ""  
    allowed\_types: List\[str\] \= None

@dataclass  
class FunctionOutputSpec:  
    name: str  
    display\_name: str  
    output\_type: str  
    description: str \= ""

@dataclass  
class FunctionComponentMetadata:  
    function\_name: str  
    component\_name: str  \# Auto-generated: CamelCase \+ "Component"  
    display\_name: str  
    description: str  
    category: str \= "Custom"  
    icon: str \= "Function"  
    inputs: List\[FunctionInputSpec\]  
    outputs: List\[FunctionOutputSpec\]  
    parsing\_version: str  
    function\_code: str  
    allowed\_libraries: List\[str\]  
    security\_level: str \= "sandboxed"  
    created\_at: str  
    updated\_at: str

class FunctionParser(ABC):  
    """Base class for function parsing strategies."""  
      
    @abstractmethod  
    def can\_parse(self, function\_code: str) \-\> bool:  
        """Check if this parser can handle the function."""  
        pass  
      
    @abstractmethod  
    def parse\_function(self, function\_code: str, function\_name: str) \-\> FunctionComponentMetadata:  
        """Parse function and return component metadata."""  
        pass  
      
    @abstractmethod  
    def validate\_function(self, function\_code: str) \-\> List\[str\]:  
        """Validate function and return list of errors."""  
        pass

class FunctionParserRegistry:  
    """Registry for function parsing strategies."""  
      
    def \_\_init\_\_(self):  
        self.parsers: Dict\[ParsingStrategy, FunctionParser\] \= {}  
        self.default\_strategy \= ParsingStrategy.TYPE\_HINTS\_BASIC  
      
    def register\_parser(self, strategy: ParsingStrategy, parser: FunctionParser):  
        self.parsers\[strategy\] \= parser  
      
    def parse\_function(self, function\_code: str, function\_name: str,   
                       strategy: ParsingStrategy \= None) \-\> FunctionComponentMetadata:  
        """Parse function using specified or auto-detected strategy."""  
          
        if strategy is None:  
            strategy \= self.\_detect\_strategy(function\_code)  
          
        parser \= self.parsers.get(strategy)  
        if not parser:  
            raise ValueError(f"No parser registered for strategy: {strategy}")  
          
        if not parser.can\_parse(function\_code):  
            raise ValueError(f"Parser {strategy} cannot handle this function")  
          
        return parser.parse\_function(function\_code, function\_name)  
      
    def \_detect\_strategy(self, function\_code: str) \-\> ParsingStrategy:  
        """Auto-detect the best parsing strategy for the function."""  
          
        \# Try parsers in order of sophistication  
        for strategy\_key in \[ParsingStrategy.DECORATOR\_METADATA,   
                             ParsingStrategy.STRUCTURED\_DOCSTRING,  
                             ParsingStrategy.TYPE\_HINTS\_BASIC\]:  
            parser \= self.parsers.get(strategy\_key)  
            if parser and parser.can\_parse(function\_code):  
                return strategy\_key  
          
        return self.default\_strategy

#### **Phase 1 MVP: Type Hints \+ Basic Docstring Parser**

\# workflow-service/app/services/function\_parser/type\_hints\_parser.py

import ast  
import inspect  
from typing import get\_type\_hints, get\_origin, get\_args, Dict, Any, List, Union \# Added Union  
from datetime import datetime \# Added datetime  
import re

\# Assuming FunctionParser, FunctionComponentMetadata, FunctionInputSpec, FunctionOutputSpec are imported from base\_parser  
\# from .base\_parser import FunctionParser, FunctionComponentMetadata, FunctionInputSpec, FunctionOutputSpec

class TypeHintsParser(FunctionParser):  
    """Parser for functions with type hints and basic docstrings."""  
      
    def \_find\_function\_def(self, tree: ast.AST) \-\> ast.FunctionDef:  
        """Helper to find the first function definition in the AST."""  
        for node in ast.walk(tree):  
            if isinstance(node, ast.FunctionDef):  
                return node  
        return None

    def can\_parse(self, function\_code: str) \-\> bool:  
        """Check if function has type hints."""  
        try:  
            tree \= ast.parse(function\_code)  
            func\_def \= self.\_find\_function\_def(tree)  
              
            if not func\_def:  
                return False

            \# Check for type hints on parameters or return  
            has\_type\_hints \= (  
                any(arg.annotation for arg in func\_def.args.args) or  
                func\_def.returns is not None  
            )  
              
            return has\_type\_hints  
        except:  
            return False  
      
    def \_generate\_component\_name(self, function\_name: str) \-\> str:  
        """Generates a CamelCase component name from a snake\_case function name."""  
        return "".join(word.capitalize() for word in function\_name.split('\_')) \+ "Component"

    def \_generate\_display\_name(self, name: str) \-\> str:  
        """Generates a display name from snake\_case or camelCase."""  
        \# Add space before capital letters (for camelCase)  
        s1 \= re.sub('(.)(\[A-Z\]\[a-z\]+)', r'\\1 \\2', name)  
        \# Add space before numbers followed by letters (for names like item1Name)  
        s2 \= re.sub('(\[a-z0-9\])(\[A-Z\])', r'\\1 \\2', s1)  
        \# Replace underscores with spaces and capitalize words  
        return ' '.join(word.capitalize() for word in s2.replace('\_', ' ').split())

    def \_extract\_description(self, docstring: str) \-\> str:  
        """Extracts the main description from the docstring (first non-empty part)."""  
        if not docstring:  
            return ""  
        lines \= \[line.strip() for line in docstring.strip().split('\\n')\]  
        description\_lines \= \[\]  
        for line in lines:  
            if line.startswith(':param') or line.startswith(':return'):  
                break  
            if line: \# Take lines until a parameter or return definition  
                description\_lines.append(line)  
            elif description\_lines: \# Stop at the first empty line after some description  
                break  
        return " ".join(description\_lines)

    def \_extract\_param\_description(self, param\_name: str, docstring: str) \-\> str:  
        """Extracts description for a specific parameter from the docstring."""  
        if not docstring:  
            return ""  
        \# Regex to find :param param\_name: description  
        match \= re.search(rf":param\\s+{re.escape(param\_name)}\\s\*:\\s\*(.+)", docstring)  
        if match:  
            return match.group(1).strip()  
        return ""

    def parse\_function(self, function\_code: str, function\_name: str) \-\> FunctionComponentMetadata:  
        """Parse function with type hints."""  
          
        \# Execute function code to get function object  
        namespace \= {}  
        try:  
            exec(function\_code, namespace)  
        except Exception as e:  
            raise ValueError(f"Could not execute function code: {e}")

        func \= namespace.get(function\_name)  
        if not func or not callable(func):  
            raise ValueError(f"Function '{function\_name}' not found or not callable in provided code.")  
          
        \# Get type hints  
        try:  
            type\_hints \= get\_type\_hints(func)  
        except Exception as e: \# Broad exception as get\_type\_hints can fail in various ways  
            \# Fallback or raise error. For now, let's use an empty dict.  
            \# print(f"Warning: Could not get type hints for {function\_name}: {e}", file=sys.stderr)  
            type\_hints \= {}

        signature \= inspect.signature(func)  
          
        \# Parse docstring  
        docstring \= inspect.getdoc(func) or ""  
        description \= self.\_extract\_description(docstring)  
          
        \# Generate inputs from parameters  
        inputs \= \[\]  
        for param\_name, param in signature.parameters.items():  
            input\_spec \= self.\_create\_input\_spec(param\_name, param, type\_hints, docstring)  
            inputs.append(input\_spec)  
          
        \# Generate outputs from return type  
        outputs \= self.\_create\_output\_specs(signature, type\_hints, docstring) \# Added docstring  
          
        \# Generate component name  
        component\_name \= self.\_generate\_component\_name(function\_name)  
          
        return FunctionComponentMetadata(  
            function\_name=function\_name,  
            component\_name=component\_name,  
            display\_name=self.\_generate\_display\_name(function\_name),  
            description=description,  
            inputs=inputs,  
            outputs=outputs,  
            parsing\_version="type\_hints\_basic\_v1",  
            function\_code=function\_code,  
            allowed\_libraries=\["json", "re", "math", "datetime", "collections", "copy", "itertools"\],  
            created\_at=datetime.utcnow().isoformat(),  
            updated\_at=datetime.utcnow().isoformat()  
        )  
      
    def \_create\_input\_spec(self, param\_name: str, param: inspect.Parameter,   
                           type\_hints: Dict, docstring: str) \-\> FunctionInputSpec:  
        """Create input specification from parameter."""  
          
        \# Map Python types to workflow input types  
        type\_mapping \= {  
            str: "string",  
            int: "int",   
            float: "float",  
            bool: "bool",  
            dict: "dict",  
            list: "list",  
            type(None): "Any" \# For Optional\[Any\] or untyped  
        }  
          
        \# Get parameter type  
        param\_annotation \= param.annotation  
        if param\_annotation is inspect.Parameter.empty: \# No type hint  
             param\_actual\_type \= str \# Default to string if no type hint  
        else:  
            param\_actual\_type \= type\_hints.get(param\_name, str) \# Fallback to str if lookup fails

        input\_type\_str \= type\_mapping.get(param\_actual\_type, "string") \# Default to string for unknown types  
          
        \# Handle Union types (Optional)  
        origin\_type \= get\_origin(param\_actual\_type)  
        if origin\_type is Union or str(origin\_type) \== 'typing.Union': \# Check str for older Pythons  
            args \= get\_args(param\_actual\_type)  
            if len(args) \== 2 and type(None) in args:  
                \# Optional type  
                non\_none\_type \= next(arg for arg in args if arg is not type(None))  
                input\_type\_str \= type\_mapping.get(non\_none\_type, "string")  
          
        \# Determine if required  
        required \= param.default is inspect.Parameter.empty  
        default\_value \= None if required else param.default  
          
        \# Extract description from docstring  
        param\_description \= self.\_extract\_param\_description(param\_name, docstring)  
          
        return FunctionInputSpec(  
            name=param\_name,  
            display\_name=self.\_generate\_display\_name(param\_name),  
            input\_type=input\_type\_str,  
            required=required,  
            default\_value=default\_value,  
            description=param\_description,  
            allowed\_types=\[input\_type\_str, "Any"\] \# Allow 'Any' for flexibility  
        )  
      
    def \_create\_output\_specs(self, signature: inspect.Signature,   
                            type\_hints: Dict, docstring: str) \-\> List\[FunctionOutputSpec\]: \# Added docstring  
        """Create output specifications from return type."""  
          
        \# Basic type mapping for outputs  
        type\_mapping \= {  
            str: "string", int: "int", float: "float", bool: "bool",  
            dict: "dict", list: "list", type(None): "Any"  
        }

        return\_annotation \= signature.return\_annotation  
        if return\_annotation is inspect.Signature.empty:  
            \# No return type hint, assume a single 'Any' output named 'result'  
            \# or could be based on docstring if available.  
            \# For now, stick to a generic 'result'  
            output\_type\_str \= "Any"  
        else:  
            return\_actual\_type \= type\_hints.get('return', dict) \# Default to dict if lookup fails  
            output\_type\_str \= type\_mapping.get(return\_actual\_type, "Any") \# Default to Any for complex/unknown return types  
              
            \# Handle Union for return type (e.g. Optional\[str\])  
            origin\_type \= get\_origin(return\_actual\_type)  
            if origin\_type is Union or str(origin\_type) \== 'typing.Union':  
                args \= get\_args(return\_actual\_type)  
                if len(args) \== 2 and type(None) in args:  
                    non\_none\_type \= next(arg for arg in args if arg is not type(None))  
                    output\_type\_str \= type\_mapping.get(non\_none\_type, "Any")

        \# Extract return description from docstring  
        return\_description\_match \= re.search(r":return(?:s)?:\\s\*(.+)", docstring)  
        return\_description \= return\_description\_match.group(1).strip() if return\_description\_match else "Result returned by the function"

        \# Standard outputs for all function components  
        outputs \= \[  
            FunctionOutputSpec(  
                name="result", \# Primary output from the function  
                display\_name="Function Result",   
                output\_type=output\_type\_str, \# Use parsed type  
                description=return\_description  
            ),  
            FunctionOutputSpec(  
                name="execution\_time",  
                display\_name="Execution Time (s)",  
                output\_type="float",  
                description="Time taken to execute the function"  
            ),  
            FunctionOutputSpec(  
                name="error",  
                display\_name="Error Message", \# Changed display name  
                output\_type="string", \# Changed from str to string  
                description="Error message if execution failed"  
            )  
        \]  
          
        return outputs  
      
    def validate\_function(self, function\_code: str) \-\> List\[str\]:  
        """Validate function for security and compatibility."""  
        errors \= \[\]  
          
        try:  
            \# Parse AST for security validation  
            tree \= ast.parse(function\_code)  
              
            \# Check for dangerous operations  
            dangerous\_nodes\_messages \= \[\] \# Changed variable name  
            for node in ast.walk(tree):  
                if isinstance(node, ast.Import):  
                    for alias in node.names:  
                        if alias.name not in self.\_get\_allowed\_imports():  
                            dangerous\_nodes\_messages.append(f"Forbidden import: {alias.name}")  
                elif isinstance(node, ast.ImportFrom):  
                    \# Allow imports from allowed modules, e.g., from collections import Counter  
                    if node.module and node.module not in self.\_get\_allowed\_imports():  
                         \# Check if it's a relative import (level \> 0), which are disallowed  
                        if node.level \> 0:  
                            dangerous\_nodes\_messages.append(f"Forbidden relative import from: {node.module or '.'\*node.level}")  
                        else:  
                            dangerous\_nodes\_messages.append(f"Forbidden import from module: {node.module}")  
                elif isinstance(node, ast.Call):  
                    if isinstance(node.func, ast.Name):  
                        if node.func.id in \['exec', 'eval', 'compile', '\_\_import\_\_', 'open', 'exit', 'quit'\]:  
                            dangerous\_nodes\_messages.append(f"Forbidden function call: {node.func.id}")  
                    elif isinstance(node.func, ast.Attribute):  
                        \# Prevent calls like os.system, etc.  
                        \# This is a simple check; more robust checks might be needed for attributes of allowed modules.  
                        \# For now, we rely on RestrictedPython for deeper sandboxing.  
                        pass   
              
            errors.extend(dangerous\_nodes\_messages)  
              
            \# Validate function structure  
            func\_def \= self.\_find\_function\_def(tree)  
            if not func\_def:  
                errors.append("No top-level function definition found in the provided code.")  
            elif not isinstance(func\_def, ast.FunctionDef): \# Should be caught by \_find\_function\_def returning None  
                errors.append("Could not identify a valid function definition.")  
            else:  
                \# Check for return statement (optional, function might modify inputs or have side effects if allowed)  
                \# For this system, we expect a return value to be mapped to 'result'.  
                has\_return \= any(isinstance(node, ast.Return) for node in ast.walk(func\_def))  
                if not has\_return:  
                    errors.append("Function must have at least one return statement to provide a 'result' output.")  
                  
        except SyntaxError as e:  
            errors.append(f"Syntax error: {e.msg} (line {e.lineno}, offset {e.offset})")  
        except Exception as e:  
            errors.append(f"Validation error: {str(e)}")  
          
        return errors  
      
    def \_get\_allowed\_imports(self) \-\> List\[str\]:  
        """Get list of allowed import modules."""  
        \# These are modules that can be imported directly, e.g., \`import json\`  
        \# or \`from collections import Counter\`.  
        return \["json", "re", "math", "datetime", "collections", "copy", "itertools", "typing"\]

#### **Component Auto-Generation System**

\# workflow-service/app/services/function\_component\_generator.py

from typing import ClassVar, List, Dict, Any \# Added Dict, Any  
from app.components.core.base\_node import BaseNode  
from app.models.workflow\_builder.components import InputBase, Output \# Assuming InputBase is the base for generated inputs  
from app.models.workflow\_builder.context import WorkflowContext  
from app.models.workflow\_builder.node\_result import NodeResult  
from app.utils.workflow\_builder.input\_helpers import create\_dual\_purpose\_input

\# Assuming FunctionComponentMetadata is imported  
\# from .function\_parser.base\_parser import FunctionComponentMetadata

class FunctionComponentGenerator:  
    """Generates workflow components from function metadata."""  
      
    def generate\_workflow\_component(self, metadata: FunctionComponentMetadata) \-\> type:  
        """Generate BaseNode subclass from function metadata."""  
          
        \# Create inputs using dual-purpose input helper  
        component\_inputs: List\[InputBase\] \= \[\] \# Explicit type  
        for input\_spec in metadata.inputs:  
            \# Map basic types to specific Input classes if available, else use generic  
            \# For now, create\_dual\_purpose\_input handles this via input\_type string  
            dual\_input \= create\_dual\_purpose\_input(  
                name=input\_spec.name,  
                display\_name=input\_spec.display\_name,  
                input\_type=input\_spec.input\_type, \# e.g., "string", "int", "dict"  
                required=input\_spec.required,  
                info=input\_spec.description,  
                input\_types=input\_spec.allowed\_types or \[input\_spec.input\_type, "Any"\]  
            )  
              
            \# Set default value if provided  
            if not input\_spec.required and input\_spec.default\_value is not None:  
                \# Ensure the default value is compatible with the input\_type  
                \# This might require more sophisticated type checking or conversion  
                dual\_input.value \= input\_spec.default\_value   
              
            component\_inputs.append(dual\_input)  
          
        \# Create outputs  
        component\_outputs: List\[Output\] \= \[ \# Explicit type  
            Output(  
                name=output\_spec.name,  
                display\_name=output\_spec.display\_name,  
                output\_type=output\_spec.output\_type \# e.g., "string", "Any"  
            )  
            for output\_spec in metadata.outputs  
        \]  
          
        \# Create component class dynamically  
        class\_attrs \= {  
            'name': metadata.component\_name, \# This should be unique system-wide  
            'display\_name': metadata.display\_name,  
            'description': metadata.description,  
            'category': metadata.category,  
            'icon': metadata.icon,  
            'inputs': component\_inputs, \# Use the generated list of InputBase objects  
            'outputs': component\_outputs, \# Use the generated list of Output objects  
            '\_function\_metadata': metadata,  \# Store metadata for executor  
            'execute': self.\_create\_execute\_method(metadata) \# Note: metadata is captured by closure  
        }  
          
        \# Create the class  
        \# The class name must be a valid Python identifier  
        component\_class \= type(str(metadata.component\_name), (BaseNode,), class\_attrs)  
          
        return component\_class  
      
    def \_create\_execute\_method(self, metadata: FunctionComponentMetadata):  
        """Create execute method for the component.  
           This method prepares parameters for the Node Executor Service.  
        """  
          
        async def execute(self\_node: BaseNode, context: WorkflowContext) \-\> NodeResult: \# Renamed self to self\_node  
            """Prepare parameters for Python function execution by the Node Executor Service."""  
            import time  
              
            start\_time \= time.time()  
            context.log(f"Preparing function component {metadata.component\_name} for execution...")  
              
            try:  
                \# Collect input values from the context based on component's input specs  
                input\_values\_for\_executor \= {}  
                for input\_spec\_from\_meta in metadata.inputs: \# Iterate metadata inputs  
                    \# self\_node refers to the instance of the dynamically created component class  
                    value \= self\_node.get\_input\_value(input\_spec\_from\_meta.name, context, input\_spec\_from\_meta.default\_value)  
                    input\_values\_for\_executor\[input\_spec\_from\_meta.name\] \= value  
                  
                \# Prepare tool\_parameters for the Orchestration Engine / Node Executor Service  
                \# The Node Executor will use 'function\_code' and 'input\_values\_for\_executor'  
                tool\_parameters \= {  
                    "function\_code": metadata.function\_code,  
                    "function\_name": metadata.function\_name, \# Name of the function to call  
                    "input\_data": input\_values\_for\_executor, \# Actual data for the function  
                    "timeout\_seconds": 30, \# Default or make configurable via metadata/UI  
                    "allowed\_libraries": metadata.allowed\_libraries,  
                    \# Potentially "python\_version" if selectable  
                }

                prep\_time \= time.time() \- start\_time  
                context.log(f"{metadata.component\_name} parameters prepared successfully in {prep\_time:.2f}s. Forwarding for execution.")

                \# Return success with tool\_parameters.  
                \# The actual outputs (result, execution\_time, error) will come from the NES.  
                return NodeResult.success(  
                    outputs={}, \# Actual outputs will be populated by NES result  
                    tool\_parameters=tool\_parameters, \# Key for orchestrator  
                    execution\_time=prep\_time  
                )  
                  
            except Exception as e:  
                error\_msg \= f"Error preparing function component {metadata.component\_name}: {str(e)}"  
                context.log(error\_msg, level="ERROR")  
                \# Include traceback for better debugging if context logging supports it  
                \# import traceback  
                \# context.log(traceback.format\_exc(), level="DEBUG")   
                return NodeResult.error(  
                    error\_message=error\_msg,  
                    execution\_time=(time.time() \- start\_time)  
                )  
          
        return execute  
      
    def generate\_executor\_component(self, metadata: FunctionComponentMetadata) \-\> type:  
        """Generate BaseComponent executor for the Node Executor Service."""  
          
        \# These imports would be at the top of the file in NES context  
        from app.core\_.base\_component import BaseComponent, ValidationResult   
        from app.core\_.component\_system import register\_component  
        \# Assuming ContainerManager is available in this scope (e.g., injected or globally accessible in NES)  
        \# from ..container\_manager import ContainerManager 

        \# The name of this executor component must match the 'name' in FunctionComponentMetadata  
        \# for the Orchestrator to correctly route the execution request.  
        @register\_component(metadata.component\_name)   
        class FunctionExecutor(BaseComponent):  
            """Dynamically generated function executor for Node Executor Service."""  
              
            def \_\_init\_\_(self):  
                super().\_\_init\_\_()  
                self.\_function\_metadata \= metadata \# Store the specific metadata for this function  
                \# self.container\_manager \= ContainerManager() \# Typically injected or retrieved from a central place in NES  
                \# For now, assume it will be set or accessed globally/via context  
                \# This needs to align with how ContainerManager is accessed in PythonCodeWrapperExecutor

            async def start(self):  
                \# If ContainerManager needs explicit start/stop, handle here or ensure it's managed globally  
                \# For example, if self.container\_manager is injected:  
                \# if hasattr(self.container\_manager, 'start') and callable(self.container\_manager.start):  
                \#     await self.container\_manager.start()  
                \# logger.info(f"FunctionExecutor for {self.\_function\_metadata.component\_name} started.")  
                pass

            async def stop(self):  
                \# if hasattr(self.container\_manager, 'stop') and callable(self.container\_manager.stop):  
                \#     await self.container\_manager.stop()  
                \# logger.info(f"FunctionExecutor for {self.\_function\_metadata.component\_name} stopped.")  
                pass

            async def validate(self, payload: Dict\[str, Any\]) \-\> ValidationResult:  
                """Validate function execution payload."""  
                \# request\_id \= payload.get("request\_id", "unknown\_req\_id\_validate") \# Added suffix  
                  
                try:  
                    \# Extract parameters from 'tool\_parameters' as prepared by WorkflowService  
                    if "tool\_parameters" in payload and isinstance(payload\["tool\_parameters"\], dict):  
                        parameters\_to\_check \= payload\["tool\_parameters"\].get("input\_data", {})  
                    else:  
                        \# Fallback if tool\_parameters is not structured as expected  
                        \# This indicates a potential issue upstream or different payload structure  
                        parameters\_to\_check \= payload.get("input\_data", {})  
                      
                    \# Validate required inputs based on stored metadata  
                    for input\_spec in self.\_function\_metadata.inputs:  
                        if input\_spec.required and input\_spec.name not in parameters\_to\_check:  
                            return ValidationResult(  
                                is\_valid=False,  
                                error\_message=f"Missing required parameter for function '{self.\_function\_metadata.function\_name}': {input\_spec.name}"  
                            )  
                      
                    return ValidationResult(is\_valid=True)  
                      
                except Exception as e:  
                    \# import traceback \# For debugging  
                    \# print(f"Validation error in FunctionExecutor: {str(e)}\\n{traceback.format\_exc()}")  
                    return ValidationResult(  
                        is\_valid=False,  
                        error\_message=f"Internal validation error: {str(e)}"  
                    )  
              
            async def process(self, payload: Dict\[str, Any\]) \-\> Dict\[str, Any\]:  
                """Execute the function in a secure container."""  
                import time \# Added import  
                \# This process method is very similar to PythonCodeWrapperExecutor.process  
                \# It adapts the PythonCodeWrapper's container execution for a specific function.

                request\_id \= payload.get("request\_id", "unknown\_req\_id\_process") \# Added suffix  
                start\_process\_time \= time.time()

                \# Access ContainerManager \- this needs a clear strategy.  
                \# Assuming it's available like in PythonCodeWrapperExecutor, e.g. self.container\_manager  
                \# If not, this needs to be initialized or injected.  
                \# For this example, let's assume it's available as self.container\_manager  
                \# This part is CRITICAL and needs to align with NES architecture.  
                \# Example: from app.components.python\_code\_wrapper\_component import python\_code\_wrapper\_executor\_instance  
                \# current\_container\_manager \= python\_code\_wrapper\_executor\_instance.container\_manager  
                \# This is a placeholder for actual ContainerManager access:  
                if not hasattr(self, 'container\_manager') or self.container\_manager is None:  
                     \# Fallback: try to get a shared instance if your app has one  
                     \# This is highly dependent on your application structure.  
                     \# For a robust solution, ContainerManager should be injected or accessible via a service locator.  
                     \# from app.global\_services import get\_container\_manager \# Example  
                     \# self.container\_manager \= get\_container\_manager()  
                     \# If still None, then it's an error.  
                     return {"status": "error", "error": "ContainerManager not available to FunctionExecutor", "execution\_time": 0.0}

                try:  
                    \# Extract parameters from 'tool\_parameters'  
                    if "tool\_parameters" in payload and isinstance(payload\["tool\_parameters"\], dict):  
                        exec\_params \= payload\["tool\_parameters"\]  
                    else:  
                        \# This case should ideally not happen if WorkflowService prepares correctly  
                        return {"status": "error", "error": "Invalid payload structure: missing tool\_parameters.", "execution\_time": 0.0}

                    user\_function\_code \= exec\_params.get("function\_code", "")  
                    user\_function\_name \= exec\_params.get("function\_name", "")  
                    user\_input\_data \= exec\_params.get("input\_data", {}) \# This is the dict of args for the function  
                    timeout \= int(exec\_params.get("timeout\_seconds", 30))  
                    \# python\_version \= exec\_params.get("python\_version", "3.11") \# If configurable

                    if not user\_function\_code.strip() or not user\_function\_name.strip():  
                        return {"status": "error", "error": "Function code or name is empty.", "execution\_time": 0.0}

                    \# The code to run in the container needs to:  
                    \# 1\. Define the user's function.  
                    \# 2\. Call the user's function with 'user\_input\_data'.  
                    \# 3\. Assign the output to 'result' variable for PythonCodeWrapper's executor.py.  
                      
                    \# Prepare arguments for the function call string  
                    arg\_list \= \[\]  
                    for p\_name in user\_input\_data.keys(): \# Iterate over keys provided in input\_data  
                        \# We pass the whole input\_data dict as 'inputs' to executor.py  
                        \# executor.py will make inputs\[p\_name\] available.  
                        arg\_list.append(f"{p\_name}=inputs.get('{p\_name}')") \# Use .get for safety

                    function\_call\_str \= f"{user\_function\_name}({', '.join(arg\_list)})"  
                      
                    \# Construct the full script for executor.py  
                    \# 'inputs' variable in executor.py will be user\_input\_data.  
                    \# 'result' variable must be assigned the output of the function.  
                    code\_to\_execute\_in\_container \= f"""  
{user\_function\_code}

\# Call the user's function and assign to 'result'  
\# The 'inputs' variable below is provided by the generic executor.py script  
\# and contains the 'user\_input\_data' dictionary.  
try:  
    result \= {function\_call\_str}  
except Exception as e:  
    \# If the function itself raises an error, capture it for structured reporting  
    import traceback  
    result \= {{  
        "\_\_function\_execution\_error\_\_": True, \# Flag to indicate error within the function  
        "error\_type": type(e).\_\_name\_\_,  
        "error\_message": str(e),  
        "traceback": traceback.format\_exc()  
    }}  
"""  
                    \# Use the existing ContainerManager from PythonCodeWrapper  
                    \# The 'inputs' to execute\_code here is the dict that executor.py will receive as 'inputs'.  
                    container\_exec\_result \= await self.container\_manager.execute\_code(  
                        code=code\_to\_execute\_in\_container,  
                        inputs=user\_input\_data, \# This becomes the 'inputs' dict in executor.py  
                        timeout=timeout,  
                        \# python\_version=python\_version, \# If needed  
                        request\_id=request\_id  
                    )

                    total\_node\_exec\_time \= time.time() \- start\_process\_time

                    if container\_exec\_result.success:  
                        \# Check if the function execution itself had an error  
                        if isinstance(container\_exec\_result.result\_data, dict) and \\  
                           container\_exec\_result.result\_data.get("\_\_function\_execution\_error\_\_"):  
                            func\_error\_details \= container\_exec\_result.result\_data  
                            return {  
                                "status": "error",  
                                "error": f"Error in function '{user\_function\_name}': {func\_error\_details.get('error\_type', '')} \- {func\_error\_details.get('error\_message', '')}",  
                                "output\_logs": container\_exec\_result.user\_output\_logs \+ f"\\nTraceback:\\n{func\_error\_details.get('traceback','')}",  
                                "execution\_time": container\_exec\_result.total\_execution\_time,  
                                "result": None,  
                            }  
                        else:  
                            return {  
                                "status": "success",  
                                "result": container\_exec\_result.result\_data, \# This is the actual return value of the user's function  
                                "output\_logs": container\_exec\_result.user\_output\_logs,  
                                "execution\_time": container\_exec\_result.total\_execution\_time,  
                            }  
                    else: \# Container execution failed (timeout, sandbox issue, etc.)  
                        return {  
                            "status": "error",  
                            "error": container\_exec\_result.error\_message, \# Error from ContainerManager/executor.py  
                            "output\_logs": container\_exec\_result.user\_output\_logs,  
                            "execution\_time": container\_exec\_result.total\_execution\_time,  
                            "result": None,  
                        }

                except Exception as e:  
                    \# import traceback \# For debugging  
                    \# critical\_error\_msg \= f"Critical unexpected error in FunctionExecutor for {self.\_function\_metadata.component\_name}, request {request\_id}: {e}\\n{traceback.format\_exc()}"  
                    \# logger.exception(critical\_error\_msg) \# Assuming logger is available  
                    return {  
                        "status": "error",   
                        "error": f"A critical internal error occurred in function executor: {str(e)}",   
                        "execution\_time": time.time() \- start\_process\_time,  
                        "output\_logs": "", \# No logs if this part fails  
                        "result": None  
                    }  
          
        \# The class is already registered by the @register\_component decorator  
        return FunctionExecutor \# Return the class itself, not an instance

#### **Function Management Service**

\# workflow-service/app/services/function\_management\_service.py  
\# Assuming necessary imports for FunctionParserRegistry, FunctionComponentGenerator,  
\# FunctionComponentMetadata, ParsingStrategy, TypeHintsParser, etc.  
\# from .function\_parser.base\_parser import FunctionParserRegistry, FunctionComponentMetadata, ParsingStrategy  
\# from .function\_parser.type\_hints\_parser import TypeHintsParser  
\# from .function\_component\_generator import FunctionComponentGenerator  
\# from ..core.component\_system import register\_component as global\_component\_registry \# For Workflow Service  
\# from ..node\_executor\_service\_client import NodeExecutorServiceClient \# To inform NES about new components

\# Placeholder for actual storage mechanism  
class FunctionStorage:  
    async def store\_function(self, metadata: FunctionComponentMetadata, user\_id: str \= None):  
        \# In a real system, this would save to a database (e.g., Firestore, PostgreSQL)  
        \# print(f"Storing function: {metadata.component\_name} for user {user\_id}")  
        \# For now, just a placeholder  
        pass

    async def load\_all\_functions(self) \-\> List\[FunctionComponentMetadata\]:  
        \# In a real system, load from database  
        \# print("Loading all functions from storage...")  
        return \[\] \# Placeholder

class FunctionManagementService:  
    """Service for managing custom function components."""  
      
    def \_\_init\_\_(self, component\_registry, node\_executor\_client): \# Pass dependencies  
        self.parser\_registry \= FunctionParserRegistry()  
        self.component\_generator \= FunctionComponentGenerator()  
        self.function\_storage \= FunctionStorage()  \# Database/file storage  
          
        \# This is the component registry for the Workflow Service (BaseNode types)  
        self.workflow\_service\_component\_registry \= component\_registry   
          
        \# Client to communicate with Node Executor Service (if needed to inform about new executors)  
        \# self.node\_executor\_client \= node\_executor\_client

        \# Registered components in this service's context (metadata)  
        self.dynamically\_registered\_functions: Dict\[str, FunctionComponentMetadata\] \= {}   
          
        \# Register default parsers  
        self.\_register\_default\_parsers()  
      
    def \_register\_default\_parsers(self):  
        self.parser\_registry.register\_parser(ParsingStrategy.TYPE\_HINTS\_BASIC, TypeHintsParser())  
        \# Register other parsers (StructuredDocstringParser, DecoratorMetadataParser) when implemented

    async def initialize\_and\_load\_functions(self):  
        """Loads all stored functions and registers them as components."""  
        all\_metadata \= await self.function\_storage.load\_all\_functions()  
        for metadata in all\_metadata:  
            try:  
                self.\_register\_component\_from\_metadata(metadata)  
                \# logger.info(f"Successfully loaded and registered function component: {metadata.component\_name}")  
            except Exception as e:  
                \# logger.error(f"Failed to load/register function component {metadata.component\_name}: {e}")  
                pass \# Decide on error handling for startup failures

    def \_register\_component\_from\_metadata(self, metadata: FunctionComponentMetadata):  
        """Internal helper to generate and register component in Workflow Service."""  
        \# Generate the BaseNode class for Workflow Service  
        workflow\_node\_class \= self.component\_generator.generate\_workflow\_component(metadata)  
          
        \# Register it with the Workflow Service's global component registry  
        \# This assumes global\_component\_registry.register() is the method  
        if hasattr(self.workflow\_service\_component\_registry, 'register'):  
             self.workflow\_service\_component\_registry.register(workflow\_node\_class)  
        else:  
            \# Fallback or specific registration logic for your app  
            \# Example: YourAppWorkflowComponentRegistry.add\_component(workflow\_node\_class)  
            raise NotImplementedError("Workflow Service component registration mechanism not defined/passed correctly.")

        \# Store metadata for local tracking if needed  
        self.dynamically\_registered\_functions\[metadata.component\_name\] \= metadata

        \# IMPORTANT: The Node Executor Service also needs to know how to execute this.  
        \# Option 1: NES dynamically generates/registers executor components at startup by loading metadata.  
        \#           This is preferred. NES would have its own FunctionManagementService instance or similar logic.  
        \# Option 2: Workflow Service informs NES (e.g., via RPC/message queue) about the new component  
        \#           and potentially sends metadata for NES to generate its executor. This is more complex.  
        \# For now, we assume NES will also load from the same FunctionStorage.

    async def register\_function\_as\_component(self, function\_code: str, function\_name: str,   
                                   user\_id: str \= None,   
                                   strategy: ParsingStrategy \= None) \-\> FunctionComponentMetadata:  
        """Register a new Python function as a dynamic workflow component."""  
          
        \# 1\. Parse the function code to extract metadata  
        \# The parser\_registry will use the specified or auto-detected strategy.  
        metadata \= self.parser\_registry.parse\_function(function\_code, function\_name, strategy)  
        \# Add user\_id to metadata if needed for ownership/auditing  
        \# metadata.owner\_id \= user\_id   
          
        \# 2\. Validate the function code using the chosen/default parsing strategy's validator  
        \# Assuming TypeHintsParser for now, or get parser from strategy  
        active\_parser \= self.parser\_registry.parsers.get(metadata.parsing\_version.split('\_')\[0\]) \# Simplified  
        if not active\_parser: \# Fallback to basic if specific version not found  
            active\_parser \= self.parser\_registry.parsers.get(ParsingStrategy.TYPE\_HINTS\_BASIC)

        validation\_errors \= active\_parser.validate\_function(function\_code)  
        if validation\_errors:  
            error\_str \= "; ".join(validation\_errors)  
            raise ValueError(f"Function validation failed: {error\_str}")  
          
        \# Check if a component with this auto-generated name already exists  
        if metadata.component\_name in self.dynamically\_registered\_functions or \\  
           (hasattr(self.workflow\_service\_component\_registry, 'is\_registered') and \\  
            self.workflow\_service\_component\_registry.is\_registered(metadata.component\_name)):  
            raise ValueError(f"A component named '{metadata.component\_name}' (generated from function '{function\_name}') already exists.")

        \# 3\. Store the function metadata persistently  
        await self.function\_storage.store\_function(metadata, user\_id)  
          
        \# 4\. Generate and register the component in Workflow Service  
        self.\_register\_component\_from\_metadata(metadata)  
          
        \# logger.info(f"Function '{function\_name}' successfully registered as component '{metadata.component\_name}'.")  
        return metadata

    def get\_all\_custom\_function\_metadata(self) \-\> List\[FunctionComponentMetadata\]:  
        return list(self.dynamically\_registered\_functions.values())

    \# Potentially methods to update/delete functions and their components  
    \# async def update\_function\_component(...)  
    \# async def delete\_function\_component(...)

\# Placeholder for where FunctionManagementService would be instantiated and used,  
\# e.g., in the main application setup of the Workflow Service.  
\#  
\# async def startup\_event\_handler():  
\#     \# Assume component\_registry is the global registry for BaseNode components  
\#     \# Assume node\_executor\_client is a client to communicate with NES if needed  
\#     function\_service \= FunctionManagementService(component\_registry=global\_app.component\_registry,   
\#                                                 node\_executor\_client=None) \# Or actual client  
\#     await function\_service.initialize\_and\_load\_functions()  
\#     global\_app.state.function\_service \= function\_service \# Store in app state

*(Note: The `FunctionManagementService.register_function` method in the user's prompt was incomplete. I've completed it based on the surrounding code and typical registration flow. The `TypeHintsParser` and `FunctionComponentGenerator` have also been refined for better consistency and to address potential issues like missing imports or clearer type handling based on the overall architecture.)*

