#!/usr/bin/env python3
"""
Example showing how to fix the workflow data flow issue.
This demonstrates the correct way to structure data for AlterMetadataComponent.
"""

# Example of what your workflow should look like:

# 1. Script Generator Output (current - this is correct):
script_generator_output = [
    {"data": "wildlife", "data_type": "string", "property_name": "title"},
    {"data": "**[Opening Scene: Captivating wildlife footage...]**", "data_type": "string", "property_name": "script"},
    {"data": "TOPIC", "data_type": "string", "property_name": "script_type"},
    {"data": "SHORT", "data_type": "string", "property_name": "video_type"}
]

# 2. Convert to metadata dictionary (what you need to do):
def convert_script_output_to_metadata(script_output):
    """Convert script generator output to metadata dictionary."""
    metadata = {}
    for item in script_output:
        property_name = item.get("property_name")
        data = item.get("data")
        if property_name and data is not None:
            metadata[property_name] = data
    return metadata

# This creates:
metadata_dict = convert_script_output_to_metadata(script_generator_output)
print("Metadata dictionary:")
print(metadata_dict)
# Output:
# {
#     "title": "wildlife",
#     "script": "**[Opening Scene: Captivating wildlife footage...]**",
#     "script_type": "TOPIC", 
#     "video_type": "SHORT"
# }

# 3. Now AlterMetadataComponent can work with this:
alter_metadata_input = {
    "input_metadata": metadata_dict,  # This is a dictionary ✅
    "updates": {
        "processed_date": "2024-01-15",
        "status": "generated",
        "word_count": len(metadata_dict["script"].split())
    },
    "keys_to_remove": []  # Remove any unwanted fields if needed
}

print("\nAlterMetadataComponent input:")
print(alter_metadata_input)

# 4. Expected AlterMetadataComponent output:
expected_output = {
    "title": "wildlife",
    "script": "**[Opening Scene: Captivating wildlife footage...]**",
    "script_type": "TOPIC",
    "video_type": "SHORT",
    "processed_date": "2024-01-15",  # Added
    "status": "generated",           # Added
    "word_count": 50                 # Added (example)
}

print("\nExpected AlterMetadataComponent output:")
print(expected_output)

print("\n" + "="*60)
print("WORKFLOW CONFIGURATION SOLUTIONS")
print("="*60)

print("\n🔧 SOLUTION 1: Use MergeDataComponent first")
print("-" * 40)
print("Workflow: Script Generator → MergeDataComponent → AlterMetadataComponent")
print("1. Script Generator outputs array of objects")
print("2. MergeDataComponent converts array to single dictionary")
print("3. AlterMetadataComponent processes the dictionary")

print("\n🔧 SOLUTION 2: Configure SelectDataComponent differently")
print("-" * 40)
print("Instead of selecting just the script text, select the entire array")
print("Then use a processing component to convert to dictionary format")

print("\n🔧 SOLUTION 3: Create a custom conversion component")
print("-" * 40)
print("Create a component that specifically converts script generator")
print("output format to metadata dictionary format")

print("\n🔧 SOLUTION 4: Fix AlterMetadataComponent input configuration")
print("-" * 40)
print("Configure AlterMetadataComponent to receive the full script")
print("generator output instead of just the selected script text")

print("\n" + "="*60)
print("RECOMMENDED SOLUTION")
print("="*60)

print("\n✅ Use MergeDataComponent to convert the script generator output")
print("   to a proper metadata dictionary before AlterMetadataComponent")
print("\nWorkflow Configuration:")
print("1. Script Generator → outputs array of script data")
print("2. MergeDataComponent → converts array to metadata dictionary")
print("3. AlterMetadataComponent → processes metadata dictionary")
print("4. CombineTextComponent → combines processed script")

print("\nMergeDataComponent Configuration:")
print("- Input: Connect to Script Generator output")
print("- Merge Strategy: 'Deep Merge'")
print("- This will convert the array to a single dictionary")

print("\nAlterMetadataComponent Configuration:")
print("- Input Metadata: Connect to MergeDataComponent output")
print("- Updates: Add your desired metadata updates")
print("- Keys to Remove: Specify any keys to remove")
