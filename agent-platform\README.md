# Agent Platform

A modular Python service for creating and managing AI agent sessions with dynamic tool loading, knowledge retrieval, and streaming responses. The platform supports agent-to-agent communication, workflow integration, and comprehensive knowledge management capabilities.

## Project Structure

```
agent-platform/
├── app/                    # Application code
│   ├── api/                # API endpoints
│   │   ├── agent_routes.py # Agent management endpoints
│   │   ├── chat_routes.py  # Chat endpoints
│   │   ├── health_routes.py # Health check endpoints
│   │   └── knowledge_routes.py # Knowledge management endpoints
│   ├── autogen_service/    # AutoGen integration
│   │   ├── agent_factory.py # Agent creation
│   │   ├── base.py         # Base agent classes
│   │   ├── head_agent.py   # Head agent implementation
│   │   ├── model_factory.py # Model client creation
│   │   └── run.py           # Service runner
│   ├── kafka_client/        # Kafka integration
│   │   ├── consumer.py      # Kafka consumer
│   │   └── producer.py      # Kafka producer
│   ├── knowledge/           # Knowledge management
│   │   ├── knowledge_manager.py # Knowledge retrieval and storage
│   │   └── knowledge_example.py # Example usage
│   ├── schemas/             # Data models
│   │   └── models.py        # Pydantic models
│   ├── shared/              # Shared components
│   │   ├── config/          # Configuration management
│   │   │   ├── base.py      # Base settings
│   │   │   └── logging_config.py  # Logging setup
│   │   └── workflows/       # Workflow definitions
│   ├── tools/               # Tool implementations
│   │   ├── tool_loader.py           # Base tool loading
│   │   ├── dynamic_tool_loader.py   # Dynamic API tool loading
│   │   ├── workflow_tool_loader.py  # Workflow tool loading
│   │   ├── mcp_tool_loader.py       # MCP tool loading
│   │   └── streaming_function_tool.py # Streaming tool support
│   ├── utils/               # Utility modules
│   ├── helper/              # Helper functions
│   │   ├── api_call.py      # HTTP request helper
│   │   ├── redis_client.py  # Redis client wrapper
│   │   └── session_manager.py # Session management
│   ├── services/            # Service layer
│   │   ├── agent_chat.py    # Agent chat service
│   │   └── knowledge_service.py # Knowledge management service
│   └── main.py              # Application entry point
├── config/                  # Configuration files
│   ├── mcp_tool.json        # MCP tool definitions
│   └── research_agent.json  # Agent configuration
├── scripts/                 # Utility scripts
│   ├── run_tests.sh         # Test runner
│   └── integration/         # Integration test scripts
├── tests/                   # Test suite
│   ├── integration/         # Integration tests
│   │   └── run_kafka_test.py  # Kafka integration test
│   ├── api/                 # API tests
│   ├── agents/              # Agent tests
│   ├── tools/               # Tool tests
│   └── shared/              # Shared component tests
├── .env.example             # Environment variable template
├── .coveragerc              # Coverage configuration
├── .dockerignore            # Docker ignore file
├── .gitignore               # Git ignore file
├── Dockerfile               # Docker configuration
├── pyproject.toml           # Poetry configuration
└── README.md                # This file
```

## Features

- **Dynamic Agent Creation**: Create and configure agents on-the-fly
- **Tool Integration**: Support for various tool types (MCP, Workflow, Function)
- **Streaming Responses**: Real-time streaming of agent responses
- **Kafka Integration**: Asynchronous message processing
- **Optimized Redis Session Management**: Maintain conversation state with compression and efficient storage
- **Comprehensive Logging**: Structured logging throughout the application
- **MCP Tool Integration**: Support for Machine Control Program tools
- **Streaming Tool Support**: Real-time streaming of tool execution results
- **Knowledge Retrieval**: RAG capabilities with document, website, and text sources
- **Agent-to-Agent Communication**: Structured messaging system for inter-agent collaboration
- **Head Agent Delegation**: Dynamic task delegation to specialized agents
- **Vector Database Integration**: ChromaDB integration for efficient knowledge storage and retrieval
- **Configuration Reloading**: Runtime configuration updates without service restart

## Installation

1. **Clone the Repository:**

   ```bash
   git clone https://gitlab.rapidinnovation.tech/ruh-catalyst/agent-platform.git
   cd agent-platform
   ```

2. **Set Up Environment:**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install Dependencies:**

   ```bash
   poetry install
   ```

4. **Run the Application:**

   ```bash
   # Run in server mode (API server)
   poetry run python -m app.main --mode server

   # Run in engine mode (Kafka consumer)
   poetry run python -m app.main --mode engine

   # For development with auto-reload
   poetry run uvicorn app.main:app --host 0.0.0.0 --port 6000 --reload
   ```

5. **Using Docker:**

   ```bash
   # Build the Docker image
   docker build -t agent-platform .

   # Run the container (runs both server and engine modes)
   docker run -p 6000:6000 --env-file .env agent-platform

   # Run with volume for persistent ChromaDB storage
   docker run -p 6000:6000 -v $(pwd)/data:/app/data --env-file .env agent-platform
   ```

## Development

### Session Management Optimizations

The platform includes optimized session management with the following features:

1. **Memory Compression**: Uses zlib compression to reduce storage size by 50-80%
2. **Message Window Management**: Maintains a configurable window of recent messages
3. **Selective Configuration Storage**: Stores only essential agent configuration data
4. **Binary Storage**: Efficient binary data handling for compressed messages
5. **Efficient Session Listing**: Uses Redis SCAN for better performance with large numbers of sessions

Configuration options for the SessionManager:

```python
session_manager = SessionManager(
    redis_client,
    session_ttl=3600,        # 1 hour TTL
    max_messages=50,         # Keep only 50 messages per session
    compression_level=6      # Compression level (0-9)
)
```

### Adding New Agents

1. Create new agent class in `app/agents/`
2. Implement required interfaces
3. Register in `head_agent.py`
4. Add configuration in `config/`

### Creating Custom Tools

1. Define tool schema in JSON
2. Implement tool logic in `app/tools/`
3. Register with appropriate tool loader:
   - `DynamicToolLoader` for API tools
   - `WorkflowToolLoader` for workflow tools
   - `McpToolLoader` for MCP tools

### Tool Types

The platform supports several types of tools:

1. **API Tools**: Connect to REST APIs with JSON payloads
2. **Workflow Tools**: Execute workflows in external workflow systems
3. **MCP Tools**: Connect to Machine Control Program services
4. **Function Tools**: Execute Python functions directly
5. **Streaming Tools**: Support real-time streaming of results

### Knowledge Management

The platform supports knowledge retrieval from various sources:

1. **Document Upload**: Extract knowledge from uploaded documents
2. **Website Scraping**: Retrieve information from websites
3. **Direct Text Input**: Add knowledge directly as text
4. **Vector Storage**: Store and retrieve knowledge using ChromaDB

To add knowledge to an agent:

```python
from app.services.knowledge_service import KnowledgeService

# Create knowledge service
knowledge_service = KnowledgeService()

# Add knowledge to an agent
await knowledge_service.add_knowledge_to_agent(
    agent_id="agent_name",
    agent=agent_instance,
    knowledge_sources={
        "texts": ["This is some knowledge about the topic."],
        "urls": ["https://example.com/information"],
        "documents": ["/path/to/document.pdf"]
    }
)
```

### Configuration Management

The application uses environment variables for configuration, loaded through `python-dotenv`. Key configuration sections include:

- API settings
- OpenAI API credentials
- Kafka connection details
- Redis connection details
- Workflow API endpoints
- Agent configuration
- Logging configuration

To reload configuration at runtime:

```bash
curl http://localhost:6000/reload-config
```

## Testing

### Setup

Ensure you have all development dependencies installed:

```bash
poetry install
```

### Running Tests

1. **Using the test script:**

   ```bash
   ./scripts/run_tests.sh
   ```

2. **Running directly with pytest:**

   ```bash
   PYTHONPATH=$PYTHONPATH:. poetry run pytest
   ```

3. **With coverage report:**

   ```bash
   PYTHONPATH=$PYTHONPATH:. poetry run pytest --cov=app --cov-report=html --cov-report=term-missing
   ```

4. **Running specific tests:**
   ```bash
   PYTHONPATH=$PYTHONPATH:. poetry run pytest tests/api/test_endpoints.py
   ```

### Coverage Report

- HTML report will be generated in `coverage_html/` directory
- Open `coverage_html/index.html` in your browser to view detailed coverage

### Continuous Integration

The test suite runs automatically on:

- Every pull request
- Push to main branch
- Daily scheduled runs

## API Documentation

When the application is running, access the API documentation at:

- Swagger UI: http://localhost:6000/docs
- ReDoc: http://localhost:6000/redoc

## Logging

The application uses a comprehensive structured logging system:

- Logs are configured in `app/shared/config/logging_config.py`
- Multiple log levels can be enabled simultaneously
- Component-specific log levels can be configured
- Logs include contextual information for easier debugging
- JSON formatting is available for machine parsing

### Log Level Configuration

You can configure logging using environment variables:

```bash
# Enable multiple log levels
LOG_LEVEL=DEBUG,INFO,WARNING,ERROR,CRITICAL

# Set component-specific log levels
KAFKA_LOG_LEVEL=DEBUG
AGENT_LOG_LEVEL=INFO
SESSION_LOG_LEVEL=WARNING
REDIS_LOG_LEVEL=ERROR

# Enable JSON formatting
LOG_FORMAT=json

# Set log directory
LOGS_DIR=custom_logs
```

### Using the Logging System

```python
from app.shared.config.logging_config import get_logger, get_level_logger, log_with_context

# Get a regular logger
logger = get_logger(__name__)
logger.info("Regular log message")

# Get a level-specific logger
debug_logger = get_level_logger("debug")
debug_logger.debug("This only appears in DEBUG logs")

# Log with context
log_with_context(
    logger,
    logging.INFO,
    "Message with context",
    extra={"user_id": "123", "session_id": "456"}
)

# Log to multiple levels simultaneously
log_with_context(
    logger,
    logging.WARNING,
    "Important message",
    log_to_levels=["info", "warning", "error"]
)
```

See `app/examples/logging_example.py` for more examples.

## Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Submit pull request

## License

[Specify License]

## Acknowledgements

- Built with [FastAPI](https://fastapi.tiangolo.com/)
- Managed using [Poetry](https://python-poetry.org/)
- Uses [autogen-core](https://github.com/microsoft/autogen) for agent framework
- Integrates with [Kafka](https://kafka.apache.org/) for message processing
- Uses [Redis](https://redis.io/) for session management
- Uses [ChromaDB](https://www.trychroma.com/) for vector storage
- Integrates with [Anthropic Claude](https://www.anthropic.com/) and [OpenAI](https://openai.com/) models
