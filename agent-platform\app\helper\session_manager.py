import uuid
import json
import zlib
import asyncio
from datetime import datetime
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>, Any, List
from autogen_core.memory import ListM<PERSON>ory, MemoryContent, MemoryMimeType
from autogen_core import CancellationToken
from ..schemas.agent_config import AgentConfig
from .redis_client import RedisClient
from ..shared.config.logging_config import get_logger

# Get logger for this module
logger = get_logger(__name__)


class SessionManager:
    def __init__(
        self,
        redis_client: RedisClient,
        session_ttl: int = 3600,
        max_messages: int = 50,
        compression_level: int = 6,
    ):
        self.redis = redis_client
        self.session_ttl = session_ttl
        self.max_messages = max_messages
        self.compression_level = compression_level
        self.session_memories: Dict[str, ListMemory] = {}
        self.session_messages: Dict[str, List[Dict[str, Any]]] = {}
        self.cancellation_tokens: Dict[str, CancellationToken] = {}
        self._lock = asyncio.Lock()  # Lock for thread safety

    async def create_session(
        self,
        agent_config: AgentConfig,
        user_id: str,
        communication_type: str,
    ) -> str:
        """Create a new session with the given agent config"""
        session_id = str(uuid.uuid4())

        try:

            # Create new memory instance for session
            async with self._lock:
                self.session_memories[session_id] = ListMemory()
                self.session_messages[session_id] = []
                self.cancellation_tokens[session_id] = CancellationToken()

            # Extract only essential config data to reduce storage
            essential_config = self._extract_essential_config(agent_config)

            session_data = {
                "session_id": session_id,
                "user_id": user_id,
                "agent_config": essential_config,
                "communication_type": communication_type,
                "created_at": datetime.utcnow().isoformat(),
            }

            key = f"session:{session_id}"
            await self.redis.set_hash(key, session_data, ttl=self.session_ttl)

            logger.info(
                "Session created",
                extra={
                    "session_id": session_id,
                    "user_id": user_id,
                    "communication_type": communication_type,
                },
            )
            return session_id

        except Exception as e:
            logger.error(
                "Failed to create session", exc_info=True, extra={"error": str(e)}
            )
            raise e

    def _extract_essential_config(self, agent_config: Any) -> Dict[str, Any]:
        """Extract only essential configuration data to reduce storage."""
        # Convert agent_config to dict if it's a Pydantic model
        config_dict = (
            agent_config.model_dump()
            if hasattr(agent_config, "model_dump")
            else agent_config
        )

        # Extract only essential fields
        essential_fields = {
            "name": config_dict.get("name"),
            "description": config_dict.get("description"),
            "agent_type": config_dict.get("agent_type"),
            "system_message": config_dict.get("system_message"),
            # Include minimal model config
            "ai_model_config": {
                "model": config_dict.get("ai_model_config", {}).get("model"),
                "api_key": config_dict.get("ai_model_config", {}).get("api_key"),
                "temperature": config_dict.get("ai_model_config", {}).get(
                    "temperature", 0.7
                ),
            },
            # Include tool references but not full tool definitions
            "tools": [
                {
                    "tool_type": tool.get("tool_type"),
                    "url": tool.get("url"),
                    "workflow": {
                        "workflow_id": tool.get("workflow", {}).get("workflow_id"),
                        "approval": tool.get("workflow", {}).get("approval"),
                    },
                }
                for tool in config_dict.get("tools", [])
            ],
            "memory_enabled": config_dict.get("memory_enabled", False),
        }

        return config_dict

    async def session_exists(self, session_id: str) -> bool:
        """Check if a session exists"""
        key = f"session:{session_id}"
        return await self.redis.exists(key)

    async def get_session_data(
        self, session_id: str
    ) -> Tuple[AgentConfig, str, Optional[ListMemory], Optional[CancellationToken]]:
        """Get session data including agent config, communication type, memory, and cancellation token"""
        key = f"session:{session_id}"

        try:
            session_data = await self.redis.get_hash(key)

            if not session_data:
                raise ValueError(f"Session {session_id} not found")

            agent_config = session_data.get("agent_config")
            if isinstance(agent_config, str):
                agent_config = json.loads(agent_config)

            communication_type = session_data.get("communication_type", "single")

            # Get memory and cancellation token for this session
            memory = None
            cancellation_token = None

            async with self._lock:
                # Check if we already have memory for this session
                memory = self.session_memories.get(session_id)

                # If not, try to load from compressed storage
                if not memory:
                    # Try to load messages from compressed storage
                    compressed_key = f"compressed_messages:{session_id}"
                    compressed_data = await self.redis.get_binary(compressed_key)

                    if compressed_data:
                        try:
                            # Decompress and load messages
                            decompressed = zlib.decompress(compressed_data).decode(
                                "utf-8"
                            )
                            messages = json.loads(decompressed)

                            # Store in our message cache
                            self.session_messages[session_id] = messages

                            # Create a new ListMemory and populate it
                            memory = ListMemory()
                            for msg in messages:
                                content = f"{msg.get('role', 'unknown')}: {msg.get('content', '')}"
                                await memory.add(
                                    MemoryContent(
                                        content=content,
                                        mime_type=MemoryMimeType.TEXT,
                                    )
                                )

                            # Store the memory for future use
                            self.session_memories[session_id] = memory
                        except Exception as e:
                            logger.error(
                                "Failed to load compressed messages",
                                exc_info=True,
                                extra={"session_id": session_id, "error": str(e)},
                            )

                cancellation_token = self.cancellation_tokens.get(session_id)
                if not cancellation_token:
                    cancellation_token = CancellationToken()
                    self.cancellation_tokens[session_id] = cancellation_token

            logger.debug("Session data retrieved", extra={"session_id": session_id})
            return agent_config, communication_type, memory, cancellation_token

        except Exception as e:
            logger.error(
                "Failed to get session data",
                exc_info=True,
                extra={"session_id": session_id, "error": str(e)},
            )
            raise e

    async def update_session_memory(
        self, session_id: str, message: Dict[str, Any]
    ) -> None:
        """Add a message to the session memory and update Redis with compression and truncation"""

        # Update in-memory storage for both ListMemory and our message cache
        memory = None
        async with self._lock:
            # Update ListMemory for compatibility with existing code
            memory = self.session_memories.get(session_id)

            # Update our message cache
            if session_id not in self.session_messages:
                self.session_messages[session_id] = []

            # Add the new message
            self.session_messages[session_id].append(message)

            # Truncate if exceeding max messages
            if len(self.session_messages[session_id]) > self.max_messages:
                # Keep the first message (usually system message) and the most recent messages
                self.session_messages[session_id] = [
                    self.session_messages[session_id][0]
                ] + self.session_messages[session_id][-(self.max_messages - 1) :]

        # Update ListMemory for compatibility
        if memory:
            content = f"{message['role']}: {message['content']}"
            await memory.add(
                MemoryContent(
                    content=content,
                    mime_type=MemoryMimeType.TEXT,
                )
            )

        # Compress and store messages in Redis
        compressed_messages = self._compress_messages(
            self.session_messages.get(session_id, [])
        )
        compressed_key = f"compressed_messages:{session_id}"
        await self.redis.set_binary(
            compressed_key, compressed_messages, ttl=self.session_ttl
        )

    def _compress_messages(self, messages: List[Dict[str, Any]]) -> bytes:
        """Compress messages for efficient storage"""
        if not messages:
            return zlib.compress(b"[]", level=self.compression_level)

        serialized = json.dumps(messages)
        return zlib.compress(serialized.encode("utf-8"), level=self.compression_level)

    async def get_session_messages(self, session_id: str) -> List[Dict[str, Any]]:
        """Get all messages for a session from in-memory cache or compressed storage"""
        # First check in-memory cache
        async with self._lock:
            if (
                session_id in self.session_messages
                and self.session_messages[session_id]
            ):
                return self.session_messages[session_id]

        # If not in memory, try to get from compressed storage
        compressed_key = f"compressed_messages:{session_id}"
        compressed_data = await self.redis.get_binary(compressed_key)

        if compressed_data:
            try:
                decompressed = zlib.decompress(compressed_data).decode("utf-8")
                messages = json.loads(decompressed)

                # Update in-memory cache
                async with self._lock:
                    self.session_messages[session_id] = messages

                return messages
            except Exception as e:
                logger.error(
                    "Failed to decompress messages",
                    exc_info=True,
                    extra={"session_id": session_id, "error": str(e)},
                )

        # Fall back to old storage method if needed
        messages_key = f"messages:{session_id}"
        messages_json = await self.redis.get_list(messages_key)
        if messages_json:
            return [json.loads(msg) for msg in messages_json]

        return []

    async def end_session(self, session_id: str) -> None:
        """End a session and clean up resources"""
        key = f"session:{session_id}"
        messages_key = f"messages:{session_id}"
        compressed_key = f"compressed_messages:{session_id}"

        # Clean up memory resources
        async with self._lock:
            if session_id in self.session_memories:
                memory = self.session_memories[session_id]
                if memory:
                    await memory.close()
                del self.session_memories[session_id]

            if session_id in self.session_messages:
                del self.session_messages[session_id]

            if session_id in self.cancellation_tokens:
                del self.cancellation_tokens[session_id]

        # Remove from Redis
        await self.redis.delete(key)
        await self.redis.delete(messages_key)
        await self.redis.delete(compressed_key)

    async def get_active_sessions(
        self, user_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get all active sessions, optionally filtered by user_id"""
        # Use Redis SCAN for efficient session listing
        pattern = "session:*"
        session_keys = await self.redis.scan(pattern)

        active_sessions = []
        for key in session_keys:
            session_data = await self.redis.get_hash(key)
            # Filter by user_id if provided
            if user_id is None or session_data.get("user_id") == user_id:
                active_sessions.append(session_data)

        return active_sessions
