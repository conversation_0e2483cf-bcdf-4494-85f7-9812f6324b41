# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import communication_pb2 as communication__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in communication_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class CommunicationServiceStub(object):
    """Communication service definition
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.createConversation = channel.unary_unary(
                '/communication.CommunicationService/createConversation',
                request_serializer=communication__pb2.CreateConversationRequest.SerializeToString,
                response_deserializer=communication__pb2.Conversation.FromString,
                _registered_method=True)
        self.getConversation = channel.unary_unary(
                '/communication.CommunicationService/getConversation',
                request_serializer=communication__pb2.GetConversationRequest.SerializeToString,
                response_deserializer=communication__pb2.Conversation.FromString,
                _registered_method=True)
        self.deleteConversation = channel.unary_unary(
                '/communication.CommunicationService/deleteConversation',
                request_serializer=communication__pb2.DeleteConversationRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.listConversations = channel.unary_unary(
                '/communication.CommunicationService/listConversations',
                request_serializer=communication__pb2.ListConversationsRequest.SerializeToString,
                response_deserializer=communication__pb2.ListConversationsResponse.FromString,
                _registered_method=True)
        self.createMessage = channel.unary_unary(
                '/communication.CommunicationService/createMessage',
                request_serializer=communication__pb2.CreateMessageRequest.SerializeToString,
                response_deserializer=communication__pb2.Message.FromString,
                _registered_method=True)
        self.deleteMessage = channel.unary_unary(
                '/communication.CommunicationService/deleteMessage',
                request_serializer=communication__pb2.DeleteMessageRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.listMessages = channel.unary_unary(
                '/communication.CommunicationService/listMessages',
                request_serializer=communication__pb2.ListMessagesRequest.SerializeToString,
                response_deserializer=communication__pb2.ListMessagesResponse.FromString,
                _registered_method=True)


class CommunicationServiceServicer(object):
    """Communication service definition
    """

    def createConversation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getConversation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteConversation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listConversations(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listMessages(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CommunicationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'createConversation': grpc.unary_unary_rpc_method_handler(
                    servicer.createConversation,
                    request_deserializer=communication__pb2.CreateConversationRequest.FromString,
                    response_serializer=communication__pb2.Conversation.SerializeToString,
            ),
            'getConversation': grpc.unary_unary_rpc_method_handler(
                    servicer.getConversation,
                    request_deserializer=communication__pb2.GetConversationRequest.FromString,
                    response_serializer=communication__pb2.Conversation.SerializeToString,
            ),
            'deleteConversation': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteConversation,
                    request_deserializer=communication__pb2.DeleteConversationRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'listConversations': grpc.unary_unary_rpc_method_handler(
                    servicer.listConversations,
                    request_deserializer=communication__pb2.ListConversationsRequest.FromString,
                    response_serializer=communication__pb2.ListConversationsResponse.SerializeToString,
            ),
            'createMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.createMessage,
                    request_deserializer=communication__pb2.CreateMessageRequest.FromString,
                    response_serializer=communication__pb2.Message.SerializeToString,
            ),
            'deleteMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteMessage,
                    request_deserializer=communication__pb2.DeleteMessageRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'listMessages': grpc.unary_unary_rpc_method_handler(
                    servicer.listMessages,
                    request_deserializer=communication__pb2.ListMessagesRequest.FromString,
                    response_serializer=communication__pb2.ListMessagesResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'communication.CommunicationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('communication.CommunicationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class CommunicationService(object):
    """Communication service definition
    """

    @staticmethod
    def createConversation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/communication.CommunicationService/createConversation',
            communication__pb2.CreateConversationRequest.SerializeToString,
            communication__pb2.Conversation.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getConversation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/communication.CommunicationService/getConversation',
            communication__pb2.GetConversationRequest.SerializeToString,
            communication__pb2.Conversation.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteConversation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/communication.CommunicationService/deleteConversation',
            communication__pb2.DeleteConversationRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listConversations(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/communication.CommunicationService/listConversations',
            communication__pb2.ListConversationsRequest.SerializeToString,
            communication__pb2.ListConversationsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/communication.CommunicationService/createMessage',
            communication__pb2.CreateMessageRequest.SerializeToString,
            communication__pb2.Message.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/communication.CommunicationService/deleteMessage',
            communication__pb2.DeleteMessageRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listMessages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/communication.CommunicationService/listMessages',
            communication__pb2.ListMessagesRequest.SerializeToString,
            communication__pb2.ListMessagesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
