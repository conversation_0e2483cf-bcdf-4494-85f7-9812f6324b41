import React, { useCallback, useEffect, useState } from "react";
import { AlertCircle, CheckCircle } from "lucide-react";
import { cn, debounce } from "@/lib/utils";
import { InputDefinition } from "@/types";
import { validateInput } from "@/utils/inputValidation";
import { useInspectorStore } from "@/store/inspectorStore";

interface ValidationWrapperProps {
  children: React.ReactNode;
  inputDef: InputDefinition;
  value: any;
  className?: string;
}

/**
 * Enhanced wrapper component for inputs that need validation
 * Integrates with the inspector store for validation state
 */
export function ValidationWrapper({
  children,
  inputDef,
  value,
  className,
}: ValidationWrapperProps) {
  const { showValidation, validationErrors, setValidationError } = useInspectorStore();

  // Get validation state for this input
  const validationState = validationErrors[inputDef.name] || { isValid: true, message: "" };
  const isValid = validationState.isValid;
  const message = validationState.message;

  // Create a debounced validation function (500ms delay)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedValidate = useCallback(
    debounce((inputDef: InputDefinition, value: any) => {
      const result = validateInput(inputDef, value);
      setValidationError(inputDef.name, result);
    }, 500),
    [setValidationError]
  );

  // We no longer validate on value change
  // Validation will only happen when explicitly triggered by the user
  // or at critical points like saving or executing the workflow

  return (
    <div className={cn("space-y-1", className)}>
      {children}
      {showValidation && (message || (inputDef.required && isValid)) && (
        <ValidationMessage isValid={isValid} message={message} />
      )}
    </div>
  );
}

interface ValidationMessageProps {
  isValid: boolean;
  message: string;
}

/**
 * Component for displaying validation messages
 */
function ValidationMessage({ isValid, message }: ValidationMessageProps) {
  if (!message && !isValid) {
    message = "Invalid input";
  }

  return (
    <div
      className={cn(
        "mt-1 flex items-center gap-1.5 text-xs",
        isValid ? "text-success" : "text-destructive"
      )}
    >
      {isValid ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
      <span>{message}</span>
    </div>
  );
}

interface ValidationRulesProps {
  rules: {
    name: string;
    isValid: boolean;
    message?: string;
  }[];
  className?: string;
}

/**
 * Component for displaying a list of validation rules
 */
export function ValidationRules({ rules, className }: ValidationRulesProps) {
  if (!rules || rules.length === 0) {
    return null;
  }

  return (
    <div className={cn("mt-2 space-y-1", className)}>
      {rules.map((rule) => (
        <div
          key={rule.name}
          className={cn(
            "flex items-center gap-1.5 text-xs",
            rule.isValid ? "text-success" : "text-muted-foreground"
          )}
        >
          {rule.isValid ? (
            <CheckCircle className="h-3 w-3" />
          ) : (
            <div className="border-muted-foreground/50 h-3 w-3 rounded-full border" />
          )}
          <span>{rule.message || rule.name}</span>
        </div>
      ))}
    </div>
  );
}
