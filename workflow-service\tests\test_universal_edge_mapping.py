"""
Test suite for universal edge mapping functionality.

This test suite follows TDD principles to drive the implementation of universal
edge mapping that works with any workflow format, not just specific examples.
"""

import json
import os
import sys
import pytest
from pathlib import Path
from typing import Dict, Any, List

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
    determine_mapping_strategy,
    create_enhanced_field_mapping,
    build_graph_from_workflow,
    compute_levels,
    group_nodes_by_level
)


class TestUniversalEdgeMapping:
    """Test universal edge mapping functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        # Load the sample workflow for testing
        sample_workflow_path = Path(__file__).parent.parent / "testing" / "sample_workflow.json"
        with open(sample_workflow_path, "r") as f:
            self.sample_workflow = json.load(f)

    def test_universal_node_type_detection(self):
        """Test that the system can detect and classify any node type."""
        # Test data with various node types
        test_nodes = [
            {"data": {"type": "mcp"}, "id": "mcp_node"},
            {"data": {"type": "component"}, "id": "component_node"},
            {"data": {"type": "api"}, "id": "api_node"},
            {"data": {"type": "custom_type"}, "id": "custom_node"},
            {"data": {"type": "unknown"}, "id": "unknown_node"},
        ]

        for node in test_nodes:
            # Should not raise an exception for any node type
            strategy = determine_mapping_strategy(
                node, "output_field", {}, "input_field"
            )

            # Should always return a valid strategy
            assert "from_field" in strategy
            assert "mapping_type" in strategy
            assert "confidence" in strategy
            assert strategy["confidence"] in ["high", "medium", "low"]

    def test_universal_handle_mapping(self):
        """Test that any sourceHandle/targetHandle combination can be mapped."""
        test_cases = [
            # Standard patterns
            ("output_data", "input_data"),
            ("result", "input"),
            ("response", "data"),
            # Custom patterns
            ("custom_output_123", "custom_input_456"),
            ("any_field_name", "another_field_name"),
            # Complex patterns
            ("nested.field.output", "nested.field.input"),
        ]

        source_node = {"data": {"type": "component"}}
        target_node = {"data": {"type": "component"}}

        for source_handle, target_handle in test_cases:
            mapping = create_enhanced_field_mapping(
                source_handle, target_handle, source_node, target_node, "test_edge"
            )

            # Should create valid mapping for any handle combination
            assert mapping["source_handle"] == source_handle
            assert mapping["target_handle"] == target_handle
            assert mapping["to_field"] == target_handle
            assert "from_field" in mapping
            assert "mapping_type" in mapping

    def test_workflow_graph_building_universal(self):
        """Test that graph building works with any workflow structure."""
        # Test with the sample workflow
        graph, edge_map, all_nodes = build_graph_from_workflow(self.sample_workflow)

        # Should build a valid graph
        assert isinstance(graph, dict)
        assert isinstance(edge_map, dict)
        assert isinstance(all_nodes, set)
        assert len(all_nodes) > 0
        assert len(edge_map) > 0

    def test_level_computation_universal(self):
        """Test that level computation works with any graph structure."""
        graph, edge_map, all_nodes = build_graph_from_workflow(self.sample_workflow)

        # Remove start node if present
        start_nodes = []
        for node in self.sample_workflow.get("nodes", []):
            if (node.get("data", {}).get("originalType") == "StartNode"):
                start_node_id = node["id"]
                if start_node_id in graph:
                    del graph[start_node_id]
                all_nodes.discard(start_node_id)
                # Find nodes connected to start
                for edge in self.sample_workflow.get("edges", []):
                    if edge["source"] == start_node_id:
                        start_nodes.append(edge["target"])

        levels = compute_levels(graph, all_nodes, start_nodes)
        level_groups = group_nodes_by_level(levels)

        # Should compute valid levels
        assert isinstance(levels, dict)
        assert isinstance(level_groups, dict)
        assert len(levels) > 0

    def test_conversion_preserves_all_edges(self):
        """Test that conversion preserves all edge information."""
        transition_schema = convert_workflow_to_transition_schema(self.sample_workflow)

        # Count original edges (excluding start node edges)
        original_edges = []
        start_node_id = None
        for node in self.sample_workflow.get("nodes", []):
            if node.get("data", {}).get("originalType") == "StartNode":
                start_node_id = node["id"]
                break

        for edge in self.sample_workflow.get("edges", []):
            if edge["source"] != start_node_id:
                original_edges.append(edge)

        # Count transitions with input_data (representing edges)
        transitions_with_inputs = 0
        total_input_data_entries = 0

        for transition in transition_schema.get("transitions", []):
            input_data = transition.get("node_info", {}).get("input_data", [])
            if input_data:
                transitions_with_inputs += 1
                total_input_data_entries += len(input_data)

        # Should preserve edge information
        assert transitions_with_inputs > 0
        assert total_input_data_entries > 0

    def test_enhanced_mapping_metadata(self):
        """Test that enhanced mapping includes proper metadata."""
        transition_schema = convert_workflow_to_transition_schema(self.sample_workflow)

        # Check that transitions have enhanced mapping metadata
        found_enhanced_mapping = False

        for transition in transition_schema.get("transitions", []):
            input_data = transition.get("node_info", {}).get("input_data", [])
            for input_entry in input_data:
                if "mapping" in input_entry:
                    found_enhanced_mapping = True
                    mapping = input_entry["mapping"][0]  # First mapping entry

                    # Should have required fields
                    assert "from_field" in mapping
                    assert "to_field" in mapping
                    assert "source_handle" in mapping
                    assert "target_handle" in mapping
                    assert "mapping_type" in mapping
                    assert "confidence" in mapping

        assert found_enhanced_mapping, "No enhanced mapping metadata found"

    def test_backward_compatibility(self):
        """Test that enhanced mapping maintains backward compatibility."""
        transition_schema = convert_workflow_to_transition_schema(self.sample_workflow)

        # Should still have traditional fields for backward compatibility
        for transition in transition_schema.get("transitions", []):
            input_data = transition.get("node_info", {}).get("input_data", [])
            for input_entry in input_data:
                # Should have traditional fields
                assert "from_transition_id" in input_entry
                assert "source_node_id" in input_entry
                assert "data_type" in input_entry

    def test_fallback_strategies(self):
        """Test fallback strategies for unknown node types and patterns."""
        # Test with completely unknown node structure
        unknown_workflow = {
            "nodes": [
                {
                    "id": "unknown_node_1",
                    "data": {
                        "type": "completely_unknown_type",
                        "definition": {
                            "name": "UnknownNode",
                            "outputs": [{"name": "unknown_output", "output_type": "unknown"}]
                        }
                    }
                },
                {
                    "id": "unknown_node_2",
                    "data": {
                        "type": "another_unknown_type",
                        "definition": {
                            "name": "AnotherUnknownNode",
                            "inputs": [{"name": "unknown_input", "input_type": "unknown"}]
                        }
                    }
                }
            ],
            "edges": [
                {
                    "id": "unknown_edge",
                    "source": "unknown_node_1",
                    "sourceHandle": "unknown_output",
                    "target": "unknown_node_2",
                    "targetHandle": "unknown_input"
                }
            ],
            "mcp_configs": []
        }

        # Should not raise an exception
        try:
            transition_schema = convert_workflow_to_transition_schema(unknown_workflow)
            assert transition_schema is not None
            assert "transitions" in transition_schema
        except Exception as e:
            pytest.fail(f"Conversion failed for unknown workflow: {e}")

    def test_api_node_mapping(self):
        """Test mapping for API nodes with various response patterns."""
        api_workflow = {
            "nodes": [
                {
                    "id": "api_node_1",
                    "data": {
                        "type": "api",
                        "definition": {
                            "name": "APINode",
                            "outputs": [{"name": "result", "output_type": "object"}]
                        }
                    }
                },
                {
                    "id": "component_node_1",
                    "data": {
                        "type": "component",
                        "definition": {
                            "name": "ProcessorComponent",
                            "inputs": [{"name": "data", "input_type": "object"}]
                        }
                    }
                }
            ],
            "edges": [
                {
                    "id": "api_edge",
                    "source": "api_node_1",
                    "sourceHandle": "result",
                    "target": "component_node_1",
                    "targetHandle": "data"
                }
            ],
            "mcp_configs": []
        }

        transition_schema = convert_workflow_to_transition_schema(api_workflow)

        # Should handle API nodes correctly
        assert "transitions" in transition_schema
        assert len(transition_schema["transitions"]) > 0

        # Check for enhanced mapping in API to component connection
        found_api_mapping = False
        for transition in transition_schema["transitions"]:
            input_data = transition.get("node_info", {}).get("input_data", [])
            for input_entry in input_data:
                if "mapping" in input_entry and input_entry["mapping"]:
                    mapping = input_entry["mapping"][0]
                    if mapping.get("source_handle") == "result" and mapping.get("target_handle") == "data":
                        found_api_mapping = True
                        assert mapping.get("mapping_type") in ["direct", "nested"]
                        break

        assert found_api_mapping, "API node mapping not found"

    def test_custom_node_types(self):
        """Test handling of custom node types with arbitrary field names."""
        custom_workflow = {
            "nodes": [
                {
                    "id": "custom_node_1",
                    "data": {
                        "type": "custom_processor",
                        "definition": {
                            "name": "CustomProcessor",
                            "outputs": [{"name": "processed_data", "output_type": "string"}]
                        }
                    }
                },
                {
                    "id": "custom_node_2",
                    "data": {
                        "type": "custom_analyzer",
                        "definition": {
                            "name": "CustomAnalyzer",
                            "inputs": [{"name": "input_stream", "input_type": "string"}]
                        }
                    }
                }
            ],
            "edges": [
                {
                    "id": "custom_edge",
                    "source": "custom_node_1",
                    "sourceHandle": "processed_data",
                    "target": "custom_node_2",
                    "targetHandle": "input_stream"
                }
            ],
            "mcp_configs": []
        }

        transition_schema = convert_workflow_to_transition_schema(custom_workflow)

        # Should handle custom node types
        assert "transitions" in transition_schema
        assert len(transition_schema["transitions"]) > 0

        # Verify custom field mapping
        found_custom_mapping = False
        for transition in transition_schema["transitions"]:
            input_data = transition.get("node_info", {}).get("input_data", [])
            for input_entry in input_data:
                if "mapping" in input_entry and input_entry["mapping"]:
                    mapping = input_entry["mapping"][0]
                    if (mapping.get("source_handle") == "processed_data" and
                        mapping.get("target_handle") == "input_stream"):
                        found_custom_mapping = True
                        # Should use direct mapping with low confidence for unknown types
                        assert mapping.get("mapping_type") == "direct"
                        assert mapping.get("confidence") == "low"
                        break

        assert found_custom_mapping, "Custom node mapping not found"

    def test_complex_workflow_patterns(self):
        """Test with complex workflow patterns including multiple connections."""
        # This test uses the sample workflow which has complex patterns
        transition_schema = convert_workflow_to_transition_schema(self.sample_workflow)

        # Should handle complex patterns successfully
        assert "transitions" in transition_schema
        assert "nodes" in transition_schema
        assert len(transition_schema["transitions"]) > 0
        assert len(transition_schema["nodes"]) > 0

        # Check for proper sequence numbering
        sequences = [t.get("sequence", 0) for t in transition_schema["transitions"]]
        assert len(set(sequences)) == len(sequences), "Duplicate sequence numbers found"
        assert min(sequences) >= 1, "Sequence should start from 1"


if __name__ == "__main__":
    pytest.main([__file__])
