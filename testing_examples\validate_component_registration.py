#!/usr/bin/env python3
"""
Validation script to ensure AlterMetadataComponent is properly registered
and discoverable in both services.
"""
import sys
import os
import importlib.util
import json

def validate_workflow_service():
    """Validate the workflow service component registration."""
    print("🔍 Validating Workflow Service Component")
    print("-" * 50)

    try:
        # Add workflow service to path
        workflow_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'workflow-service')
        sys.path.insert(0, workflow_path)

        # Import the component
        from app.components.processing.alter_metadata import AlterMetadataComponent

        # Create instance
        component = AlterMetadataComponent()

        # Get definition
        definition = component.get_definition()

        print(f"✅ Component imported successfully")
        print(f"✅ Component name: {definition['name']}")
        print(f"✅ Display name: {definition['display_name']}")
        print(f"✅ Category: {definition['category']}")
        print(f"✅ Number of inputs: {len(definition['inputs'])}")
        print(f"✅ Number of outputs: {len(definition['outputs'])}")

        # Validate inputs are dual-purpose
        dual_purpose_count = sum(1 for inp in definition['inputs'] if inp.get('is_handle', False))
        print(f"✅ Dual-purpose inputs: {dual_purpose_count}/{len(definition['inputs'])}")

        # Check for modern execute method
        has_execute = hasattr(component, 'execute') and callable(getattr(component, 'execute'))
        print(f"✅ Has execute method: {has_execute}")

        # Check for legacy build method
        has_build = hasattr(component, 'build') and callable(getattr(component, 'build'))
        print(f"✅ Has build method (legacy): {has_build}")

        return True

    except Exception as e:
        print(f"❌ Workflow service validation failed: {str(e)}")
        return False


def validate_node_executor_service():
    """Validate the node executor service component registration."""
    print("\n🔍 Validating Node Executor Service Component")
    print("-" * 50)

    try:
        # Add node executor service to path
        executor_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'node-executor-service')
        sys.path.insert(0, executor_path)

        # Change to the executor directory to handle relative imports
        original_cwd = os.getcwd()
        os.chdir(executor_path)

        # Import the component using importlib to avoid path issues
        spec = importlib.util.spec_from_file_location(
            "alter_metadata_component",
            os.path.join(executor_path, "app", "components", "alter_metadata_component.py")
        )
        module = importlib.util.module_from_spec(spec)

        # Mock the missing dependencies
        import types
        mock_base = types.ModuleType('app.core_.base_component')
        mock_base.BaseComponent = type('BaseComponent', (), {
            '__init__': lambda self: None,
            'request_schema': None
        })
        mock_base.ValidationResult = type('ValidationResult', (), {
            '__init__': lambda self, is_valid=True, error_message="", error_details=None: setattr(self, 'is_valid', is_valid) or setattr(self, 'error_message', error_message)
        })

        mock_system = types.ModuleType('app.core_.component_system')
        mock_system.register_component = lambda name: lambda cls: cls
        mock_system.get_registered_components = lambda: {"AlterMetadataComponent": type}

        sys.modules['app.core_.base_component'] = mock_base
        sys.modules['app.core_.component_system'] = mock_system

        spec.loader.exec_module(module)
        AlterMetadataComponent = module.AlterMetadataComponent

        # Create instance
        component = AlterMetadataComponent()

        print(f"✅ Component imported successfully")
        print(f"✅ Component class: {component.__class__.__name__}")

        # Check for required methods
        has_validate = hasattr(component, 'validate') and callable(getattr(component, 'validate'))
        has_process = hasattr(component, 'process') and callable(getattr(component, 'process'))

        print(f"✅ Has validate method: {has_validate}")
        print(f"✅ Has process method: {has_process}")

        # Check for request schema
        has_schema = hasattr(component, 'request_schema') and component.request_schema is not None
        print(f"✅ Has request schema: {has_schema}")

        # Check component registration (using mock)
        registered_components = mock_system.get_registered_components()
        is_registered = "AlterMetadataComponent" in registered_components
        print(f"✅ Component registered: {is_registered}")

        if is_registered:
            registered_class = registered_components["AlterMetadataComponent"]
            print(f"✅ Registered class: {registered_class.__name__}")

        # Restore original directory
        os.chdir(original_cwd)

        return True

    except Exception as e:
        print(f"❌ Node executor service validation failed: {str(e)}")
        import traceback
        print(f"Error details: {traceback.format_exc()}")
        return False


def validate_component_consistency():
    """Validate consistency of the workflow service component."""
    print("\n🔍 Validating Component Consistency")
    print("-" * 50)

    try:
        # Import from workflow service only (since we know it works)
        workflow_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'workflow-service')
        sys.path.insert(0, workflow_path)

        # Import workflow component
        workflow_spec = importlib.util.spec_from_file_location(
            "workflow_alter_metadata",
            os.path.join(workflow_path, "app", "components", "processing", "alter_metadata.py")
        )
        workflow_module = importlib.util.module_from_spec(workflow_spec)
        workflow_spec.loader.exec_module(workflow_module)
        WorkflowComponent = workflow_module.AlterMetadataComponent

        # Create instance
        workflow_component = WorkflowComponent()

        # Get workflow definition
        workflow_definition = workflow_component.get_definition()

        # Check name consistency
        workflow_name = workflow_definition['name']
        expected_name = "AlterMetadataComponent"

        name_consistent = workflow_name == expected_name
        print(f"✅ Name consistency: {name_consistent} ({workflow_name})")

        # Check input consistency
        workflow_inputs = [inp['name'] for inp in workflow_definition['inputs']]
        expected_inputs = ['input_metadata', 'updates', 'keys_to_remove']

        inputs_consistent = set(workflow_inputs) == set(expected_inputs)
        print(f"✅ Input consistency: {inputs_consistent}")
        print(f"   Workflow inputs: {workflow_inputs}")
        print(f"   Expected inputs: {expected_inputs}")

        # Check output consistency
        workflow_outputs = [out['name'] for out in workflow_definition['outputs']]
        expected_outputs = ['output_metadata', 'error']

        outputs_consistent = set(workflow_outputs) == set(expected_outputs)
        print(f"✅ Output consistency: {outputs_consistent}")
        print(f"   Workflow outputs: {workflow_outputs}")
        print(f"   Expected outputs: {expected_outputs}")

        # Check that all inputs are dual-purpose
        all_dual_purpose = all(inp.get('is_handle', False) for inp in workflow_definition['inputs'])
        print(f"✅ All inputs dual-purpose: {all_dual_purpose}")

        return name_consistent and inputs_consistent and outputs_consistent and all_dual_purpose

    except Exception as e:
        print(f"❌ Consistency validation failed: {str(e)}")
        import traceback
        print(f"Error details: {traceback.format_exc()}")
        return False


def validate_file_structure():
    """Validate that all required files exist."""
    print("\n🔍 Validating File Structure")
    print("-" * 50)

    required_files = [
        "workflow-service/app/components/processing/alter_metadata.py",
        "node-executor-service/app/components/alter_metadata_component.py",
        "node-executor-service/app/components/__init__.py",
        "workflow-service/tests/test_alter_metadata_component.py",
        "node-executor-service/tests/test_alter_metadata_component.py",
    ]

    all_exist = True

    for file_path in required_files:
        full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), file_path)
        exists = os.path.exists(full_path)
        status = "✅" if exists else "❌"
        print(f"{status} {file_path}")

        if not exists:
            all_exist = False

    return all_exist


def main():
    """Run all validation checks."""
    print("🧪 AlterMetadataComponent Registration Validation")
    print("=" * 70)

    results = []

    # Run all validations
    results.append(("File Structure", validate_file_structure()))
    results.append(("Workflow Service", validate_workflow_service()))
    results.append(("Node Executor Service", validate_node_executor_service()))
    results.append(("Component Consistency", validate_component_consistency()))

    # Summary
    print("\n📊 Validation Summary")
    print("=" * 70)

    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False

    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 All validations passed! AlterMetadataComponent is properly registered and consistent.")
    else:
        print("⚠️  Some validations failed. Please review the errors above.")

    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
