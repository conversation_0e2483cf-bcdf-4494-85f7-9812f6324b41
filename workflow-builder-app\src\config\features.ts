/**
 * Feature flags for the application
 *
 * These flags control which features are enabled in the application.
 * They can be overridden by environment variables.
 */
export const FEATURES = {
  // Validation features
  FRONTEND_VALIDATION: true, // Enable frontend validation
  BACKEND_VALIDATION: false, // Disable backend validation
  HYBRID_VALIDATION: false, // Disable hybrid validation
  VALIDATION_DEBUG: process.env.NEXT_PUBLIC_VALIDATION_DEBUG === "true", // Disable validation debugging by default

  // Validation behavior
  VALIDATE_ON_EDIT: false, // Disable validation during editing
  VALIDATE_ON_SAVE: true, // Enable validation before saving
  VALIDATE_ON_EXECUTE: true, // Enable validation before execution
};
