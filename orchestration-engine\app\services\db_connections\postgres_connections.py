import json
import psycopg2
from psycopg2.extras import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from psycopg2 import pool
from app.config.config import settings
from app.utils.enhanced_logger import get_logger

logger = get_logger("PostgresManager")

# Global connection pool
_connection_pool = None
# Global PostgresManager instance (singleton)
_postgres_manager_instance = None


def get_connection_pool():
    """
    Get or create the global connection pool.
    """
    global _connection_pool
    if _connection_pool is None:
        try:
            # Create connection pool
            _connection_pool = pool.ThreadedConnectionPool(
                minconn=1,
                maxconn=10,  # Adjust based on your application needs
                host=settings.db_host,
                port=int(settings.db_port),
                user=settings.db_user,
                password=(
                    settings.db_password.get_secret_value()
                    if settings.db_password
                    else None
                ),
                dbname=settings.db_name,
            )
            logger.info("PostgreSQL connection pool created")
        except Exception as e:
            logger.error(f"Error creating PostgreSQL connection pool: {e}")
            _connection_pool = None
    return _connection_pool


def get_postgres_manager():
    """
    Get or create the global PostgresManager instance (singleton pattern).
    """
    global _postgres_manager_instance
    if _postgres_manager_instance is None:
        _postgres_manager_instance = PostgresManager()
    return _postgres_manager_instance


class PostgresManager:
    """
    Manages connections and operations with a PostgreSQL database.
    Provides methods for storing and retrieving workflow state and transition results.
    Uses a connection pool for better performance and resource management.
    """

    def __init__(self):
        """
        Initializes the PostgresManager by loading environment variables and
        attempting to establish a connection to PostgreSQL.
        """
        self.logger = logger
        self.db_host = settings.db_host
        self.db_port = settings.db_port
        self.db_user = settings.db_user
        self.db_password = (
            settings.db_password.get_secret_value() if settings.db_password else None
        )
        self.db_name = settings.db_name

        if (
            not self.db_host
            or not self.db_port
            or not self.db_user
            or self.db_password is None
            or not self.db_name
        ):
            raise ValueError(
                "PostgreSQL connection details are missing in .env file. "
                "Please ensure DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, and DB_NAME are set."
            )

        # We don't store a connection directly anymore, just check if the pool is available
        self.pool = get_connection_pool()
        if self.pool:
            self.logger.info("PostgreSQL connection pool is available")
        else:
            self.logger.warning("PostgreSQL connection pool is not available")

        # Ensure tables exist when the manager is initialized
        if self.pool:
            self._ensure_tables_exist()

    def is_connected(self):
        """
        Checks if the connection to PostgreSQL is active.

        Returns:
            bool: True if connected, False otherwise.
        """
        if not self.pool:
            return False

        conn = None
        try:
            # Get a connection from the pool
            conn = self.pool.getconn()
            # Check if connection is still active
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                return True
        except Exception:
            self.logger.warning("PostgreSQL connection lost or pool is not available")
            return False
        finally:
            # Return the connection to the pool
            if conn:
                self.pool.putconn(conn)

    def _ensure_tables_exist(self):
        """
        Ensures that the required tables exist in the database.
        Creates them if they don't exist.
        """
        if not self.is_connected():
            self.logger.error(
                "Cannot ensure tables exist: PostgreSQL is not connected."
            )
            return False

        conn = None
        try:
            conn = self.pool.getconn()
            with conn.cursor() as cursor:
                # Create workflow_state table if it doesn't exist
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS workflow_state (
                        id SERIAL PRIMARY KEY,
                        correlation_id VARCHAR(255) UNIQUE NOT NULL,
                        workflow_id VARCHAR(255) NOT NULL,
                        state_data JSONB NOT NULL,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        archived BOOLEAN DEFAULT FALSE
                    )
                """
                )

                # Create index on correlation_id if it doesn't exist
                cursor.execute(
                    """
                    DO $$
                    BEGIN
                        IF NOT EXISTS (
                            SELECT 1 FROM pg_indexes
                            WHERE indexname = 'idx_workflow_state_correlation_id'
                        ) THEN
                            CREATE INDEX idx_workflow_state_correlation_id ON workflow_state(correlation_id);
                        END IF;
                    END $$;
                """
                )

                # Create transition_results table if it doesn't exist
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS transition_results (
                        id SERIAL PRIMARY KEY,
                        correlation_id VARCHAR(255) NOT NULL,
                        transition_id VARCHAR(255) NOT NULL,
                        result_data JSONB NOT NULL,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        archived BOOLEAN DEFAULT FALSE
                    )
                """
                )

                # Create composite index on correlation_id and transition_id if it doesn't exist
                cursor.execute(
                    """
                    DO $$
                    BEGIN
                        IF NOT EXISTS (
                            SELECT 1 FROM pg_indexes
                            WHERE indexname = 'idx_transition_results_composite'
                        ) THEN
                            CREATE UNIQUE INDEX idx_transition_results_composite
                            ON transition_results(correlation_id, transition_id);
                        END IF;
                    END $$;
                """
                )

                conn.commit()
                self.logger.info("PostgreSQL tables and indexes created or verified.")
                return True
        except psycopg2.Error as e:
            self.logger.error(f"Error ensuring tables exist: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                self.pool.putconn(conn)

    def store_workflow_state(self, correlation_id, workflow_id, state_data):
        """
        Stores workflow state in PostgreSQL.

        Args:
            correlation_id (str): Unique identifier for the workflow execution.
            workflow_id (str): Identifier for the workflow.
            state_data (dict): The workflow state data to store.

        Returns:
            bool: True if the state was stored successfully, False otherwise.
        """
        if not self.is_connected():
            self.logger.error(
                "Cannot store workflow state: PostgreSQL is not connected."
            )
            return False

        # Ensure tables exist
        if not self._ensure_tables_exist():
            return False

        conn = None
        try:
            conn = self.pool.getconn()
            with conn.cursor() as cursor:
                # Check if a record with this correlation_id already exists
                cursor.execute(
                    "SELECT id FROM workflow_state WHERE correlation_id = %s",
                    (correlation_id,),
                )
                existing_record = cursor.fetchone()

                if existing_record:
                    # Update existing record
                    cursor.execute(
                        """
                        UPDATE workflow_state
                        SET workflow_id = %s, state_data = %s, updated_at = NOW()
                        WHERE correlation_id = %s
                        """,
                        (workflow_id, Json(state_data), correlation_id),
                    )
                    self.logger.debug(
                        f"Updated workflow state for correlation_id: {correlation_id}"
                    )
                else:
                    # Insert new record
                    cursor.execute(
                        """
                        INSERT INTO workflow_state (correlation_id, workflow_id, state_data)
                        VALUES (%s, %s, %s)
                        """,
                        (correlation_id, workflow_id, Json(state_data)),
                    )
                    self.logger.debug(
                        f"Inserted new workflow state for correlation_id: {correlation_id}"
                    )

                conn.commit()
                return True
        except psycopg2.Error as e:
            self.logger.error(f"Error storing workflow state: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                self.pool.putconn(conn)

    def get_workflow_state(self, correlation_id):
        """
        Retrieves workflow state from PostgreSQL.

        Args:
            correlation_id (str): Unique identifier for the workflow execution.

        Returns:
            dict: The workflow state data, or None if not found.
        """
        if not self.is_connected():
            self.logger.error("Cannot get workflow state: PostgreSQL is not connected.")
            return None

        conn = None
        try:
            conn = self.pool.getconn()
            with conn.cursor(cursor_factory=DictCursor) as cursor:
                cursor.execute(
                    """
                    SELECT correlation_id, workflow_id, state_data, created_at, updated_at
                    FROM workflow_state
                    WHERE correlation_id = %s
                    """,
                    (correlation_id,),
                )
                record = cursor.fetchone()

                if record:
                    # Convert to dictionary
                    result = dict(record)
                    self.logger.debug(
                        f"Retrieved workflow state for correlation_id: {correlation_id}"
                    )
                    return result
                else:
                    self.logger.debug(
                        f"No workflow state found for correlation_id: {correlation_id}"
                    )
                    return None
        except psycopg2.Error as e:
            self.logger.error(f"Error retrieving workflow state: {e}")
            return None
        finally:
            if conn:
                self.pool.putconn(conn)

    def store_transition_result(self, correlation_id, transition_id, result_data):
        """
        Stores transition result in PostgreSQL.

        Args:
            correlation_id (str): Unique identifier for the workflow execution.
            transition_id (str): Identifier for the transition.
            result_data (dict): The transition result data to store.

        Returns:
            bool: True if the result was stored successfully, False otherwise.
        """
        self.logger.debug(
            f"Attempting to store transition result for {transition_id} in correlation {correlation_id}"
        )
        self.logger.debug(f"Result data type: {type(result_data)}")
        self.logger.debug(f"Result data: {result_data}")

        if not self.is_connected():
            self.logger.error(
                "Cannot store transition result: PostgreSQL is not connected."
            )
            return False

        # Ensure tables exist
        if not self._ensure_tables_exist():
            self.logger.error("Failed to ensure tables exist")
            return False

        conn = None
        try:
            conn = self.pool.getconn()
            with conn.cursor() as cursor:
                # Check if a record with this correlation_id and transition_id already exists
                cursor.execute(
                    "SELECT id FROM transition_results WHERE correlation_id = %s AND transition_id = %s",
                    (correlation_id, transition_id),
                )
                existing_record = cursor.fetchone()

                # Convert result_data to JSON using psycopg2's Json adapter
                try:
                    # Test if the data can be serialized to JSON
                    json_str = json.dumps(result_data)
                    self.logger.debug(
                        f"Successfully serialized result data to JSON string"
                    )

                    # Convert to psycopg2's Json object
                    json_data = Json(result_data)
                    self.logger.debug(f"Converted to psycopg2 Json object")
                except Exception as e:
                    self.logger.error(f"Error converting result data to JSON: {e}")
                    self.logger.error(f"Result data: {result_data}")
                    return False

                if existing_record:
                    # Update existing record
                    self.logger.debug(
                        f"Updating existing record for transition {transition_id}"
                    )
                    cursor.execute(
                        """
                        UPDATE transition_results
                        SET result_data = %s, updated_at = NOW()
                        WHERE correlation_id = %s AND transition_id = %s
                        """,
                        (json_data, correlation_id, transition_id),
                    )
                    self.logger.debug(
                        f"Updated result for transition {transition_id} in correlation {correlation_id}"
                    )
                else:
                    # Insert new record
                    self.logger.debug(
                        f"Inserting new record for transition {transition_id}"
                    )
                    cursor.execute(
                        """
                        INSERT INTO transition_results (correlation_id, transition_id, result_data)
                        VALUES (%s, %s, %s)
                        """,
                        (correlation_id, transition_id, json_data),
                    )
                    self.logger.debug(
                        f"Inserted new result for transition {transition_id} in correlation {correlation_id}"
                    )

                conn.commit()
                self.logger.debug(
                    f"Successfully stored transition result for {transition_id}"
                )
                return True
        except psycopg2.Error as e:
            self.logger.error(f"Error storing transition result: {e}")
            if conn:
                conn.rollback()
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error storing transition result: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                self.pool.putconn(conn)

    def get_transition_result(self, correlation_id, transition_id):
        """
        Retrieves transition result from PostgreSQL.

        Args:
            correlation_id (str): Unique identifier for the workflow execution.
            transition_id (str): Identifier for the transition.

        Returns:
            dict: The transition result data, or None if not found.
        """
        if not self.is_connected():
            self.logger.error(
                "Cannot get transition result: PostgreSQL is not connected."
            )
            return None

        conn = None
        try:
            conn = self.pool.getconn()
            with conn.cursor(cursor_factory=DictCursor) as cursor:
                cursor.execute(
                    """
                    SELECT result_data
                    FROM transition_results
                    WHERE correlation_id = %s AND transition_id = %s
                    """,
                    (correlation_id, transition_id),
                )
                record = cursor.fetchone()

                if record and "result_data" in record:
                    self.logger.debug(
                        f"Retrieved result for transition {transition_id} in correlation {correlation_id}"
                    )
                    return record["result_data"]
                else:
                    self.logger.debug(
                        f"No result found for transition {transition_id} in correlation {correlation_id}"
                    )
                    return None
        except psycopg2.Error as e:
            self.logger.error(f"Error retrieving transition result: {e}")
            return None
        finally:
            if conn:
                self.pool.putconn(conn)

    def close_connection(self):
        """
        Closes the connection pool to the PostgreSQL database.
        """
        global _connection_pool
        if self.pool:
            self.pool.closeall()
            _connection_pool = None
            self.pool = None
            self.logger.info("PostgreSQL connection pool closed.")
