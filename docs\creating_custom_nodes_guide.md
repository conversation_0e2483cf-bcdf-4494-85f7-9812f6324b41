# Creating Custom Nodes: A Step-by-Step Guide

This guide walks you through the process of creating, testing, and deploying custom nodes in our workflow platform. Custom nodes allow you to extend the platform's capabilities with your own logic without modifying the core codebase.

## Table of Contents

1. [Accessing the Custom Node Editor](#1-accessing-the-custom-node-editor)
2. [Defining Your Custom Node](#2-defining-your-custom-node)
3. [Implementing Node Logic](#3-implementing-node-logic)
4. [Testing Your Custom Node](#4-testing-your-custom-node)
5. [Deploying Your Custom Node](#5-deploying-your-custom-node)
6. [Best Practices](#6-best-practices)
7. [Common Pitfalls to Avoid](#7-common-pitfalls-to-avoid)
8. [Limitations and Constraints](#8-limitations-and-constraints)

## 1. Accessing the Custom Node Editor

To create a new custom node:

1. Log in to the Workflow Builder App
2. Navigate to the main menu and select **Custom Nodes**
3. Click the **+ Create New Node** button in the top-right corner
4. The Custom Node Editor interface will open with a form on the left and a live preview on the right

![Custom Node Editor Interface](https://placeholder-image.com/custom-node-editor.png)

## 2. Defining Your Custom Node

The custom node definition form contains several sections that define your node's appearance, behavior, and functionality.

### Basic Information

| Field | Description | Example |
|-------|-------------|---------|
| **Name** | Unique identifier (lowercase, alphanumeric, underscores) | `http_client` |
| **Display Name** | User-friendly name shown in the UI | `HTTP Client` |
| **Description** | Detailed explanation of the node's purpose | `Makes HTTP requests to external APIs` |
| **Category** | Grouping for the component palette | `Integration` |
| **Icon** | Visual representation from the Lucide icon library | `Globe` |
| **Version** | Semantic version number (major.minor.patch) | `1.0.0` |
| **Beta** | Toggle if the node is in beta/experimental status | `false` |
| **Requires Approval** | Toggle if node execution requires human approval | `false` |

### Input Ports

Click **+ Add Input** to define each input port. For each input:

| Field | Description | Example |
|-------|-------------|---------|
| **Name** | Unique identifier for the input | `url` |
| **Display Name** | User-friendly label | `URL` |
| **Input Type** | Data type (string, number, boolean, object, array) | `string` |
| **Required** | Whether the input must have a value | `true` |
| **Default Value** | Value to use if none is provided | `https://` |
| **Description** | Explanation of the input's purpose | `The URL to send the request to` |
| **Validation** | Rules to validate input values | `pattern: ^https?://.*$` |

### Output Ports

Click **+ Add Output** to define each output port. For each output:

| Field | Description | Example |
|-------|-------------|---------|
| **Name** | Unique identifier for the output | `response` |
| **Display Name** | User-friendly label | `Response` |
| **Output Type** | Data type (string, number, boolean, object, array, any) | `object` |
| **Description** | Explanation of the output's purpose | `The HTTP response object` |

### Execution Configuration

Select the execution type for your custom node:

1. **Python Code**: Server-side execution with Python
2. **HTTP Endpoint**: Call an external API
3. **JavaScript Code**: Client-side execution for UI operations

## 3. Implementing Node Logic

### Python Code Execution

For `python_code` execution type, write Python code that processes the inputs and produces outputs:

```python
# Available imports: json, re, math, datetime, collections, copy, itertools
import json

# Access inputs using the inputs dictionary
url = inputs.get('url')
method = inputs.get('method', 'GET')
headers = inputs.get('headers', {})

# IMPORTANT: For HTTP requests, use the http_endpoint execution type instead
# The Python execution environment does not include the requests library
# This is just a demonstration of how to process inputs and set outputs

# Example processing (string manipulation)
parsed_url = url.split('://')
protocol = parsed_url[0] if len(parsed_url) > 1 else 'unknown'
domain = parsed_url[1].split('/')[0] if len(parsed_url) > 1 else url

# IMPORTANT: Always set the result variable with your outputs
result = {
    'protocol': protocol,
    'domain': domain,
    'method': method,
    'headers_count': len(headers)
}
```

**Guidelines for Python Code:**
- Always use the `inputs` dictionary to access input values
- Always set the `result` dictionary with your outputs
- Use only the allowed modules (json, re, math, datetime, collections, copy, itertools)
- Keep execution time under 5 seconds
- Handle exceptions gracefully
- No network access is available (use `http_endpoint` execution type instead)
- No file system access is available

**Important Note on HTTP Requests:**
For making HTTP requests, you should use the `http_endpoint` execution type instead of trying to use network libraries in Python code. The Python execution environment is deliberately restricted and does not include libraries like `requests`, `urllib`, or other HTTP clients for security reasons.

### HTTP Endpoint Execution

For `http_endpoint` execution type, provide the endpoint details:

```json
{
  "endpoint_url": "https://your-api.example.com/custom-node",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer ${auth_token}"
  },
  "timeout": 30
}
```

**Guidelines for HTTP Endpoints:**
- Use HTTPS for secure communication
- Implement proper authentication
- Handle rate limiting appropriately
- Ensure the endpoint returns JSON responses
- Structure the response to match your defined outputs

### JavaScript Code Execution

For `javascript_code` execution type, write JavaScript code for UI operations:

```javascript
// Access input values
const selectedOption = inputs.selectedOption || '';
const showAdvanced = inputs.showAdvanced || false;

// UI operations
if (showAdvanced) {
  // Show advanced fields
  setFieldVisibility('advancedOption1', true);
  setFieldVisibility('advancedOption2', true);
} else {
  // Hide advanced fields
  setFieldVisibility('advancedOption1', false);
  setFieldVisibility('advancedOption2', false);
}

// Set validation state
if (selectedOption === 'api_key' && !inputs.apiKey) {
  setFieldValidation('apiKey', {
    isValid: false,
    message: 'API Key is required when API Key authentication is selected'
  });
}
```

**Guidelines for JavaScript Code:**
- Use only for UI operations (field visibility, validation, etc.)
- Keep code execution under 500ms
- Don't attempt to access external resources
- Use the provided UI helper functions (setFieldVisibility, setFieldValidation)
- JavaScript code runs only during workflow design, not execution

## 4. Testing Your Custom Node

### Using the Test Panel

1. Click the **Test** tab in the Custom Node Editor
2. Enter test values for each input
3. Click **Run Test** to execute your node with the provided inputs
4. Review the outputs and execution logs

### Debugging Tips

- Check the **Execution Log** tab for detailed logs
- Use `console.log()` statements in JavaScript code for debugging UI operations
- For Python code, add debug information to the `result` dictionary during development
- Test edge cases and error conditions explicitly

### Iterative Testing

1. Make changes to your node definition or code
2. Click **Save Draft** to save without publishing
3. Run tests to verify your changes
4. Repeat until your node works as expected

## 5. Deploying Your Custom Node

### Publishing Your Node

1. Ensure all tests pass successfully
2. Click **Validate** to check for any issues
3. Fix any validation errors that are reported
4. Click **Publish** to make your node available

### Versioning

When updating an existing node:

1. Increment the version number appropriately:
   - **Patch** (1.0.0 → 1.0.1): Bug fixes
   - **Minor** (1.0.0 → 1.1.0): New features, backward compatible
   - **Major** (1.0.0 → 2.0.0): Breaking changes
2. Provide release notes describing the changes
3. Click **Publish New Version**

### Sharing and Permissions

To share your custom node with others:

1. Go to the **Sharing** tab
2. Choose the visibility level:
   - **Private**: Only you can use it
   - **Organization**: Available to your organization
   - **Public**: Available to all users (requires approval)
3. Click **Update Permissions**

## 6. Best Practices

### Design Principles

- **Single Responsibility**: Each node should do one thing well
- **Clear Naming**: Use descriptive names for the node and its inputs/outputs
- **Comprehensive Documentation**: Provide detailed descriptions for all fields
- **Graceful Error Handling**: Handle errors and edge cases appropriately
- **Consistent Styling**: Follow platform conventions for naming and structure

### Code Quality

- **Readability**: Write clean, well-commented code
- **Efficiency**: Optimize for performance, especially for resource-intensive operations
- **Validation**: Validate all inputs before processing
- **Error Messages**: Provide clear, actionable error messages
- **Idempotency**: Operations should be idempotent when possible

### Security Considerations

- **Sensitive Data**: Never hardcode credentials or sensitive information
- **Input Validation**: Always validate and sanitize inputs
- **Least Privilege**: Request only the permissions you need
- **Rate Limiting**: Implement rate limiting for external API calls
- **Data Privacy**: Be mindful of data privacy regulations

## 7. Common Pitfalls to Avoid

### Definition Pitfalls

- **Duplicate Names**: Using names that conflict with built-in components
- **Missing Validation**: Not defining validation rules for inputs
- **Unclear Purpose**: Vague descriptions that don't explain the node's function
- **Overly Complex**: Trying to do too much in a single node
- **Poor Categorization**: Placing the node in an inappropriate category

### Code Pitfalls

- **Infinite Loops**: Creating code that may never terminate
- **Resource Leaks**: Not properly closing resources (connections, files)
- **Hardcoded Values**: Embedding configuration that should be inputs
- **Excessive Dependencies**: Trying to use unavailable libraries
- **Synchronous Blocking**: Blocking execution for long-running operations
- **Missing Result**: Forgetting to set the `result` variable in Python code

### Testing Pitfalls

- **Happy Path Only**: Testing only the ideal scenario
- **Ignoring Edge Cases**: Not testing boundary conditions
- **Insufficient Validation**: Not verifying all outputs
- **Overlooking Performance**: Not testing with realistic data volumes

## 8. Limitations and Constraints

Be aware of these limitations when creating custom nodes:

### Execution Environment Limitations

- **Python Code Execution**:
  - Maximum execution time: 5 seconds
  - Maximum memory usage: 128MB
  - Limited standard library access (json, re, math, datetime, collections, copy, itertools)
  - No file system or network access outside of allowed libraries
  - No third-party libraries or package imports
  - No persistent state between executions
  - No concurrent or parallel execution within a node

- **HTTP Endpoint Execution**:
  - Restricted to allowlisted domains only
  - Blocked protocols: file://, ftp://, ws://, wss://
  - Maximum request timeout: 30 seconds
  - Maximum response size: 10MB
  - Supported methods: GET, POST, PUT, DELETE
  - Supported content types: application/json, text/plain, application/xml
  - No support for client certificates
  - Rate limiting: 100 requests per hour per user

- **JavaScript Code Execution**:
  - Limited to UI operations only
  - No access to browser APIs like localStorage, indexedDB, or navigator
  - No access to DOM outside the node's own container
  - Maximum execution time: 500ms
  - Maximum memory usage: 10MB
  - No custom validation functions for input fields
  - No cross-field validation capabilities

### Resource Quotas and Rate Limits

- **Execution Quotas**:
  - Free tier: 1,000 custom node executions per month
  - Standard tier: 10,000 custom node executions per month
  - Enterprise tier: Customizable limits

- **Rate Limits**:
  - API requests: 100 requests per minute
  - Custom node creation/update: 50 per day
  - Workflow executions with custom nodes: 10 concurrent executions

- **Storage Limits**:
  - Custom node definitions: 10MB total per user/organization
  - Execution logs: 7-day retention for free tier, 30-day for standard, 90-day for enterprise

### Definition Constraints

- Maximum node definition size: 100KB
- Maximum number of custom nodes per user/organization: 100
- Node names must be unique within a namespace
- Reserved keywords and names cannot be used
- Maximum 10 versions per custom node
- No automatic version migration for workflows using older versions
- No branching or parallel version development
- Maximum of 10 inputs and 10 outputs per custom node

### UI Constraints

- Custom node visual customization is limited to predefined options (color, icon, size)
- No support for custom rendering of node content beyond the standard template
- Limited to validation rules defined in the schema

### Workflow Integration Limitations

- Maximum workflow execution time: 30 minutes
- Maximum nodes in a single workflow: 100
- Maximum custom nodes in a single workflow: 50
- Maximum workflow nesting depth: 5
- No support for dynamic node creation during execution
- No support for dynamic edge creation during execution
- Limited retry capabilities for custom nodes (max 3 retries)
- No custom exception types
- No transaction support for partial rollback
- No compensation workflows for failed executions

### Practical Limitations You Might Face

- **Development Experience**: No step-through debugging or breakpoint capabilities
- **Error Diagnosis**: Error messages may be sanitized for security reasons
- **Performance Variability**: Execution time may vary based on system load
- **Version Compatibility**: Custom nodes are tied to the platform version they were created in
- **Testing Constraints**: Limited ability to simulate production environments
- **Debugging Challenges**: Limited runtime debugging compared to local development
- **Integration Complexity**: Difficulty in testing interactions with other nodes
- **Documentation Gaps**: May encounter undocumented behaviors or edge cases
- **Update Propagation**: Changes to custom nodes may require manual updates to existing workflows
- **Collaboration Limitations**: Limited tools for team development of custom nodes

For a complete list of limitations, refer to the [Custom Node Limitations](https://docs.example.com/custom-nodes/limitations) documentation.

---

By following this guide, you can create powerful custom nodes that extend the platform's capabilities while maintaining security, reliability, and performance. If you have questions or need assistance, please contact our support <NAME_EMAIL>.
