from typing import List, Dict, Any, Optional
from app.models.workflow_builder.components import InputBase, StringInput


def generate_dynamic_inputs(
    base_name: str,
    base_display_name: str,
    count: int,
    input_type: str = "string",
    is_handle: bool = True,
    input_types: Optional[List[str]] = None,
    info: Optional[str] = None,
) -> List[InputBase]:
    """
    Generate a list of dynamic inputs with sequential names.

    Args:
        base_name: The base name for the inputs (e.g., "input" -> "input_1", "input_2", etc.)
        base_display_name: The base display name for the inputs
        count: The number of inputs to generate
        input_type: The type of input to generate
        is_handle: Whether the inputs should be handles
        input_types: The types of connections the inputs can accept (if handles)
        info: The info text for the inputs

    Returns:
        A list of InputBase objects
    """
    inputs = []

    for i in range(1, count + 1):
        # Create the input name and display name
        name = f"{base_name}_{i}"
        display_name = f"{base_display_name} {i}"

        # Create the handle input name if needed
        handle_name = f"{name}_handle" if not name.endswith("_handle") else name

        # Create the input
        if input_type == "string":
            input_def = StringInput(
                name=name,
                display_name=display_name,
                info=info,
                is_handle=is_handle,
                input_types=input_types or ["Any"],
            )
        else:
            # Add support for other input types as needed
            input_def = StringInput(
                name=name,
                display_name=display_name,
                info=info,
                is_handle=is_handle,
                input_types=input_types or ["Any"],
            )

        inputs.append(input_def)

    return inputs


def update_component_with_dynamic_inputs(
    component: Any,
    config: Dict[str, Any],
    dynamic_input_name: str,
    base_name: str,
    base_display_name: str,
    default_count: int = 2,
) -> None:
    """
    Update a component's inputs based on configuration.

    This function is used to dynamically update a component's inputs
    based on the configuration provided by the user.

    Args:
        component: The component to update
        config: The component configuration
        dynamic_input_name: The name of the dynamic input field in the config
        base_name: The base name for the dynamic inputs
        base_display_name: The base display name for the dynamic inputs
        default_count: The default number of inputs to generate
    """
    # Get the number of inputs from the config
    count = int(config.get(dynamic_input_name, default_count))

    # Generate the dynamic inputs
    dynamic_inputs = generate_dynamic_inputs(
        base_name=base_name, base_display_name=base_display_name, count=count
    )

    # Update the component's inputs
    # This assumes the component has an 'inputs' attribute that is a list
    if hasattr(component, "inputs"):
        # Filter out existing dynamic inputs
        static_inputs = [
            inp for inp in component.inputs if not inp.name.startswith(f"{base_name}_")
        ]

        # Add the new dynamic inputs
        component.inputs = static_inputs + dynamic_inputs
