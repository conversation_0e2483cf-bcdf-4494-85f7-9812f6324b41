"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { updatePasswordSchema, UpdatePasswordType, validatePassword } from "@/lib/schemas/auth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { EyeIcon, EyeOffIcon, Loader2, CheckCircle, XCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { authApi } from "@/lib/authApi";

interface UpdatePasswordFormProps {
  token: string;
}

export function UpdatePasswordForm({ token }: UpdatePasswordFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [passwordValidations, setPasswordValidations] = useState({
    length: false,
    hasNumber: false,
    hasSymbol: false,
  });
  const [showConfirmation, setShowConfirmation] = useState(false);
  const router = useRouter();

  const form = useForm<UpdatePasswordType>({
    resolver: zodResolver(updatePasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  // Update password validations when password changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "password" || name === undefined) {
        setPasswordValidations(validatePassword(value.password || ""));
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const onSubmit = async (data: UpdatePasswordType) => {
    setIsLoading(true);
    try {
      await authApi.updatePassword({
        token,
        password: data.password,
      });
      setShowConfirmation(true);
    } catch (error: any) {
      console.error("Update password error:", error);
      form.setError("root", {
        type: "manual",
        message: error.message || "Failed to update password. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (showConfirmation) {
    return (
      <div className="space-y-6 text-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="rounded-full bg-green-100 p-3">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>
          <h2 className="text-xl font-semibold">Password Updated</h2>
          <p className="text-muted-foreground">
            Your password has been updated successfully. You can now log in with your new password.
          </p>
        </div>
        <Button onClick={() => router.push("/login")} className="w-full">
          Go to Login
        </Button>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {form.formState.errors.root && (
          <div className="rounded-md bg-red-50 p-3 text-sm text-red-500">
            {form.formState.errors.root.message}
          </div>
        )}

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>New Password</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    placeholder="Create a new password"
                    type={showPassword ? "text" : "password"}
                    disabled={isLoading}
                    {...field}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOffIcon className="text-muted-foreground h-4 w-4" />
                    ) : (
                      <EyeIcon className="text-muted-foreground h-4 w-4" />
                    )}
                  </Button>
                </div>
              </FormControl>
              <FormMessage />

              {/* Password validation indicators */}
              <div className="mt-2 space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  {passwordValidations.length ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="text-muted-foreground h-4 w-4" />
                  )}
                  <span
                    className={
                      passwordValidations.length ? "text-green-500" : "text-muted-foreground"
                    }
                  >
                    Between 6-15 characters
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  {passwordValidations.hasNumber ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="text-muted-foreground h-4 w-4" />
                  )}
                  <span
                    className={
                      passwordValidations.hasNumber ? "text-green-500" : "text-muted-foreground"
                    }
                  >
                    At least one number
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  {passwordValidations.hasSymbol ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="text-muted-foreground h-4 w-4" />
                  )}
                  <span
                    className={
                      passwordValidations.hasSymbol ? "text-green-500" : "text-muted-foreground"
                    }
                  >
                    At least one symbol
                  </span>
                </div>
              </div>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Confirm Password</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    placeholder="Confirm your new password"
                    type={showConfirmPassword ? "text" : "password"}
                    disabled={isLoading}
                    {...field}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={isLoading}
                  >
                    {showConfirmPassword ? (
                      <EyeOffIcon className="text-muted-foreground h-4 w-4" />
                    ) : (
                      <EyeIcon className="text-muted-foreground h-4 w-4" />
                    )}
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Update Password
        </Button>
      </form>
    </Form>
  );
}
