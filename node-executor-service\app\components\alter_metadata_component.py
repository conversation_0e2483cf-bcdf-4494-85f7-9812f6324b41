"""
Alter Metadata Component - Modifies metadata dictionary keys.
"""
import logging
import traceback
import copy
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator, ValidationError

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class AlterMetadataRequest(BaseModel):
    """Schema for metadata alteration requests."""
    input_metadata: Dict[str, Any] = Field(..., description="The metadata dictionary to modify")
    updates: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Dictionary of key-value pairs to update or add to the metadata")
    keys_to_remove: Optional[List[str]] = Field(default_factory=list, description="List of keys to remove from the metadata")

    @field_validator('input_metadata')
    def validate_input_metadata(cls, v):
        if not isinstance(v, dict):
            raise ValueError(f"input_metadata must be a dictionary, got {type(v).__name__}")
        return v

    @field_validator('updates')
    def validate_updates(cls, v):
        if v is not None and not isinstance(v, dict):
            raise ValueError(f"updates must be a dictionary, got {type(v).__name__}")
        return v

    @field_validator('keys_to_remove')
    def validate_keys_to_remove(cls, v):
        if v is not None and not isinstance(v, list):
            raise ValueError(f"keys_to_remove must be a list, got {type(v).__name__}")
        return v


@register_component("AlterMetadataComponent")
class AlterMetadataComponent(BaseComponent):
    """
    Component for modifying metadata dictionary keys.

    This component takes a metadata dictionary and applies updates and removals
    to its keys.
    """

    def __init__(self):
        """
        Initialize the AlterMetadataComponent.
        """
        logger.info("Initializing Alter Metadata Component")
        super().__init__()
        # Set the request schema for automatic validation
        self.request_schema = AlterMetadataRequest
        logger.info("Alter Metadata Component initialized successfully")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a metadata alteration payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating metadata alteration payload for request_id: {request_id}")
        logger.debug(f"Payload to validate for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for validation.")
            # Use the tool_parameters as the actual payload for validation
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        logger.info(f"VALIDATION PARAMETERS KEYS: {list(parameters.keys())}")

        try:
            # First use schema validation (from parent class)
            schema_validation = await super().validate(payload)
            if not schema_validation.is_valid:
                # Try validating the parameters instead
                try:
                    AlterMetadataRequest(**parameters)
                except ValidationError as e:
                    logger.error(f"Schema validation failed for request_id {request_id}: {schema_validation.error_message}")
                    return schema_validation
            logger.debug(f"Schema validation passed for request_id {request_id}")

            # Check for required input_metadata field
            if "input_metadata" not in parameters:
                error_msg = f"Required field 'input_metadata' not found in parameters for request_id {request_id}"
                logger.error(error_msg)
                logger.error(f"Available keys in parameters: {list(parameters.keys())}")
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"input_metadata": "input_metadata is required"}
                )

            # Validate input_metadata type
            input_metadata = parameters.get("input_metadata")
            if not isinstance(input_metadata, dict):
                error_msg = f"Field 'input_metadata' must be a dictionary, got {type(input_metadata).__name__} for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"input_metadata": "must be a dictionary"}
                )

            # Validate updates type if provided
            updates = parameters.get("updates")
            if updates is not None and not isinstance(updates, dict):
                error_msg = f"Field 'updates' must be a dictionary, got {type(updates).__name__} for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"updates": "must be a dictionary"}
                )

            # Validate keys_to_remove type if provided
            keys_to_remove = parameters.get("keys_to_remove")
            if keys_to_remove is not None and not isinstance(keys_to_remove, list):
                error_msg = f"Field 'keys_to_remove' must be a list, got {type(keys_to_remove).__name__} for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"keys_to_remove": "must be a list"}
                )

            logger.info(f"Metadata alteration payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(is_valid=False, error_message=error_msg, error_details={"validation_error": str(e)})

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the metadata alteration operation.

        Args:
            payload: The request payload containing:
                - input_metadata: The metadata dictionary to modify
                - updates: Dictionary of updates to apply (optional)
                - keys_to_remove: List of keys to remove (optional)

        Returns:
            A dictionary containing the altered metadata or an error message
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing metadata alteration request for request_id: {request_id}")
        logger.info(f"PAYLOAD KEYS: {list(payload.keys())}")
        logger.debug(f"Full payload for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for parameters.")
            # Use the tool_parameters as the actual payload
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        logger.info(f"PARAMETERS KEYS: {list(parameters.keys())}")

        try:
            # Get inputs from parameters
            input_metadata = parameters.get("input_metadata")
            updates = parameters.get("updates", {}) or {}  # Ensure it's never None
            keys_to_remove = parameters.get("keys_to_remove", []) or []  # Ensure it's never None

            logger.info(f"Altering metadata for request_id {request_id}")

            # Validate input_metadata type before accessing .keys()
            if input_metadata is None:
                error_msg = f"Input metadata is missing for request_id {request_id}. Please provide a dictionary to modify."
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg
                }

            if not isinstance(input_metadata, dict):
                error_msg = f"Input metadata must be a dictionary, got {type(input_metadata).__name__} for request_id {request_id}. Received value: {str(input_metadata)[:100]}{'...' if len(str(input_metadata)) > 100 else ''}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg
                }

            # Validate updates type
            if updates is not None and not isinstance(updates, dict):
                error_msg = f"Updates must be a dictionary, got {type(updates).__name__} for request_id {request_id}. Received value: {str(updates)[:100]}{'...' if len(str(updates)) > 100 else ''}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg
                }

            # Validate keys_to_remove type
            if keys_to_remove is not None and not isinstance(keys_to_remove, list):
                error_msg = f"Keys to remove must be a list, got {type(keys_to_remove).__name__} for request_id {request_id}. Received value: {str(keys_to_remove)[:100]}{'...' if len(str(keys_to_remove)) > 100 else ''}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg
                }

            logger.debug(f"Input metadata keys: {list(input_metadata.keys())}")
            logger.debug(f"Updates: {updates}")
            logger.debug(f"Keys to remove: {keys_to_remove}")

            # Create a copy of the input metadata
            result = copy.deepcopy(input_metadata)

            # Apply updates
            if updates:
                result.update(updates)
                logger.info(f"Updated keys: {list(updates.keys())} for request_id {request_id}")

            # Remove keys
            if keys_to_remove:
                for key in keys_to_remove:
                    if key in result:
                        del result[key]
                        logger.debug(f"Removed key '{key}' for request_id {request_id}")
                logger.info(f"Removed keys: {keys_to_remove} for request_id {request_id}")

            logger.info(f"Metadata altered successfully for request_id {request_id}. Result keys: {list(result.keys())}")

            return {
                "status": "success",
                "result": result
            }

        except Exception as e:
            error_msg = f"Error altering metadata for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg
            }
