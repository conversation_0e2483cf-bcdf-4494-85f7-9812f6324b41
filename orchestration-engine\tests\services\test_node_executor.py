import pytest
import asyncio
import json
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from aiokafka.errors import KafkaError
from app.services.node_executor import NodeExecutor, NodeExecutionError


class TestNodeExecutor:
    """
    Test suite for NodeExecutor class.
    Tests Kafka message processing, node execution, and error handling.
    """

    @pytest.fixture
    def mock_producer(self):
        """Create a mock Kafka producer."""
        producer = Mock(spec=AIOKafkaProducer)
        producer._sender = Mock()
        producer._sender._running = True
        producer.send = AsyncMock()
        return producer

    @pytest.fixture
    def mock_consumer(self):
        """Create a mock Kafka consumer."""
        consumer = Mock(spec=AIOKafkaConsumer)
        consumer.start = AsyncMock()
        consumer.stop = AsyncMock()
        return consumer

    @pytest.fixture
    def node_executor(self, mock_producer):
        """Create a NodeExecutor instance with mocked dependencies."""
        with patch("app.services.node_executor.AIOKafkaConsumer", return_value=Mock()):
            executor = NodeExecutor(producer=mock_producer)
            executor._consumer = Mock()
            executor._consumer.start = AsyncMock()
            executor._consumer.stop = AsyncMock()
            executor._consumer_task = Mock()
            executor._consumer_task.done = Mock(return_value=False)
            return executor

    @pytest.mark.asyncio
    async def test_init(self, mock_producer):
        """Test initialization of NodeExecutor."""
        with patch("app.services.node_executor.AIOKafkaConsumer", return_value=Mock()):
            executor = NodeExecutor(producer=mock_producer)
            assert executor.producer == mock_producer
            assert executor._consumer is None
            assert executor._consumer_task is None
            assert isinstance(executor._pending_requests, dict)

    @pytest.mark.asyncio
    async def test_start_stop(self, node_executor):
        """Test starting and stopping the executor."""
        # Mock the internal methods
        node_executor._start_internal_consumer = AsyncMock()
        node_executor._stop_internal_consumer = AsyncMock()

        # Test start
        await node_executor.start()
        node_executor._start_internal_consumer.assert_called_once()

        # Test stop
        await node_executor.stop()
        node_executor._stop_internal_consumer.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_tool_success(self, node_executor):
        """Test successful execution of a tool."""
        # Setup
        server_script_path = "http://example.com/api"
        tool_name = "test_tool"
        tool_parameters = {"param1": "value1"}
        expected_result = {"status": "success", "data": "test_result"}

        # Create a future that's already done with a result
        future = asyncio.Future()
        future.set_result(expected_result)
        
        # Mock the pending_requests dict to return our future
        node_executor._pending_requests = {}
        
        # Use side_effect to store the future in _pending_requests when send is called
        async def mock_send(*args, **kwargs):
            # Extract request_id from the payload
            payload = args[1] if len(args) > 1 else kwargs.get("value")
            request_id = payload.get("request_id")
            # Store the future
            node_executor._pending_requests[request_id] = future
            
        node_executor.producer.send.side_effect = mock_send

        # Execute
        result = await node_executor.execute_tool(server_script_path, tool_name, tool_parameters)

        # Assert
        assert result == expected_result
        node_executor.producer.send.assert_called_once()
        # Check that the first argument to send was the request topic
        assert node_executor.producer.send.call_args[0][0] == node_executor._request_topic

    @pytest.mark.asyncio
    async def test_execute_tool_kafka_error(self, node_executor):
        """Test handling of Kafka errors during tool execution."""
        # Setup
        server_script_path = "http://example.com/api"
        tool_name = "test_tool"
        tool_parameters = {"param1": "value1"}
        
        # Make the producer.send method raise a KafkaError
        node_executor.producer.send.side_effect = KafkaError("Kafka connection error")

        # Execute and assert
        with pytest.raises(NodeExecutionError):
            await node_executor.execute_tool(server_script_path, tool_name, tool_parameters)

    @pytest.mark.asyncio
    async def test_execute_tool_consumer_not_running(self, node_executor):
        """Test error when consumer is not running."""
        # Setup
        server_script_path = "http://example.com/api"
        tool_name = "test_tool"
        tool_parameters = {"param1": "value1"}
        
        # Make the consumer task appear to be done
        node_executor._consumer_task.done.return_value = True

        # Execute and assert
        with pytest.raises(RuntimeError):
            await node_executor.execute_tool(server_script_path, tool_name, tool_parameters)

    @pytest.mark.asyncio
    async def test_execute_tool_producer_not_running(self, node_executor):
        """Test error when producer is not running."""
        # Setup
        server_script_path = "http://example.com/api"
        tool_name = "test_tool"
        tool_parameters = {"param1": "value1"}
        
        # Make the producer appear to be not running
        node_executor.producer._sender._running = False

        # Execute and assert
        with pytest.raises(RuntimeError):
            await node_executor.execute_tool(server_script_path, tool_name, tool_parameters)
