"""
Service for managing knowledge sources for AutoGen agents.
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

from ..knowledge.knowledge_manager import KnowledgeManager
from ..schemas.agent_config import AgentConfig
from ..autogen_service.model_factory import ModelFactory
from autogen_agentchat.agents import AssistantAgent

logger = logging.getLogger(__name__)


class KnowledgeService:
    """Service for managing knowledge sources for AutoGen agents."""

    def __init__(self):
        self.knowledge_managers = {}  # Map of agent_id -> KnowledgeManager

    async def create_knowledge_manager(
        self, agent_id: str, collection_name: Optional[str] = None
    ) -> KnowledgeManager:
        """
        Create a knowledge manager for an agent.

        Args:
            agent_id: ID of the agent
            collection_name: Optional name for the collection

        Returns:
            The created KnowledgeManager
        """
        if agent_id in self.knowledge_managers:
            return self.knowledge_managers[agent_id]

        # Use agent_id as collection name if not provided
        if collection_name is None:
            collection_name = f"knowledge_{agent_id}"

        # Create knowledge manager
        knowledge_manager = KnowledgeManager(collection_name=collection_name)
        self.knowledge_managers[agent_id] = knowledge_manager

        return knowledge_manager

    async def add_knowledge_to_agent(
        self, agent_id: str, agent: AssistantAgent, knowledge_sources: Dict[str, Any]
    ) -> AssistantAgent:
        """
        Add knowledge to an agent from various sources.

        Args:
            agent_id: ID of the agent
            agent: The AssistantAgent instance
            knowledge_sources: Dictionary with keys:
                - 'texts': List of text strings
                - 'urls': List of website URLs
                - 'documents': List of document paths

        Returns:
            The enhanced agent
        """
        # Get or create knowledge manager
        if agent_id in self.knowledge_managers:
            knowledge_manager = self.knowledge_managers[agent_id]
        else:
            knowledge_manager = await self.create_knowledge_manager(agent_id)

        # Add texts
        if "texts" in knowledge_sources and knowledge_sources["texts"]:
            for i, text in enumerate(knowledge_sources["texts"]):
                try:
                    await knowledge_manager.add_text(text, f"text_input_{i}")
                except Exception as e:
                    logger.error(f"Error adding text {i}: {str(e)}")

        # Add URLs
        if "urls" in knowledge_sources and knowledge_sources["urls"]:
            for url in knowledge_sources["urls"]:
                try:
                    await knowledge_manager.add_website(url)
                except Exception as e:
                    logger.error(f"Error adding URL {url}: {str(e)}")

        # Add documents
        if "documents" in knowledge_sources and knowledge_sources["documents"]:
            for doc_path in knowledge_sources["documents"]:
                try:
                    await knowledge_manager.add_document(doc_path)
                except Exception as e:
                    logger.error(f"Error adding document {doc_path}: {str(e)}")

        # Enhance agent with knowledge
        return await knowledge_manager.enhance_agent_with_knowledge(agent)

    async def create_knowledgeable_agent(
        self, config: AgentConfig, knowledge_sources: Dict[str, Any]
    ) -> AssistantAgent:
        """
        Create a new agent with knowledge from various sources.

        Args:
            config: Agent configuration
            knowledge_sources: Dictionary with knowledge sources

        Returns:
            The created agent with knowledge
        """
        # Create model client from config
        model_config = {
            "provider": "OpenAIChatCompletionClient",
            "model": config.ai_model_config["model"],
            "api_key": config.ai_model_config.get("api_key"),
        }

        # Add optional parameters if present
        for param in ["temperature", "max_tokens"]:
            if param in config.ai_model_config:
                model_config[param] = config.ai_model_config[param]

        model_client = ModelFactory.create_model_client(model_config)

        if not model_client:
            raise ValueError("Failed to create model client")

        # Create agent without knowledge first
        agent = AssistantAgent(
            name=config.name,
            model_client=model_client,
            system_message=config.system_message,
            tools=[],  # Tools can be added separately
        )

        # Add knowledge to the agent
        return await self.add_knowledge_to_agent(
            agent_id=config.name, agent=agent, knowledge_sources=knowledge_sources
        )

    async def clear_knowledge(self, agent_id: str) -> bool:
        """
        Clear all knowledge for an agent.

        Args:
            agent_id: ID of the agent

        Returns:
            True if successful, False otherwise
        """
        if agent_id not in self.knowledge_managers:
            return False

        try:
            await self.knowledge_managers[agent_id].clear_knowledge()
            return True
        except Exception as e:
            logger.error(f"Error clearing knowledge for agent {agent_id}: {str(e)}")
            return False

    async def close_all(self) -> None:
        """Close all knowledge managers."""
        for agent_id, manager in self.knowledge_managers.items():
            try:
                await manager.close()
            except Exception as e:
                logger.error(
                    f"Error closing knowledge manager for agent {agent_id}: {str(e)}"
                )

        self.knowledge_managers = {}
