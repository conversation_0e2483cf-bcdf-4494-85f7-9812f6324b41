import json
import re

from app.utils.enhanced_logger import get_logger

logger = get_logger("HelperFunctions")


def load_schema(file_path):
    """
    Loads the JSON schema from the specified file path.
    """
    with open(file_path, "r") as file:
        return json.load(file)


def fix_invalid_escapes(json_str):
    return re.sub(r'\\(?!["\\/bfnrt])', r"\\\\", json_str)


def safe_json_loads(s: str):
    try:
        # First, attempt to parse normally
        return json.loads(s)
    except json.JSONDecodeError as e:
        if "Invalid \\escape" in str(e):
            # Escape all invalid backslashes that are not part of valid escape sequences
            # This pattern replaces \ with \\ only if it's not followed by a valid escape character
            s = re.sub(r'\\(?!["\\/bfnrtu])', r"\\\\", s)
            try:
                return json.loads(s)
            except json.JSONDecodeError as e2:
                raise ValueError(f"JSON decoding failed even after sanitizing: {e2}")
        else:
            raise


def format_execution_result(output_schema, execution_result):
    """
    Formats the execution result based on the simplified output schema.
    Includes detailed debugging prints to track data types.
    """
    formatted_result = []
    # logger.debug("--- START format_execution_result ---")
    # logger.debug("output_schema:", output_schema)
    # logger.debug(f"execution_result (at function start):{execution_result}")
    # logger.debug(
    #     f"Type of execution_result (at function start):{type(execution_result)}"
    # )
    if not execution_result:
        logger.debug("execution_result is empty, returning empty list")
        return formatted_result

    field_name_to_description = {}
    if "predefined_fields" in output_schema:
        # logger.debug("predefined_fields found in output_schema")
        for field_def in output_schema["predefined_fields"]:
            if "field_name" in field_def:
                field_name_to_description[field_def["field_name"]] = field_def
        # logger.debug(f"field_name_to_description:, {field_name_to_description}")
    else:
        logger.warning("WARNING: predefined_fields NOT found in output_schema!")

    def handle_data_type(data_value, data_type):
        if data_type == "string":
            return str(data_value)
        elif data_type == "array":
            return data_value if isinstance(data_value, list) else [data_value]
        elif data_type == "object":
            return data_value if isinstance(data_value, dict) else {"value": data_value}
        elif data_type == "number":
            return float(data_value)
        else:
            return data_value

    def process_item(property_name, data_value, field_def):
        data_type = "text"
        if "data_type" in field_def and "type" in field_def["data_type"]:
            data_type = field_def["data_type"]["type"]
            logger.debug(f"Data type set to '{data_type}' based on schema")
        else:
            logger.warning(
                f"WARNING: Data type for '{property_name}' not found in schema description"
            )

        if data_type == "array" and isinstance(data_value, list):
            nested_items = []
            item_type = field_def["data_type"].get("items", {}).get("type", "string")
            for item in data_value:
                if item_type == "object" and isinstance(item, dict):
                    nested_properties = field_def["data_type"]["items"].get(
                        "properties", {}
                    )
                    nested_field_defs = [
                        {"field_name": k, "data_type": v}
                        for k, v in nested_properties.items()
                    ]
                    nested_items.append(
                        {
                            "data": [
                                process_item(
                                    nested_property["field_name"],
                                    item[nested_property["field_name"]],
                                    nested_property,
                                )
                                for nested_property in nested_field_defs
                                if nested_property["field_name"] in item
                            ],
                            "data_type": item_type,
                            "property_name": property_name,
                        }
                    )
                else:
                    nested_items.append(
                        {
                            "data": handle_data_type(item, item_type),
                            "data_type": item_type,
                            "property_name": property_name,
                        }
                    )
            formatted_item = {
                "data": nested_items,
                "data_type": data_type,
                "property_name": property_name,
            }
        elif data_type == "object" and isinstance(data_value, dict):
            nested_items = []
            properties = field_def["data_type"].get("properties", {})
            for nested_property_name, nested_data_value in data_value.items():
                if nested_property_name in properties:
                    nested_field_def = {
                        "field_name": nested_property_name,
                        "data_type": properties[nested_property_name],
                    }
                    nested_items.append(
                        process_item(
                            nested_property_name, nested_data_value, nested_field_def
                        )
                    )
                else:
                    nested_items.append(
                        {
                            "data": nested_data_value,
                            "data_type": "unknown",
                            "property_name": nested_property_name,
                        }
                    )
            formatted_item = {
                "data": nested_items,
                "data_type": data_type,
                "property_name": property_name,
            }
        else:
            formatted_item = {
                "data": handle_data_type(data_value, data_type),
                "data_type": data_type,
                "property_name": property_name,
            }
        # logger.debug(f"Formatted item: {formatted_item}")
        return formatted_item

    # logger.debug("Starting to process execution_result items...")
    for item in execution_result:
        if isinstance(item, dict):
            for property_name, data_value in item.items():
                logger.debug(f"Property: {property_name} Value: {data_value}")
                if property_name in field_name_to_description:
                    logger.debug(
                        f"Property '{property_name}' found in schema description"
                    )
                    field_def = field_name_to_description[property_name]
                    formatted_result.append(
                        process_item(property_name, data_value, field_def)
                    )
                else:
                    logger.debug(
                        f"WARNING: Property '{property_name}' NOT found in schema description"
                    )
                    formatted_result.append(
                        {
                            "data": data_value,
                            "data_type": "unknown",
                            "property_name": property_name,
                        }
                    )

    # logger.debug("--- END format_execution_result ---")
    # logger.debug(f"Final formatted_result: {formatted_result}")
    return formatted_result
