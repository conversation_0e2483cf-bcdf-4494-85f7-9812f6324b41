import requests  # Make sure requests is installed: pip install requests
import json
import asyncio
from typing import Dict, Any, List, ClassVar, Optional

from app.components.hitl.base_hitl_component import (
    BaseHITLComponent,
)
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    MultilineInput,
    BoolInput,
)
from app.models.workflow_builder.components import Output


# Helper function to convert n8n flag names to Discord integer values
# Reference: https://discord.com/developers/docs/resources/channel#message-object-message-flags
def convert_flags_to_int(flag_names: Optional[List[str]]) -> Optional[int]:
    if not flag_names:
        return None

    flag_map = {
        "SUPPRESS_EMBEDS": 1 << 2,  # 4
        "SUPPRESS_NOTIFICATIONS": 1 << 12,  # 4096
        # Add other flags here if needed
    }

    total_flags = 0
    for flag_name in flag_names:
        total_flags |= flag_map.get(flag_name, 0)

    return total_flags if total_flags > 0 else None


class DiscordComponent(BaseHITLComponent):
    """
    Sends Discord messages via Bot or Webhook, incorporating advanced options.

    This component allows workflows to send messages to Discord channels,
    optionally waiting for a reply. It supports bot-based messaging
    (including TTS, replies, flags) and basic webhook-based messaging.
    """

    name: ClassVar[str] = "DiscordComponent"
    display_name: ClassVar[str] = "Send Discord Message"
    description: ClassVar[str] = (
        "Sends a message to a Discord channel via Bot or Webhook with options like TTS, replies, and flags, and optionally waits for a reply."
    )

    icon: ClassVar[str] = "MessageCircle"

    inputs: ClassVar[List[InputBase]] = [
        # Channel ID
        HandleInput(
            name="channel_id_handle",
            display_name="Channel ID",
            is_handle=True,
            input_types=["string"],
            info="Connect the Discord channel ID.",
        ),
        StringInput(
            name="channel_id",
            display_name="Channel ID (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="Direct Discord channel ID.",
        ),
        # Message Content
        HandleInput(
            name="message_handle",
            display_name="Message",
            is_handle=True,
            input_types=["string"],
            info="Connect the message content.",
        ),
        MultilineInput(
            name="message",
            display_name="Message (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="Direct message content.",
        ),
        # --- Options based on n8n reference ---
        BoolInput(
            name="tts",
            display_name="Text-to-Speech (TTS)",
            required=False,
            is_handle=False,
            value=False,
            info="Send as TTS message (Bot only).",
        ),
        StringInput(
            name="reply_to_message_id",
            display_name="Reply To Message ID",
            required=False,
            is_handle=False,
            value="",
            info="Message ID to reply to (Bot only).",
        ),
        # Simple way to input flags for now, could be enhanced (e.g., multi-select if framework supports)
        StringInput(
            name="flags",
            display_name="Flags (Comma-separated)",
            required=False,
            is_handle=False,
            value="",
            placeholder="e.g. SUPPRESS_EMBEDS,SUPPRESS_NOTIFICATIONS",
            info="Message flags (Bot only): SUPPRESS_EMBEDS, SUPPRESS_NOTIFICATIONS.",
        ),
        # --- End n8n options ---
        BoolInput(
            name="use_webhook",
            display_name="Use Webhook",
            required=False,
            is_handle=False,
            value=False,
            info="Use webhook instead of bot (fewer options).",
        ),
        BoolInput(
            name="wait_for_reply",
            display_name="Wait for Reply",
            required=False,
            is_handle=False,
            value=False,
            info="Wait for a reply (Bot only, placeholder).",
        ),
        # Inherit timeout_seconds from base class if needed
    ]

    outputs: ClassVar[List[Output]] = [
        Output(
            name="message_id",
            display_name="Message ID",
            output_type="string",
            info="The unique ID of the sent message (Bot only).",
        ),
        Output(
            name="sent_status",
            display_name="Sent Status",
            output_type="bool",
            info="True if the message was sent successfully.",
        ),
        # Inherit response_data, timed_out, error, action_taken from base class
        # Re-declare for clarity if preferred:
        Output(
            name="response_data",
            display_name="Reply Data",
            output_type="string",
            info="Reply content if waited.",
        ),
        Output(
            name="timed_out",
            display_name="Timed Out",
            output_type="bool",
            info="True if waiting for reply timed out.",
        ),
        Output(
            name="error",
            display_name="Error",
            output_type="string",
            info="Error message if failed.",
        ),
        Output(
            name="action_taken",
            display_name="Action Taken",
            output_type="string",
            info="Action status (e.g., SENT, FAILED).",
        ),
    ]

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Sends a Discord message using Bot or Webhook, handling options.
        """
        self.log("info", "Executing DiscordComponent...")

        # Initialize output variables
        message_id = None
        sent_status = False
        response_data = None
        timed_out = False
        error_msg = None
        action_taken = "PENDING"

        # --- Get Inputs ---
        channel_id = kwargs.get("channel_id_handle") or kwargs.get("channel_id")
        message_text = kwargs.get("message_handle") or kwargs.get("message")
        use_webhook = kwargs.get("use_webhook", False)
        wait_for_reply = kwargs.get("wait_for_reply", False)
        timeout_seconds = kwargs.get("timeout_seconds", 3600)  # Default timeout

        # Get additional options
        tts = kwargs.get("tts", False)
        reply_to_message_id = kwargs.get("reply_to_message_id")
        flags_str = kwargs.get("flags", "")

        # --- Input Validation ---
        if not channel_id:
            error_msg = "Discord channel ID is missing."
            self.log("error", error_msg)
            return self._format_output(message_id, False, None, False, error_msg, "FAILED")

        if not message_text:
            error_msg = "Message content is missing."
            self.log("error", error_msg)
            return self._format_output(message_id, False, None, False, error_msg, "FAILED")

        # --- Prepare API Call ---
        base_api_url = "https://discord.com/api/v10"

        try:
            if use_webhook:
                # --- Webhook Sending ---
                self.log(
                    "info",
                    f"Attempting to send message via Webhook to channel implicitly defined by webhook URL.",
                )
                # Placeholder: Get webhook URL from config/environment
                # webhook_url = self.get_config("DISCORD_WEBHOOK_URL")
                webhook_url = "YOUR_DISCORD_WEBHOOK_URL"  # Replace with actual retrieval

                if not webhook_url or webhook_url == "YOUR_DISCORD_WEBHOOK_URL":
                    raise ValueError("Webhook URL is not configured.")

                payload = {"content": message_text}
                # Note: Webhooks support embeds, username/avatar overrides, but not TTS, flags, or standard replies easily.

                response = requests.post(webhook_url, json=payload)

                # Check response status (204 No Content for success)
                if response.status_code == 204:
                    sent_status = True
                    action_taken = "SENT_WEBHOOK"
                    # message_id is not typically returned by webhooks easily
                    self.log("info", "Message sent successfully via Webhook.")
                else:
                    error_msg = f"Webhook failed ({response.status_code}): {response.text}"
                    self.log("error", error_msg)
                    action_taken = "FAILED"

                if wait_for_reply:
                    self.log("warning", "Wait for reply is not supported with Webhooks.")

            else:
                # --- Bot Sending ---
                self.log("info", f"Attempting to send message via Bot to channel {channel_id}.")
                # Placeholder: Get bot token from config/environment
                # bot_token = self.get_config("DISCORD_BOT_TOKEN")
                bot_token = "YOUR_DISCORD_BOT_TOKEN"  # Replace with actual retrieval

                if not bot_token or bot_token == "YOUR_DISCORD_BOT_TOKEN":
                    raise ValueError("Discord Bot Token is not configured.")

                headers = {"Authorization": f"Bot {bot_token}", "Content-Type": "application/json"}

                # Construct payload with options
                payload: Dict[str, Any] = {"content": message_text}
                if tts:
                    payload["tts"] = True
                if reply_to_message_id:
                    payload["message_reference"] = {"message_id": reply_to_message_id}

                # Process flags
                if flags_str:
                    flag_names = [f.strip() for f in flags_str.split(",") if f.strip()]
                    discord_flags = convert_flags_to_int(flag_names)
                    if discord_flags is not None:
                        payload["flags"] = discord_flags

                # Add embeds and files here if needed later, similar to payload construction
                # if embeds: payload["embeds"] = formatted_embeds
                # if files: handle multipart/form-data request instead of json

                api_endpoint = f"{base_api_url}/channels/{channel_id}/messages"
                response = requests.post(api_endpoint, headers=headers, json=payload)

                # Check response status (200 OK for success)
                if response.status_code == 200:
                    response_json = response.json()
                    message_id = response_json.get("id")
                    sent_status = True
                    action_taken = "SENT_BOT"
                    self.log("info", f"Message sent successfully via Bot. Message ID: {message_id}")

                    # Placeholder: Wait for reply logic
                    if wait_for_reply:
                        self.log(
                            "info",
                            f"Waiting for reply (timeout: {timeout_seconds}s) - Placeholder logic.",
                        )
                        # Simulate timeout for now
                        # In a real scenario, you'd need a mechanism to listen for messages
                        # on the Discord Gateway or poll the channel, matching user/channel/time.
                        # This is complex and often requires a running bot instance.
                        await asyncio.sleep(timeout_seconds)  # Simulate waiting
                        timed_out = True
                        action_taken = "TIMED_OUT"
                        self.log("warning", "Wait for reply timed out (placeholder).")

                else:
                    # Try to parse Discord's error format
                    try:
                        error_data = response.json()
                        error_msg = f"Bot failed ({response.status_code}): {error_data.get('message', response.text)} Code: {error_data.get('code', 'N/A')}"
                    except json.JSONDecodeError:
                        error_msg = f"Bot failed ({response.status_code}): {response.text}"
                    self.log("error", error_msg)
                    action_taken = "FAILED"

        except requests.exceptions.RequestException as e:
            error_msg = f"Network error sending Discord message: {str(e)}"
            self.log("error", error_msg)
            action_taken = "FAILED"
        except ValueError as e:  # Catch config errors
            error_msg = str(e)
            self.log("error", error_msg)
            action_taken = "FAILED_SETUP"
        except Exception as e:
            error_msg = f"An unexpected error occurred: {str(e)}"
            self.log("exception", error_msg)  # Use exception level log for tracebacks
            action_taken = "FAILED"

        # --- Return Results ---
        return self._format_output(
            message_id, sent_status, response_data, timed_out, error_msg, action_taken
        )

    def _format_output(
        self, message_id, sent_status, response_data, timed_out, error, action_taken
    ):
        """Helper to format the output dictionary consistently."""
        return {
            "message_id": message_id,
            "sent_status": sent_status,
            "response_data": response_data,
            "timed_out": timed_out,
            "error": error,
            "action_taken": action_taken,
        }
