"use client";

import React, { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { authApi } from "@/lib/authApi";
import { Button } from "@/components/ui/button";
import { Workflow, CheckCircle, XCircle, Loader2 } from "lucide-react";
import Link from "next/link";

export default function VerifyEmailPage() {
  const params = useParams();
  const router = useRouter();
  const [status, setStatus] = useState<"loading" | "success" | "error">("loading");
  const [message, setMessage] = useState("Verifying your email...");

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        const token = params.token as string;
        await authApi.verifyEmailOtp(token);
        setStatus("success");
        setMessage("Your email has been verified successfully!");
      } catch (error) {
        console.error("Email verification error:", error);
        setStatus("error");
        setMessage("Failed to verify your email. The link may be invalid or expired.");
      }
    };

    verifyEmail();
  }, [params.token]);

  return (
    <div className="bg-background flex min-h-screen flex-col items-center justify-center p-4">
      <div className="bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg">
        <div className="flex flex-col items-center space-y-2">
          <div className="rounded-md bg-blue-600 p-2 shadow-md">
            <Workflow className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-center text-2xl font-bold">Email Verification</h1>
        </div>

        <div className="flex flex-col items-center justify-center space-y-4 py-8">
          {status === "loading" && <Loader2 className="text-primary h-16 w-16 animate-spin" />}
          {status === "success" && <CheckCircle className="h-16 w-16 text-green-500" />}
          {status === "error" && <XCircle className="h-16 w-16 text-red-500" />}
          <p className="text-muted-foreground text-center">{message}</p>
        </div>

        <div className="flex justify-center">
          <Button asChild>
            <Link href="/login">Go to Login</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
