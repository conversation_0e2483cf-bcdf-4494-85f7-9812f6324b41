import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { CustomScrollArea } from "@/components/ui/custom-scroll-area";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Download,
  Play,
  AlertCircle,
  CheckCircle,
  Terminal,
  FileText,
  Square,
} from "lucide-react";
import { LogDisplay } from "./LogDisplay";
import { ApprovalRequest } from "./ApprovalRequest";
import { useExecutionStore } from "@/store/executionStore";
import {
  executeWorkflowWithUserInputs,
  WorkflowExecuteWithUserInputsPayload
} from "@/lib/api";
import { SSEClient } from "@/lib/sseClient";
import { shouldIncludeField, logFieldStatus } from "@/lib/field-utils";
import { clearApprovalEvent, clearAllApprovalEvents } from "@/lib/approvalUtils";

interface ExecutionDialogProps {
  onClose: () => void;
  onStopExecution?: () => void;
}

export function ExecutionDialog({ onClose, onStopExecution }: ExecutionDialogProps) {
  // Get state from Zustand store
  const {
    isDialogOpen,
    missingFields,
    // setMissingFields is no longer used since we're not adding fields from StartNode parameters
    activeTab,
    setActiveTab,
    fieldValues,
    setFieldValues,
    updateFieldValue,
    errors,
    setErrors,
    isFormValid,
    setIsFormValid,
    logs,
    addLog,
    isExecuting,
    setIsExecuting,
    isStreaming,
    setIsStreaming,
    correlationId,
    setCorrelationId,
  } = useExecutionStore();

  // Ref for auto-scrolling logs
  const logsEndRef = useRef<HTMLDivElement>(null);

  // Ref for SSE client
  const sseClientRef = useRef<SSEClient | null>(null);

  // Validate form - memoized to avoid dependency issues
  const validateForm = useCallback(
    (values: Record<string, any>, errors: Record<string, string>) => {
      const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
      console.log(`[${timestamp}] [validateForm] ========== VALIDATING FORM ==========`);
      console.log(`[${timestamp}] [validateForm] Total fields to validate: ${missingFields.length}`);
      console.log(`[${timestamp}] [validateForm] Total values provided: ${Object.keys(values).length}`);
      console.log(`[${timestamp}] [validateForm] Total errors: ${Object.keys(errors).length}`);

      // Check if all required fields have values and no errors
      let allFieldsValid = true;
      let requiredFieldsCount = 0;
      let validFieldsCount = 0;

      // First pass: count required fields and check if all fields are valid
      missingFields.forEach((field) => {
        const fieldId = `${field.nodeId}_${field.name}`;
        // For boolean fields, consider them as having a value even if false
        const hasValue = field.inputType === "boolean" ? values[fieldId] !== undefined : !!values[fieldId];
        const hasError = !!errors[fieldId];
        // Consider fields required unless explicitly marked as optional
        const isRequired = field.required !== false;

        console.log(`[${timestamp}] [validateForm] Checking field: ${fieldId}`);
        console.log(`[${timestamp}] [validateForm] - Field type: ${field.inputType}`);
        console.log(`[${timestamp}] [validateForm] - Is required: ${isRequired ? "YES" : "NO"} (required !== false: ${field.required !== false})`);
        console.log(`[${timestamp}] [validateForm] - Has value: ${hasValue ? "YES" : "NO"}`);
        console.log(`[${timestamp}] [validateForm] - Current value: ${JSON.stringify(values[fieldId])}`);
        console.log(`[${timestamp}] [validateForm] - Has error: ${hasError ? "YES" : "NO"}`);
        if (hasError) {
          console.log(`[${timestamp}] [validateForm] - Error message: ${errors[fieldId]}`);
        }

        if (isRequired) {
          requiredFieldsCount++;
        }

        // Field is valid if:
        // 1. It's not required, OR
        // 2. It has a value AND no errors
        const isFieldValid = !isRequired || (hasValue && !hasError);
        console.log(`[${timestamp}] [validateForm] - Field is valid: ${isFieldValid ? "YES" : "NO"}`);

        if (isFieldValid) {
          validFieldsCount++;
        } else {
          allFieldsValid = false;
        }
      });

      console.log(`[${timestamp}] [validateForm] Required fields: ${requiredFieldsCount}, Valid fields: ${validFieldsCount}`);
      console.log(`[${timestamp}] [validateForm] Form validation result: ${allFieldsValid ? "VALID" : "INVALID"}`);

      // Force immediate update of isFormValid state
      setIsFormValid(allFieldsValid);

      // Return the validation result so it can be used immediately
      return allFieldsValid;
    },
    [missingFields, setIsFormValid],
  );

  // Ref to track if the dialog has been initialized
  const hasInitializedRef = useRef(false);

  // Initialize form values when dialog opens - runs only once when dialog opens
  useEffect(() => {
    // Only run this effect when the dialog is first opened
    // This prevents infinite loops by not re-running when fieldValues or errors change
    if (!isDialogOpen) {
      // Reset the initialization flag when dialog closes
      hasInitializedRef.current = false;
      return;
    }

    // If we've already initialized for this dialog session, skip
    if (hasInitializedRef.current) return;

    // Mark that we've initialized for this dialog session
    hasInitializedRef.current = true;

    const initialValues: Record<string, any> = {};
    const initialErrors: Record<string, string> = {};

    // Initialize with fresh values - don't use previously entered values
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    console.log(`[${timestamp}] [ExecutionDialog] ========== INITIALIZING EXECUTION DIALOG WITH FRESH VALUES ==========`);
    console.log(`[${timestamp}] [ExecutionDialog] Missing fields count: ${missingFields.length}`);
    // Note: We're intentionally not using window.startNodeCollectedParameters to ensure fresh input values

    // Log details about missing fields
    if (missingFields.length > 0) {
      console.log(`[${timestamp}] [ExecutionDialog] Missing fields details:`);
      missingFields.forEach((field, index) => {
        console.log(`[${timestamp}] [ExecutionDialog]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`);
        console.log("The field", field);
        // Ensure field has proper display_name and node_name
        if (!field.displayName) {
          console.log(`[${timestamp}] [ExecutionDialog] WARNING: Field ${index + 1} is missing display_name, using name instead`);
          field.displayName = field.name || "Unnamed Field";
        }

        if (!field.nodeId) {
          console.log(`[${timestamp}] [ExecutionDialog] WARNING: Field ${index + 1} is missing node_name, using "Unknown Node" instead`);
          field.nodeName = "Unknown Node";
        }
      });
    }

    // Get the current nodes from the window object
    // This is used to check if a node still exists in the workflow
    const currentNodeIds = new Set(window.currentWorkflowNodes?.map((node: any) => node.id) || []);
    console.log(`[${timestamp}] [ExecutionDialog] Current node IDs in workflow:`, Array.from(currentNodeIds));

    // For prebuilt workflows, the currentNodeIds might be empty even though the nodes exist
    // In this case, we should trust the missingFields array which was populated by the validation
    const isPrebuiltWorkflow = currentNodeIds.size === 0 && missingFields.length > 0;
    if (isPrebuiltWorkflow) {
      console.log(`[${timestamp}] [ExecutionDialog] Detected prebuilt workflow - currentNodeIds is empty but missingFields has ${missingFields.length} fields`);
      console.log(`[${timestamp}] [ExecutionDialog] Will not filter out fields based on node existence for prebuilt workflow`);
    }

    // Create a list of all fields that should be displayed
    // This includes both missing fields and fields with values in the StartNode
    // Filter out fields from nodes that no longer exist in the workflow, but only if not a prebuilt workflow
    // Also filter out fields that are not connected to the Start node
    // And filter out fields that already have values configured in the inspector panel
    const allFields = [...missingFields].filter(field => {
      // For prebuilt workflows, skip the node existence check
      if (isPrebuiltWorkflow) {
        // For loaded workflows, we need to handle the case where required might not be explicitly set
        // Consider fields required unless explicitly marked as optional (required: false)
        const isRequired = field.required !== false;
        // Also include fields directly connected to the Start node
        const isDirectlyConnected = field.directly_connected_to_start === true;

        // Check if the field already has a value configured in the inspector panel
        // First, find the node in the current workflow
        const node = window.currentWorkflowNodes?.find((node: any) => node.id === field.nodeId);

        // Check if the node has a config with a value for this field
        const hasConfiguredValue = node?.data?.config && node.data.config[field.name] !== undefined;

        // Check if this field has an incoming connection from another node
        const hasIncomingConnection = (field.is_handle === true) && (field.is_connected === true);

        // Use the shared utility function to log field status
        logFieldStatus(
          "ExecutionDialog",
          `${field.nodeId}_${field.name}`,
          field.nodeName,
          field.name,
          isRequired,
          field.required,
          isDirectlyConnected,
          hasConfiguredValue,
          hasConfiguredValue ? node.data.config[field.name] : undefined,
          hasIncomingConnection
        );

        // Use the shared utility function to determine if the field should be included
        return shouldIncludeField(isRequired, isDirectlyConnected, hasConfiguredValue, hasIncomingConnection);
      }

      // For regular workflows, first check if the node still exists
      const nodeExists = currentNodeIds.has(field.nodeId);
      if (!nodeExists) {
        console.log(`[${timestamp}] [ExecutionDialog] Filtering out field from deleted node: ${field.nodeName} (${field.nodeId})`);
        return false;
      }

      // Check if the node is connected to the Start node
      if (!field.connected_to_start) {
        console.log(`[${timestamp}] [ExecutionDialog] Filtering out field from node not connected to Start node: ${field.nodeName} (${field.nodeId})`);
        return false;
      }

      // For loaded workflows, we need to handle the case where required might not be explicitly set
      // Consider fields required unless explicitly marked as optional (required: false)
      const isRequired = field.required !== false;
      // Also include fields directly connected to the Start node
      const isDirectlyConnected = field.directly_connected_to_start === true;

      // Check if the field already has a value configured in the inspector panel
      // Find the node in the current workflow
      const node = window.currentWorkflowNodes?.find((node: any) => node.id === field.nodeId);

      // Check if the node has a config with a value for this field
      const hasConfiguredValue = node?.data?.config && node.data.config[field.name] !== undefined;

      // Check if this field has an incoming connection from another node
      const hasIncomingConnection = (field.is_handle === true) && (field.is_connected === true);

      // Use the shared utility function to log field status
      logFieldStatus(
        "ExecutionDialog",
        `${field.nodeId}_${field.name}`,
        field.nodeName,
        field.name,
        isRequired,
        field.required,
        isDirectlyConnected,
        hasConfiguredValue,
        hasConfiguredValue ? node.data.config[field.name] : undefined,
        hasIncomingConnection
      );

      // Use the shared utility function to determine if the field should be included
      return shouldIncludeField(isRequired, isDirectlyConnected, hasConfiguredValue, hasIncomingConnection);
    });

    // We're not using StartNode parameters anymore to ensure fresh input values
    // Instead, we'll just use the missing fields that were collected during validation

    // Log the fields we're going to use
    console.log(`[${timestamp}] [ExecutionDialog] Using ${allFields.length} fields from validation`);

    // No need to update missing fields since we're not adding any new fields from StartNode parameters

    // Get a snapshot of the current fieldValues to avoid dependency issues
    const currentFieldValues = { ...fieldValues };

    // Process all fields to set initial values
    console.log(`[${timestamp}] [ExecutionDialog] ========== PROCESSING FIELDS FOR INITIALIZATION ==========`);
    console.log(`[${timestamp}] [ExecutionDialog] Total fields to process: ${allFields.length}`);
    console.log(`[${timestamp}] [ExecutionDialog] Existing field values: ${Object.keys(currentFieldValues).length}`);

    allFields.forEach((field) => {
      const fieldId = `${field.nodeId}_${field.name}`;
      console.log(`[${timestamp}] [ExecutionDialog] Processing field: ${fieldId}`);
      console.log(`[${timestamp}] [ExecutionDialog] Field details - Node: ${field.nodeName}, Name: ${field.name}, Type: ${field.inputType}`);

      // Always use fresh values, even if we already have a value for this field in the current session
      if (currentFieldValues[fieldId] !== undefined) {
        console.log(`[${timestamp}] [ExecutionDialog] Not using existing value for ${fieldId} to ensure fresh input`);
      }

      // Always use default values instead of previously stored values
      console.log(`[${timestamp}] [ExecutionDialog] Using default values for field ${fieldId} to ensure fresh input`);

      // If no value in StartNode, use default values based on input type
      console.log(`[${timestamp}] [ExecutionDialog] Setting default value for field ${field.name} (${field.inputType})`);

      // Handle different input types with appropriate defaults
      switch (field.inputType) {
        case "string":
        case "text":
          initialValues[fieldId] = "";
          console.log(`[${timestamp}] [ExecutionDialog] Set default empty string for string field ${fieldId}`);
          break;

        case "number":
        case "int":
        case "float":
          initialValues[fieldId] = 0;
          console.log(`[${timestamp}] [ExecutionDialog] Set default 0 for numeric field ${fieldId}`);
          break;

        case "boolean":
        case "bool":
          initialValues[fieldId] = false;
          console.log(`[${timestamp}] [ExecutionDialog] Set default false for boolean field ${fieldId}`);
          break;

        case "dropdown":
          // For dropdowns, use the first option or empty string
          if (field.options && field.options.length > 0) {
            initialValues[fieldId] = field.options[0];
            console.log(`[${timestamp}] [ExecutionDialog] Set default first option "${field.options[0]}" for dropdown field ${fieldId}`);
          } else {
            initialValues[fieldId] = "";
            console.log(`[${timestamp}] [ExecutionDialog] Set default empty string for dropdown field ${fieldId} (no options available)`);
          }
          break;

        case "object":
        case "dict":
        case "json":
          // Try to extract properties from the field info
          try {
            // Special case for keywords field
            if (field.name === "keywords" || field.name === "tool_arg_keywords") {
              const keywordsObj = {
                time: "",
                objective: "",
                audience: "",
                gender: "",
                tone: "",
                speakers: "",
              };
              initialValues[fieldId] = JSON.stringify(keywordsObj, null, 2);
              console.log(`[${timestamp}] [ExecutionDialog] Set default keywords object for field ${fieldId}: ${JSON.stringify(keywordsObj)}`);
            } else {
              // For other object fields, use empty object
              initialValues[fieldId] = "{}";
              console.log(`[${timestamp}] [ExecutionDialog] Set default empty object for object field ${fieldId}`);
            }
          } catch (e) {
            console.error(`[${timestamp}] [ExecutionDialog] Error initializing object field ${field.name}:`, e);
            initialValues[fieldId] = "{}";
            console.log(`[${timestamp}] [ExecutionDialog] Set default empty object after error for field ${fieldId}`);
          }
          break;

        case "array":
        case "list":
          initialValues[fieldId] = "[]";
          console.log(`[${timestamp}] [ExecutionDialog] Set default empty array for array field ${fieldId}`);
          break;

        case "credential":
          initialValues[fieldId] = "";
          console.log(`[${timestamp}] [ExecutionDialog] Set default empty string for credential field ${fieldId}`);
          break;

        default:
          initialValues[fieldId] = "";
          console.log(`[${timestamp}] [ExecutionDialog] Set default empty string for unknown type field ${fieldId} (type: ${field.inputType})`);
          break;
      }

      console.log(`[${timestamp}] [ExecutionDialog] Final default value for ${fieldId}: ${JSON.stringify(initialValues[fieldId])}`);

      // Log whether this field will have an initial error
      if (field.inputType !== "boolean" && !initialValues[fieldId]) {
        console.log(`[${timestamp}] [ExecutionDialog] Field ${fieldId} will have initial error: This field is required`);
      }

      // Set initial error if field is empty
      if (field.inputType !== "boolean" && !initialValues[fieldId]) {
        initialErrors[fieldId] = "This field is required";
      }
    });

    // Only update field values if we have new values to set
    if (Object.keys(initialValues).length > 0) {
      console.log(`[${timestamp}] [ExecutionDialog] ========== UPDATING FORM STATE ==========`);
      console.log(`[${timestamp}] [ExecutionDialog] Setting ${Object.keys(initialValues).length} initial field values`);

      // Log each field being set
      Object.entries(initialValues).forEach(([fieldId, value]) => {
        console.log(`[${timestamp}] [ExecutionDialog] Setting field ${fieldId} = ${JSON.stringify(value)}`);
      });

      // Log initial errors
      if (Object.keys(initialErrors).length > 0) {
        console.log(`[${timestamp}] [ExecutionDialog] Setting ${Object.keys(initialErrors).length} initial field errors`);
        Object.entries(initialErrors).forEach(([fieldId, errorMsg]) => {
          console.log(`[${timestamp}] [ExecutionDialog] Field error: ${fieldId} - ${errorMsg}`);
        });
      } else {
        console.log(`[${timestamp}] [ExecutionDialog] No initial field errors`);
      }

      // Merge with existing values
      const mergedValues = {
        ...currentFieldValues,
        ...initialValues,
      };

      // Get a snapshot of the current errors to avoid dependency issues
      const currentErrors = { ...errors };
      const mergedErrors = {
        ...currentErrors,
        ...initialErrors,
      };

      console.log(`[${timestamp}] [ExecutionDialog] Total field values after merge: ${Object.keys(mergedValues).length}`);
      console.log(`[${timestamp}] [ExecutionDialog] Total field errors after merge: ${Object.keys(mergedErrors).length}`);

      // Update state
      setFieldValues(mergedValues);
      setErrors(mergedErrors);

      // Validate form
      console.log(`[${timestamp}] [ExecutionDialog] Validating form with merged values and errors`);
      const isValid = validateForm(mergedValues, mergedErrors);

      // Force immediate update of button state if needed
      if (isValid) {
        console.log(`[${timestamp}] [ExecutionDialog] Initial form validation result: VALID, Run button should be enabled`);
        // Dispatch a custom event for immediate UI updates
        window.dispatchEvent(new CustomEvent('form-validation-complete', { detail: { isValid } }));
      } else {
        console.log(`[${timestamp}] [ExecutionDialog] Initial form validation result: INVALID, Run button should be disabled`);
      }
    } else {
      console.log(`[${timestamp}] [ExecutionDialog] No new field values to set`);
    }

    // No need for cleanup function here since we handle the reset in the main effect body
  }, [isDialogOpen, missingFields, validateForm]);

  // Auto-scroll logs to bottom
  useEffect(() => {
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [logs]);

  // Clean up SSE connection when component unmounts
  useEffect(() => {
    return () => {
      // Close any existing SSE connection
      if (sseClientRef.current) {
        console.log("Closing SSE connection on component unmount");
        sseClientRef.current.close();
        sseClientRef.current = null;
      }
    };
  }, []);

  // State for approval request
  const [approvalNeeded, setApprovalNeeded] = useState(false);
  const [approvalDetails, setApprovalDetails] = useState<{
    correlationId: string;
    nodeId: string;
    nodeName: string;
    timestamp?: number;
    approvalKey?: string;
  } | null>(null);

  // State for dialog content warnings (currently unused but kept for future use)
  const [, setDialogContentWarnings] = useState<string[]>([]);

  // Function to add default descriptions to dialog content
  const addDefaultDescriptions = useCallback(() => {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    console.log(`[${timestamp}] [addDefaultDescriptions] Adding default descriptions to dialog content`);

    // This is a temporary fix - in the future, these descriptions should be added
    // in the node configuration where the dialog content is defined

    // Clear any existing warnings
    setDialogContentWarnings([]);

    // In a real implementation, you would modify the node configuration
    // For now, we'll just log that this action would be taken
    console.log(`[${timestamp}] [addDefaultDescriptions] In a real implementation, this would modify the node configuration to add descriptions`);

    // Add a log entry about fixing the warnings
    addLog("✅ Added default descriptions to dialog content");
  }, [addLog]);

  // Listen for workflow terminal status events
  useEffect(() => {
    const handleTerminalStatus = (event: CustomEvent) => {
      console.log("Received workflow-terminal-status event:", event.detail);
      // Force update the UI to show the Run Again button
      // We don't need to set isExecuting to false, just ensure the button is enabled
      setIsFormValid(true);

      // Make sure isStreaming is set to false to enable the Run Again button
      setIsStreaming(false);

      // Clear any approval state when workflow completes
      setApprovalNeeded(false);

      // Clear from the tracking system if we have details
      if (approvalDetails) {
        clearApprovalEvent(
          approvalDetails.correlationId,
          approvalDetails.nodeId
        );
      }

      setApprovalDetails(null);
    };

    // Add event listener
    window.addEventListener("workflow-terminal-status", handleTerminalStatus as EventListener);

    // Clean up
    return () => {
      window.removeEventListener("workflow-terminal-status", handleTerminalStatus as EventListener);
    };
  }, [setIsFormValid, setIsStreaming]);

  // Listen for form validation complete events
  useEffect(() => {
    const handleFormValidationComplete = (event: CustomEvent) => {
      const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
      console.log(`[${timestamp}] [FormValidationComplete] Received form-validation-complete event:`, event.detail);

      if (event.detail && event.detail.isValid) {
        console.log(`[${timestamp}] [FormValidationComplete] Form is valid, forcing button state update`);
        // Force update the UI to enable the Run button
        setIsFormValid(true);
      }
    };

    // Add event listener
    window.addEventListener("form-validation-complete", handleFormValidationComplete as EventListener);

    // Clean up
    return () => {
      window.removeEventListener("form-validation-complete", handleFormValidationComplete as EventListener);
    };
  }, [setIsFormValid]);

  // Listen for workflow approval needed events
  useEffect(() => {
    let lastProcessedTimestamp = 0;

    const handleApprovalNeeded = (event: CustomEvent) => {
      console.log("Received workflow-approval-needed event:", event.detail);

      // Force processing of events that have the approvalKey property (from our improved utility)
      // or if it's a newer event than the last one we processed
      // or if the event has the force flag set
      const shouldProcess = event.detail.approvalKey ||
                           event.detail.force ||
                           !event.detail.timestamp ||
                           event.detail.timestamp > lastProcessedTimestamp;

      if (shouldProcess) {
        lastProcessedTimestamp = event.detail.timestamp || Date.now();

        // Switch to logs tab to show the approval UI
        setActiveTab("logs");

        // Set approval state with a slight delay to ensure UI updates
        // This helps with race conditions in React's state updates
        setTimeout(() => {
          setApprovalNeeded(true);
          setApprovalDetails(event.detail);

          // Log the approval state for debugging
          console.log("Setting approval state:", {
            approvalNeeded: true,
            approvalDetails: event.detail
          });
        }, 100);

        console.log(`Processed approval event for node ${event.detail.nodeName} (${event.detail.nodeId}) with timestamp ${lastProcessedTimestamp}`);
      } else {
        console.log(`Ignoring older approval event with timestamp ${event.detail.timestamp} (last processed: ${lastProcessedTimestamp})`);
      }
    };

    // Handle direct UI update events
    const handleApprovalUIUpdate = () => {
      console.log("Received approval-ui-update event");

      // Check if we have a pending approval that needs to be displayed
      if (window._pendingApproval) {
        console.log("Processing pending approval from UI update event:", window._pendingApproval);

        // Switch to logs tab
        setActiveTab("logs");

        // Set approval state
        setApprovalNeeded(true);
        setApprovalDetails({
          correlationId: window._pendingApproval.correlationId,
          nodeId: window._pendingApproval.nodeId,
          nodeName: window._pendingApproval.nodeName,
          timestamp: window._pendingApproval.timestamp
        });
      }
    };

    // Add event listeners
    window.addEventListener("workflow-approval-needed", handleApprovalNeeded as EventListener);
    window.addEventListener("approval-ui-update", handleApprovalUIUpdate as EventListener);

    // Add a periodic check for pending approvals that might have been missed
    const checkInterval = setInterval(() => {
      if (window._pendingApproval && !approvalNeeded) {
        console.log("Found pending approval that wasn't processed:", window._pendingApproval);

        // Manually trigger the approval UI
        setApprovalNeeded(true);
        setApprovalDetails({
          correlationId: window._pendingApproval.correlationId,
          nodeId: window._pendingApproval.nodeId,
          nodeName: window._pendingApproval.nodeName,
          timestamp: window._pendingApproval.timestamp
        });

        // Switch to logs tab
        setActiveTab("logs");
      }
    }, 2000);

    // Clean up
    return () => {
      window.removeEventListener("workflow-approval-needed", handleApprovalNeeded as EventListener);
      window.removeEventListener("approval-ui-update", handleApprovalUIUpdate as EventListener);
      clearInterval(checkInterval);
      // Clear all approval events when component unmounts
      clearAllApprovalEvents();
    };
  }, [setActiveTab, approvalNeeded]);

  // Check logs for approval needed status
  useEffect(() => {
    // Only check if we're not already in approval mode and we have logs
    if (!approvalNeeded && logs.length > 0 && correlationId) {
      // Check the last 10 logs (or all logs if fewer than 10)
      const logsToCheck = logs.slice(Math.max(0, logs.length - 10));

      for (const log of logsToCheck) {
        // Only look for logs that match our specific criteria
        if (log.includes("workflow_status") &&
            log.includes("waiting_for_approval") &&
            log.includes("approval_required") &&
            log.includes("status") &&
            log.includes("paused")) {

          console.log("Found potential approval request in logs, checking details");

          try {
            const logData = JSON.parse(log);

            // Only consider it a valid approval request if it meets specific criteria
            if (logData.workflow_status === "waiting_for_approval" &&
                logData.approval_required === true &&
                logData.status === "paused" &&
                logData.node_id) {

              console.log("Found valid approval request in logs:", {
                correlationId,
                nodeId: logData.node_id,
                nodeName: logData.node_name || logData.node_id
              });

              // Set approval state
              setApprovalNeeded(true);
              setApprovalDetails({
                correlationId,
                nodeId: logData.node_id,
                nodeName: logData.node_name || logData.node_id,
                timestamp: Date.now()
              });

              // Switch to logs tab
              setActiveTab("logs");

              // Also set the window flag directly for immediate access
              window._pendingApproval = {
                correlationId,
                nodeId: logData.node_id,
                nodeName: logData.node_name || logData.node_id,
                timestamp: Date.now()
              };

              // Add to approval history for debugging
              if (window._approvalEventHistory) {
                window._approvalEventHistory.push({
                  correlationId,
                  nodeId: logData.node_id,
                  nodeName: logData.node_name || logData.node_id,
                  timestamp: Date.now(),
                  status: 'detected_in_logs'
                });
              }

              // Force a UI update
              setTimeout(() => {
                window.dispatchEvent(new CustomEvent('approval-ui-update'));
              }, 200);

              break;
            } else {
              console.log("Log contains waiting_for_approval but doesn't meet criteria:", logData);
            }
          } catch (e) {
            console.error("Error parsing log data:", e);
          }
        }
      }
    }
  }, [logs, approvalNeeded, correlationId, setActiveTab]);

  // Handle when approval is sent
  const handleApprovalSent = () => {
    console.log("Approval sent, waiting for workflow to continue...");

    // Add to approval history for debugging
    if (window._approvalEventHistory && approvalDetails) {
      window._approvalEventHistory.push({
        correlationId: approvalDetails.correlationId,
        nodeId: approvalDetails.nodeId,
        nodeName: approvalDetails.nodeName || approvalDetails.nodeId,
        timestamp: Date.now(),
        status: 'approval_sent'
      });
    }

    // Clear approval state completely
    setApprovalNeeded(false);

    // Clear from the tracking system if we have details
    if (approvalDetails) {
      clearApprovalEvent(
        approvalDetails.correlationId,
        approvalDetails.nodeId
      );

      // Also clear the window flag
      window._pendingApproval = undefined;
    }

    // Reset approval details
    setApprovalDetails(null);

    // Log the state change
    console.log("Approval state cleared after sending approval");
  };

  // Handle field change - memoized to prevent unnecessary re-renders
  const handleFieldChange = useCallback((fieldId: string, value: any, type: string) => {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    console.log(`[${timestamp}] [handleFieldChange] ========== FIELD CHANGE DETECTED ==========`);
    console.log(`[${timestamp}] [handleFieldChange] Field: ${fieldId}`);
    console.log(`[${timestamp}] [handleFieldChange] Type: ${type}`);
    console.log(`[${timestamp}] [handleFieldChange] New value: ${JSON.stringify(value)}`);
    console.log(`[${timestamp}] [handleFieldChange] Previous value: ${JSON.stringify(fieldValues[fieldId])}`);

    // Extract node ID and field name from the field ID for logging purposes
    const parts = fieldId.split("_");
    const nodeId = parts[0];
    const fieldName = parts.slice(1).join("_");
    console.log(`[${timestamp}] [handleFieldChange] Field belongs to node ${nodeId}, field name: ${fieldName}`);

    // Find the field definition to check if it's required
    const fieldDef = missingFields.find(
      (field) => `${field.nodeId}_${field.name}` === fieldId
    );
    // Consider fields required unless explicitly marked as optional
    const isRequired = fieldDef?.required !== false;
    console.log(`[${timestamp}] [handleFieldChange] Field is required: ${isRequired ? "YES" : "NO"} (required !== false: ${fieldDef?.required !== false})`);

    // Validate field
    const newErrors = { ...errors };
    console.log(`[${timestamp}] [handleFieldChange] Current errors: ${JSON.stringify(newErrors[fieldId] || "none")}`);

    // Perform validation based on field type
    if (isRequired && type !== "boolean" && !value) {
      console.log(`[${timestamp}] [handleFieldChange] VALIDATION ERROR: Field is required but empty`);
      newErrors[fieldId] = "This field is required";
    } else if (
      (type === "object" ||
        type === "dict" ||
        type === "json" ||
        type === "array" ||
        type === "list") &&
      typeof value === "string" &&
      value.trim() !== ""
    ) {
      console.log(`[${timestamp}] [handleFieldChange] Validating JSON format for ${type} field`);
      try {
        JSON.parse(value);
        console.log(`[${timestamp}] [handleFieldChange] JSON validation passed`);
        delete newErrors[fieldId];
      } catch (e: any) {
        console.log(`[${timestamp}] [handleFieldChange] VALIDATION ERROR: Invalid JSON format - ${e.message}`);
        newErrors[fieldId] = "Invalid JSON format";
      }
    } else {
      console.log(`[${timestamp}] [handleFieldChange] Field validation passed`);
      delete newErrors[fieldId];
    }

    // Update field value in store
    console.log(`[${timestamp}] [handleFieldChange] Updating field value in store`);
    updateFieldValue(fieldId, value);

    // Log validation result
    if (newErrors[fieldId]) {
      console.log(`[${timestamp}] [handleFieldChange] Field has error: ${newErrors[fieldId]}`);
    } else {
      console.log(`[${timestamp}] [handleFieldChange] Field is valid`);
    }

    // Update errors
    console.log(`[${timestamp}] [handleFieldChange] Updating errors in state`);
    setErrors(newErrors);

    // Create merged values for form validation
    const mergedValues = {
      ...fieldValues,
      [fieldId]: value,
    };

    console.log(`[${timestamp}] [handleFieldChange] Validating entire form with updated values`);

    // Validate form - use the return value to update local state immediately
    const isValid = validateForm(mergedValues, newErrors);
    console.log(`[${timestamp}] [handleFieldChange] Form validation result: ${isValid ? "VALID" : "INVALID"}`);

    // Force immediate update of button state if needed
    if (isValid) {
      console.log(`[${timestamp}] [handleFieldChange] Form is now valid, Run button should be enabled`);
      // We could dispatch a custom event here if needed for immediate UI updates
      window.dispatchEvent(new CustomEvent('form-validation-complete', { detail: { isValid } }));
    }
  }, [missingFields, errors, fieldValues, updateFieldValue, setErrors, validateForm]);

  // Function to set up SSE connection
  const setupSSEConnection = useCallback((correlationId: string) => {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    console.log(`[${timestamp}] [setupSSEConnection] Setting up SSE connection for correlation ID: ${correlationId}`);

    // Close any existing connection
    if (sseClientRef.current) {
      console.log(`[${timestamp}] [setupSSEConnection] Closing existing SSE connection`);
      sseClientRef.current.close();
      sseClientRef.current = null;
    }

    // Create a new SSE client
    const sseClient = new SSEClient(correlationId, {
      onOpen: () => {
        console.log(`[${timestamp}] [setupSSEConnection] SSE connection opened`);
        setIsStreaming(true);
        addLog("Connected to execution stream...");
      },
      onMessage: (event) => {
        console.log(`[${timestamp}] [setupSSEConnection] SSE message received:`, event);

        // Parse the data if it's a string
        let parsedData;
        try {
          if (typeof event.data === "string") {
            parsedData = JSON.parse(event.data);
          }
        } catch (e) {
          console.error(`[${timestamp}] [setupSSEConnection] Error parsing SSE message data:`, e);
        }

        // Handle different event types
        if (event.type === "connected") {
          addLog(`Stream connected: ${event.data}`);
        } else if (event.type === "log") {
          addLog(event.data);
        } else if (event.type === "warning") {
          addLog(`⚠️ ${event.data}`);
        } else if (event.type === "error") {
          addLog(`❌ ${event.data}`);
        } else if (event.type === "info") {
          addLog(`ℹ️ ${event.data}`);
        } else if (event.type === "success") {
          addLog(`✅ ${event.data}`);
        } else if (event.type === "completed") {
          addLog(`✓ ${event.data}`);
          setIsExecuting(false);
        } else {
          // Default message handling
          // Skip empty objects which are likely keep-alive messages
          if (event.data !== "{}" && !(parsedData && Object.keys(parsedData).length === 0)) {
            addLog(event.data);
          }

          // Check for workflow status in the parsed data
          if (parsedData && parsedData.workflow_status) {
            // Log the workflow status
            const status = parsedData.workflow_status.toLowerCase();
            if (status === "failed") {
              addLog(`❌ Workflow execution failed`);
            } else if (status === "completed") {
              addLog(`✅ Workflow execution completed successfully`);
            } else if (status === "cancelled") {
              addLog(`⚠️ Workflow execution was cancelled`);
            }

            // Handle waiting_for_approval status
            // Only add the log message if it's a valid approval request
            if (status === "waiting_for_approval" &&
                parsedData.approval_required === true &&
                parsedData.status === "paused") {
              // Use node_id as fallback if node_name is not available
              const nodeName = parsedData.node_name || parsedData.node_id || "Unknown";
              addLog(
                `⏸️ Workflow is waiting for approval. Node: ${nodeName}`,
              );
            }
          }
        }
      },
      onError: (error) => {
        console.error(`[${timestamp}] [setupSSEConnection] SSE connection error:`, error);
        setIsStreaming(false);
        addLog("❌ Error in execution stream connection");
      },
      onClose: (wasError) => {
        console.log(`[${timestamp}] [setupSSEConnection] SSE connection closed`, wasError ? "due to an error" : "");
        setIsStreaming(false);
      },
    });

    // Store the client in the ref
    sseClientRef.current = sseClient;

    // Connect to the SSE stream
    sseClient.connect();

    console.log(`[${timestamp}] [setupSSEConnection] SSE connection setup complete`);
  }, [addLog, setIsStreaming, setIsExecuting]);

  // Handle form submission - memoized to prevent unnecessary re-renders
  const handleSubmit = useCallback(async () => {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    console.log(`[${timestamp}] [handleSubmit] ========== FORM SUBMISSION INITIATED ==========`);
    console.log(`[${timestamp}] [handleSubmit] Form is valid: ${isFormValid ? "YES" : "NO"}`);

    if (!isFormValid) {
      console.log(`[${timestamp}] [handleSubmit] Submission blocked - form is not valid`);
      return;
    }

    try {
      console.log(`[${timestamp}] [handleSubmit] Executing workflow with values from the store...`);
      console.log(`[${timestamp}] [handleSubmit] Total field values: ${Object.keys(fieldValues).length}`);

      // Log all field values being submitted
      Object.entries(fieldValues).forEach(([fieldId, value]) => {
        console.log(`[${timestamp}] [handleSubmit] Field value: ${fieldId} = ${JSON.stringify(value)}`);
      });

      // Switch to logs tab before execution
      console.log(`[${timestamp}] [handleSubmit] Switching to logs tab`);
      setActiveTab("logs");

      // Get workflow_id from URL params
      const urlParams = new URLSearchParams(window.location.search);
      const workflow_id = urlParams.get("workflow_id");

      if (!workflow_id) {
        console.error(`[${timestamp}] [handleSubmit] No workflow_id found in URL parameters`);
        addLog("❌ Error: No workflow ID found. Cannot execute workflow.");
        return;
      }

      console.log(`[${timestamp}] [handleSubmit] Using workflow_id from URL: ${workflow_id}`);
      addLog(`Preparing to execute workflow with ID: ${workflow_id}`);

      // Prepare the user_dependent_fields array and user_payload_template object
      const user_dependent_fields: string[] = [];
      const user_payload_template: Record<string, any> = {};

      // Process field values for the execution payload
      missingFields.forEach((field) => {
        const fieldId = `${field.nodeId}_${field.name}`;
        const value = fieldValues[fieldId];

        if (value !== undefined) {
          // Add field name to dependent fields array
          user_dependent_fields.push(field.name);

          // Add field value to payload template
          // For nested fields, we need to parse the JSON string
          if (field.inputType === 'object' || field.inputType === 'json' ||
              field.inputType === 'dict' || field.inputType === 'array' ||
              field.inputType === 'list') {
            try {
              user_payload_template[field.name] = JSON.parse(value);
            } catch (e) {
              console.error(`[${timestamp}] [handleSubmit] Error parsing JSON for field ${field.name}:`, e);
              user_payload_template[field.name] = value; // Use as string if parsing fails
            }
          } else {
            user_payload_template[field.name] = value;
          }
        }
      });

      console.log(`[${timestamp}] [handleSubmit] Prepared user_dependent_fields:`, user_dependent_fields);
      console.log(`[${timestamp}] [handleSubmit] Prepared user_payload_template:`, user_payload_template);

      // Create the execution payload
      const executionPayload: WorkflowExecuteWithUserInputsPayload = {
        user_id:"123",
        workflow_id: workflow_id,
        approval: "true", // Always set to "true" for user-initiated executions
        payload: {
          user_dependent_fields: user_dependent_fields,
          user_payload_template: user_payload_template
        }
      };

      console.log(`[${timestamp}] [handleSubmit] Sending execution request with payload:`, executionPayload);
      addLog("Sending workflow execution request...");

      // Set executing state
      setIsExecuting(true);

      // Execute the workflow with the new API
      const result = await executeWorkflowWithUserInputs(executionPayload);

      if (result.success) {
        console.log(`[${timestamp}] [handleSubmit] Execution request successful:`, result);
        addLog(`✅ Workflow execution started successfully`);

        // Check if we have a correlation ID for streaming
        if (result.correlationId) {
          setCorrelationId(result.correlationId);
          addLog(`Streaming logs with correlation ID: ${result.correlationId}`);

          // Set up SSE connection directly instead of calling onExecute
          console.log(`[${timestamp}] [handleSubmit] Setting up SSE connection directly`);
          setupSSEConnection(result.correlationId);
        } else {
          console.error(`[${timestamp}] [handleSubmit] No correlationId returned from execution API`);
          addLog("⚠️ No correlation ID returned. Cannot stream execution logs.");
          setIsExecuting(false);
        }
      } else {
        console.error(`[${timestamp}] [handleSubmit] Execution request failed:`, result);
        addLog(`❌ Error executing workflow: ${result.message || "Unknown error"}`);
        setIsExecuting(false);
      }
    } catch (error) {
      console.error(`[${timestamp}] [handleSubmit] ERROR executing workflow:`, error);
      addLog(`❌ Error: ${error instanceof Error ? error.message : String(error)}`);
      setIsExecuting(false);
    }
  }, [isFormValid, fieldValues, missingFields, setActiveTab, addLog, setCorrelationId, setIsExecuting, setupSSEConnection]);

  // Download logs - memoized to prevent unnecessary re-renders
  const handleDownloadLogs = useCallback(() => {
    if (!Array.isArray(logs) || logs.length === 0) {
      console.warn("No logs available to download");
      return;
    }

    const blob = new Blob([logs.join("\n")], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `workflow-execution-logs-${new Date().toISOString().replace(/:/g, "-")}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [logs]);

  // Only render the Dialog when isDialogOpen is true to avoid unnecessary renders
  if (!isDialogOpen) {
    return null;
  }

  return (
    <Dialog
      open={true}
      onOpenChange={(open) => {
        if (!open) onClose();
      }}
    >
      <DialogContent className="flex max-h-[90vh] max-w-3xl flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Workflow Execution
            {isStreaming && (
              <Badge
                variant="outline"
                className="ml-2 animate-pulse bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
              >
                Execution in progress
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-grow flex-col overflow-hidden">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="flex flex-grow flex-col overflow-hidden"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="parameters" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Required Parameters
              </TabsTrigger>
              <TabsTrigger
                value="logs"
                className="flex items-center gap-2"
                disabled={missingFields.length > 0 && !isFormValid}
              >
                <Terminal className="h-4 w-4" />
                Execution Logs
                {logs.length > 0 && (
                  <span className="bg-primary text-primary-foreground ml-1 flex h-5 w-5 items-center justify-center rounded-full text-xs">
                    {logs.length}
                  </span>
                )}
              </TabsTrigger>
            </TabsList>

            {/* Parameters Tab */}
            <TabsContent
              value="parameters"
              className="mt-4 flex flex-grow flex-col overflow-hidden"
            >
              {missingFields.length > 0 ? (
                <>
                  <div className="mb-4 flex items-start gap-2 rounded-md border border-blue-200 bg-blue-50 p-2">
                    <AlertCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-blue-500" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">Workflow Parameters</p>
                      <p className="text-xs text-blue-700">
                        Please review and edit the following parameters before executing the
                        workflow. All values are editable.
                      </p>
                    </div>
                  </div>

                  <CustomScrollArea className="mb-4 max-h-[50vh] flex-grow">
                    <div className="space-y-4 p-2">
                      {missingFields.map((field) => {
                        const fieldId = `${field.nodeId}_${field.name}`;
                        // Consider fields required unless explicitly marked as optional
                        const isRequired = field.required !== false;

                        return (
                          <div key={fieldId} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label
                                htmlFor={fieldId}
                                className="flex items-center gap-2 text-sm font-medium"
                              >
                                {field.nodeName || "Unknown Node"}: {field.displayName || field.name || "Unnamed Field"}
                                <Badge variant="outline" className="text-xs">
                                  {field.inputType}
                                </Badge>
                                {isRequired ? (
                                  <Badge
                                    variant="destructive"
                                    className="text-xs"
                                  >
                                    Required
                                  </Badge>
                                ) : (
                                  <Badge
                                    variant="outline"
                                    className="text-xs"
                                  >
                                    Optional
                                  </Badge>
                                )}
                                {field.directly_connected_to_start && (
                                  <Badge
                                    variant="secondary"
                                    className="bg-blue-100 text-xs text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                                  >
                                    Connected to Start
                                  </Badge>
                                )}
                              </Label>
                            </div>

                            {field.info && (
                              <p className="text-muted-foreground text-xs">{field.info}</p>
                            )}

                            {field.inputType === "boolean" ? (
                              <div className="flex items-center space-x-2">
                                <input
                                  id={fieldId}
                                  type="checkbox"
                                  checked={fieldValues[fieldId] === true}
                                  onChange={(e) => {
                                    // For boolean fields, ensure we're passing a proper boolean value
                                    const boolValue = e.target.checked;
                                    console.log(`Boolean field ${fieldId} changed to: ${boolValue}`);
                                    handleFieldChange(fieldId, boolValue, field.inputType);
                                  }}
                                  className="text-primary focus:ring-primary h-4 w-4 rounded border-gray-300"
                                />
                                <Label htmlFor={fieldId} className="text-sm">
                                  {field.displayName}
                                </Label>
                              </div>
                            ) : field.inputType === "object" ||
                              field.inputType === "dict" ||
                              field.inputType === "json" ? (
                              // Dynamic handling for object fields
                              <div className="space-y-3 rounded-md border bg-gray-50 p-3 dark:bg-gray-800">
                                <p className="text-muted-foreground mb-2 text-xs">
                                  Fill in the following fields:
                                </p>

                                {/* Determine object properties dynamically */}
                                {(() => {
                                  // Default properties for known fields
                                  let objectProperties = [];

                                  // Special case for keywords field
                                  if (
                                    field.name === "keywords" ||
                                    field.name === "tool_arg_keywords"
                                  ) {
                                    objectProperties = [
                                      "time",
                                      "objective",
                                      "audience",
                                      "gender",
                                      "tone",
                                      "speakers",
                                    ];
                                  } else if (field.schema && field.schema.properties) {
                                    // If we have schema information, use it to determine properties
                                    objectProperties = Object.keys(field.schema.properties);
                                  } else {
                                    // For other object fields, try to extract properties from existing values
                                    try {
                                      const currentValue = JSON.parse(fieldValues[fieldId] || "{}");
                                      objectProperties = Object.keys(currentValue);

                                      // If no properties found, add a default property
                                      if (objectProperties.length === 0) {
                                        objectProperties = ["value"];
                                      }
                                    } catch (e) {
                                      objectProperties = ["value"];
                                    }
                                  }

                                  // Parse the current JSON value
                                  let currentObject: Record<string, any> = {};
                                  try {
                                    currentObject = JSON.parse(fieldValues[fieldId] || "{}");
                                  } catch (e) {
                                    currentObject = {};
                                  }

                                  return objectProperties.map((propName) => (
                                    <div key={propName} className="space-y-1">
                                      <Label
                                        htmlFor={`${fieldId}_${propName}`}
                                        className="text-xs font-medium"
                                      >
                                        {propName.charAt(0).toUpperCase() + propName.slice(1)}
                                      </Label>
                                      <Input
                                        id={`${fieldId}_${propName}`}
                                        value={currentObject[propName] || ""}
                                        onChange={(e) => {
                                          // Update the specific property in the object
                                          const updatedObject: Record<string, any> = {
                                            ...currentObject,
                                            [propName]: e.target.value,
                                          };
                                          // Update the full JSON string
                                          handleFieldChange(
                                            fieldId,
                                            JSON.stringify(updatedObject, null, 2),
                                            field.inputType,
                                          );
                                        }}
                                        placeholder={`Enter ${propName}...`}
                                        className="text-xs"
                                      />
                                    </div>
                                  ));
                                })()}

                                {/* Add button for adding new properties */}
                              </div>
                            ) : field.inputType === "array" || field.inputType === "list" ? (
                              <Textarea
                                id={fieldId}
                                value={fieldValues[fieldId] || ""}
                                onChange={(e) =>
                                  handleFieldChange(fieldId, e.target.value, field.inputType)
                                }
                                placeholder={`Enter ${field.displayName || field.name || "value"}...`}
                                className={`font-mono text-sm ${errors[fieldId] ? "border-red-500" : ""}`}
                                rows={4}
                              />
                            ) : field.inputType === "number" ||
                              field.inputType === "int" ||
                              field.inputType === "float" ? (
                              <Input
                                id={fieldId}
                                type="number"
                                value={fieldValues[fieldId] || ""}
                                onChange={(e) =>
                                  handleFieldChange(fieldId, e.target.value, field.inputType)
                                }
                                placeholder={`Enter ${field.displayName || field.name || "value"}...`}
                                className={errors[fieldId] ? "border-red-500" : ""}
                              />
                            ) : (
                              <Input
                                id={fieldId}
                                value={fieldValues[fieldId] || ""}
                                onChange={(e) =>
                                  handleFieldChange(fieldId, e.target.value, field.inputType)
                                }
                                placeholder={`Enter ${field.displayName || field.name || "value"}...`}
                                className={errors[fieldId] ? "border-red-500" : ""}
                              />
                            )}

                            {errors[fieldId] && (
                              <p className="text-xs text-red-500">{errors[fieldId]}</p>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </CustomScrollArea>

                  <div className="mt-4 flex justify-end">
                    <button
                      type="button"
                      onClick={handleSubmit}
                      disabled={!isFormValid || isExecuting}
                      className={`bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-9 items-center justify-center gap-1 rounded-md px-4 py-2 text-sm font-medium shadow-xs disabled:pointer-events-none disabled:opacity-50 ${!isFormValid || isExecuting ? "cursor-not-allowed opacity-50" : ""}`}
                    >
                      <Play className="h-4 w-4" />
                      Run Workflow
                    </button>
                  </div>
                </>
              ) : (
                <div className="flex flex-grow items-center justify-center">
                  <div className="p-6 text-center">
                    <CheckCircle className="mx-auto mb-4 h-12 w-12 text-green-500" />
                    <h3 className="mb-2 text-lg font-medium">No Parameters Found</h3>
                    <p className="text-muted-foreground mb-4 text-sm">
                      No workflow parameters were found. This is unusual - most workflows require
                      parameters.
                    </p>
                    <div className="flex justify-center gap-3">
                      <button
                        type="button"
                        onClick={() => {
                          // Close dialog to let user check the workflow
                          onClose();
                        }}
                        className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-1 rounded-md border px-4 py-2 text-sm font-medium shadow-xs"
                      >
                        <FileText className="mr-1 h-4 w-4" />
                        Check Workflow
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setActiveTab("logs");
                          handleSubmit();
                        }}
                        className="bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-9 items-center justify-center gap-1 rounded-md px-4 py-2 text-sm font-medium shadow-xs"
                      >
                        <Play className="mr-1 h-4 w-4" />
                        Execute Anyway
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Logs Tab */}
            <TabsContent value="logs" className="mt-4 flex flex-grow flex-col overflow-hidden">
              <div className="mb-2 flex items-center justify-between">
                <h3 className="text-sm font-medium">Execution Logs</h3>
                {logs.length > 0 && (
                  <button
                    type="button"
                    onClick={handleDownloadLogs}
                    className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-7 items-center justify-center gap-1 rounded-md border px-2 text-sm text-xs font-medium shadow-xs"
                  >
                    <Download className="mr-1 h-3 w-3" />
                    Download Logs
                  </button>
                )}
              </div>

              <Card className="flex-grow overflow-hidden border">
                <CardContent className="p-0">
                  {/* Approval UI */}
                  {approvalNeeded && approvalDetails && (
                    <div className="p-2">
                      <ApprovalRequest
                        correlationId={approvalDetails.correlationId}
                        nodeId={approvalDetails.nodeId}
                        nodeName={approvalDetails.nodeName}
                        onApprovalSent={handleApprovalSent}
                      />
                    </div>
                  )}

                  <LogDisplay logs={logs} showStreamingStatus={true} />

                  {/* Debug Panel for Approval State (only in development mode) */}
                  {process.env.NODE_ENV === 'development' && (
                    <details className="mt-2 border-t pt-2 text-xs" open>
                      <summary className="cursor-pointer font-medium">Debug Approval State</summary>
                      <pre className="mt-2 overflow-auto p-2 bg-gray-100 rounded">
                        {JSON.stringify({
                          approvalNeeded,
                          approvalDetails,
                          correlationId,
                          logsWithApproval: logs.filter(log =>
                            log.toLowerCase().includes("waiting_for_approval") &&
                            log.toLowerCase().includes("approval_required") &&
                            log.toLowerCase().includes("status") &&
                            log.toLowerCase().includes("paused")
                          ).length,
                          validApprovalLogs: logs.filter(log => {
                            try {
                              const data = JSON.parse(log);
                              return data.workflow_status === "waiting_for_approval" &&
                                     data.approval_required === true &&
                                     data.status === "paused";
                            } catch (e) {
                              return false;
                            }
                          }).length,
                          pendingApproval: window._pendingApproval,
                          lastApprovalTimestamp: window._lastApprovalTimestamp,
                          approvalEventHistory: window._approvalEventHistory,
                          currentTimestamp: Date.now()
                        }, null, 2)}
                      </pre>
                      <div className="mt-2 flex gap-2">
                        <button
                          onClick={() => {
                            if (window._pendingApproval) {
                              setApprovalNeeded(true);
                              setApprovalDetails({
                                correlationId: window._pendingApproval.correlationId,
                                nodeId: window._pendingApproval.nodeId,
                                nodeName: window._pendingApproval.nodeName,
                                timestamp: window._pendingApproval.timestamp
                              });
                              console.log("Manually triggered approval UI from debug panel");

                              // Add to history
                              if (window._approvalEventHistory) {
                                window._approvalEventHistory.push({
                                  correlationId: window._pendingApproval.correlationId,
                                  nodeId: window._pendingApproval.nodeId,
                                  nodeName: window._pendingApproval.nodeName,
                                  timestamp: Date.now(),
                                  status: 'manually_triggered'
                                });
                              }
                            } else {
                              console.log("No pending approval to trigger");

                              // Check logs for a valid approval request
                              const validApprovalLogs = logs.filter(log => {
                                try {
                                  const data = JSON.parse(log);
                                  return data.workflow_status === "waiting_for_approval" &&
                                         data.approval_required === true &&
                                         data.status === "paused" &&
                                         data.node_id;
                                } catch (e) {
                                  return false;
                                }
                              });

                              if (validApprovalLogs.length > 0) {
                                try {
                                  const logData = JSON.parse(validApprovalLogs[0]);
                                  setApprovalNeeded(true);
                                  setApprovalDetails({
                                    correlationId: correlationId || '',
                                    nodeId: logData.node_id,
                                    nodeName: logData.node_name || logData.node_id,
                                    timestamp: Date.now()
                                  });
                                  console.log("Manually triggered approval UI from logs");
                                } catch (e) {
                                  console.error("Error parsing log data:", e);
                                }
                              }
                            }
                          }}
                          className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs"
                        >
                          Force Show Approval
                        </button>
                        <button
                          onClick={() => {
                            clearAllApprovalEvents();
                            setApprovalNeeded(false);
                            setApprovalDetails(null);
                            console.log("Reset approval state from debug panel");
                          }}
                          className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs"
                        >
                          Reset Approval State
                        </button>
                      </div>
                    </details>
                  )}

                  {/* Dialog Content Warnings */}
                  {logs.some(log => log.includes("Missing 'Description'")) && (
                    <div className="border-t p-2">
                      <div className="flex items-start gap-2 rounded-md border border-yellow-200 bg-yellow-50 p-2 mb-2">
                        <AlertCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-yellow-500" />
                        <div className="flex-1">
                          <p className="text-xs font-medium text-yellow-800">Dialog Content Warnings</p>
                          <p className="text-xs text-yellow-700">
                            Missing descriptions for dialog content. This won't prevent execution but should be fixed for better user experience.
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={addDefaultDescriptions}
                          className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 text-xs px-2 py-1 rounded"
                        >
                          Add Default Descriptions
                        </button>
                      </div>
                    </div>
                  )}

                  {correlationId && (
                    <div className="text-muted-foreground border-t p-2 text-xs">
                      <span className="font-medium">Correlation ID:</span> {correlationId}
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="mt-4 flex justify-between">
                {missingFields.length > 0 && (
                  <button
                    type="button"
                    onClick={() => setActiveTab("parameters")}
                    className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-1 rounded-md border px-4 py-2 text-sm font-medium shadow-xs"
                  >
                    <FileText className="mr-1 h-4 w-4" />
                    Back to Parameters
                  </button>
                )}

                {/* Always show Run Again button regardless of missing fields */}
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={!isFormValid || isStreaming}
                  className={`bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-9 items-center justify-center gap-1 rounded-md px-4 py-2 text-sm font-medium shadow-xs disabled:pointer-events-none disabled:opacity-50 ${!isFormValid || isStreaming ? "cursor-not-allowed opacity-50" : ""} ${missingFields.length === 0 ? "ml-auto" : ""}`}
                >
                  <Play className="h-4 w-4" />
                  {isStreaming ? "Executing..." : "Run Again"}
                </button>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="mt-4 flex items-center justify-between">
          {/* Left side - Stop Execution button (shown when streaming) */}
          <div className="flex items-center gap-2">
            {/* Show stop execution button when streaming */}
            {isStreaming && onStopExecution && (
                <button
                  type="button"
                  onClick={async () => {
                    // Add a log entry before stopping
                    const timestamp = new Date().toISOString().substring(11, 19);
                    addLog(`[${timestamp}] 🛑 Sending request to stop workflow execution...`);

                    // Call the onStopExecution prop
                    if (onStopExecution) {
                      onStopExecution();
                    }
                  }}
                  className="inline-flex h-9 items-center justify-center gap-1.5 rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-red-700"
                  title="Stop the current workflow execution"
                >
                  <Square className="h-4 w-4" />
                  Stop Workflow Execution
                </button>
              )}
          </div>

          {/* Right side - Close button */}
          <button
            type="button"
            onClick={onClose}
            className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium shadow-xs"
            title="Close this dialog without stopping execution"
          >
            Close
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
