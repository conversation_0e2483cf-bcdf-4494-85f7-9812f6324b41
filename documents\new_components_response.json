{"AI": {"AgenticAI": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "credential", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use.", "value": "", "options": [], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "objective_handle", "display_name": "Objective", "info": "Connect the task or objective from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "objective", "display_name": "Objective (Direct)", "info": "The task or objective for the agent to accomplish. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "input_variables_handle", "display_name": "Input Variables", "info": "Connect a dictionary of variables from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["dict"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "input_variables", "display_name": "Input Variables (Direct)", "info": "Dictionary of variables to provide to the agent. Used if no connection is provided.", "value": {}, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dict"}, {"name": "tools_handle", "display_name": "Tools", "info": "Connect a list of tools from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["list"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "tools", "display_name": "Tools (Direct)", "info": "List of tools available to the agent. Used if no connection is provided.", "value": [], "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": true, "visibility_rules": null, "visibility_logic": "OR", "input_type": "list"}, {"name": "memory_handle", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "agent_type", "display_name": "Agent Type", "info": "The type of agent to create.", "value": "ReAct", "options": ["ReAct", "Conversational"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.ai.agentic_ai.AgenticAI", "interface_issues": []}, "BasicLLMChain": {"name": "BasicLLMChain", "display_name": "LLM Chain (Basic)", "description": "Runs a simple LLMChain with a prompt template.", "category": "AI", "icon": "Chain", "beta": false, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "credential", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use.", "value": "", "options": [], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "llm_handle", "display_name": "LLM Object", "info": "Connect a language model object from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "prompt_template_handle", "display_name": "Prompt Template", "info": "Connect a prompt template object from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "input_variables_handle", "display_name": "Input Variables", "info": "Connect a dictionary of variables from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["dict"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "input_variables", "display_name": "Input Variables (Direct)", "info": "Dictionary of variables to fill the prompt template. Used if no connection is provided.", "value": {}, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dict"}], "outputs": [{"name": "output_text", "display_name": "Generated Text", "output_type": "string", "method": null}, {"name": "full_response", "display_name": "Full Chain Response", "output_type": "dict", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.ai.basic_llm_chain.BasicLLMChain", "interface_issues": []}, "Classifier": {"name": "Classifier", "display_name": "Text Classifier", "description": "Categorizes text into one of several predefined classes or labels.", "category": "AI", "icon": "Tag", "beta": false, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "credential", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use.", "value": "", "options": [], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "text_handle", "display_name": "Text to Classify", "info": "Connect the text that needs to be assigned a category.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "text", "display_name": "Text to Classify (Direct)", "info": "The text that needs to be assigned a category. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "categories_handle", "display_name": "Categories", "info": "Connect a list of possible categories from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["list", "string"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "categories", "display_name": "Categories (Direct)", "info": "The set of valid categories the text can be assigned to. Provide as a list or comma-separated string. Used if no connection is provided.", "value": [], "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": true, "visibility_rules": null, "visibility_logic": "OR", "input_type": "list"}], "outputs": [{"name": "category", "display_name": "Assigned Category", "output_type": "string", "method": null}, {"name": "confidence", "display_name": "Confidence", "output_type": "float", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.ai.classifier.Classifier", "interface_issues": []}, "InformationExtractor": {"name": "InformationExtractor", "display_name": "Information Extractor", "description": "Extracts specific information from text based on a query.", "category": "AI", "icon": "FileSearch", "beta": false, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "credential", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use.", "value": "", "options": [], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "text_handle", "display_name": "Source Text", "info": "Connect the text from which to extract information.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "text", "display_name": "Source Text (Direct)", "info": "The text from which to extract information. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "query_handle", "display_name": "Extraction Query", "info": "Connect the query specifying what information to extract.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "query", "display_name": "Extraction Query (Direct)", "info": "Specifies what information to extract (e.g., 'Extract email addresses', 'Find the customer name'). Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}], "outputs": [{"name": "extracted_info", "display_name": "Extracted Information", "output_type": "string", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.ai.information_extractor.InformationExtractor", "interface_issues": []}, "OpenAIModule": {"name": "OpenAIModule", "display_name": "OpenAI Call", "description": "Direct OpenAI API call.", "category": "AI", "icon": "OpenAI", "beta": false, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "credential", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use.", "value": "", "options": [], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "endpoint", "display_name": "API Endpoint", "info": "The OpenAI API endpoint to use.", "value": "ChatCompletion", "options": ["ChatCompletion", "Completion (Legacy)"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "messages_handle", "display_name": "Messages", "info": "Connect a list of message objects from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["list"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "endpoint", "field_value": "ChatCompletion", "operator": "equals"}], "visibility_logic": "OR", "input_type": "handle"}, {"name": "messages", "display_name": "Messages (Direct)", "info": "List of message objects for ChatCompletion. Each message should have 'role' and 'content' keys. Used if no connection is provided.", "value": [], "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": true, "visibility_rules": [{"field_name": "endpoint", "field_value": "ChatCompletion", "operator": "equals"}], "visibility_logic": "OR", "input_type": "list"}, {"name": "prompt_handle", "display_name": "Prompt", "info": "Connect a text prompt from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "endpoint", "field_value": "Completion (Legacy)", "operator": "equals"}], "visibility_logic": "OR", "input_type": "handle"}, {"name": "prompt", "display_name": "Prompt (Direct)", "info": "Text prompt for Completion API. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "endpoint", "field_value": "Completion (Legacy)", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum number of tokens to generate.", "value": 1000, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "int"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "value": 0.7, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "float"}], "outputs": [{"name": "response", "display_name": "API Response", "output_type": "dict", "method": null}, {"name": "content", "display_name": "Generated Content", "output_type": "string", "method": null}, {"name": "usage", "display_name": "Token Usage", "output_type": "dict", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.ai.openai_module.OpenAIModule", "interface_issues": []}, "QuestionAnswerModule": {"name": "QuestionAnswerModule", "display_name": "Question Answering", "description": "Answers questions based on a provided context document.", "category": "AI", "icon": "HelpCircle", "beta": false, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "credential", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use.", "value": "", "options": [], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "question_handle", "display_name": "Question", "info": "Connect the question to be answered.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "question", "display_name": "Question (Direct)", "info": "The question to be answered. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "context_handle", "display_name": "Context Document", "info": "Connect the context document containing the information needed to answer the question.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "context", "display_name": "Context Document (Direct)", "info": "The text containing the information needed to answer the question. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}], "outputs": [{"name": "answer", "display_name": "Answer", "output_type": "string", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.ai.question_answer_module.QuestionAnswerModule", "interface_issues": []}, "SentimentAnalyzer": {"name": "SentimentAnalyzer", "display_name": "Sentiment Analyzer", "description": "Analyzes text to determine its sentiment (Positive, Negative, Neutral).", "category": "AI", "icon": "<PERSON><PERSON><PERSON>", "beta": false, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "credential", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use.", "value": "", "options": [], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "text_handle", "display_name": "Text to Analyze", "info": "Connect the text whose sentiment needs to be evaluated.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "text", "display_name": "Text to Analyze (Direct)", "info": "The text whose sentiment needs to be evaluated. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}], "outputs": [{"name": "sentiment", "display_name": "Sentiment", "output_type": "string", "method": null}, {"name": "confidence", "display_name": "Confidence", "output_type": "float", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.ai.sentiment_analyzer.SentimentAnalyzer", "interface_issues": []}, "Summarizer": {"name": "Summa<PERSON><PERSON>", "display_name": "Text Summarizer", "description": "Creates a concise summary of a longer piece of text.", "category": "AI", "icon": "FileText", "beta": false, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "credential", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use.", "value": "", "options": [], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "text_handle", "display_name": "Text to Summarize", "info": "Connect the text that needs to be condensed.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "text", "display_name": "Text to Summarize (Direct)", "info": "The full text that needs to be condensed. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "max_length", "display_name": "Max Summary Length", "info": "Maximum length of the summary in words. Use 0 for automatic length.", "value": 200, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "int"}], "outputs": [{"name": "summary", "display_name": "Summary", "output_type": "string", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.ai.summarizer.Summarizer", "interface_issues": []}}, "Logic": {"ConditionalNode": {"name": "ConditionalNode", "display_name": "If-Else Router", "description": "Compares 'Text Input' and 'Compare Text', then routes 'Input Value' to the 'True' or 'False' output.", "category": "Logic", "icon": "GitBranch", "beta": false, "requires_approval": false, "inputs": [{"name": "input_text", "display_name": "Text Input", "info": "The text to compare. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "multiline"}, {"name": "match_text", "display_name": "Compare Text", "info": "The text to compare against 'Text Input'. Typically entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": true, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "multiline"}, {"name": "operator", "display_name": "Operator", "info": "The comparison operator to apply.", "value": "equals", "options": ["equals", "not equals", "contains", "starts with", "ends with", "regex"], "real_time_refresh": true, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "case_sensitive", "display_name": "Case Sensitive", "info": "If true, comparison is case sensitive (ignored for 'regex').", "value": false, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "operator", "field_value": "equals", "operator": "equals"}, {"field_name": "operator", "field_value": "not equals", "operator": "equals"}, {"field_name": "operator", "field_value": "contains", "operator": "equals"}, {"field_name": "operator", "field_value": "starts with", "operator": "equals"}, {"field_name": "operator", "field_value": "ends with", "operator": "equals"}], "visibility_logic": "OR", "input_type": "bool"}, {"name": "routed_input", "display_name": "Input Value (to Route)", "info": "The value/message to pass through the selected output route. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "multiline"}, {"name": "route_input_text_if_unconnected", "display_name": "Route 'Text Input' if 'Input Value' is Not Connected?", "info": "If checked and 'Input Value (to Route)' is not connected, the 'Text Input' value will be routed instead.", "value": false, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}], "outputs": [{"name": "true_result", "display_name": "True", "output_type": "Any", "method": null}, {"name": "false_result", "display_name": "False", "output_type": "Any", "method": null}], "is_valid": true, "path": "components.control_flow.conditionalNode.ConditionalNode", "interface_issues": []}, "LoopNode": {"name": "LoopNode", "display_name": "For Each Loop", "description": "Iterates over an input list, outputting each item. Aggregates results optionally.", "category": "Logic", "icon": "Repeat", "beta": false, "requires_approval": false, "inputs": [{"name": "input_list", "display_name": "List to Iterate", "info": "Connect the list of items to loop through.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["list", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "loop_input", "display_name": "<PERSON><PERSON> (for Aggregation)", "info": "Connect the result from the loop's body for the *previous* item here if you want to aggregate results.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}], "outputs": [{"name": "item", "display_name": "Current Item", "output_type": "Any", "method": null}, {"name": "index", "display_name": "Current Index", "output_type": "int", "method": null}, {"name": "done", "display_name": "Done (Aggregated)", "output_type": "list", "method": null}, {"name": "is_looping", "display_name": "Is Looping?", "output_type": "bool", "method": null}], "is_valid": true, "path": "components.control_flow.loopNode.LoopNode", "interface_issues": []}, "SwitchNodeDynamic": {"name": "SwitchNodeDynamic", "display_name": "Switch (Dynamic Cases)", "description": "Routes data based on matching against a user-defined list of cases. Requires Frontend support for dynamic handles.", "category": "Logic", "icon": "GitMerge", "beta": true, "requires_approval": false, "inputs": [{"name": "input_value_compare_handle", "display_name": "Input Value (to Compare)", "info": "Connect the value to compare against the different cases.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "input_value_compare", "display_name": "Input Value (Direct)", "info": "The value to compare against the different cases. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "cases", "display_name": "Cases (Match Values)", "info": "List of string values to match against. Add/remove cases using UI controls. Frontend renders corresponding outputs.", "value": [], "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": true, "visibility_rules": null, "visibility_logic": "OR", "input_type": "list"}, {"name": "case_sensitive", "display_name": "Case Sensitive Match", "info": "If checked, the comparison will be case-sensitive.", "value": false, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}, {"name": "routed_input_handle", "display_name": "Input Value (to Route)", "info": "Connect the actual data/message to pass through the selected output route.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "routed_input", "display_name": "Input Value (Direct)", "info": "The data/message to pass through the selected output route. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "multiline"}], "outputs": [{"name": "default_output", "display_name": "<PERSON><PERSON><PERSON>", "output_type": "Any", "method": null}], "is_valid": true, "path": "components.control_flow.switchNode.SwitchNodeDynamic", "interface_issues": []}}, "Data Interaction": {"ApiRequestNode": {"name": "ApiRequestNode", "display_name": "API Request", "description": "Makes a single HTTP request to the specified URL.", "category": "Data Interaction", "icon": "Globe", "beta": false, "requires_approval": false, "inputs": [{"name": "url", "display_name": "URL", "info": "The URL to make the request to.", "value": "", "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "method", "display_name": "HTTP Method", "info": "The HTTP method to use for the request.", "value": "GET", "options": ["GET", "POST", "PUT", "PATCH", "DELETE"], "real_time_refresh": true, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "query_params", "display_name": "Query Parameters", "info": "Key-value pairs to append to the URL query string (optional).", "value": {}, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dict"}, {"name": "headers", "display_name": "Request Headers", "info": "Key-value pairs for request headers (optional).", "value": {}, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dict"}, {"name": "body", "display_name": "Request Body (JSON)", "info": "Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized.", "value": {}, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "method", "field_value": "POST", "operator": "equals"}, {"field_name": "method", "field_value": "PUT", "operator": "equals"}, {"field_name": "method", "field_value": "PATCH", "operator": "equals"}], "visibility_logic": "OR", "input_type": "dict"}, {"name": "timeout", "display_name": "Timeout (seconds)", "info": "Maximum time to wait for a response.", "value": 10, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "int"}, {"name": "follow_redirects", "display_name": "Follow Redirects", "info": "Automatically follow HTTP redirects (e.g., 301, 302).", "value": true, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}, {"name": "save_to_file", "display_name": "Save Response to File", "info": "Save response body to a temporary file instead of returning content directly. 'Result' output will be the file path.", "value": false, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}, {"name": "output_format", "display_name": "Output Format", "info": "'auto': Try JSON, fallback to text. 'json': Force JSON parsing (error if invalid). 'text': Return as text. 'bytes': Return raw bytes. 'file_path': Always save to file (requires 'Save Response to File'). 'metadata_dict': Return dict with status, headers, body/path, error.", "value": "auto", "options": ["auto", "json", "text", "bytes", "file_path", "metadata_dict"], "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "raise_on_error", "display_name": "Raise Exception on HTTP Error", "info": "Stop workflow execution if an HTTP error status (4xx, 5xx) is received.", "value": false, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any", "method": null}, {"name": "status_code", "display_name": "Status Code", "output_type": "int", "method": null}, {"name": "response_headers", "display_name": "Response Headers", "output_type": "dict", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.data_interaction.api_request.ApiRequestNode", "interface_issues": ["Component ApiRequestNode's build method does not have a deprecation warning."]}, "WebhookComponent": {"name": "WebhookComponent", "display_name": "Webhook Trigger", "description": "Receives data from an external system via HTTP request.", "category": "Data Interaction", "icon": "Globe", "beta": true, "requires_approval": false, "inputs": [{"name": "method", "display_name": "Allowed Method", "info": "HTTP method the webhook endpoint will accept. Set by framework.", "value": "POST", "options": ["POST", "GET", "ANY"], "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "require_auth", "display_name": "Require Authentication", "info": "If enabled, framework requires requests to include a valid auth token.", "value": false, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}, {"name": "auth_token", "display_name": "<PERSON><PERSON>", "info": "Secret token framework uses to validate requests if auth is required.", "value": "", "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "require_auth", "field_value": true, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "webhook_url", "display_name": "Webhook URL", "info": "The unique URL for triggering this workflow. Send HTTP requests here.", "value": "FRAMEWORK_GENERATED_URL", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "curl_example", "display_name": "cURL Example", "info": null, "value": "FRAMEWORK_GENERATED_CURL", "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "received_headers", "display_name": "Received Headers", "info": null, "value": "", "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "received_query_params", "display_name": "Received Query Params", "info": null, "value": "", "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "received_body", "display_name": "Received Body", "info": null, "value": "", "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "received_method", "display_name": "Received Method", "info": null, "value": "", "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}], "outputs": [{"name": "headers", "display_name": "Request Headers", "output_type": "dict", "method": null}, {"name": "query_params", "display_name": "Query Parameters", "output_type": "dict", "method": null}, {"name": "body", "display_name": "Request Body", "output_type": "Any", "method": null}, {"name": "webhook_url_out", "display_name": "Webhook URL", "output_type": "string", "method": null}, {"name": "method_out", "display_name": "Request Method", "output_type": "string", "method": null}, {"name": "trigger_data", "display_name": "Trigger Data (Combined)", "output_type": "dict", "method": null}], "is_valid": true, "path": "components.data_interaction.webhook.WebhookComponent", "interface_issues": ["Component WebhookComponent's build method does not have a deprecation warning."]}}, "Helpers": {"DocExtractorComponent": {"name": "DocExtractorComponent", "display_name": "Document Extractor", "description": "Extracts text content from document files (PDF, DOCX, TXT).", "category": "Helpers", "icon": "FileText", "beta": false, "requires_approval": false, "inputs": [{"name": "file_path_handle", "display_name": "File Path", "info": "Connect a file path from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "file_path", "display_name": "File Path (Direct)", "info": "Path to the document file. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "file_type", "display_name": "File Type", "info": "The type of document file to extract from.", "value": "Auto-Detect", "options": ["Auto-Detect", "PDF", "DOCX", "TXT"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}], "outputs": [{"name": "text_content", "display_name": "Text Content", "output_type": "string", "method": null}, {"name": "page_count", "display_name": "Page Count", "output_type": "int", "method": null}, {"name": "error", "display_name": "Error", "output_type": "string", "method": null}], "is_valid": true, "path": "components.helper.doc_extractor.DocExtractorComponent", "interface_issues": ["Component DocExtractorComponent's build method does not have a deprecation warning."]}, "IDGeneratorComponent": {"name": "IDGeneratorComponent", "display_name": "ID Generator", "description": "Generates various types of unique identifiers (UUID, timestamp, short ID).", "category": "Helpers", "icon": "Fingerprint", "beta": false, "requires_approval": false, "inputs": [{"name": "id_type", "display_name": "ID Type", "info": "The type of unique identifier to generate.", "value": "UUIDv4", "options": ["UUIDv4", "Timestamp ID", "Short ID"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "short_id_length", "display_name": "Short ID Length", "info": "The length of the short ID (only used when ID Type is 'Short ID').", "value": 8, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "id_type", "field_value": "Short ID", "operator": "equals"}], "visibility_logic": "OR", "input_type": "int"}], "outputs": [{"name": "unique_id", "display_name": "Unique ID", "output_type": "string", "method": null}, {"name": "error", "display_name": "Error", "output_type": "string", "method": null}], "is_valid": true, "path": "components.helper.id_generator.IDGeneratorComponent", "interface_issues": ["Component IDGeneratorComponent's build method does not have a deprecation warning."]}}, "IO": {"InputNode": {"name": "InputNode", "display_name": "Workflow Input", "description": "Defines an input parameter for the workflow.", "category": "IO", "icon": "LogIn", "beta": false, "requires_approval": false, "inputs": [{"name": "input_name", "display_name": "Input Name", "info": "The variable name for this input (for documentation/UI).", "value": "input_data", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": true, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "data_type", "display_name": "Data Type", "info": "The expected data type for this input.", "value": "string", "options": ["Any", "bool", "dict", "float", "int", "json", "list", "string"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": true, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "default_value_str", "display_name": "Default/Test Value", "info": "Enter the value. Use JSON format for lists/dicts.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "multiline"}, {"name": "input_description", "display_name": "Description", "info": "Optional description of what this input represents.", "value": "", "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}], "outputs": [{"name": "output", "display_name": "Output", "output_type": "Any", "method": null}], "is_valid": true, "path": "components.io.input_node.InputNode", "interface_issues": ["Component InputNode's build method does not have a deprecation warning."]}, "OutputNode": {"name": "OutputNode", "display_name": "Workflow Output", "description": "Defines an output or result of the workflow.", "category": "IO", "icon": "LogOut", "beta": false, "requires_approval": false, "inputs": [{"name": "data_to_output", "display_name": "Data", "info": "Connect the data source that should be marked as workflow output.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "output_name", "display_name": "Output Name", "info": "The name associated with this specific workflow output result.", "value": "output_data", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": true, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "output_description", "display_name": "Description", "info": "Optional description of what this output represents.", "value": "", "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "final_output", "display_name": "Is Final Output", "info": "Mark if this is considered a primary/final result of the workflow.", "value": true, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}], "outputs": [], "is_valid": true, "path": "components.io.output_node.OutputNode", "interface_issues": ["Component OutputNode's build method does not have a deprecation warning."]}, "StartNode": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "method": null}], "is_valid": true, "path": "components.io.start_node.StartNode", "interface_issues": ["Component StartNode's build method does not have a deprecation warning."]}}, "Processing": {"AlterMetadataComponent": {"name": "AlterMetadataComponent", "display_name": "<PERSON>er Metada<PERSON>", "description": "Modifies metadata dictionary keys.", "category": "Processing", "icon": "Tag", "beta": false, "requires_approval": false, "inputs": [{"name": "input_metadata_handle", "display_name": "Input Metadata", "info": "Connect a metadata dictionary from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["dict", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "input_metadata", "display_name": "Input Metadata (Direct)", "info": "The metadata dictionary to modify. Used if no connection is provided.", "value": {}, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dict"}, {"name": "updates_handle", "display_name": "<PERSON>ada<PERSON> Updates", "info": "Connect a dictionary of updates from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["dict"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "updates", "display_name": "<PERSON>ada<PERSON> Updates (Direct)", "info": "Dictionary of key-value pairs to update or add to the metadata.", "value": {}, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dict"}, {"name": "keys_to_remove_handle", "display_name": "Keys to Remove", "info": "Connect a list of keys to remove from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["list"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "keys_to_remove", "display_name": "Keys to Remove (Direct)", "info": "List of keys to remove from the metadata.", "value": [], "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": true, "visibility_rules": null, "visibility_logic": "OR", "input_type": "list"}], "outputs": [{"name": "output_metadata", "display_name": "Updated Metadata", "output_type": "dict", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.alter_metadata.AlterMetadataComponent", "interface_issues": ["Component AlterMetadataComponent's build method does not have a deprecation warning."]}, "CombineTextComponent": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs or a list with a separator.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The list of strings or a single string to combine. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["list", "string", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "additional_texts", "display_name": "Additional Texts", "info": "Add as many additional text entries as needed. Each entry can be edited directly.", "value": [], "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": true, "visibility_rules": null, "visibility_logic": "OR", "input_type": "list"}, {"name": "additional_texts_from_connection", "display_name": "Additional Texts (From Connection)", "info": "Additional list of strings to combine. Can be connected from another node or entered directly.", "value": [], "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["list", "Any"], "required": false, "is_handle": true, "is_list": true, "visibility_rules": null, "visibility_logic": "OR", "input_type": "list"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with.", "value": "\\n", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}], "outputs": [{"name": "output_text", "display_name": "Combined Text", "output_type": "string", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.combine_text.CombineTextComponent", "interface_issues": []}, "ConditionalProcessor": {"name": "ConditionalProcessor", "display_name": "Conditional Processor", "description": "Processes input differently based on the selected mode.", "category": "Processing", "icon": "SwitchCamera", "beta": false, "requires_approval": false, "inputs": [{"name": "mode", "display_name": "Processing Mode", "info": "Select the processing mode.", "value": "Text", "options": ["Text", "Number", "Boolean", "JSON"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "text_input", "display_name": "Text Input", "info": "Input text to process.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "Text", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "text_operation", "display_name": "Text Operation", "info": "Operation to perform on the text.", "value": "Uppercase", "options": ["Uppercase", "Lowercase", "Capitalize", "Reverse"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "Text", "operator": "equals"}], "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "number_input", "display_name": "Number Input", "info": "Input number to process.", "value": 0, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "Number", "operator": "equals"}], "visibility_logic": "OR", "input_type": "int"}, {"name": "number_operation", "display_name": "Number Operation", "info": "Operation to perform on the number.", "value": "Square", "options": ["Square", "C<PERSON>", "Double", "Half"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "Number", "operator": "equals"}], "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "boolean_input", "display_name": "Boolean Input", "info": "Input boolean to process.", "value": true, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "Boolean", "operator": "equals"}], "visibility_logic": "OR", "input_type": "bool"}, {"name": "boolean_operation", "display_name": "Boolean Operation", "info": "Operation to perform on the boolean.", "value": "Negate", "options": ["Negate", "AND with True", "OR with False", "XOR with True"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "Boolean", "operator": "equals"}], "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "json_input", "display_name": "JSON Input", "info": "Input JSON to process.", "value": "{}", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "JSON", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "json_operation", "display_name": "JSON Operation", "info": "Operation to perform on the JSON.", "value": "Pretty Print", "options": ["Pretty Print", "<PERSON>", "Extract Values", "Add Timestamp"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "JSON", "operator": "equals"}], "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "text_input_handle", "display_name": "Text Input", "info": "Connect text input.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "Text", "operator": "equals"}], "visibility_logic": "OR", "input_type": "handle"}, {"name": "number_input_handle", "display_name": "Number Input", "info": "Connect number input.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["int", "float"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "Number", "operator": "equals"}], "visibility_logic": "OR", "input_type": "handle"}, {"name": "boolean_input_handle", "display_name": "Boolean Input", "info": "Connect boolean input.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["bool"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "Boolean", "operator": "equals"}], "visibility_logic": "OR", "input_type": "handle"}, {"name": "json_input_handle", "display_name": "JSON Input", "info": "Connect JSON input.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["dict", "string"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "mode", "field_value": "JSON", "operator": "equals"}], "visibility_logic": "OR", "input_type": "handle"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any", "method": null}, {"name": "error", "display_name": "Error", "output_type": "string", "method": null}], "is_valid": true, "path": "components.processing.conditional_processor.ConditionalProcessor", "interface_issues": []}, "DataToDataFrameComponent": {"name": "DataToDataFrameComponent", "display_name": "Data to DataFrame", "description": "Converts data to a Pandas DataFrame.", "category": "Processing", "icon": "Table", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data_handle", "display_name": "Input Data", "info": "Connect data to convert to a DataFrame.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["list", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "orientation", "display_name": "Data Orientation", "info": "The orientation of the input data.", "value": "Auto-Detect", "options": ["Records (List of Dicts)", "Columns (Dict of Lists)", "Auto-Detect"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}], "outputs": [{"name": "output_dataframe", "display_name": "DataFrame", "output_type": "DataFrame", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.data_to_dataframe.DataToDataFrameComponent", "interface_issues": ["Component DataToDataFrameComponent's build method does not have a deprecation warning."]}, "DynamicCombineTextComponent": {"name": "DynamicCombineTextComponent", "display_name": "Dynamic Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "value": 2, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "int"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with.", "value": "\\n", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": 1, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 2, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 3, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 4, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 5, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 6, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 7, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 8, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 9, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 10, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": 2, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 3, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 4, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 5, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 6, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 7, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 8, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 9, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 10, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": 3, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 4, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 5, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 6, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 7, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 8, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 9, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 10, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": 4, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 5, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 6, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 7, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 8, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 9, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 10, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": 5, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 6, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 7, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 8, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 9, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 10, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": 6, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 7, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 8, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 9, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 10, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": 7, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 8, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 9, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 10, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": 8, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 9, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 10, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": 9, "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": 10, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": 10, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}], "outputs": [{"name": "output_text", "display_name": "Combined Text", "output_type": "string", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.dynamic_combine_text.DynamicCombineTextComponent", "interface_issues": ["Component DynamicCombineTextComponent's build method does not have a deprecation warning."]}, "DynamicFormProcessor": {"name": "DynamicFormProcessor", "display_name": "Dynamic Form Processor", "description": "Processes a dynamic form with validation.", "category": "Processing", "icon": "FormInput", "beta": false, "requires_approval": false, "inputs": [{"name": "form_type", "display_name": "Form Type", "info": "Type of form to process.", "value": "Contact", "options": ["Contact", "Survey", "Registration", "Custom"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "num_fields", "display_name": "Number of Fields", "info": "Number of form fields to show.", "value": 3, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "int"}, {"name": "enable_validation", "display_name": "Enable Validation", "info": "Whether to validate form inputs.", "value": true, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}, {"name": "form_field", "display_name": "Form Field", "info": "Connect form fields to process.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "int", "float", "bool", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dynamic_handle", "min_handles": 1, "max_handles": 10, "default_handles": 3, "base_name": "field", "base_display_name": "Field", "allow_direct_input": true, "show_add_button": true, "show_remove_button": true}, {"name": "field_1_label", "display_name": "Name Field Label", "info": "Label for the first field.", "value": "Name", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "form_type", "field_value": "Contact", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "field_2_label", "display_name": "Email Field Label", "info": "Label for the second field.", "value": "Email", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "form_type", "field_value": "Contact", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "field_3_label", "display_name": "Message Field Label", "info": "Label for the third field.", "value": "Message", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "form_type", "field_value": "Contact", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "field_1_validation", "display_name": "Name Validation", "info": "Regex pattern for name validation.", "value": "^[a-zA-Z ]{2,50}$", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "form_type", "field_value": "Contact", "operator": "equals"}, {"field_name": "enable_validation", "field_value": true, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "field_2_validation", "display_name": "Email Validation", "info": "Regex pattern for email validation.", "value": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "form_type", "field_value": "Contact", "operator": "equals"}, {"field_name": "enable_validation", "field_value": true, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "field_3_validation", "display_name": "Message Validation", "info": "Regex pattern for message validation.", "value": "^.{10,500}$", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "form_type", "field_value": "Contact", "operator": "equals"}, {"field_name": "enable_validation", "field_value": true, "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}], "outputs": [{"name": "processed_form", "display_name": "Processed Form", "output_type": "dict", "method": null}, {"name": "validation_errors", "display_name": "Validation Errors", "output_type": "dict", "method": null}, {"name": "is_valid", "display_name": "Is <PERSON>", "output_type": "bool", "method": null}], "is_valid": true, "path": "components.processing.dynamic_form_processor.DynamicFormProcessor", "interface_issues": []}, "DynamicInputProcessor": {"name": "DynamicInputProcessor", "display_name": "Dynamic Input Processor", "description": "Processes a variable number of inputs with dynamic handles.", "category": "Processing", "icon": "Layers", "beta": false, "requires_approval": false, "inputs": [{"name": "operation", "display_name": "Operation", "info": "Operation to perform on the inputs.", "value": "Concatenate", "options": ["Concatenate", "Sum", "Average", "Min", "Max"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "num_handles", "display_name": "Number of Inputs", "info": "Number of input handles to show.", "value": 2, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "int"}, {"name": "separator", "display_name": "Separator", "info": "Separator to use when concatenating strings.", "value": " ", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": [{"field_name": "operation", "field_value": "Concatenate", "operator": "equals"}], "visibility_logic": "OR", "input_type": "string"}, {"name": "dynamic_input", "display_name": "Dynamic Input", "info": "Connect inputs to process.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "int", "float", "Any"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dynamic_handle", "min_handles": 1, "max_handles": 10, "default_handles": 2, "base_name": "input", "base_display_name": "Input", "allow_direct_input": true, "show_add_button": true, "show_remove_button": true}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any", "method": null}, {"name": "error", "display_name": "Error", "output_type": "string", "method": null}], "is_valid": true, "path": "components.processing.dynamic_input_processor.DynamicInputProcessor", "interface_issues": []}, "ExtractDataKeyComponent": {"name": "ExtractDataKeyComponent", "display_name": "Extract Data Key", "description": "Retrieves a value from a dictionary by key.", "category": "Processing", "icon": "Key", "beta": false, "requires_approval": false, "inputs": [{"name": "input_dict_handle", "display_name": "Input Dictionary", "info": "Connect a dictionary from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["dict", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "key_handle", "display_name": "Key Name", "info": "Connect a string containing the key name.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "key", "display_name": "Key Name (Direct)", "info": "The key to extract from the dictionary. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "error_if_missing", "display_name": "Error if Missing", "info": "If enabled, returns an error if the key is not found. Otherwise, returns None.", "value": false, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}], "outputs": [{"name": "output_value", "display_name": "Value", "output_type": "Any", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.extract_data_key.ExtractDataKeyComponent", "interface_issues": ["Component ExtractDataKeyComponent's build method does not have a deprecation warning."]}, "InputValidator": {"name": "InputValidator", "display_name": "Input Validator", "description": "Validates various types of inputs with visual feedback.", "category": "Processing", "icon": "CheckCircle", "beta": false, "requires_approval": false, "inputs": [{"name": "string_input", "display_name": "String Input", "info": "String input with validation (min length: 3, max length: 50).", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "email_input", "display_name": "Email Input", "info": "Email input with validation.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "int_input", "display_name": "Integer Input", "info": "Integer input with validation (min: 1, max: 100).", "value": 0, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "int"}, {"name": "float_input", "display_name": "Float Input", "info": "Float input with validation (min: 0.0, max: 1.0).", "value": 0.0, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "float"}, {"name": "list_input", "display_name": "List Input", "info": "List input with validation (min items: 1, max items: 5).", "value": [], "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": true, "visibility_rules": null, "visibility_logic": "OR", "input_type": "list"}, {"name": "dict_input", "display_name": "Dictionary Input", "info": "Dictionary input with validation (required keys: 'name', 'value').", "value": {}, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dict"}, {"name": "enable_validation", "display_name": "Enable Validation", "info": "Whether to validate inputs.", "value": true, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}], "outputs": [{"name": "validation_result", "display_name": "Validation Result", "output_type": "dict", "method": null}, {"name": "is_valid", "display_name": "Is <PERSON>", "output_type": "bool", "method": null}, {"name": "validated_inputs", "display_name": "Validated Inputs", "output_type": "dict", "method": null}], "is_valid": true, "path": "components.processing.input_validator.InputValidator", "interface_issues": []}, "MergeDataComponent": {"name": "MergeDataComponent", "display_name": "Merge Data", "description": "Combines dictionaries or lists.", "category": "Processing", "icon": "Combine", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data_1_handle", "display_name": "Input 1", "info": "Connect the first data structure to merge.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["dict", "list", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "input_data_2_handle", "display_name": "Input 2", "info": "Connect the second data structure to merge.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["dict", "list", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "merge_strategy", "display_name": "Merge Strategy (Dicts)", "info": "How to handle conflicts when merging dictionaries.", "value": "Overwrite", "options": ["Overwrite", "Deep Merge", "Error on Conflict"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}], "outputs": [{"name": "output_data", "display_name": "Merged Data", "output_type": "Any", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.merge_data.MergeDataComponent", "interface_issues": ["Component MergeDataComponent's build method does not have a deprecation warning."]}, "MessageToDataComponent": {"name": "MessageToDataComponent", "display_name": "Message To Data", "description": "Extracts fields from a Message object.", "category": "Processing", "icon": "Package", "beta": false, "requires_approval": false, "inputs": [{"name": "input_message_handle", "display_name": "Input Message", "info": "Connect a Message object from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["Message", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "fields_to_extract_handle", "display_name": "Fields to Extract", "info": "Connect a list of field names to extract.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["list"], "required": false, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "fields_to_extract", "display_name": "Fields to Extract (Direct)", "info": "List of field names to extract from the message. Leave empty to extract all fields.", "value": [], "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": true, "visibility_rules": null, "visibility_logic": "OR", "input_type": "list"}], "outputs": [{"name": "output_data", "display_name": "Extracted Data", "output_type": "dict", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.message_to_data.MessageToDataComponent", "interface_issues": ["Component MessageToDataComponent's build method does not have a deprecation warning."]}, "ParseJSONDataComponent": {"name": "ParseJSONDataComponent", "display_name": "Parse JSON", "description": "Converts JSON string to Python object.", "category": "Processing", "icon": "Braces", "beta": false, "requires_approval": false, "inputs": [{"name": "input_json_string", "display_name": "JSON String", "info": "The JSON string to parse. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}], "outputs": [{"name": "output_data", "display_name": "Parsed Data", "output_type": "Any", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.parse_json_data.ParseJSONDataComponent", "interface_issues": []}, "SaveToFileComponent": {"name": "SaveToFileComponent", "display_name": "Save To File", "description": "Writes data to a file (text or JSON).", "category": "Processing", "icon": "Save", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data_handle", "display_name": "Data to Save", "info": "Connect data from another node to save to a file.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "dict", "list", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "file_path_handle", "display_name": "File Path", "info": "Connect a string containing the file path.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "file_path", "display_name": "File Path (Direct)", "info": "The path where the file will be saved. Used if no connection is provided.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "overwrite", "display_name": "Overwrite", "info": "If enabled, overwrites existing files. If disabled, returns an error if the file exists.", "value": true, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}, {"name": "encoding", "display_name": "Encoding", "info": "The character encoding to use when writing the file.", "value": "utf-8", "options": ["utf-8", "latin-1", "ascii"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "create_dirs", "display_name": "Create Directories", "info": "If enabled, creates any missing directories in the file path.", "value": true, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}], "outputs": [{"name": "file_path_out", "display_name": "File Path Saved", "output_type": "string", "method": null}, {"name": "success", "display_name": "Success", "output_type": "bool", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.save_to_file.SaveToFileComponent", "interface_issues": ["Component SaveToFileComponent's build method does not have a deprecation warning."]}, "SelectDataComponent": {"name": "SelectDataComponent", "display_name": "Select Data", "description": "Extracts elements from lists or dictionaries.", "category": "Processing", "icon": "Filter", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The data to select from. Can be connected from another node or entered directly.", "value": {}, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["list", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dict"}, {"name": "data_type", "display_name": "Data Type", "info": "The type of data structure to select from.", "value": "Auto-Detect", "options": ["Auto-Detect", "List", "Dictionary"], "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dropdown"}, {"name": "selector", "display_name": "Selector", "info": "For lists: index or slice (e.g., '0', '1:5'). For dicts: key name or path (e.g., 'user.name'). Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}], "outputs": [{"name": "output_data", "display_name": "Selected Data", "output_type": "Any", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.select_data.SelectDataComponent", "interface_issues": []}, "SplitTextComponent": {"name": "SplitTextComponent", "display_name": "Split Text", "description": "Splits text into a list using a delimiter.", "category": "Processing", "icon": "Scissors", "beta": false, "requires_approval": false, "inputs": [{"name": "input_text", "display_name": "Input Text", "info": "The text to split. Can be connected from another node or entered directly.", "value": "", "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "delimiter", "display_name": "Delimiter", "info": "The character or string to split the text by.", "value": ",", "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "string"}, {"name": "max_splits", "display_name": "Max Splits", "info": "Maximum number of splits to perform. -1 means no limit.", "value": -1, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "int"}, {"name": "include_delimiter", "display_name": "Include Delimiter", "info": "If enabled, the delimiter will be included at the end of each split part (except the last one).", "value": false, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}], "outputs": [{"name": "output_list", "display_name": "Split List", "output_type": "list", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.split_text.SplitTextComponent", "interface_issues": ["Component SplitTextComponent's build method does not have a deprecation warning."]}, "UpdateDataComponent": {"name": "UpdateDataComponent", "display_name": "Update Data", "description": "Modifies a dictionary with specified updates.", "category": "Processing", "icon": "Edit", "beta": false, "requires_approval": false, "inputs": [{"name": "input_dict_handle", "display_name": "Input Dictionary", "info": "Connect a dictionary from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["dict", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "input_dict", "display_name": "Input Dictionary (Direct)", "info": "The dictionary to be updated. Enter as JSON. Used if no connection is provided.", "value": {}, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dict"}, {"name": "updates_handle", "display_name": "Updates", "info": "Connect updates from another node.", "value": null, "options": null, "real_time_refresh": false, "advanced": false, "input_types": ["dict", "Any"], "required": true, "is_handle": true, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "handle"}, {"name": "updates", "display_name": "Updates (Direct)", "info": "The updates to apply to the input dictionary. Enter as JSON. Used if no connection is provided.", "value": {}, "options": null, "real_time_refresh": false, "advanced": false, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "dict"}, {"name": "deep_update", "display_name": "Deep Update", "info": "If enabled, performs a deep merge of nested dictionaries. Otherwise, performs a shallow merge.", "value": false, "options": null, "real_time_refresh": false, "advanced": true, "input_types": null, "required": false, "is_handle": false, "is_list": false, "visibility_rules": null, "visibility_logic": "OR", "input_type": "bool"}], "outputs": [{"name": "output_dict", "display_name": "Updated Dictionary", "output_type": "dict", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.update_data.UpdateDataComponent", "interface_issues": ["Component UpdateDataComponent's build method does not have a deprecation warning."]}}, "MCP Marketplace": {"mcp_generate-script_script_generate": {"name": "mcp_generate-script_script_generate", "display_name": "Script Generator: Script Generate", "description": "Generate a video script based on topic and keywords", "category": "MCP Marketplace", "icon": "ServerCog", "beta": true, "inputs": [{"name": "topic", "display_name": "Topic", "info": "The topic of the video to be covered", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}, {"name": "script_type", "display_name": "Script Type", "info": "The type of script", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}, {"name": "keywords", "display_name": "Keywords", "info": "Keywords for the script", "input_type": "dict", "input_types": ["dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}}, {"name": "video_type", "display_name": "Video Type", "info": "The type of video", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}, {"name": "link", "display_name": "Link", "info": "Optional link for the script", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}], "outputs": [{"name": "title", "display_name": "Title", "output_type": "string", "method": null}, {"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "output_type": "string", "method": null}, {"name": "script_type", "display_name": "Script Type", "output_type": "string", "method": null}, {"name": "video_type", "display_name": "Video Type", "output_type": "string", "method": null}, {"name": "link", "display_name": "Link", "output_type": "string", "method": null}], "is_valid": true, "config": {}, "type": "MCPMarketplaceComponent", "mcp_info": {"server_id": "generate-script", "server_path": "https://script-generation-mcp-624209391722.us-central1.run.app/sse", "tool_name": "script_generate", "tool_id": 1, "endpoint": "/script", "input_schema": {"type": "object", "properties": {"topic": {"type": "string", "description": "The topic of the video to be covered"}, "script_type": {"type": "string", "description": "The type of script"}, "keywords": {"type": "object", "description": "Keywords for the script", "properties": {"time": {"type": "string", "description": "Time for the script"}, "objective": {"type": "string", "description": "Objective of the script"}, "audience": {"type": "string", "description": "Audience for the script"}, "gender": {"type": "string", "description": "Gender for the script"}, "tone": {"type": "string", "description": "Tone of the script"}, "speakers": {"type": "string", "description": "Speaker in the script"}}}, "video_type": {"type": "string", "description": "The type of video"}, "link": {"type": "string", "description": "Optional link for the script"}}, "required": ["topic", "script_type", "keywords", "video_type"]}, "output_schema": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the generated script"}, "script": {"type": "string", "description": "The generated script"}, "script_type": {"type": "string", "description": "Type of the script"}, "video_type": {"type": "string", "description": "The type of video"}, "link": {"type": "string", "description": "Optional link for the script"}}, "required": []}}, "path": "mcp_marketplace.mcp_generate-script_script_generate"}, "mcp_generate-audio_generate_audio": {"name": "mcp_generate-audio_generate_audio", "display_name": "Audio Generator: Generate Audio", "description": "Generate audio from a script using text-to-speech", "category": "MCP Marketplace", "icon": "ServerCog", "beta": true, "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "the script to generate voice", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}, {"name": "voice_id", "display_name": "Voice Id", "info": "the play ht voice id to generate voice", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}], "outputs": [{"name": "audio_ids", "display_name": "Audio Ids", "output_type": "list", "method": null}, {"name": "voice_id", "display_name": "Voice Id", "output_type": "string", "method": null}, {"name": "audio_script", "display_name": "Audio Script", "output_type": "string", "method": null}], "is_valid": true, "config": {}, "type": "MCPMarketplaceComponent", "mcp_info": {"server_id": "generate-audio", "server_path": "http://localhost:5002/sse", "tool_name": "generate_audio", "tool_id": 2, "endpoint": "/audio", "input_schema": {"type": "object", "properties": {"script": {"type": "string", "description": "the script to generate voice"}, "voice_id": {"type": "string", "description": "the play ht voice id to generate voice"}}, "required": ["script", "voice_id"]}, "output_schema": {"type": "object", "properties": {"audio_ids": {"type": "array", "description": "List of generated audio IDs", "items": {"type": "string"}}, "voice_id": {"type": "string", "description": "Identifier for the voice used in audio generation"}, "audio_script": {"type": "string", "description": "<PERSON><PERSON><PERSON> used to generate the audio"}}, "required": []}}, "path": "mcp_marketplace.mcp_generate-audio_generate_audio"}, "mcp_generate-audio_fetch_audio": {"name": "mcp_generate-audio_fetch_audio", "display_name": "Audio Generator: Fetch Audio", "description": "Fetch generated audio files by their IDs", "category": "MCP Marketplace", "icon": "ServerCog", "beta": true, "inputs": [{"name": "audio_ids", "display_name": "Audio Ids", "info": "An array of audio IDs representing the script to generate voice", "input_type": "list", "input_types": ["list", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": []}], "outputs": [{"name": "audio_urls", "display_name": "Audio Urls", "output_type": "list", "method": null}, {"name": "mimetype", "display_name": "Mimetype", "output_type": "string", "method": null}], "is_valid": true, "config": {}, "type": "MCPMarketplaceComponent", "mcp_info": {"server_id": "generate-audio", "server_path": "http://localhost:5002/sse", "tool_name": "fetch_audio", "tool_id": 3, "endpoint": "/audio", "input_schema": {"type": "object", "properties": {"audio_ids": {"type": "array", "description": "An array of audio IDs representing the script to generate voice", "items": {"type": "string"}}}, "required": ["audio_ids"]}, "output_schema": {"type": "object", "properties": {"audio_urls": {"type": "array", "description": "generated audio links", "items": {"type": "string"}}, "mimetype": {"type": "string", "description": "generated audio file mimetype"}}, "required": []}}, "path": "mcp_marketplace.mcp_generate-audio_fetch_audio"}, "mcp_generate-subtitle_generate_subtitle": {"name": "mcp_generate-subtitle_generate_subtitle", "display_name": "Subtitle Generator: Generate Subtitle", "description": "Generate subtitles from script and audio files", "category": "MCP Marketplace", "icon": "ServerCog", "beta": true, "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "the script to generate subtitle", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}, {"name": "audio_urls", "display_name": "Audio Urls", "info": "An array of audio urls representing the script to generate voice", "input_type": "list", "input_types": ["list", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": []}], "outputs": [{"name": "subtitle", "display_name": "Subtitle", "output_type": "string", "method": null}], "is_valid": true, "config": {}, "type": "MCPMarketplaceComponent", "mcp_info": {"server_id": "generate-subtitle", "server_path": "http://localhost:5003/sse", "tool_name": "generate_subtitle", "tool_id": 4, "endpoint": "/content", "input_schema": {"type": "object", "properties": {"script": {"type": "string", "description": "the script to generate subtitle"}, "audio_urls": {"type": "array", "description": "An array of audio urls representing the script to generate voice", "items": {"type": "string"}}}, "required": ["script", "audio_urls"]}, "output_schema": {"type": "object", "properties": {"subtitle": {"type": "string", "description": "generated subtitle of the audio"}}, "required": []}}, "path": "mcp_marketplace.mcp_generate-subtitle_generate_subtitle"}, "mcp_generate-stock-video_generate_stock_video": {"name": "mcp_generate-stock-video_generate_stock_video", "display_name": "Stock Video Generator: Generate Stock Video", "description": "Generate stock video clips based on script content", "category": "MCP Marketplace", "icon": "ServerCog", "beta": true, "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "the subtitle script of the video", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}], "outputs": [{"name": "stock_video_clips", "display_name": "Stock Video Clips", "output_type": "list", "method": null}], "is_valid": true, "config": {}, "type": "MCPMarketplaceComponent", "mcp_info": {"server_id": "generate-stock-video", "server_path": "http://localhost:5004/sse", "tool_name": "generate_stock_video", "tool_id": 5, "endpoint": "/stock-video", "input_schema": {"type": "object", "properties": {"script": {"type": "string", "description": "the subtitle script of the video"}}, "required": ["script"]}, "output_schema": {"type": "object", "properties": {"stock_video_clips": {"type": "array", "description": "List of stock video clips", "items": {"type": "object", "properties": {"at_time": {"type": "number", "description": "Time at which the video clip starts"}, "url": {"type": "string", "description": "URL of the stock video clip"}, "search_terms": {"type": "array", "items": {"type": "string"}, "description": "list of search terms"}, "mimetype": {"type": "string", "description": "mimetype of the stock video clip"}}}}}, "required": []}}, "path": "mcp_marketplace.mcp_generate-stock-video_generate_stock_video"}, "mcp_generate-stock-image_generate_ai_stock_image": {"name": "mcp_generate-stock-image_generate_ai_stock_image", "display_name": "Stock Image Generator: Generate Ai Stock Image", "description": "Generate AI-created stock images based on script content", "category": "MCP Marketplace", "icon": "ServerCog", "beta": true, "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "the subtitle script of the video", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}, {"name": "view_type", "display_name": "View Type", "info": "The view type for the video (enumerated value)", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}], "outputs": [{"name": "stock_image_clips", "display_name": "Stock Image Clips", "output_type": "list", "method": null}], "is_valid": true, "config": {}, "type": "MCPMarketplaceComponent", "mcp_info": {"server_id": "generate-stock-image", "server_path": "http://localhost:5005/sse", "tool_name": "generate_ai_stock_image", "tool_id": 6, "endpoint": "/stock-image", "input_schema": {"type": "object", "properties": {"script": {"type": "string", "description": "the subtitle script of the video"}, "view_type": {"type": "string", "description": "The view type for the video (enumerated value)"}}, "required": ["script", "view_type"]}, "output_schema": {"type": "object", "properties": {"stock_image_clips": {"type": "array", "description": "List of stock video clips", "items": {"type": "object", "properties": {"at_time": {"type": "number", "description": "Time at which the image clip starts"}, "url": {"type": "string", "description": "URL of the image"}, "prompt": {"type": "string", "description": "prompt for the image"}, "mimetype": {"type": "string", "description": "mimetype of the image clip"}}}}}, "required": []}}, "path": "mcp_marketplace.mcp_generate-stock-image_generate_ai_stock_image"}, "mcp_generate-video_generate_video": {"name": "mcp_generate-video_generate_video", "display_name": "Video Generator: Generate Video", "description": "Generate a complete video from stock clips, images, audio, and subtitles", "category": "MCP Marketplace", "icon": "ServerCog", "beta": true, "inputs": [{"name": "view_type", "display_name": "View Type", "info": "The view type for the video (enumerated value)", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}, {"name": "stock_video_clips", "display_name": "Stock Video Clips", "info": "List of stock video clips", "input_type": "list", "input_types": ["list", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": []}, {"name": "stock_image_clips", "display_name": "Stock Image Clips", "info": "List of stock image clips", "input_type": "list", "input_types": ["list", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": []}, {"name": "event_stock_clips", "display_name": "Event Stock Clips", "info": "List of event stock clips", "input_type": "list", "input_types": ["list", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": []}, {"name": "audio_urls", "display_name": "Audio Urls", "info": "List of audio URLs (must contain at least one URL)", "input_type": "list", "input_types": ["list", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": []}, {"name": "avatar_video_ids", "display_name": "Avatar Video Ids", "info": "Optional list of avatar video IDs (alphanumeric values)", "input_type": "list", "input_types": ["list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": []}, {"name": "subtitles", "display_name": "Subtitles", "info": "Subtitle script of the video", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ""}], "outputs": [{"name": "thumbnail", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "output_type": "dict", "method": null}, {"name": "video_link", "display_name": "Video Link", "output_type": "dict", "method": null}, {"name": "duration", "display_name": "Duration", "output_type": "float", "method": null}], "is_valid": true, "config": {}, "type": "MCPMarketplaceComponent", "mcp_info": {"server_id": "generate-video", "server_path": "https://video-generation-mcp-624209391722.us-central1.run.app/sse", "tool_name": "generate_video", "tool_id": 7, "endpoint": "/generate-video", "input_schema": {"type": "object", "properties": {"view_type": {"type": "string", "description": "The view type for the video (enumerated value)"}, "stock_video_clips": {"type": "array", "description": "List of stock video clips", "items": {"type": "object", "properties": {"at_time": {"type": "number", "minimum": 0, "description": "Time at which the video clip starts"}, "url": {"type": "string", "format": "uri", "description": "URL of the stock video clip"}}, "required": ["at_time", "url"]}}, "stock_image_clips": {"type": "array", "description": "List of stock image clips", "items": {"type": "object", "properties": {"at_time": {"type": "number", "minimum": 0, "description": "Time at which the image clip starts"}, "url": {"type": "string", "format": "uri", "description": "URL of the stock image clip"}}, "required": ["at_time", "url"]}}, "event_stock_clips": {"type": "array", "description": "List of event stock clips", "items": {"type": "object", "properties": {"clip": {"type": "number", "minimum": 0, "description": "Index of the event stock clip"}, "at_time": {"type": "number", "minimum": 0, "description": "Time at which the event clip starts"}, "duration": {"type": "number", "exclusiveMinimum": 0, "description": "Duration of the event clip"}}, "required": ["clip", "at_time", "duration"]}}, "audio_urls": {"type": "array", "description": "List of audio URLs (must contain at least one URL)", "items": {"type": "string", "format": "uri"}}, "avatar_video_ids": {"type": "array", "description": "Optional list of avatar video IDs (alphanumeric values)", "items": {"type": "string"}}, "subtitles": {"type": "string", "description": "Subtitle script of the video"}}, "required": ["view_type", "stock_video_clips", "stock_image_clips", "event_stock_clips", "audio_urls", "subtitles"]}, "output_schema": {"type": "object", "properties": {"thumbnail": {"type": "object", "description": "", "properties": {"url": {"type": "string", "description": "URL of the thumbnail"}, "mimetype": {"type": "string", "description": "MIME type of the thumbnail"}}}, "video_link": {"type": "object", "description": "", "properties": {"url": {"type": "string", "description": "URL of the video"}, "mimetype": {"type": "string", "description": "MIME type of the video"}}}, "duration": {"type": "number", "description": "Duration of the video"}}, "required": []}}, "path": "mcp_marketplace.mcp_generate-video_generate_video"}}}