# AlterMetadataComponent Testing Guide

## 🧪 **Complete Testing Examples**

This guide provides comprehensive examples of how to test the AlterMetadataComponent across different scenarios and environments.

## **Quick Start Testing**

### **1. Run All Tests at Once**

```bash
# Run manual tests
python testing_examples/manual_testing_guide.py

# Run integration tests  
python testing_examples/integration_testing_examples.py

# Run node executor tests
python testing_examples/node_executor_testing_examples.py

# Run validation
python validate_component_registration.py

# Run demonstration
python demo_alter_metadata_workflow.py
```

## **Testing Scenarios**

### **📋 Basic Functionality Tests**

#### **Test 1: Simple Metadata Update**
```python
test_data = {
    "input_metadata": {
        "document_id": "DOC-001",
        "title": "Test Document", 
        "version": "1.0"
    },
    "updates": {
        "version": "2.0",
        "status": "published"
    },
    "keys_to_remove": []
}

# Expected Result:
{
    "document_id": "DOC-001",
    "title": "Test Document",
    "version": "2.0",        # Updated
    "status": "published"    # Added
}
```

#### **Test 2: Key Removal**
```python
test_data = {
    "input_metadata": {
        "name": "Product A",
        "price": 99.99,
        "deprecated_field": "old_value",
        "temp_id": "TEMP-123"
    },
    "updates": {},
    "keys_to_remove": ["deprecated_field", "temp_id"]
}

# Expected Result:
{
    "name": "Product A",
    "price": 99.99
    # deprecated_field and temp_id removed
}
```

#### **Test 3: Combined Operations**
```python
test_data = {
    "input_metadata": {
        "user_id": "USER-123",
        "status": "inactive",
        "temp_session": "session_456"
    },
    "updates": {
        "status": "active",
        "last_login": "2024-01-15"
    },
    "keys_to_remove": ["temp_session"]
}

# Expected Result:
{
    "user_id": "USER-123",
    "status": "active",      # Updated
    "last_login": "2024-01-15"  # Added
    # temp_session removed
}
```

### **🚨 Error Handling Tests**

#### **Test 4: Missing Input Metadata**
```python
test_data = {
    "updates": {"version": "2.0"},
    "keys_to_remove": []
    # input_metadata missing
}

# Expected: Error - "Input metadata is missing"
```

#### **Test 5: Invalid Data Types**
```python
# Invalid input_metadata type
test_data = {
    "input_metadata": "not_a_dict",  # Should be dict
    "updates": {},
    "keys_to_remove": []
}

# Expected: Error - "Input metadata must be a dictionary"
```

### **⚡ Performance Tests**

#### **Test 6: Large Dataset**
```python
# Generate large metadata (1000+ fields)
large_metadata = {f"field_{i}": f"value_{i}" for i in range(1000)}
large_updates = {f"update_{i}": f"new_value_{i}" for i in range(100)}

test_data = {
    "input_metadata": large_metadata,
    "updates": large_updates,
    "keys_to_remove": ["field_999"]
}

# Expected: Completes in < 1 second
```

### **🔄 Integration Tests**

#### **Test 7: Document Processing Workflow**
```python
# Simulates real document processing pipeline
initial_metadata = {
    "document_id": "DOC-2024-001",
    "filename": "report.pdf",
    "status": "uploaded",
    "temp_upload_id": "TEMP-12345"
}

processing_updates = {
    "status": "processed",
    "word_count": 2847,
    "analysis_date": "2024-01-15T10:35:00Z"
}

cleanup_fields = ["temp_upload_id"]

# Expected: Document metadata properly updated and cleaned
```

## **Testing Methods**

### **🔧 Method 1: Workflow Service Testing**

```python
import asyncio
from app.components.processing.alter_metadata import AlterMetadataComponent
from app.models.workflow_builder.context import WorkflowContext

async def test_workflow_service():
    component = AlterMetadataComponent()
    context = WorkflowContext(workflow_id="test", execution_id="test")
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_metadata": {"name": "test"},
        "updates": {"version": "2.0"},
        "keys_to_remove": []
    }
    
    result = await component.execute(context)
    print(f"Status: {result.status}")
    print(f"Output: {result.outputs}")

asyncio.run(test_workflow_service())
```

### **🔧 Method 2: Node Executor Service Testing**

```python
import asyncio
from app.components.alter_metadata_component import AlterMetadataComponent

async def test_node_executor():
    component = AlterMetadataComponent()
    
    payload = {
        "request_id": "test_001",
        "tool_parameters": {
            "input_metadata": {"name": "test"},
            "updates": {"version": "2.0"},
            "keys_to_remove": []
        }
    }
    
    # Validate
    validation = await component.validate(payload)
    print(f"Valid: {validation.is_valid}")
    
    # Process
    if validation.is_valid:
        result = await component.process(payload)
        print(f"Result: {result}")

asyncio.run(test_node_executor())
```

### **🔧 Method 3: Legacy Build Method Testing**

```python
from app.components.processing.alter_metadata import AlterMetadataComponent

def test_legacy_method():
    component = AlterMetadataComponent()
    
    result = component.build(
        input_metadata={"name": "test"},
        updates={"version": "2.0"},
        keys_to_remove=[]
    )
    
    print(f"Legacy result: {result}")

test_legacy_method()
```

## **Expected Results**

### **✅ Success Scenarios**

| Test Case | Input | Expected Output |
|-----------|-------|-----------------|
| Basic Update | `{"name": "test"}` + `{"version": "2.0"}` | `{"name": "test", "version": "2.0"}` |
| Key Removal | `{"name": "test", "temp": "x"}` + remove `["temp"]` | `{"name": "test"}` |
| Combined | `{"name": "test", "temp": "x"}` + `{"version": "2.0"}` + remove `["temp"]` | `{"name": "test", "version": "2.0"}` |

### **❌ Error Scenarios**

| Test Case | Input | Expected Error |
|-----------|-------|----------------|
| Missing Input | No `input_metadata` | "Input metadata is missing" |
| Wrong Type | `input_metadata: "string"` | "must be a dictionary" |
| Invalid Updates | `updates: "string"` | "Updates must be a dictionary" |
| Invalid Keys | `keys_to_remove: "string"` | "must be a list" |

## **Performance Benchmarks**

| Dataset Size | Expected Time | Memory Usage |
|--------------|---------------|--------------|
| Small (10 fields) | < 0.001s | Minimal |
| Medium (100 fields) | < 0.01s | Low |
| Large (1000 fields) | < 0.1s | Moderate |
| Very Large (10k fields) | < 1.0s | Higher |

## **Testing Checklist**

### **🔍 Pre-Testing Setup**
- [ ] Both services are running
- [ ] Component is registered in Node Executor Service
- [ ] All dependencies are installed
- [ ] Test data is prepared

### **🧪 Core Functionality**
- [ ] Basic metadata updates work
- [ ] Key removal works correctly
- [ ] Combined operations work
- [ ] Empty operations handle gracefully
- [ ] Large datasets process efficiently

### **🚨 Error Handling**
- [ ] Missing input metadata detected
- [ ] Invalid data types rejected
- [ ] Clear error messages provided
- [ ] Graceful failure handling

### **⚡ Performance**
- [ ] Small datasets: < 0.01s
- [ ] Large datasets: < 1.0s
- [ ] Memory usage reasonable
- [ ] Concurrent requests handled

### **🔄 Integration**
- [ ] Workflow Service execution works
- [ ] Node Executor Service processing works
- [ ] Legacy build method works
- [ ] End-to-end flow successful

### **📊 Validation**
- [ ] Component definition correct
- [ ] Input/output schemas match
- [ ] Dual-purpose inputs functional
- [ ] Visibility rules (if any) work

## **Troubleshooting**

### **Common Issues**

#### **Issue: Component not found**
```bash
# Solution: Check registration
python validate_component_registration.py
```

#### **Issue: Import errors**
```bash
# Solution: Check Python path
export PYTHONPATH=/path/to/workflow-service:/path/to/node-executor-service
```

#### **Issue: Validation fails**
```python
# Solution: Check payload format
payload = {
    "request_id": "test",
    "tool_parameters": {  # Note: tool_parameters wrapper
        "input_metadata": {...},
        "updates": {...},
        "keys_to_remove": [...]
    }
}
```

#### **Issue: Performance problems**
```python
# Solution: Check data size and optimize
if len(input_metadata) > 10000:
    # Consider chunking or optimization
    pass
```

## **Continuous Testing**

### **Automated Testing**
```bash
# Add to CI/CD pipeline
python -m pytest testing_examples/ -v
```

### **Monitoring**
```python
# Add performance monitoring
import time
start_time = time.time()
result = await component.execute(context)
execution_time = time.time() - start_time
assert execution_time < 1.0, f"Performance degraded: {execution_time}s"
```

## **Next Steps**

1. **Run the provided test suites** to verify functionality
2. **Create custom tests** for your specific use cases
3. **Set up automated testing** in your CI/CD pipeline
4. **Monitor performance** in production
5. **Extend tests** as you add new features

The AlterMetadataComponent is thoroughly tested and ready for production use! 🚀
