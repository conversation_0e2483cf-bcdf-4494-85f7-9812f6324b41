import grpc
from typing import List, Optional, Dict, Any
from app.core.config import settings
from app.grpc_ import agent_pb2, agent_pb2_grpc
import json
from fastapi import HTTPException
from google.protobuf.field_mask_pb2 import FieldMask


class AgentServiceClient:
    """
    A gRPC client for interacting with the Agent Service.

    This client provides methods to create, retrieve, update, delete,
    and list agents using gRPC communication.

    Attributes:
        channel (grpc.Channel): The gRPC channel for communication.
        stub (agent_pb2_grpc.AgentServiceStub): The gRPC stub for making requests.
    """

    def __init__(self):
        """
        Initializes the AgentServiceClient with a gRPC insecure channel.
        """
        self.channel = grpc.insecure_channel(
            f"{settings.AGENT_SERVICE_HOST}:{settings.AGENT_SERVICE_PORT}"
        )
        self.stub = agent_pb2_grpc.AgentServiceStub(self.channel)

    def _create_owner_proto(self, user_details: dict):
        return agent_pb2.Owner(
            id=user_details["id"],
            email=user_details["email"],
            full_name=user_details["full_name"],
            fcm_token=user_details.get("fcm_token"),
        )

    def _handle_error(self, e: grpc.RpcError):
        """
        Handle gRPC errors and convert them to appropriate HTTP exceptions.

        Args:
            e: The gRPC error

        Returns:
            HTTPException with appropriate status code and detail
        """
        status_code = e.code()
        details = e.details()

        # Map gRPC status codes to HTTP status codes
        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            raise HTTPException(status_code=412, detail=details)
        elif status_code == grpc.StatusCode.RESOURCE_EXHAUSTED:
            raise HTTPException(status_code=429, detail=details)
        else:
            # For any other error, return 500 Internal Server Error
            raise HTTPException(status_code=500, detail="Internal server error")

    def _get_category_enum(self, category: str) -> int:
        """
        Maps a category string to its corresponding Category enum value.

        Args:
            category (str): The category string (e.g., "engineering", "marketing", etc.).

        Returns:
            int: The corresponding Category enum value. Defaults to GENERAL if not found.
        """
        category_map = {
            "engineering": agent_pb2.Category.ENGINEERING,
            "marketing": agent_pb2.Category.MARKETING,
            "sales": agent_pb2.Category.SALES,
            "customer_support": agent_pb2.Category.CUSTOMER_SUPPORT,
            "human_resources": agent_pb2.Category.HUMAN_RESOURCES,
            "finance": agent_pb2.Category.FINANCE,
            "operations": agent_pb2.Category.OPERATIONS,
            "general": agent_pb2.Category.GENERAL,
        }
        return category_map.get(category.lower(), agent_pb2.Category.GENERAL)

    def _get_visibility_enum(self, visibility: str) -> int:
        visibility_map = {
            "private": agent_pb2.Visibility.PRIVATE,
            "public": agent_pb2.Visibility.PUBLIC,
        }
        return visibility_map.get(visibility.lower(), agent_pb2.Visibility.PRIVATE)

    def _get_owner_type_enum(self, owner_type: str) -> int:
        owner_type_map = {
            "user": agent_pb2.OwnerType.USER,
            "enterprise": agent_pb2.OwnerType.ENTERPRISE,
            "platform": agent_pb2.OwnerType.PLATFORM,
        }
        return owner_type_map.get(owner_type.lower(), agent_pb2.OwnerType.USER)

    def _get_status_enum(self, status: str) -> int:
        status_map = {"active": agent_pb2.Status.ACTIVE, "inactive": agent_pb2.Status.INACTIVE}
        return status_map.get(status.lower(), agent_pb2.Status.ACTIVE)

    def _get_tone_enum(self, tone: str) -> int:
        """Convert tone string to enum value"""
        if not tone:
            return agent_pb2.Tone.PROFESSIONAL

        tone = tone.upper()
        if tone == "PROFESSIONAL":
            return agent_pb2.Tone.PROFESSIONAL
        elif tone == "FRIENDLY":
            return agent_pb2.Tone.FRIENDLY
        elif tone == "CASUAL":
            return agent_pb2.Tone.CASUAL
        elif tone == "FORMAL":
            return agent_pb2.Tone.FORMAL
        elif tone == "ENTHUSIASTIC":
            return agent_pb2.Tone.ENTHUSIASTIC
        else:
            return agent_pb2.Tone.PROFESSIONAL

    def _get_variable_type_proto_enum(self, var_type: str) -> int:

        variable_type_map = {
            "text": agent_pb2.VariableType.TEXT,
            "number": agent_pb2.VariableType.NUMBER,
            "json": agent_pb2.VariableType.JSON,
        }
        return variable_type_map.get(var_type.lower(), agent_pb2.VariableType.TEXT)

    async def createAgent(
        self,
        name: str,
        description: str,
        avatar: str = None,
        owner_type: str = None,
        system_message: str = None,
        model_provider: str = None,
        model_name: str = None,
        model_api_key: str = None,
        workflow_ids: List[str] = None,
        mcp_server_ids: List[str] = None,
        agent_topic_type: str = None,
        subscriptions: str = None,
        visibility: str = None,
        tags: List[str] = None,
        owner_details: dict = None,
        ruh_credentials: bool = False,
        department: str = None,
        organization_id: str = None,
        tone: str = None,
        files: List[str] = None,
        urls: List[str] = None,
        is_a2a: bool = False,
        is_customizable: bool = False,
        capabilities_data: Optional[Dict[str, Any]] = None,
        example_prompts: Optional[List[str]] = None,
        category: Optional[str] = "general",
        variables_data: Optional[List[Dict[str, Any]]] = None,
    ) -> agent_pb2.CreateAgentResponse:
        try:
            owner = self._create_owner_proto(owner_details)

            request = agent_pb2.CreateAgentRequest(
                name=name,
                description=description,
                avatar=avatar,
                owner=owner,
                owner_type=self._get_owner_type_enum(owner_type),
                system_message=system_message,
                model_provider=model_provider,
                model_name=model_name,
                model_api_key=model_api_key,
                workflow_ids=workflow_ids or [],
                mcp_server_ids=mcp_server_ids or [],
                agent_topic_type=agent_topic_type,
                subscriptions=subscriptions,
                visibility=self._get_visibility_enum(visibility),
                tags=tags if tags else [],
                department=department,
                organization_id=organization_id,
                tone=self._get_tone_enum(tone),
                files=files or [],
                urls=urls or [],
                ruh_credentials=ruh_credentials,
                is_a2a=is_a2a,
                is_customizable=is_customizable,
                example_prompts=example_prompts,
                category=self._get_category_enum(category),
            )
            capabilities_data_proto = None
            if capabilities_data:
                capabilities_data_proto = agent_pb2.AgentCapabilitiesData(
                    capabilities_json=(
                        json.dumps(capabilities_data.get("capabilities"))
                        if capabilities_data.get("capabilities")
                        else ""
                    ),
                    input_modes=capabilities_data.get("input_modes") or [],
                    output_modes=capabilities_data.get("output_modes") or [],
                    response_model=capabilities_data.get("response_model") or [],
                )
            if capabilities_data_proto:
                request.agent_capabilities.CopyFrom(capabilities_data_proto)

            print(f"[DEBUG] Variable Data: {variables_data}")
            # --- NEW: Process and add variables_data to the protobuf request ---
            if variables_data:
                proto_variables_list = []
                for var_dict in variables_data:
                    var_name = var_dict.get("name")
                    if not var_name:
                        continue  # Skip variable if name is missing

                    var_description = var_dict.get("description")
                    var_type_py_str = var_dict.get("type")

                    default_val = var_dict.get("default_value")
                    default_val_str_proto = None
                    if default_val is not None:
                        # Serialize complex types (like JSON for default_value) to string
                        if var_type_py_str.lower() == "json" and isinstance(
                            default_val, (dict, list)
                        ):
                            default_val_str_proto = json.dumps(default_val)
                        else:
                            default_val_str_proto = str(default_val)

                    proto_var = agent_pb2.AgentVariableData(name=var_name, type=var_type_py_str)
                    if var_description is not None:
                        proto_var.description = var_description
                    if default_val_str_proto is not None:
                        proto_var.default_value = default_val_str_proto
                    proto_variables_list.append(proto_var)

                request.variables.extend(proto_variables_list)

            response = self.stub.createAgent(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in createAgent: {str(e)}")
            raise self._handle_error(e)

    async def create_agent_avatar(
        self,
        url: str,
        owner_details: dict,
    ) -> agent_pb2.CreateAgentAvatarResponse:
        """
        Create a new agent avatar.

        Args:
            url: The URL of the avatar image in GCS
            owner_details: Details of the owner creating the avatar

        Returns:
            agent_pb2.CreateAgentAvatarResponse: The response containing the created avatar
        """
        try:
            owner = self._create_owner_proto(owner_details)
            request = agent_pb2.CreateAgentAvatarRequest(
                url=url,
                owner=owner,
            )
            response = self.stub.createAgentAvatar(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_agent_avatar(
        self,
        avatar_id: str,
    ) -> agent_pb2.GetAgentAvatarResponse:
        """
        Get an agent avatar by ID.

        Args:
            avatar_id: The ID of the avatar to retrieve

        Returns:
            agent_pb2.GetAgentAvatarResponse: The response containing the avatar details
        """
        try:
            request = agent_pb2.GetAgentAvatarRequest(
                id=avatar_id,
            )
            response = self.stub.getAgentAvatar(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_agent_avatars(
        self,
        page: int = 1,
        limit: int = 10,
    ) -> agent_pb2.ListAgentAvatarsResponse:
        """
        List all agent avatars with pagination.

        Args:
            page: The page number for pagination
            limit: The number of items per page

        Returns:
            agent_pb2.ListAgentAvatarsResponse: The response containing the list of avatars
        """
        try:
            request = agent_pb2.ListAgentAvatarsRequest(
                page=page,
                page_size=limit,
            )
            response = self.stub.listAgentAvatars(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in list_agent_avatars: {str(e)}")
            raise self._handle_error(e)

    async def delete_agent_avatar(
        self,
        avatar_id: str,
        owner_details: dict,
    ) -> agent_pb2.DeleteAgentAvatarResponse:
        """
        Delete an agent avatar.

        Args:
            avatar_id: The ID of the avatar to delete
            owner_details: Details of the owner deleting the avatar

        Returns:
            agent_pb2.DeleteAgentAvatarResponse: The response confirming the deletion
        """
        try:
            owner = self._create_owner_proto(owner_details)
            request = agent_pb2.DeleteAgentAvatarRequest(
                id=avatar_id,
                owner=owner,
            )
            response = self.stub.deleteAgentAvatar(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def getAgentById(self, agent_id: str) -> agent_pb2.AgentResponse:
        """
        Calls the gRPC GetAgent endpoint to retrieve an agent by its ID.

        Args:
            agent_id (str): The unique identifier of the agent.

        Returns:
            agent_pb2.AgentResponse: The response containing the agent details.

        Raises:
            HTTPException: If there is an error during the gRPC request.
        """
        print(f"[DEBUG] Calling getAgentById with agent_id: {agent_id}")
        request = agent_pb2.GetAgentRequest(id=agent_id)
        try:
            print(f"[DEBUG] Making gRPC call to getAgent")
            response = self.stub.getAgent(request)
            print(
                f"[DEBUG] gRPC response received: success={response.success}, message={response.message}"
            )
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in getAgentById: {str(e)}")
            raise self._handle_error(e)

    async def deleteAgentById(
        self, agent_id: str, owner_details: dict
    ) -> agent_pb2.DeleteAgentResponse:
        """
        Calls the gRPC DeleteAgent endpoint to remove an agent by its ID.

        Args:
            agent_id (str): The unique identifier of the agent to delete.

        Returns:
            agent_pb2.DeleteAgentResponse: The response confirming the deletion.

        Raises:
            grpc.RpcError: If there is an error during the gRPC request.
        """
        try:
            owner = self._create_owner_proto(owner_details)
            request = agent_pb2.DeleteAgentRequest(id=agent_id, owner=owner)
            response = self.stub.deleteAgent(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_agents(
        self,
        page: int = 1,
        page_size: int = 10,
        department: Optional[str] = None,
        status: Optional[str] = None,
        visibility: Optional[str] = None,
        user_id: Optional[str] = None,
        organization_id: Optional[str] = None,
        is_bench_employee: Optional[bool] = None,
        is_a2a: Optional[bool] = None,
        is_customizable: Optional[bool] = None,
        category: Optional[str] = None,
    ) -> agent_pb2.ListAgentsResponse:
        """List all agents with pagination and filtering"""
        try:
            request = agent_pb2.ListAgentsRequest(page=page, page_size=page_size)

            if user_id:
                request.owner_id = user_id

            if organization_id:
                request.organization_id = organization_id

            # Add filters if provided
            if department:
                request.department = department

            if status:
                request.status = self._get_status_enum(status)

            if visibility:
                request.visibility = self._get_visibility_enum(visibility)

            if is_bench_employee is not None:
                request.is_bench_employee = is_bench_employee

            if is_a2a is not None:
                request.is_a2a = is_a2a

            if is_customizable is not None:
                request.is_customizable = is_customizable

            if category:
                request.agent_category = self._get_category_enum(category)

            response = self.stub.listAgents(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in list_agents: {str(e)}")
            raise self._handle_error(e)

    async def getTemplate(self, template_id: str) -> agent_pb2.GetTemplateResponse:
        """Gets a template by ID."""
        try:
            request = agent_pb2.GetTemplateRequest(id=template_id)
            response = self.stub.getTemplate(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def listTemplates(
        self,
        page: int,
        page_size: int,
        department: Optional[str] = None,
        organization_id: Optional[str] = None,
        user_id: Optional[str] = None,
    ) -> agent_pb2.ListTemplatesResponse:
        """Lists all templates."""
        try:
            request = agent_pb2.ListTemplatesRequest(page=page, page_size=page_size)

            # Add filters if provided
            if department:
                request.department = department
            if organization_id:
                request.organization_id = organization_id
            if user_id:
                request.owner_id = user_id

            response = self.stub.listTemplates(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in listTemplates: {str(e)}")
            raise self._handle_error(e)

    async def createAgentFromTemplate(
        self, template_id: str, owner_type: str, owner_details: dict
    ) -> agent_pb2.CreateAgentFromTemplateResponse:
        """Creates a new agent from a template."""
        try:
            print(f"[DEBUG] Creating agent from template with ID: {template_id}")
            owner = self._create_owner_proto(owner_details)
            request = agent_pb2.CreateAgentFromTemplateRequest(
                template_id=template_id,
                owner_type=self._get_owner_type_enum(owner_type),
                owner=owner,
            )
            response = self.stub.createAgentFromTemplate(request)
            print(f"[DEBUG] Agent from template response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in createAgentFromTemplate: {str(e)}")
            raise self._handle_error(e)

    async def get_marketplace_agents(
        self,
        page: int = 1,
        page_size: int = 10,
        search: Optional[str] = None,
        department: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        sort_by: Optional[str] = None,
    ) -> Any:
        """
        Retrieves a paginated list of public agents for the marketplace.

        Args:
            page: Page number for pagination
            page_size: Number of items per page
            search: Optional search term to filter by name or description
            department: Optional department filter
            category: Optional category filter
            tags: Optional tags filter as a dictionary of key-value pairs
            sort_by: Optional sort criteria (NEWEST, OLDEST, MOST_POPULAR, HIGHEST_RATED)

        Returns:
            Response containing the list of marketplace agents and pagination metadata
        """
        try:
            request_args = {
                "page": page,
                "page_size": page_size,
                "visibility": "PUBLIC",  # Marketplace only shows public agents
            }

            if search:
                request_args["search"] = search
            if department:
                request_args["department"] = department
            if category:
                request_args["category"] = self._get_category_enum(category)
            if tags:
                request_args["tags"] = tags
            if sort_by:
                request_args["sort_by"] = sort_by

            request = agent_pb2.GetMarketplaceAgentsRequest(**request_args)

            print(f"[DEBUG] gRPC Client: Sending GetMarketplaceAgentsRequest: {request}")
            response = self.stub.getMarketplaceAgents(request)
            print(f"[DEBUG] gRPC Client: Received response: {response}")

            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in get_marketplace_agents: {e.details()}")
            raise self._handle_error(e)

    async def get_marketplace_agent_detail(
        self, agent_id: str, user_id: Optional[str] = None
    ) -> Any:
        """
        Retrieves detailed information about a specific marketplace agent.

        Args:
            agent_id: The unique identifier of the agent to retrieve
            user_id: Optional user ID for personalized results

        Returns:
            Response containing the detailed agent information
        """
        try:
            request_args = {"id": agent_id}
            if user_id:
                request_args["user_id"] = user_id

            request = agent_pb2.GetMarketplaceAgentDetailRequest(**request_args)

            print(f"[DEBUG] gRPC Client: Sending GetMarketplaceAgentDetailRequest: {request}")
            response = self.stub.getMarketplaceAgentDetail(request)
            print(f"[DEBUG] gRPC Client: Received response: {response}")

            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in get_marketplace_agent_detail: {e.details()}")
            raise self._handle_error(e)

    async def rate_agent(
        self, agent_id: str, user_id: str, rating: float
    ) -> agent_pb2.RateAgentResponse:
        """
        Rate an agent with a score from 1 to 5.

        Args:
            agent_id: The ID of the agent to rate
            user_id: The ID of the user providing the rating
            rating: Rating value between 1.0 and 5.0

        Returns:
            The gRPC RateAgentResponse
        """
        try:
            request = agent_pb2.RateAgentRequest(agent_id=agent_id, user_id=user_id, rating=rating)
            print(f"[DEBUG] Sending rate agent request: {request}")
            response = self.stub.rateAgent(request)
            print(f"[DEBUG] Rate agent response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in rate_agent: {str(e)}")
            raise self._handle_error(e)

    async def use_agent(self, agent_id: str, user_id: str) -> agent_pb2.UseAgentResponse:
        """
        Mark an agent as used and increment its usage count.

        Args:
            agent_id: The ID of the agent to use
            user_id: The ID of the user using the agent

        Returns:
            The gRPC UseAgentResponse
        """
        try:
            request = agent_pb2.UseAgentRequest(agent_id=agent_id, user_id=user_id)
            print(f"[DEBUG] Sending use agent request: {request}")
            response = self.stub.useAgent(request)
            print(f"[DEBUG] Use agent response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in use_agent: {str(e)}")
            raise self._handle_error(e)

    async def update_agent_core_details(
        self,
        agent_id: str,
        owner_details: dict,
        update_payload: dict,  # Contains fields like name, description, etc.
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            print(f"[DEBUG] Update payload: {update_payload}")
            owner_proto = self._create_owner_proto(owner_details)

            request_args = {
                "agent_id": agent_id,
                "owner": owner_proto,
            }
            update_paths = []

            # Populate request_args and update_paths from update_payload
            # Example for 'name':
            if "name" in update_payload:
                request_args["name"] = update_payload["name"]
                update_paths.append("name")
            if "description" in update_payload:
                request_args["description"] = update_payload["description"]
                update_paths.append("description")
            if "avatar" in update_payload:
                request_args["avatar"] = update_payload["avatar"]
                update_paths.append("avatar")
            if "system_message" in update_payload:
                request_args["system_message"] = update_payload["system_message"]
                update_paths.append("system_message")
            if "ruh_credentials" in update_payload:
                request_args["ruh_credentials"] = update_payload["ruh_credentials"]
                update_paths.append("ruh_credentials")
            if "model_provider" in update_payload:
                request_args["model_provider"] = update_payload["model_provider"]
                update_paths.append("model_provider")
            if "model_name" in update_payload:
                request_args["model_name"] = update_payload["model_name"]
                update_paths.append("model_name")
            if "model_api_key" in update_payload:
                request_args["model_api_key"] = update_payload["model_api_key"]
                update_paths.append("model_api_key")
            if "department" in update_payload and update_payload["department"] is not None:
                request_args["department"] = update_payload["department"]
                update_paths.append("department")
            if "tone" in update_payload and update_payload["tone"] is not None:
                request_args["tone"] = self._get_tone_enum(update_payload["tone"])  # Helper needed
                update_paths.append("tone")
            if (
                "agent_topic_type" in update_payload
                and update_payload["agent_topic_type"] is not None
            ):
                request_args["agent_topic_type"] = update_payload["agent_topic_type"]
                update_paths.append("agent_topic_type")

            if "category" in update_payload:
                request_args["category"] = self._get_category_enum(update_payload["category"])
                update_paths.append("agent_category")
            if not update_paths:
                raise ValueError("No fields provided for core details update.")

            print(f"[DEBUG] Update paths: {update_paths}")

            request_args["update_mask"] = FieldMask(paths=update_paths)
            request = agent_pb2.UpdateAgentCoreDetailsRequest(**request_args)

            response = self.stub.UpdateAgentCoreDetails(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in update_agent_core_details: {str(e)}")
            raise self._handle_error(e)

    async def update_agent_capabilities(
        self,
        agent_id: str,
        owner_details: dict,
        update_payload: dict,  # Contains capabilities, input_modes, output_modes
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            owner_proto = self._create_owner_proto(owner_details)
            request_args = {
                "agent_id": agent_id,
                "owner": owner_proto,
            }
            update_paths = []

            if "capabilities" in update_payload:
                request_args["capabilities"] = (
                    json.dumps(update_payload["capabilities"])
                    if update_payload["capabilities"] is not None
                    else ""
                )
                update_paths.append("capabilities")
            if "input_modes" in update_payload:
                request_args["input_modes"] = (
                    update_payload["input_modes"]
                    if update_payload["input_modes"] is not None
                    else []
                )
                update_paths.append("input_modes")
            if "output_modes" in update_payload:
                request_args["output_modes"] = (
                    update_payload["output_modes"]
                    if update_payload["output_modes"] is not None
                    else []
                )
                update_paths.append("output_modes")

            if "response_model" in update_payload:
                request_args["response_model"] = (
                    update_payload["response_model"]
                    if update_payload["response_model"] is not None
                    else []
                )
                update_paths.append("response_model")

            if not update_paths:
                raise ValueError("No capabilities fields provided for update.")

            request_args["update_mask"] = FieldMask(paths=update_paths)
            request = agent_pb2.UpdateAgentCapabilitiesRequest(**request_args)
            response = self.stub.UpdateAgentCapabilities(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in update_agent_capabilities: {str(e)}")
            raise self._handle_error(e)

    async def update_agent_knowledge(
        self, agent_id: str, owner_details: dict, update_payload: dict  # Contains files, urls
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            owner_proto = self._create_owner_proto(owner_details)
            request_args = {
                "agent_id": agent_id,
                "owner": owner_proto,
            }
            update_paths = []

            if "files" in update_payload:  # Assumes full list replacement if key exists
                request_args["files"] = (
                    update_payload["files"] if update_payload["files"] is not None else []
                )
                update_paths.append("files")
            if "urls" in update_payload:  # Assumes full list replacement if key exists
                request_args["urls"] = (
                    update_payload["urls"] if update_payload["urls"] is not None else []
                )
                update_paths.append("urls")

            if not update_paths:
                raise ValueError("No knowledge fields (files/urls) provided for update.")

            request_args["update_mask"] = FieldMask(paths=update_paths)
            request = agent_pb2.UpdateAgentKnowledgeRequest(**request_args)
            response = self.stub.UpdateAgentKnowledge(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
        except ValueError as ve:
            raise ve

    async def update_agent_mcp_servers(
        self,
        agent_id: str,
        owner_details: dict,
        mcp_server_ids: list[str],  # Direct list for replacement
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            owner_proto = self._create_owner_proto(owner_details)
            request = agent_pb2.UpdateAgentMcpServersRequest(
                agent_id=agent_id,
                owner=owner_proto,
                mcp_server_ids=mcp_server_ids if mcp_server_ids is not None else [],
            )
            response = self.stub.UpdateAgentMcpServers(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_agent_workflows(
        self, agent_id: str, owner_details: dict, workflow_ids: list[str]
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            owner_proto = self._create_owner_proto(owner_details)
            request = agent_pb2.UpdateAgentWorkflowsRequest(
                agent_id=agent_id,
                owner=owner_proto,
                workflow_ids=workflow_ids if workflow_ids is not None else [],
            )
            response = self.stub.UpdateAgentWorkflows(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_agent_settings(
        self,
        agent_id: str,
        owner_details: dict,
        update_payload: dict,
    ) -> agent_pb2.UpdateAgentPartResponse:
        try:
            owner_proto = self._create_owner_proto(owner_details)
            request_args = {
                "agent_id": agent_id,
                "owner": owner_proto,
            }
            update_paths = []

            if "user_ids" in update_payload:
                request_args["user_ids"] = (
                    update_payload["user_ids"] if update_payload["user_ids"] is not None else []
                )
                update_paths.append("user_ids")
            if "agent_topic_type" in update_payload:
                request_args["agent_topic_type"] = update_payload["agent_topic_type"]
                update_paths.append("agent_topic_type")
            if "subscriptions" in update_payload:
                request_args["subscriptions"] = update_payload["subscriptions"]
                update_paths.append("subscriptions")
            if "tags" in update_payload:
                # Ensure tags is an array
                if isinstance(update_payload["tags"], str) and update_payload["tags"]:
                    try:
                        parsed_tags = json.loads(update_payload["tags"])
                        if isinstance(parsed_tags, dict):
                            # Convert dict to array of strings
                            request_args["tags"] = [f"{k}:{v}" for k, v in parsed_tags.items()]
                        elif isinstance(parsed_tags, list):
                            request_args["tags"] = parsed_tags
                        else:
                            request_args["tags"] = []
                    except json.JSONDecodeError:
                        request_args["tags"] = []
                else:
                    request_args["tags"] = (
                        update_payload["tags"] if update_payload["tags"] is not None else []
                    )
                update_paths.append("tags")
            if "status" in update_payload and update_payload["status"] is not None:
                request_args["status"] = self._get_status_enum(
                    update_payload["status"]
                )  # Helper needed
                update_paths.append("status")
            if (
                "is_changes_marketplace" in update_payload
                and update_payload["is_changes_marketplace"] is not None
            ):
                request_args["is_changes_marketplace"] = update_payload["is_changes_marketplace"]
                update_paths.append("is_changes_marketplace")
            if (
                "is_bench_employee" in update_payload
                and update_payload["is_bench_employee"] is not None
            ):
                request_args["is_bench_employee"] = update_payload["is_bench_employee"]
                update_paths.append("is_bench_employee")
            if "is_a2a" in update_payload and update_payload["is_a2a"] is not None:
                request_args["is_a2a"] = update_payload["is_a2a"]
                update_paths.append("is_a2a")
            if (
                "is_customizable" in update_payload
                and update_payload["is_customizable"] is not None
            ):
                request_args["is_customizable"] = update_payload["is_customizable"]
                update_paths.append("is_customizable")
            if "example_prompts" in update_payload:
                request_args["example_prompts"] = (
                    update_payload["example_prompts"]
                    if update_payload["example_prompts"] is not None
                    else None
                )
                update_paths.append("example_prompts")

            if not update_paths:
                raise ValueError("No fields provided for settings update.")

            print(f"[DEBUG] Update paths: {update_paths}")

            request_args["update_mask"] = FieldMask(paths=update_paths)
            request = agent_pb2.UpdateAgentSettingsRequest(**request_args)
            response = self.stub.UpdateAgentSettings(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in update_agent_settings: {str(e)}")
            raise self._handle_error(e)

    async def toggle_agent_visibility(
        self, agent_id: str, owner_details: dict
    ) -> agent_pb2.ToggleAgentVisibilityResponse:
        try:
            print(f"[DEBUG] Toggling agent visibility for agent ID: {agent_id}")
            owner_proto = self._create_owner_proto(owner_details)
            request = agent_pb2.ToggleAgentVisibilityRequest(agent_id=agent_id, owner=owner_proto)
            response = self.stub.ToggleAgentVisibility(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in toggle_agent_visibility: {str(e)}")
            raise self._handle_error(e)
