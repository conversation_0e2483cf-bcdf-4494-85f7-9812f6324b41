# Comparison: Standard Components vs. Custom Node Components

## 1. Overview of Existing Component System

The current system implements components using a structured class-based approach across multiple services. Let's examine how standard components are currently defined and used in the system.

### 1.1 Standard Component Architecture

#### Node Executor Service Components

In the Node Executor Service, components are implemented as Python classes that:

1. **Inherit from BaseComponent**: All components extend the `BaseComponent` abstract base class
2. **Register via Decorator**: Use the `@register_component` decorator to register with the component registry
3. **Implement the `process` Method**: Define the core execution logic in the `process` method
4. **Optionally Define Validation**: Can implement a `validate` method for input validation
5. **Use Pydantic Models**: Often define request schemas using Pydantic for automatic validation

```python
# Example of a standard component in Node Executor Service
from app.core_.base_component import BaseComponent
from app.core_.component_system import register_component
from pydantic import BaseModel, Field

# Define request schema using Pydantic
class ParseJSONRequest(BaseModel):
    input_json_string: str = Field(..., description="JSON string to parse")
    strict_mode: bool = Field(False, description="Whether to use strict mode")

@register_component("ParseJSONDataComponent")
class ParseJSONDataCompExecutor(BaseComponent):
    def __init__(self):
        super().__init__()
        self.request_schema = ParseJSONRequest

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        # Implementation of JSON parsing logic
        json_string = payload.get("input_json_string")
        strict_mode = payload.get("strict_mode", False)

        # Process and return results
        return {"result": json.loads(json_string)}
```

#### Workflow Service Components

In the Workflow Service, components (nodes) are implemented as:

1. **Subclasses of BaseNode**: All nodes extend the `BaseNode` abstract base class
2. **Class Variables for Metadata**: Define name, display_name, category, etc. as class variables
3. **Inputs and Outputs as Class Variables**: Define static input/output definitions
4. **Implement the `execute` Method**: Define execution logic in the `execute` method
5. **Auto-Discovery via Module Import**: Components are discovered by importing their modules

```python
# Example of a standard node in Workflow Service
from app.services.workflow_builder_backend.components.core.base_node import BaseNode
from app.services.workflow_builder_backend.models.context import WorkflowContext
from app.services.workflow_builder_backend.models.node_result import NodeResult

class EmailComponent(BaseHITLComponent):
    name: ClassVar[str] = "EmailComponent"
    display_name: ClassVar[str] = "Send Email"
    description: ClassVar[str] = "Sends an email to recipients and optionally waits for a reply."
    category: ClassVar[str] = "HITL"
    icon: ClassVar[str] = "Mail"

    inputs: ClassVar[List[InputBase]] = [
        # Input definitions
        TextInput(name="to", display_name="To", required=True),
        TextInput(name="subject", display_name="Subject", required=True),
        TextAreaInput(name="body", display_name="Body", required=True),
    ]

    outputs: ClassVar[List[Output]] = [
        # Output definitions
        Output(name="success", display_name="Success", output_type="boolean"),
        Output(name="response", display_name="Response", output_type="string"),
    ]

    async def execute(self, context: WorkflowContext) -> NodeResult:
        # Implementation of email sending logic
        inputs = context.node_outputs.get(context.current_node_id, {})
        # Process and return results
        return NodeResult.success(outputs={"success": True})
```

### 1.2 Component Registration and Discovery

#### Node Executor Service

- Components are registered in a global `COMPONENT_REGISTRY` dictionary
- The `@register_component` decorator adds the component class to this registry
- The `ComponentManager` discovers and instantiates components at startup
- Components are accessed by their registered name (e.g., "ParseJSONDataComponent")

#### Workflow Service

- Components are discovered by scanning Python modules in the components directory
- The `ComponentService` creates `ComponentDefinition` objects from discovered classes
- Components are registered in the `ComponentRegistry` by category and name
- The registry is exposed via API endpoints for the frontend to consume

### 1.3 Execution Flow

1. **Frontend**: User configures a workflow with nodes and connections
2. **API Gateway**: Receives execution request and forwards to Workflow Service
3. **Workflow Service**: Processes the workflow definition and executes nodes in sequence
4. **Node Execution**: For each node, the Workflow Service:
   - Creates an instance of the node class
   - Calls the `execute` method with the workflow context
   - For external execution, may communicate with Node Executor Service via Kafka
5. **Node Executor Service**: Receives execution requests, routes to appropriate component
6. **Component Processing**: Component processes the request and returns results
7. **Result Handling**: Results flow back through the same path to the frontend

## 2. Custom Node Components vs. Standard Components

### 2.1 Definition Format

| Aspect | Standard Components | Custom Node Components |
|--------|---------------------|------------------------|
| **Format** | Python class definitions | JSON schema definitions |
| **Location** | Source code in repository | MongoDB database |
| **Metadata** | Class variables | JSON properties |
| **Inputs/Outputs** | Static class variables | JSON arrays in definition |
| **Implementation** | Python method code | Code string or HTTP endpoint URL |
| **Validation** | Class-based validation | Schema-based validation |
| **Discovery** | Module import and class inspection | Database query |

### 2.2 Registration Process

| Aspect | Standard Components | Custom Node Components |
|--------|---------------------|------------------------|
| **Registration Time** | Application startup | Runtime via API |
| **Registration Method** | Automatic via import | API endpoint call |
| **Validation** | Minimal at startup | Comprehensive at registration |
| **Versioning** | Git/source control | Database versioning |
| **Namespacing** | Module path | User/organization namespace |
| **Permissions** | None (all components available) | User/role-based access control |

### 2.3 Execution Flow

| Aspect | Standard Components | Custom Node Components |
|--------|---------------------|------------------------|
| **Class Instantiation** | Direct class instantiation | Adapter pattern (`CustomNodeAdapter`) |
| **Code Execution** | Direct method call | Sandboxed execution |
| **Security Boundaries** | Process-level isolation | Restricted execution environment |
| **Resource Limits** | System-wide limits | Per-node configurable limits |
| **Error Handling** | Standard exception handling | Sandboxed error capture |
| **Execution Location** | Server process | Isolated execution environment |

### 2.4 Data Handling

| Aspect | Standard Components | Custom Node Components |
|--------|---------------------|------------------------|
| **Input Access** | Direct object access | Dictionary access only |
| **Output Format** | Flexible return types | Strict schema validation |
| **State Management** | Can maintain instance state | Stateless execution |
| **File System Access** | Unrestricted (within service) | Restricted or none |
| **Network Access** | Unrestricted (within service) | Allowlist-based restriction |
| **Database Access** | Direct access possible | No direct access |

### 2.5 Security Boundaries

| Aspect | Standard Components | Custom Node Components |
|--------|---------------------|------------------------|
| **Code Isolation** | Process-level | Sandbox-level |
| **Resource Limits** | System-wide | Per-node configurable |
| **Input Validation** | Optional | Mandatory |
| **Code Review** | Pre-deployment | At registration time |
| **Execution Monitoring** | Basic logging | Comprehensive monitoring |
| **User Separation** | None | Strict separation |

## 3. Transformation Process

### 3.1 From Custom Node Definition to Execution

1. **Registration**: Custom node JSON definition is validated and stored in MongoDB
2. **Discovery**: When components are requested, custom nodes are loaded from MongoDB
3. **Adaptation**: Custom nodes are transformed into `ComponentDefinition` objects
4. **Execution Preparation**: When a workflow is executed:
   - The `CustomNodeAdapter` class wraps the custom node definition
   - The adapter implements the `BaseNode` interface
   - The adapter's `execute` method handles dispatching to the appropriate executor
5. **Execution**: Based on the execution type:
   - `python_code`: Code is sent to Node Executor for sandboxed execution
   - `http_endpoint`: Request is sent to the specified endpoint
   - `javascript_code`: Code is executed in the browser (UI operations only)
6. **Result Handling**: Results are validated against the output schema and returned

### 3.2 Orchestration Engine Integration

Custom nodes are transformed into a format compatible with the orchestration engine:

```json
{
  "nodes": [
    {
      "id": "custom-node-1",
      "type": "custom",
      "definition": {
        "name": "CustomHttpClient",
        "execution_type": "python_code",
        "code": "import requests\n\nresponse = requests.get(inputs['url'])\nreturn {'response': response.json()}"
      }
    }
  ],
  "transitions": [
    {
      "id": "transition1",
      "source_node": "start-node",
      "target_node": "custom-node-1",
      "input_mapping": {
        "url": "$.global_context.api_url"
      }
    }
  ]
}
```

## 4. Implementation Details

### 4.1 Custom Node Adapter Implementation

The `CustomNodeAdapter` class is a key component that bridges the gap between custom node definitions and the existing component system:

```python
# workflow-service/app/services/workflow_builder_backend/components/custom/custom_node_adapter.py

from app.services.workflow_builder_backend.components.core.base_node import BaseNode
from app.services.workflow_builder_backend.models.context import WorkflowContext
from app.services.workflow_builder_backend.models.node_result import NodeResult, NodeStatus
from app.services.workflow_builder_backend.services.execution.custom_node_executor import CustomNodeExecutor

class CustomNodeAdapter(BaseNode):
    """Adapter that wraps a custom node definition as a BaseNode."""

    def __init__(self, custom_node_def):
        # Store the custom node definition
        self.custom_node_def = custom_node_def

        # Map definition fields to BaseNode class variables
        self.name = custom_node_def["name"]
        self.display_name = custom_node_def["display_name"]
        self.description = custom_node_def["description"]
        self.category = custom_node_def["category"]
        self.icon = custom_node_def["icon"]
        self.beta = custom_node_def.get("beta", False)
        self.requires_approval = custom_node_def.get("requires_approval", False)

        # Create inputs and outputs from the definition
        self.inputs = self._create_inputs_from_definition()
        self.outputs = self._create_outputs_from_definition()

        # Create the executor
        self.executor = CustomNodeExecutor()

    def _create_inputs_from_definition(self):
        """Convert JSON input definitions to InputBase objects."""
        inputs = []
        for input_def in self.custom_node_def.get("inputs", []):
            # Create appropriate input type based on input_type
            input_type = input_def.get("input_type", "string")
            if input_type == "string":
                inputs.append(TextInput(
                    name=input_def["name"],
                    display_name=input_def["display_name"],
                    required=input_def.get("required", False),
                    default_value=input_def.get("default_value", ""),
                    info=input_def.get("description", "")
                ))
            # Add other input types as needed
        return inputs

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """Execute the custom node based on its execution type."""
        try:
            # Get the inputs from the context
            inputs = context.node_outputs.get(context.current_node_id, {})

            # Execute the node using the appropriate executor
            execution_type = self.custom_node_def["execution"]["type"]

            if execution_type == "python_code":
                return await self._execute_python_code(inputs, context)
            elif execution_type == "http_endpoint":
                return await self._execute_http_endpoint(inputs, context)
            else:
                return NodeResult.error(
                    error_message=f"Unsupported execution type: {execution_type}"
                )
        except Exception as e:
            return NodeResult.error(
                error_message=str(e)
            )

    async def _execute_python_code(self, inputs, context):
        """Execute Python code in the Node Executor Service."""
        try:
            # Prepare the execution request
            code = self.custom_node_def["execution"]["code"]

            # Send to Node Executor Service via Kafka
            result = await self.executor.execute_python_code(
                code=code,
                inputs=inputs,
                node_id=context.current_node_id,
                workflow_id=context.workflow_id
            )

            return NodeResult.success(outputs=result)
        except Exception as e:
            return NodeResult.error(error_message=f"Python code execution error: {str(e)}")
```

### 4.2 Custom Node Executor Component

The Node Executor Service needs a new component to handle custom node execution:

```python
# node-executor-service/app/components/custom_node_component.py

import asyncio
import traceback
from RestrictedPython import compile_restricted
from RestrictedPython.Guards import safe_globals, limited_builtins
from app.core_.base_component import BaseComponent
from app.core_.component_system import register_component

@register_component("CustomNodeComponent")
class CustomNodeComponent(BaseComponent):
    """Component for executing custom nodes."""

    def __init__(self):
        super().__init__()
        # Initialize the restricted environment
        self._setup_restricted_environment()

    def _setup_restricted_environment(self):
        """Set up the restricted execution environment."""
        # Create a restricted globals dictionary
        self.restricted_globals = safe_globals.copy()

        # Add safe libraries
        self.allowed_modules = {
            "json": __import__("json"),
            "re": __import__("re"),
            "math": __import__("math"),
            "datetime": __import__("datetime"),
            "collections": __import__("collections"),
            "copy": __import__("copy"),
            "itertools": __import__("itertools"),
        }

        # Add limited builtins
        self.restricted_globals["__builtins__"] = limited_builtins

        # Add allowed modules
        for name, module in self.allowed_modules.items():
            self.restricted_globals[name] = module

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Process a custom node execution request."""
        try:
            # Extract parameters
            code = payload.get("code", "")
            inputs = payload.get("inputs", {})
            node_id = payload.get("node_id", "unknown")
            workflow_id = payload.get("workflow_id", "unknown")

            # Execute the code in a restricted environment
            return await self._execute_restricted_code(code, inputs, node_id, workflow_id)
        except Exception as e:
            return {
                "error": str(e),
                "traceback": traceback.format_exc()
            }

    async def _execute_restricted_code(self, code, inputs, node_id, workflow_id):
        """Execute code in a restricted environment."""
        try:
            # Create a namespace for execution
            namespace = self.restricted_globals.copy()
            namespace["inputs"] = inputs
            namespace["result"] = {}

            # Compile the code
            byte_code = compile_restricted(
                code,
                filename=f"<custom-node-{node_id}>",
                mode="exec"
            )

            # Execute with resource limits
            with self._resource_limits():
                exec(byte_code, namespace)

            # Get the result
            if "result" in namespace:
                return namespace["result"]
            else:
                return {"error": "Code did not produce a 'result' variable"}
        except Exception as e:
            return {"error": f"Code execution error: {str(e)}"}

    @contextmanager
    def _resource_limits(self):
        """Apply resource limits to execution."""
        # Set up resource limits
        old_limits = resource.getrlimit(resource.RLIMIT_CPU)
        resource.setrlimit(resource.RLIMIT_CPU, (5, 5))  # 5 seconds CPU time

        try:
            yield
        finally:
            # Restore original limits
            resource.setrlimit(resource.RLIMIT_CPU, old_limits)
```

### 4.3 Component Registry Integration

The `ComponentRegistry` class needs to be extended to load custom nodes from MongoDB:

```python
# workflow-service/app/services/workflow_builder_backend/core/component_registry.py

class ComponentRegistry:
    # ... existing code ...

    async def load_custom_nodes(self):
        """Load custom node definitions from MongoDB."""
        try:
            # Get the MongoDB client
            db_client = get_mongodb_client()
            custom_nodes_collection = db_client.workflow_db.custom_nodes

            # Query for active custom nodes
            custom_nodes = await custom_nodes_collection.find(
                {"status": "active"}
            ).to_list(length=None)

            # Register each custom node
            for node_def in custom_nodes:
                # Convert MongoDB document to ComponentDefinition
                component_def = self._convert_custom_node_to_component_def(node_def)

                # Register the component
                self.register_component(component_def)

            logger.info(f"Loaded {len(custom_nodes)} custom nodes from MongoDB")
        except Exception as e:
            logger.error(f"Error loading custom nodes: {str(e)}")

    def _convert_custom_node_to_component_def(self, node_def):
        """Convert a custom node definition to a ComponentDefinition."""
        # Create a ComponentDefinition from the custom node
        return ComponentDefinition(
            name=node_def["name"],
            display_name=node_def["display_name"],
            description=node_def["description"],
            category=node_def["category"],
            icon=node_def["icon"],
            beta=node_def.get("beta", False),
            inputs=[self._convert_input_def(inp) for inp in node_def.get("inputs", [])],
            outputs=[self._convert_output_def(out) for out in node_def.get("outputs", [])],
            is_valid=True,
            path=f"custom_nodes.{node_def['namespace']}.{node_def['name']}",
            type="custom_node",
            custom_node_id=str(node_def["_id"])
        )
```

## 5. Example Comparison

### 5.1 Standard Component (Node Executor Service)

```python
@register_component("HttpClientComponent")
class HttpClientComponent(BaseComponent):
    def __init__(self):
        super().__init__()
        self.request_schema = HttpClientRequest

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        url = payload.get("url")
        method = payload.get("method", "GET")
        headers = payload.get("headers", {})

        response = requests.request(method, url, headers=headers)
        return {
            "status_code": response.status_code,
            "response": response.json() if response.headers.get("content-type") == "application/json" else response.text
        }
```

### 5.2 Equivalent Custom Node Component

```json
{
  "name": "CustomHttpClient",
  "display_name": "Custom HTTP Client",
  "description": "Makes HTTP requests to custom endpoints",
  "category": "Integration",
  "icon": "Globe",
  "version": "1.0.0",
  "author": {
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "inputs": [
    {
      "name": "url",
      "display_name": "URL",
      "input_type": "string",
      "required": true
    },
    {
      "name": "method",
      "display_name": "Method",
      "input_type": "string",
      "required": false,
      "default_value": "GET"
    },
    {
      "name": "headers",
      "display_name": "Headers",
      "input_type": "object",
      "required": false,
      "default_value": {}
    }
  ],
  "outputs": [
    {
      "name": "status_code",
      "display_name": "Status Code",
      "output_type": "number"
    },
    {
      "name": "response",
      "display_name": "Response",
      "output_type": "any"
    }
  ],
  "execution": {
    "type": "python_code",
    "code": "import requests\n\nurl = inputs.get('url')\nmethod = inputs.get('method', 'GET')\nheaders = inputs.get('headers', {})\n\nresponse = requests.request(method, url, headers=headers)\n\nresult = {\n    'status_code': response.status_code,\n    'response': response.json() if response.headers.get('content-type') == 'application/json' else response.text\n}"
  }
}
```

## 6. Integration with Existing System

### 6.1 Component Discovery Flow

The integration of custom nodes with the existing component discovery flow would work as follows:

1. **Startup Process**:
   - The Workflow Service starts and initializes the `ComponentService`
   - The `ComponentService` discovers built-in components from Python modules
   - The `ComponentService` calls `ComponentRegistry.load_custom_nodes()` to load custom nodes from MongoDB
   - All components (built-in and custom) are registered in the `ComponentRegistry`

2. **API Request Flow**:
   - Frontend requests components via `/api/v1/components` endpoint
   - API Gateway forwards request to Workflow Service
   - Workflow Service returns all registered components, including custom nodes
   - Frontend displays all components in the component palette

3. **Custom Node Creation Flow**:
   - User creates a custom node via the frontend editor
   - Frontend sends the node definition to `/api/v1/custom-nodes` endpoint
   - API Gateway forwards request to Workflow Service
   - Workflow Service validates and stores the node in MongoDB
   - The node is immediately available for use in workflows

### 6.2 Execution Flow Integration

The execution flow for workflows containing custom nodes would work as follows:

1. **Workflow Execution Request**:
   - Frontend sends workflow execution request to API Gateway
   - API Gateway forwards request to Workflow Service
   - Workflow Service processes the workflow definition

2. **Node Type Detection**:
   - For each node, the Workflow Service checks the node type
   - If the node is a built-in component, it's executed directly
   - If the node is a custom node, the `CustomNodeAdapter` is used

3. **Custom Node Execution**:
   - The `CustomNodeAdapter` determines the execution type
   - For `python_code`, it sends a request to the Node Executor Service
   - The Node Executor Service routes the request to the `CustomNodeComponent`
   - The `CustomNodeComponent` executes the code in a sandboxed environment
   - Results are returned to the Workflow Service
   - The Workflow Service continues with the next node in the workflow

### 6.3 Security Integration

The security model for custom nodes integrates with the existing system as follows:

1. **Authentication and Authorization**:
   - Reuses existing authentication mechanisms
   - Adds custom node-specific permissions
   - Enforces namespace-based access control

2. **Execution Security**:
   - Leverages the existing Node Executor Service isolation
   - Adds additional sandboxing for custom node code
   - Implements resource limits specific to custom nodes

3. **Audit and Monitoring**:
   - Extends existing logging to include custom node operations
   - Adds custom node-specific metrics
   - Provides detailed execution history for custom nodes

## 7. Conclusion

The proposed custom node integration provides a flexible and secure way for users to extend the platform's capabilities without modifying core code. By leveraging the existing component architecture and adding adapters and sandboxing mechanisms, custom nodes can be seamlessly integrated with the current system.

Key benefits of this approach include:

1. **Minimal Changes to Core Services**: The integration requires focused additions rather than extensive modifications to existing code.

2. **Consistent User Experience**: Custom nodes appear and behave like built-in components in the workflow editor.

3. **Strong Security Boundaries**: The sandboxed execution environment ensures that custom code cannot compromise the system.

4. **Flexible Execution Models**: Support for multiple execution types (Python code, HTTP endpoints, JavaScript) provides flexibility for different use cases.

5. **Scalable Architecture**: The design allows for future expansion of custom node capabilities without major architectural changes.

By implementing this integration, the platform will provide users with powerful extensibility options while maintaining the security, reliability, and performance of the core system.
