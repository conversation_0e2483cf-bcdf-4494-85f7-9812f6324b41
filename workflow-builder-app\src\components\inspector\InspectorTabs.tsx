import React from "react";
import { useInspector } from "./InspectorContext";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Settings, Info, Sliders } from "lucide-react";
import { NodeInfoPanel } from "./NodeInfoPanel";
import { NodeSettingsPanel } from "./NodeSettingsPanel";
import { NodeAdvancedPanel } from "./NodeAdvancedPanel";

/**
 * Component for the tabs section of the inspector panel
 */
export function InspectorTabs() {
  const { 
    selectedNode, 
    activeTab, 
    setActiveTab,
    handleLabelChange,
    handleConfigChange,
    handleDefinitionChange,
    isInputConnected,
    shouldDisableInput,
    getConnectionInfo
  } = useInspector();

  // If no node is selected, don't render anything
  if (!selectedNode) return null;

  return (
    <Tabs 
      value={activeTab} 
      onValueChange={(value) => setActiveTab(value as "settings" | "info" | "advanced")}
      className="flex flex-grow flex-col overflow-hidden"
    >
      <TabsList className="mx-4 mt-2 grid grid-cols-3">
        <TabsTrigger value="settings" className="text-xs">
          <Settings className="mr-1.5 h-3.5 w-3.5" /> Settings
        </TabsTrigger>
        <TabsTrigger value="info" className="text-xs">
          <Info className="mr-1.5 h-3.5 w-3.5" /> Info
        </TabsTrigger>
        <TabsTrigger value="advanced" className="text-xs">
          <Sliders className="mr-1.5 h-3.5 w-3.5" /> Advanced
        </TabsTrigger>
      </TabsList>

      <TabsContent
        value="settings"
        className="flex h-full flex-grow flex-col overflow-hidden"
      >
        <NodeSettingsPanel
          node={selectedNode}
          onLabelChange={handleLabelChange}
          onConfigChange={handleConfigChange}
          isInputConnected={isInputConnected}
          shouldDisableInput={shouldDisableInput}
          getConnectionInfo={getConnectionInfo}
        />
      </TabsContent>

      <TabsContent
        value="info"
        className="flex h-full flex-grow flex-col overflow-hidden"
      >
        <NodeInfoPanel node={selectedNode} />
      </TabsContent>

      <TabsContent
        value="advanced"
        className="flex h-full flex-grow flex-col overflow-hidden"
      >
        <NodeAdvancedPanel 
          node={selectedNode} 
          onDefinitionChange={handleDefinitionChange} 
        />
      </TabsContent>
    </Tabs>
  );
}
