{"semi": true, "singleQuote": false, "tabWidth": 2, "trailingComma": "all", "printWidth": 100, "bracketSpacing": true, "arrowParens": "always", "endOfLine": "lf", "useTabs": false, "quoteProps": "as-needed", "jsxSingleQuote": false, "bracketSameLine": false, "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "plugins": ["prettier-plugin-tailwindcss"], "tailwindConfig": "./tailwind.config.ts", "tailwindFunctions": ["cn", "cva", "clsx"]}