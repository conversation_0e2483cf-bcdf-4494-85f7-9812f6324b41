import os
import json
import base64
import uuid
import tempfile
from dotenv import load_dotenv
from google.cloud import storage
from typing import Union
load_dotenv()

from google.oauth2 import service_account

class GCSUploadService:
    def __init__(self):
        # Decode the base64 GCS credentials stored in environment variables
        decoded_gcs_cred = json.loads(base64.b64decode(os.getenv("GCS_CRED")).decode("utf-8"))

        # Convert to Google Auth credentials
        credentials = service_account.Credentials.from_service_account_info(decoded_gcs_cred)

        # Initialize GCS Client with credentials
        self.storage_client = storage.Client(credentials=credentials, project=decoded_gcs_cred["project_id"])
        self.bucket_name = os.getenv("BUCKET_NAME")

    def upload_file_from_server(self, file_path: str, folder_name: str = None):
        """
        Upload a local file from the server to GCS.

        :param file_path: Path to the local file on the server
        :param folder_name: (Optional) Folder inside the bucket where the file should be saved
        :return: Dictionary containing filename, public URL, and content type
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        # Extract filename and content type
        file_name = os.path.basename(file_path)
        file_extension = os.path.splitext(file_name)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"

        # Determine folder structure
        filename = f"{folder_name}/{unique_filename}" if folder_name else unique_filename

        # Upload file to GCS
        bucket = self.storage_client.bucket(self.bucket_name)
        blob = bucket.blob(filename)
        blob.upload_from_filename(file_path)

        public_url = f"https://storage.googleapis.com/{self.bucket_name}/{filename}"

        return {
            "filename": filename,
            "publicUrl": public_url,
            "contentType": blob.content_type or "application/octet-stream"
        }

    def upload_json_as_file(self, json_data: dict, folder_name: str) -> dict:
        """
        Upload a JSON string or dict as a file to GCS.

        :param json_data: The JSON data in string or dict format.
        :param folder_name: (Optional) Folder inside the bucket where the file should be saved.
        :return: Dictionary containing filename, public URL, and content type.
        """
        # Handle None case
        if json_data is None:
            raise ValueError("JSON data cannot be None")
        
        print(f"[DEBUG] Received JSON data: {json_data} (Type: {type(json_data)})")

        # Convert dict to JSON string if needed
        if isinstance(json_data, dict):
            json_str = json.dumps(json_data)
       
        else:
            raise TypeError(f"Unsupported type for json_data: {type(json_data)}")

        print(f"[Uploading JSON data to GCS]: {json_str}")
        unique_filename = f"{uuid.uuid4()}.json"
        filename = f"{folder_name}/{unique_filename}" if folder_name else unique_filename

        bucket = self.storage_client.bucket(self.bucket_name)
        blob = bucket.blob(filename)

        blob.upload_from_string(
            json_str
        )

        public_url = f"https://storage.googleapis.com/{self.bucket_name}/{filename}"

        print(f"[Uploaded JSON to GCS]: {public_url}")
        return {
            "filename": filename,
            "publicUrl": public_url,
            "contentType": "application/json"
        }

# Example Usage
if __name__ == "__main__":
    gcs_service = GCSUploadService()

    # Upload a file from the local server
    # file_path = "test.txt"  # Change this to your local file path
    # result = gcs_service.upload_file_from_server(file_path, "uploads")
    # print("Uploaded File Info:", result)

    # Example usage of upload_json_as_file
    json_data = json.dumps({"example": "data", "value": 123})
    result = gcs_service.upload_json_as_file(json_data, "workflows")
    print("Uploaded JSON Info:", result)