from pydantic import create_model, BaseModel
from autogen_core.tools import BaseTool
from autogen_core import CancellationToken
import httpx
import asyncio
import json
import slugify
import logging
from typing import Dict, List, Any
from ..shared.config.base import get_settings
from ..helper.api_call import HttpRequestHelper, HttpMethods, AuthType
from ..kafka_client.producer import kafka_producer

# Configure logger
logger = logging.getLogger(__name__)

# Map JSON Schema types to Python types
json_type_to_py = {
    "string": str,
    "integer": int,
    "number": float,
    "boolean": bool,
    "object": dict,
    "array": list,
}

workflow_execute_endpoint = "workflow-execute/execute"
stream_endpoint = "workflow-execute/stream"


class WorkflowToolLoader:
    """
    Utility class to load workflows as dynamic tools and execute them.
    """

    def __init__(self):
        """
        Initialize the workflow tool loader.

        Args:
            workflow_api_base_url: Base URL for workflow execution API
            stream_api_base_url: Base URL for streaming results
            auth_token: Optional authentication token
            auth_type: Authentication type (default: BEARER)
        """
        self.settings = get_settings()
        logger.info("Initializing WorkflowToolLoader")

        # Initialize HTTP request helper for API calls
        self.http_client = HttpRequestHelper(
            base_url=self.settings.workflow_api_gateway.api_url,
            auth_token=self.settings.workflow_api_gateway.api_key,
            auth_type=AuthType.API_KEY,
            api_key_name="X-Agent-Platform-Auth-Key",
            timeout=60,  # Longer timeout for workflow operations
        )

        self.kafka_agent_response_topic = self.settings.kafka.kafka_agent_response_topic
        logger.debug(
            f"HTTP client initialized with base URL: {self.settings.workflow_api_gateway.api_url}"
        )

    def create_workflow_tool_from_metadata(
        self,
        run_id: str,
        workflow_metadata: Dict[str, Any],
    ) -> BaseTool:
        """
        Create a dynamic tool from workflow metadata.

        Args:
            workflow_metadata: Workflow metadata from the API

        Returns:
            A BaseTool instance that can execute the workflow
        """
        workflow_id = workflow_metadata.get("id")
        logger.info(f"Creating workflow tool for workflow ID: {workflow_id}")

        name = f"{workflow_metadata.get('name', 'unnamed').lower().replace(' ', '_')}"

        name = slugify.slugify(name)

        description = workflow_metadata.get(
            "description",
            f"Execute workflow: {workflow_metadata.get('name', workflow_id)}",
        )

        json_schema = workflow_metadata.get("start_nodes", {})
        logger.debug(f"Workflow tool schema for {name}: {json_schema}")

        # Create the dynamic tool with our execution endpoint
        return self._create_workflow_execution_tool(
            run_id=run_id,
            workflow_id=workflow_id,
            name=name,
            description=description,
            json_schema=json_schema,
        )

    def _create_workflow_execution_tool(
        self,
        run_id: str,
        workflow_id: str,
        name: str,
        description: str,
        json_schema: Dict[str, Any],
    ) -> BaseTool:
        """
        Create a dynamic tool that executes a workflow.

        Args:
            workflow_id: ID of the workflow to execute
            name: Name for the tool
            description: Description for the tool
            json_schema: JSON schema for the tool parameters

        Returns:
            A BaseTool instance
        """
        logger.debug(
            f"Creating execution tool for workflow {workflow_id} with name {name}"
        )

        # Check if json_schema is a list (new format) or dict (old format)
        if isinstance(json_schema, list):
            # New format - list of field definitions
            logger.debug(
                f"Processing new schema format (list) with {len(json_schema)} fields"
            )
            fields = {}
            for field_def in json_schema:
                field_name = field_def.get("field")
                if not field_name:
                    continue

                # Determine the base type from the field definition
                field_type_str = field_def.get("type", "string")

                # Handle enum type specially
                if field_type_str == "enum" and "enum" in field_def:
                    py_type = str
                    logger.debug(
                        f"Added enum field {field_name} with values: {field_def.get('enum')}"
                    )
                else:
                    py_type = json_type_to_py.get(field_type_str, str)
                    logger.debug(f"Added field {field_name} with type {py_type}")

                # All fields are required in the new format
                fields[field_name] = (py_type, ...)
        else:
            # Old format - JSON Schema object
            logger.debug("Processing traditional JSON Schema format")
            fields = {}
            for k, v in json_schema.get("properties", {}).items():
                py_type = json_type_to_py.get(v.get("type", "string"), str)
                default = ... if k in json_schema.get("required", []) else None
                fields[k] = (py_type, default)
                logger.debug(f"Added field {k} with type {py_type} to tool schema")

        ArgsModel = create_model(f"{name}_Args", **fields)
        ArgsModel.model_rebuild()  # Ensure the model is fully defined
        logger.debug(f"Created args model for {name}: {ArgsModel}")

        class WorkflowTool(BaseTool[ArgsModel, BaseModel]):
            def __init__(self, loader: WorkflowToolLoader):
                super().__init__(
                    args_type=ArgsModel,
                    return_type=BaseModel,
                    name=name,
                    description=description,
                )
                self.workflow_id = workflow_id
                self.loader = loader
                self.run_id = run_id
                self.settings = get_settings()
                self.kafka_agent_response_topic = (
                    self.settings.kafka.kafka_agent_response_topic
                )
                logger.debug(f"Initialized WorkflowTool for {workflow_id}")

            async def run(self, args: ArgsModel, cancellation_token: CancellationToken):
                """
                Execute the workflow and return the result as a string.
                If the workflow returns a stream, join the stream and return the full result.
                If an error occurs, return a user-friendly error message.
                """
                args_dict = args.model_dump(exclude_none=True)

                payload = {
                    "workflow_id": self.workflow_id,
                    "approval": False,
                    "payload": {
                        "user_dependent_fields": list(args_dict.keys()),
                        "user_payload_template": args_dict,
                    },
                }
                logger.info(f"Workflow execution payload: {payload}")

                headers = [
                    ("correlationId", self.run_id.encode("utf-8")),
                    (
                        "reply-topic",
                        self.kafka_agent_response_topic.encode("utf-8"),
                    ),
                ]

                try:
                    logger.info(
                        f"Sending execution request for workflow {self.workflow_id}"
                    )
                    response = await asyncio.to_thread(
                        self.loader.http_client.post,
                        workflow_execute_endpoint,
                        json_data=payload,
                    )
                    logger.debug(f"Execution response: {response}")

                    if isinstance(response, dict) and "correlationId" in response:
                        correlation_id = response["correlationId"]
                        logger.info(
                            f"Got correlation ID from response: {correlation_id}"
                        )

                        await kafka_producer.init_kafka_producer()

                        resp = {
                            "run_id": self.run_id,
                            "correlation_id": correlation_id,
                            "message": "Agent initiated video generation",
                            "type": "workflow_execution",
                            "success": True,
                            "final": True,
                        }
                        await kafka_producer.send_message(
                            self.kafka_agent_response_topic,
                            resp,
                            headers,
                        )
                    else:
                        logger.warning(f"No correlation ID in response: {response}")

                    # # Stream the results
                    # async def stream_results():
                    #     stream_url = f"{stream_endpoint}/{correlation_id}"
                    #     logger.info(f"Starting to stream results from: {stream_url}")

                    #     async with httpx.AsyncClient(
                    #         base_url=self.loader.http_client.base_url,
                    #         headers=self.loader.http_client.headers,
                    #         timeout=300,
                    #     ) as client:
                    #         try:
                    #             logger.info(
                    #                 f"Opening stream connection to {stream_url}"
                    #             )
                    #             async with client.stream(
                    #                 HttpMethods.GET.value, stream_url
                    #             ) as response:
                    #                 response.raise_for_status()
                    #                 logger.info(
                    #                     f"Stream connection established, status: {response.status_code}"
                    #                 )

                    #                 buffer = ""
                    #                 async for chunk in response.aiter_text():
                    #                     logger.info(
                    #                         f"Received stream chunk of size: {len(chunk)} bytes"
                    #                     )
                    #                     buffer += chunk
                    #                     while "\n\n" in buffer:
                    #                         message, buffer = buffer.split("\n\n", 1)
                    #                         for line in message.split("\n"):
                    #                             if line.startswith("data:"):
                    #                                 data = line[5:].strip()
                    #                                 try:
                    #                                     json_data = json.loads(data)
                    #                                     logger.info(
                    #                                         f"Parsed JSON data from stream: {json_data}"
                    #                                     )
                    #                                     yield json.dumps(json_data)
                    #                                 except json.JSONDecodeError:
                    #                                     logger.warning(
                    #                                         f"Failed to parse JSON from stream: {data}"
                    #                                     )
                    #                                     yield data
                    #         except Exception as e:
                    #             logger.error(
                    #                 f"Error streaming results: {str(e)}", exc_info=True
                    #             )
                    #             yield json.dumps(
                    #                 {"error": f"Streaming error: {str(e)}"}
                    #             )

                    # # Join the stream and return the full result as a string
                    # logger.info(
                    #     f"Returning stream generator for workflow {self.workflow_id}"
                    # )
                    # result_chunks = []
                    # async for chunk in stream_results():
                    #     result_chunks.append(chunk)
                    # return "\n".join(result_chunks)

                    return {
                        "success": True,
                        "message": "Workflow execution initiated",
                        "correlation_id": correlation_id,
                    }

                except Exception as e:
                    logger.error(
                        f"Error executing workflow {self.workflow_id}: {str(e)}",
                        exc_info=True,
                    )
                    resp = {
                        "run_id": self.run_id,
                        "correlation_id": None,
                        "message": f"Workflow execution error: {str(e)}",
                        "type": "workflow_execution",
                        "success": False,
                        "final": True,
                    }
                    await kafka_producer.send_message(
                        self.kafka_agent_response_topic,
                        resp,
                        headers,
                    )
                    return f"Workflow execution error: {str(e)}"

            def return_value_as_string(self, value):
                return str(value)

            async def join_stream(self, value):
                """
                Helper to join all chunks from an async generator.
                If value is already a string, return as is.
                """
                logger.info(f"Joining stream for workflow {self.workflow_id}")
                if hasattr(value, "__aiter__"):
                    results = []
                    async for chunk in value:
                        logger.info(f"Appending chunk of size: {len(chunk)} bytes")
                        results.append(chunk)
                    joined = "\n".join(results)
                    logger.info(f"Joined stream, total size: {len(joined)} bytes")
                    return joined
                logger.info("Value is not a stream, returning as string")
                return str(value)

        logger.info(f"Created workflow tool: {name}")
        return WorkflowTool(self)

    async def load_workflows_as_tools(self, run_id, workflows) -> List[BaseTool]:
        """
        Load all available workflows as tools.

        Args:
            workflows_endpoint: API endpoint to get workflow metadata

        Returns:
            List of BaseTool instances for each workflow
        """
        try:
            logger.info(
                f"Loading workflows as tools, count: {len(workflows) if isinstance(workflows, list) else 'unknown'}"
            )

            if not isinstance(workflows, list):
                logger.error(
                    f"Error: Expected list of workflows, got {type(workflows)}"
                )
                return []

            # Create a tool for each workflow
            tools = []
            for workflow in workflows:
                try:
                    workflow_id = workflow.get("id", "unknown")
                    logger.info(f"Creating tool for workflow: {workflow_id}")
                    tool = self.create_workflow_tool_from_metadata(run_id, workflow)
                    tools.append(tool)
                    logger.info(
                        f"Successfully created tool for workflow: {workflow_id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Error creating tool for workflow {workflow.get('id', 'unknown')}: {str(e)}",
                        exc_info=True,
                    )

            logger.info(f"Successfully loaded {len(tools)} workflow tools")
            return tools

        except Exception as e:
            logger.error(f"Error loading workflows: {str(e)}", exc_info=True)
            return []
