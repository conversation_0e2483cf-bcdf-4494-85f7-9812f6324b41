from typing import Dict, Any, List, ClassVar
from abc import ABC, abstractmethod

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    IntInput,
    BoolInput,
)
from app.models.workflow_builder.components import Output
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input


class BaseHITLComponent(BaseNode, ABC):
    """
    Abstract base class for all Human-in-the-Loop (HITL) components.

    This class defines the common interface and functionality for components that
    facilitate human interaction within automated workflows. Concrete HITL components
    should inherit from this class and implement the build method.
    """

    # These should be overridden by subclasses
    name: ClassVar[str] = "BaseHITLComponent"
    display_name: ClassVar[str] = "Base HITL (Abstract)"
    description: ClassVar[str] = (
        "Abstract base class defining the common interface for HITL interactions."
    )

    category: ClassVar[str] = "Human-in-the-Loop"
    icon: ClassVar[str] = "UserCheck"

    # Flag to indicate this is an abstract base class that shouldn't be displayed in the UI
    is_abstract: ClassVar[bool] = True

    inputs: ClassVar[List[InputBase]] = [
        # Input data - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="input_data",
            display_name="Input Data",
            input_type="dict",
            required=False,
            info="Data passed into the component for context or processing. Can be connected from another node or entered directly.",
            input_types=["Any"],
        ),
        # Message - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="message",
            display_name="Message",
            input_type="string",
            required=False,
            info="Message or instructions for the human. Can be connected from another node or entered directly.",
            input_types=["string"],
        ),
        # Timeout - direct input in inspector
        IntInput(
            name="timeout_seconds",
            display_name="Timeout (s)",
            required=False,
            is_handle=False,
            value=3600,
            info="Maximum time (in seconds) to wait for a human response, if applicable.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(
            name="response_data",
            display_name="Human Response",
            output_type="Any",
        ),
        Output(
            name="action_taken",
            display_name="Action Taken",
            output_type="string",
            # Description would be in info if available: "Description of the primary action performed or its status (e.g., 'SENT', 'APPROVED', 'REJECTED')."
        ),
        Output(
            name="timed_out",
            display_name="Timed Out",
            output_type="bool",
            # "True if the component stopped waiting due to timeout."
        ),
        Output(
            name="error",
            display_name="Error",
            output_type="str",
            # "Any error message encountered during execution."
        ),
    ]

    def log(self, level: str, message: str):
        """
        Placeholder logging method.

        Args:
            level: The log level (e.g., 'info', 'warning', 'error')
            message: The message to log
        """
        print(f"[{level.upper()}] {message}")  # Placeholder logger

    def get_config(self, key: str) -> str:
        """
        Placeholder method to retrieve configuration values.

        Args:
            key: The configuration key to retrieve

        Returns:
            The configuration value as a string
        """
        return f"PLACEHOLDER_CONFIG_{key}"  # Placeholder config access

    @abstractmethod
    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Abstract method that must be implemented by concrete HITL components.

        This method contains the main execution logic for the component, including
        sending messages to humans, waiting for responses, and processing those responses.

        Args:
            **kwargs: Input values for the component.

        Returns:
            A dictionary with the component's outputs.
        """
        raise NotImplementedError("Subclasses must implement build method")
