"""
Conditional Component for Node Executor Service

This component processes switch-case conditional logic from the workflow service,
evaluating multiple conditions and routing data to matching outputs.

Supports 9 operators: equals, not_equals, contains, starts_with, ends_with,
greater_than, less_than, exists, is_empty
"""

import logging
import time
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


class ConditionalRequest(BaseModel):
    """Request schema for conditional component."""

    # Primary input data (optional)
    primary_input_data: Optional[Any] = Field(
        None,
        description="Main data to route through conditions"
    )

    # Number of conditions
    num_conditions: int = Field(
        default=2,
        ge=1,
        le=10,
        description="Number of conditions to evaluate (1-10)"
    )

    # Global context for variable resolution
    global_context: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Global context variables"
    )

    # Condition 1 configuration (required)
    condition_1_source: str = Field(
        ...,
        description="Source of data for condition evaluation"
    )
    condition_1_variable: Optional[str] = Field(
        None,
        description="Global context variable name (for global_context source)"
    )
    condition_1_operator: str = Field(
        ...,
        description="Comparison operator to apply"
    )
    condition_1_expected_value: Optional[str] = Field(
        None,
        description="Value to compare against"
    )
    condition_1_use_primary: bool = Field(
        default=True,
        description="Route primary input data when condition matches"
    )
    condition_1_custom_input: Optional[Any] = Field(
        None,
        description="Custom data to route when condition matches"
    )

    # Condition 2 configuration (optional)
    condition_2_source: Optional[str] = Field(
        None,
        description="Source of data for condition evaluation"
    )
    condition_2_variable: Optional[str] = Field(
        None,
        description="Global context variable name (for global_context source)"
    )
    condition_2_operator: Optional[str] = Field(
        None,
        description="Comparison operator to apply"
    )
    condition_2_expected_value: Optional[str] = Field(
        None,
        description="Value to compare against"
    )
    condition_2_use_primary: Optional[bool] = Field(
        None,
        description="Route primary input data when condition matches"
    )
    condition_2_custom_input: Optional[Any] = Field(
        None,
        description="Custom data to route when condition matches"
    )

    # Additional conditions can be added dynamically (3-10)
    # For now, we'll handle them in the process method

    @field_validator('condition_1_source')
    @classmethod
    def validate_source(cls, v):
        """Validate condition source."""
        valid_sources = ["node_output", "global_context"]
        if v not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        return v

    @field_validator('condition_1_operator')
    @classmethod
    def validate_operator(cls, v):
        """Validate condition operator."""
        valid_operators = [
            "equals", "not_equals", "contains", "starts_with", "ends_with",
            "greater_than", "less_than", "exists", "is_empty"
        ]
        if v not in valid_operators:
            raise ValueError(f"Operator must be one of {valid_operators}")
        return v


@register_component("ConditionalNode")
class ConditionalExecutor(BaseComponent):
    """
    Component for processing switch-case conditional logic.

    This component evaluates multiple conditions and routes data to matching outputs.
    Supports up to 10 conditions with 9 different operators.
    """

    def __init__(self):
        """Initialize the ConditionalExecutor."""
        logger.info("Initializing Conditional Executor Component")
        super().__init__()
        # Set the request schema for automatic validation
        self.request_schema = ConditionalRequest
        logger.info("Conditional Executor Component initialized successfully")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a conditional processing payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult indicating if the payload is valid
        """
        try:
            # Handle tool_parameters wrapper
            if "tool_parameters" in payload:
                parameters = payload["tool_parameters"]
            else:
                parameters = payload

            # Basic structure validation
            if not isinstance(parameters, dict):
                return ValidationResult(
                    is_valid=False,
                    error_message="Parameters must be a dictionary"
                )

            # Validate using schema (basic validation)
            # Note: We'll do more detailed validation in process method
            # since conditions can be dynamic (1-10)

            # Check required fields for condition 1
            required_fields = ["condition_1_source", "condition_1_operator"]
            for field in required_fields:
                if field not in parameters:
                    return ValidationResult(
                        is_valid=False,
                        error_message=f"Missing required field: {field}"
                    )

            # Validate operators
            valid_operators = [
                "equals", "not_equals", "contains", "starts_with", "ends_with",
                "greater_than", "less_than", "exists", "is_empty"
            ]

            num_conditions = parameters.get("num_conditions", 1)
            for i in range(1, num_conditions + 1):
                operator_field = f"condition_{i}_operator"
                if operator_field in parameters:
                    operator = parameters[operator_field]
                    if operator not in valid_operators:
                        return ValidationResult(
                            is_valid=False,
                            error_message=f"Invalid operator '{operator}' for condition {i}"
                        )

            # Validate sources
            valid_sources = ["node_output", "global_context"]
            for i in range(1, num_conditions + 1):
                source_field = f"condition_{i}_source"
                if source_field in parameters:
                    source = parameters[source_field]
                    if source not in valid_sources:
                        return ValidationResult(
                            is_valid=False,
                            error_message=f"Invalid source '{source}' for condition {i}"
                        )

            logger.info("Conditional payload validation successful")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_message = f"Validation error: {str(e)}"
            logger.error(f"Conditional payload validation failed: {error_message}")
            return ValidationResult(is_valid=False, error_message=error_message)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process conditional logic with switch-case evaluation.

        Args:
            payload: The processing payload

        Returns:
            Dictionary containing the processing result
        """
        start_time = time.time()
        request_id = payload.get("request_id", "unknown")

        try:
            logger.info(f"Processing conditional logic for request {request_id}")

            # Handle tool_parameters wrapper
            if "tool_parameters" in payload:
                parameters = payload["tool_parameters"]
            else:
                parameters = payload

            # Get number of conditions
            num_conditions = parameters.get("num_conditions", 1)
            num_conditions = max(1, min(10, int(num_conditions)))  # Clamp to 1-10

            # Get global context
            global_context = parameters.get("global_context", {})

            # Initialize result
            result = {}
            matched_conditions = []

            # Evaluate each condition
            for condition_num in range(1, num_conditions + 1):
                try:
                    # Get condition configuration
                    condition_config = self._extract_condition_config(parameters, condition_num)

                    if not condition_config:
                        continue  # Skip if condition not configured

                    # Get input data for this condition
                    input_data = self._get_condition_input_data(parameters, condition_num)

                    # Evaluate the condition
                    condition_matches = self._evaluate_condition(
                        condition_config,
                        input_data,
                        global_context
                    )

                    if condition_matches:
                        matched_conditions.append(condition_num)
                        output_name = f"condition_{condition_num}_output"
                        result[output_name] = input_data
                        logger.info(f"Condition {condition_num} matched - routing to {output_name}")

                except Exception as e:
                    logger.warning(f"Error evaluating condition {condition_num}: {e}")
                    # Continue with other conditions

            # If no conditions matched, route to default output
            if not matched_conditions:
                # Use primary input data for default output
                default_data = self._unwrap_dual_purpose_input(
                    parameters.get("primary_input_data")
                )
                result["default_output"] = default_data
                logger.info("No conditions matched - routing to default_output")

            execution_time = time.time() - start_time
            logger.info(f"Conditional processing completed. Matched conditions: {matched_conditions}. Time: {execution_time:.2f}s")

            return {
                "status": "success",
                "result": result,
                "execution_time": execution_time,
                "request_id": request_id
            }

        except Exception as e:
            execution_time = time.time() - start_time
            error_message = f"Error in conditional processing: {str(e)}"
            logger.error(f"Conditional processing failed for request {request_id}: {error_message}")

            return {
                "status": "error",
                "error": error_message,
                "execution_time": execution_time,
                "request_id": request_id
            }

    def _extract_condition_config(self, parameters: Dict[str, Any], condition_num: int) -> Optional[Dict[str, Any]]:
        """
        Extract condition configuration from parameters.

        Args:
            parameters: The request parameters
            condition_num: The condition number (1, 2, etc.)

        Returns:
            Dictionary containing condition configuration or None if not found
        """
        source_key = f"condition_{condition_num}_source"
        operator_key = f"condition_{condition_num}_operator"

        # Check if this condition is configured
        if source_key not in parameters or operator_key not in parameters:
            return None

        config = {
            "source": parameters[source_key],
            "operator": parameters[operator_key],
            "expected_value": parameters.get(f"condition_{condition_num}_expected_value", ""),
            "variable": parameters.get(f"condition_{condition_num}_variable", "")
        }

        return config

    def _get_condition_input_data(self, parameters: Dict[str, Any], condition_num: int) -> Any:
        """
        Get input data for a specific condition based on routing configuration.

        Args:
            parameters: The request parameters
            condition_num: The condition number (1, 2, etc.)

        Returns:
            The input data for the condition
        """
        use_primary_key = f"condition_{condition_num}_use_primary"
        custom_input_key = f"condition_{condition_num}_custom_input"

        use_primary = parameters.get(use_primary_key, True)

        if use_primary:
            # Use primary input data
            return self._unwrap_dual_purpose_input(
                parameters.get("primary_input_data")
            )
        else:
            # Use custom input for this condition
            return self._unwrap_dual_purpose_input(
                parameters.get(custom_input_key)
            )

    def _unwrap_dual_purpose_input(self, input_data: Any) -> Any:
        """
        Unwrap dual-purpose input data if it's wrapped.

        Args:
            input_data: The input data (may be wrapped)

        Returns:
            The unwrapped input data
        """
        if isinstance(input_data, dict) and "value" in input_data and "transition_id" in input_data:
            # This is a wrapped dual-purpose input
            return input_data["value"]
        else:
            # This is unwrapped data
            return input_data

    def _evaluate_condition(self, condition_config: Dict[str, Any], input_data: Any, global_context: Dict[str, Any]) -> bool:
        """
        Evaluate a single condition based on configuration.

        Args:
            condition_config: Dictionary containing condition configuration
            input_data: Input data for node_output source
            global_context: Global context variables

        Returns:
            True if condition matches, False otherwise
        """
        try:
            operator = condition_config.get("operator", "equals")
            source = condition_config.get("source", "node_output")
            expected_value = condition_config.get("expected_value", "")

            # Get the actual value to compare
            if source == "global_context":
                variable_name = condition_config.get("variable", "")
                actual_value = self._resolve_global_context_variable(variable_name, global_context)
            else:  # node_output
                actual_value = input_data

            # Evaluate based on operator
            return self._apply_operator(operator, actual_value, expected_value)

        except Exception as e:
            # Log error but don't fail the entire execution - skip this condition
            logger.warning(f"Error evaluating condition: {e}")
            return False

    def _apply_operator(self, operator: str, actual_value: Any, expected_value: Any) -> bool:
        """
        Apply the specified operator to compare actual and expected values.

        Args:
            operator: The comparison operator
            actual_value: The actual value to compare
            expected_value: The expected value to compare against

        Returns:
            True if the comparison matches, False otherwise
        """
        try:
            if operator == "equals":
                return str(actual_value) == str(expected_value)

            elif operator == "not_equals":
                return str(actual_value) != str(expected_value)

            elif operator == "contains":
                actual_str = str(actual_value) if actual_value is not None else ""
                expected_str = str(expected_value) if expected_value is not None else ""
                return expected_str in actual_str

            elif operator == "starts_with":
                actual_str = str(actual_value) if actual_value is not None else ""
                expected_str = str(expected_value) if expected_value is not None else ""
                return actual_str.startswith(expected_str)

            elif operator == "ends_with":
                actual_str = str(actual_value) if actual_value is not None else ""
                expected_str = str(expected_value) if expected_value is not None else ""
                return actual_str.endswith(expected_str)

            elif operator == "greater_than":
                try:
                    actual_num = float(actual_value)
                    expected_num = float(expected_value)
                    return actual_num > expected_num
                except (ValueError, TypeError):
                    return False

            elif operator == "less_than":
                try:
                    actual_num = float(actual_value)
                    expected_num = float(expected_value)
                    return actual_num < expected_num
                except (ValueError, TypeError):
                    return False

            elif operator == "exists":
                return actual_value is not None

            elif operator == "is_empty":
                if actual_value is None:
                    return True
                if isinstance(actual_value, str) and actual_value == "":
                    return True
                return False

            else:
                # Invalid operator - skip condition
                logger.warning(f"Unknown operator '{operator}' - skipping condition")
                return False

        except Exception as e:
            logger.warning(f"Error applying operator '{operator}': {e}")
            return False

    def _resolve_global_context_variable(self, variable_name: str, global_context: Dict[str, Any]) -> Any:
        """
        Resolve a global context variable by name.

        Args:
            variable_name: Name of the variable to resolve
            global_context: Global context dictionary

        Returns:
            The variable value or None if not found
        """
        if not variable_name:
            return None
        return global_context.get(variable_name)
