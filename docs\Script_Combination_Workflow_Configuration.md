# Script Combination Workflow Configuration

## 🎯 **Workflow Overview**

This configuration creates a workflow that combines multiple script outputs from SelectDataComponent instances into a single final script using CombineTextComponent.

## 📋 **Component Configuration Details**

### **1. MCP Script Generator**
- **Purpose**: Generates script data array
- **Output**: Array of script objects with property-based structure
- **Configuration**: Use existing MCP configuration

### **2. SelectDataComponent_1 (First Script Extractor)**

**Node Configuration:**
```json
{
  "name": "SelectDataComponent_1",
  "display_name": "Extract Script 1",
  "config": {
    "data_type": "Auto-Detect",
    "search_mode": "Smart Search",
    "field_matching_mode": "Auto-detect",
    "selector": "script"
  }
}
```

**Connections:**
- **Input**: `input_data` ← MCP Script Generator output
- **Output**: `output_data` → Script text (string)

### **3. SelectDataComponent_2 (Second Script Extractor)**

**Node Configuration:**
```json
{
  "name": "SelectDataComponent_2", 
  "display_name": "Extract Script 2",
  "config": {
    "data_type": "Auto-Detect",
    "search_mode": "Smart Search", 
    "field_matching_mode": "Auto-detect",
    "selector": "script"
  }
}
```

**Connections:**
- **Input**: `input_data` ← MCP Script Generator output
- **Output**: `output_data` → Script text (string)

### **4. CombineTextComponent (Final Combiner)**

**Node Configuration:**
```json
{
  "name": "CombineTextComponent",
  "display_name": "Combine Scripts",
  "config": {
    "num_additional_inputs": 1,
    "separator": "\\n\\n--- SCRIPT TRANSITION ---\\n\\n"
  }
}
```

**Connections:**
- **Main Input**: `main_input` ← SelectDataComponent_1 `output_data`
- **Input 1**: `input_1` ← SelectDataComponent_2 `output_data`
- **Output**: `output_text` → Final combined script

## 🔄 **Alternative: Structured Data Preservation**

For preserving metadata before text combination:

### **Enhanced SelectDataComponent Configuration**

**Extract Full Objects Instead of Just Text:**
```json
{
  "data_type": "List",
  "search_mode": "Exact Path",
  "field_matching_mode": "Auto-detect",
  "selector": "0"  // First object
}
```

### **MergeDataComponent Configuration**

**Merge Script Objects:**
```json
{
  "name": "MergeDataComponent",
  "display_name": "Merge Script Data",
  "config": {
    "num_additional_inputs": 1,
    "merge_strategy": "Deep Merge"
  }
}
```

**Connections:**
- **Main Input**: `main_input` ← SelectDataComponent_1 `output_data`
- **Input 1**: `input_1` ← SelectDataComponent_2 `output_data`
- **Output**: `output_data` → Merged script object

## 🎨 **Separator Options for Script Combination**

### **Professional Script Separators:**
1. **Scene Break**: `"\\n\\n--- SCENE BREAK ---\\n\\n"`
2. **Act Transition**: `"\\n\\n[ACT TRANSITION]\\n\\n"`
3. **Time Cut**: `"\\n\\n[TIME CUT]\\n\\n"`
4. **Location Change**: `"\\n\\n[LOCATION: NEW SCENE]\\n\\n"`

### **Simple Separators:**
1. **Double Line**: `"\\n\\n"`
2. **Triple Line**: `"\\n\\n\\n"`
3. **Dashed Line**: `"\\n\\n---\\n\\n"`
4. **Numbered Parts**: `"\\n\\n=== PART 2 ===\\n\\n"`

## 📊 **Expected Data Flow**

### **Input Data (MCP Script Generator Output):**
```json
[
  {
    "data": "marketing",
    "data_type": "string", 
    "property_name": "title"
  },
  {
    "data": "[Scene 1 script content...]",
    "data_type": "string",
    "property_name": "script"
  },
  {
    "data": "TOPIC",
    "data_type": "string",
    "property_name": "script_type"
  }
]
```

### **SelectDataComponent Output:**
```
"[Scene 1 script content...]"
```

### **CombineTextComponent Output:**
```
"[Scene 1 script content...]

--- SCRIPT TRANSITION ---

[Scene 1 script content...]"
```

## ⚙️ **Implementation Considerations**

### **1. Connection Handling**
- Both SelectDataComponent instances connect to the **same** MCP Script Generator output
- This creates a fan-out pattern where one output feeds multiple components
- The workflow engine handles this automatically

### **2. Text Processing**
- SelectDataComponent extracts raw script text (string)
- CombineTextComponent handles string concatenation with separators
- No additional text processing needed

### **3. Error Handling**
- If either SelectDataComponent fails, the workflow will stop
- Consider adding error handling or default values
- Monitor for empty script extractions

### **4. Performance**
- Script text can be large (several KB)
- Consider memory usage for very long scripts
- Text concatenation is efficient for reasonable script sizes

## 🚀 **Deployment Steps**

1. **Update SelectDataComponent**: Ensure both instances use identical configurations
2. **Configure CombineTextComponent**: Set appropriate separator for script flow
3. **Test Data Flow**: Verify script extraction and combination
4. **Validate Output**: Check final script formatting and readability
5. **Monitor Performance**: Ensure acceptable processing times

## 🎯 **Expected Results**

- **Consistent Extraction**: Both SelectDataComponent instances will extract identical script text
- **Clean Combination**: Scripts will be combined with clear separators
- **Preserved Formatting**: Original script formatting maintained
- **Single Output**: Final workflow produces one combined script text
- **Reliable Processing**: Consistent results across multiple executions
