from typing import Optional
from pydantic import BaseModel


class AgentCreationRequest(BaseModel):
    agent_id: str
    user_id: str
    communication_type: str
    run_id: str


class AgentChatRequest(BaseModel):
    run_id: str
    session_id: str
    chat_context: list[dict[str, str]]


class AgentChatResponse(BaseModel):
    run_id: str
    session_id: str
    agent_response: Optional[dict] = None
    success: bool = True
    message: Optional[str] = None
    final: Optional[bool] = False
