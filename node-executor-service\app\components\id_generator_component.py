"""
ID Generator Component - Generates various types of unique identifiers.
"""
import logging
import traceback
import uuid
import time
import random
import string
import asyncio
from typing import Dict, Any, List, Optional, Union, ClassVar
from pydantic import BaseModel, Field, ValidationError

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

# Import the logger configuration
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("IDGeneratorComponent")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class IDGeneratorRequest(BaseModel):
    """Schema for ID generation requests."""
    id_type: str = Field("UUIDv4", description="The type of unique identifier to generate")
    short_id_length: int = Field(8, description="The length of the short ID (only used when ID Type is 'Short ID')")


@register_component("IDGeneratorComponent")
class IDGeneratorComponent(BaseComponent):
    """
    Generates various types of unique identifiers.

    This component can generate different types of unique identifiers including:
    - UUIDv4: Standard universally unique identifier
    - Timestamp ID: Time-based identifier
    - Short ID: Configurable length alphanumeric identifier
    """

    def __init__(self):
        """
        Initialize the IDGeneratorComponent.
        """
        logger.info("Initializing ID Generator Component")
        super().__init__()
        # Set the request schema for automatic validation
        self.request_schema = IDGeneratorRequest
        logger.info("ID Generator Component initialized successfully")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate an ID generator payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult: The result of the validation
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating ID generator payload for request_id: {request_id}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for validation.")
            # Use the tool_parameters as the actual payload for validation
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        # Use the parent class validation which uses the request schema
        validation_result = await super().validate(parameters)

        if not validation_result.is_valid:
            logger.error(f"Validation failed: {validation_result.error_message}")
            return validation_result

        # Additional validation beyond schema
        id_type = parameters.get("id_type", "UUIDv4")
        short_id_length = parameters.get("short_id_length", 8)

        # Validate id_type
        valid_id_types = ["UUIDv4", "Timestamp ID", "Short ID"]
        if id_type not in valid_id_types:
            error_message = f"Invalid ID type: {id_type}. Must be one of {valid_id_types}"
            logger.error(error_message)
            return ValidationResult(is_valid=False, error_message=error_message)

        # Validate short_id_length if id_type is Short ID
        if id_type == "Short ID":
            if not isinstance(short_id_length, int) or short_id_length <= 0:
                error_message = f"Short ID length must be a positive integer, got: {short_id_length}"
                logger.error(error_message)
                return ValidationResult(is_valid=False, error_message=error_message)

        logger.info("Payload validation successful")
        return ValidationResult(is_valid=True)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Executes the ID generation operation.

        Args:
            payload: The request payload containing:
                - id_type: The type of ID to generate ("UUIDv4", "Timestamp ID", or "Short ID")
                - short_id_length: The length of the short ID (only used when id_type is "Short ID")

        Returns:
            A dictionary with either:
                - status: "success" or "error"
                - result: The generated unique ID (on success)
                - error: An error message (on error)
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing ID generation request for request_id: {request_id}")
        logger.debug(f"Full payload for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for parameters.")
            # Use the tool_parameters as the actual payload
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        try:
            # Get input values
            id_type = parameters.get("id_type", "UUIDv4")
            short_id_length = parameters.get("short_id_length", 8)

            # Generate ID based on type
            if id_type == "UUIDv4":
                unique_id = str(uuid.uuid4())
                logger.info(f"Generated UUIDv4: {unique_id}")
                return {"result": unique_id}

            elif id_type == "Timestamp ID":
                # Generate millisecond timestamp
                unique_id = str(int(time.time() * 1000))
                logger.info(f"Generated Timestamp ID: {unique_id}")
                return {"result": unique_id}

            elif id_type == "Short ID":
                # Validate short_id_length
                if not isinstance(short_id_length, int) or short_id_length <= 0:
                    error_msg = f"Short ID length must be a positive integer, got: {short_id_length}"
                    logger.error(error_msg)
                    return {"error": error_msg}

                # Generate random alphanumeric string
                chars = string.ascii_letters + string.digits
                unique_id = ''.join(random.choices(chars, k=short_id_length))
                logger.info(f"Generated Short ID (length {short_id_length}): {unique_id}")
                return {"result": unique_id}

            else:
                error_msg = f"Unknown ID type: {id_type}"
                logger.error(error_msg)
                return {"error": error_msg}

        except Exception as e:
            error_msg = f"Error generating ID: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details: {traceback.format_exc()}")
            return {"error": error_msg}
