import React, { useState, useEffect } from "react";
import { Node } from "reactflow";
import { WorkflowNodeData, InputDefinition } from "@/types";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { InputRenderer } from "../InputRenderer";
import { checkInputVisibility } from "@/utils/inputVisibility";
import { 
  useComponentStateStore, 
  clearMcpToolsState, 
  clearAllMcpToolsState 
} from "@/store/mcpToolsStore";
import { fetchMCPTools, debugMCPTools } from "@/lib/api";

interface MCPNodeInspectorProps {
  node: Node<WorkflowNodeData>;
  onConfigChange: (inputName: string, value: any) => void;
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void;
  isInputConnected: (inputName: string) => boolean;
  shouldDisableInput: (inputName: string) => boolean;
  getConnectionInfo: (inputName: string) => {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };
  setNotification: (notification: {
    isOpen: boolean;
    title: string;
    message: string;
    preserveState?: boolean;
  }) => void;
}

/**
 * Specialized component for MCP node settings
 */
export function MCPNodeInspector({
  node,
  onConfigChange,
  onNodeDataChange,
  isInputConnected,
  shouldDisableInput,
  getConnectionInfo,
  setNotification,
}: MCPNodeInspectorProps) {
  // State for MCP tool schemas
  const [toolSchemas, setToolSchemas] = useState<Record<string, any>>({});
  
  // Get the component state store
  const componentState = useComponentStateStore();
  
  // Helper function to get config value
  const getConfigValue = (inputName: string, defaultValue: any) => {
    if (inputName === "sse_url") {
      return componentState.getValue(
        node.id,
        "sse_url",
        node.data.config?.sse_url || defaultValue
      );
    }
    if (inputName === "mcp_type") {
      return componentState.getValue(
        node.id,
        "mcp_type",
        node.data.config?.mcp_type || defaultValue
      );
    }
    if (inputName === "selected_tool_name") {
      return componentState.getValue(
        node.id,
        "selected_tool_name",
        node.data.config?.selected_tool_name || defaultValue
      );
    }
    // For connection_status, check if it's in the raw inputs
    if (inputName === "connection_status") {
      const rawInputs = node.data.config?.inputs || [];
      const statusInput = rawInputs.find((input: any) => input.name === "connection_status");
      if (statusInput && statusInput.value) {
        return statusInput.value;
      }
      if (node.data.config?.connection_status) {
        return node.data.config.connection_status;
      }
    }
    
    return node.data.config?.[inputName] ?? defaultValue;
  };
  
  // Handle button clicks for MCP tools
  const handleMCPButtonClick = async (inputDef: InputDefinition) => {
    try {
      // Create a clean config with only essential properties
      const currentConfig: any = {
        mode: getConfigValue("mode", "Stdio"),
        command: getConfigValue("command", ""),
        sse_url: getConfigValue("sse_url", ""),
        selected_tool_name: getConfigValue("selected_tool_name", ""),
        connection_status: getConfigValue("connection_status", "Not Connected"),
      };

      // For selected_tool_name, make sure we use the new value
      if (inputDef.name === "selected_tool_name") {
        currentConfig.selected_tool_name = inputDef.value;
      }

      // For clear state buttons, also clear the local storage
      if (inputDef.name === "clear_stdio_state" || inputDef.name === "clear_sse_state") {
        clearMcpToolsState(node.id);
      }

      // For SSE mode, make sure the SSE URL is included
      if (inputDef.name === "fetch_sse_tools") {
        // Get the current SSE URL value
        const sseUrlValue = getConfigValue("sse_url", "");
        
        // Make sure mode is set to SSE
        currentConfig.mode = "SSE";
        currentConfig.sse_url = sseUrlValue;
      } else if (inputDef.name === "fetch_stdio_tools") {
        // Get the current command value
        const commandValue = getConfigValue("command", "");
        
        // Make sure mode is set to Stdio
        currentConfig.mode = "Stdio";
        currentConfig.command = commandValue;
      }

      // Send debug info to backend
      await debugMCPTools({
        node_type: node.data.type,
        button_name: inputDef.name,
        node_config: currentConfig,
      });

      // Use direct API call to fetch tools
      const fetchResult = await fetchMCPTools(currentConfig, inputDef.name);

      if (fetchResult.success) {
        // Update the node with the new configuration
        const newConfig = fetchResult.result;
        const newData = {
          ...node.data,
          config: newConfig,
        };
        onNodeDataChange(node.id, newData);

        // Check if there was a connection error in the response
        const connectionStatus = newConfig.status || newConfig.connection_status;
        const connectionError = newConfig.error;

        if (
          connectionStatus === "Error" ||
          connectionStatus === "Connection Failed" ||
          connectionError
        ) {
          // Show error message with custom notification
          setNotification({
            isOpen: true,
            title: "Connection Error",
            message: connectionError || "Failed to connect to MCP server",
            preserveState: true,
          });
          return;
        }

        // Extract and store tool schemas from the response
        try {
          // Extract tool schemas from the response
          const toolsList = newConfig._internal_state?.mcp_tools_list || [];
          const extractedSchemas: Record<string, any> = {};

          toolsList.forEach((tool: any) => {
            if (tool && tool.name) {
              // Store the tool schema by name
              extractedSchemas[tool.name] = {
                name: tool.name,
                description: tool.description || "",
                schema: tool.input_schema || {},
              };
            }
          });

          // Update the tool schemas state
          setToolSchemas(extractedSchemas);
        } catch (error) {
          console.error("Error extracting tool schemas:", error);
        }

        // Show success message with custom notification
        setNotification({
          isOpen: true,
          title: "Success",
          message: "MCP tools fetched successfully!",
          preserveState: true,
        });
      } else {
        // Show error message with custom notification
        setNotification({
          isOpen: true,
          title: "Error",
          message: `Failed to fetch MCP tools: ${fetchResult.message || "Unknown error"}`,
          preserveState: true,
        });
      }
    } catch (error: any) {
      console.error("Error fetching MCP tools:", error);
      setNotification({
        isOpen: true,
        title: "Error",
        message: `Error fetching MCP tools: ${error.message || String(error)}`,
        preserveState: true,
      });
    }
  };
  
  // Generate dynamic inputs based on tool schema
  const generateDynamicInputs = (toolName: string, schema: any) => {
    if (!schema || !schema.properties) {
      return [];
    }

    const properties = schema.properties;
    const dynamicInputs = [];

    // Generate inputs based on schema properties
    for (const [propName, propDef] of Object.entries(properties)) {
      const propType = (propDef as any).type || "string";
      const propDesc = (propDef as any).description || `Input for ${propName}`;

      if (propType === "number" || propType === "integer") {
        dynamicInputs.push({
          name: `tool_arg_${propName}`,
          display_name: propName.charAt(0).toUpperCase() + propName.slice(1),
          info: propDesc,
          input_type: "int",
          value: (propDef as any).default || 0,
        });
      } else if (propType === "object") {
        // Special handling for object type inputs
        const nestedProperties = (propDef as any).properties || {};

        // Create an input with object type
        const objectInput = {
          name: `tool_arg_${propName}`,
          display_name: propName.charAt(0).toUpperCase() + propName.slice(1),
          info: propDesc,
          input_type: "object", // Explicitly set to object type
          properties: nestedProperties,
          value: (propDef as any).default || {},
        };

        dynamicInputs.push(objectInput);
      } else if (propType === "array") {
        // Handle array type inputs
        dynamicInputs.push({
          name: `tool_arg_${propName}`,
          display_name: propName.charAt(0).toUpperCase() + propName.slice(1),
          info: propDesc,
          input_type: "array",
          value: (propDef as any).default || [],
        });
      } else {
        // Default to string for other types
        dynamicInputs.push({
          name: `tool_arg_${propName}`,
          display_name: propName.charAt(0).toUpperCase() + propName.slice(1),
          info: propDesc,
          input_type: "string",
          value: (propDef as any).default || "",
        });
      }
    }

    return dynamicInputs;
  };
  
  return (
    <div className="space-y-6">
      {/* Basic Settings */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Basic Settings</h3>
        {node.data.definition?.inputs
          .filter(inputDef => 
            !inputDef.is_handle && 
            ["mode", "command", "sse_url"].includes(inputDef.name)
          )
          .map((inputDef) => {
            // Check visibility rules
            const isVisible = checkInputVisibility(
              inputDef,
              node,
              node.data.config || {},
            );
            if (!isVisible) return null;

            // Special handling for mode dropdown
            if (inputDef.name === "mode") {
              return (
                <div key={inputDef.name} className="pb-2">
                  <Label
                    htmlFor={`config-${node?.id}-${inputDef.name}`}
                    className="flex items-center justify-between text-xs font-medium"
                  >
                    <span>{inputDef.display_name}</span>
                    {inputDef.required && (
                      <Badge
                        variant="destructive"
                        className="h-4 px-1 text-[9px]"
                      >
                        Required
                      </Badge>
                    )}
                  </Label>
                  {inputDef.info && (
                    <p className="text-muted-foreground mt-0.5 mb-1.5 text-xs">
                      {inputDef.info}
                    </p>
                  )}
                  <InputRenderer
                    inputDef={inputDef}
                    value={getConfigValue(inputDef.name, inputDef.value)}
                    onChange={(name, value) => {
                      // Clear state when switching modes
                      if (name === "mode") {
                        clearMcpToolsState(node.id);
                      }
                      onConfigChange(name, value);
                    }}
                    isDisabled={shouldDisableInput(inputDef.name)}
                    isConnected={isInputConnected(inputDef.name)}
                    connectionInfo={getConnectionInfo(inputDef.name)}
                    nodeId={node.id}
                  />
                  
                  {/* Add buttons to clear MCP tools state */}
                  {inputDef.name === "mode" && (
                    <div className="mt-2 flex flex-col gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full text-xs"
                        onClick={() => {
                          clearMcpToolsState(node.id);
                          setNotification({
                            isOpen: true,
                            title: "MCP Tools State Cleared",
                            message: `State cleared for node ${node.id}`,
                            preserveState: true,
                          });
                        }}
                      >
                        Clear This Node's Settings
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-destructive border-destructive hover:bg-destructive/10 w-full text-xs"
                        onClick={() => {
                          clearAllMcpToolsState();
                          setNotification({
                            isOpen: true,
                            title: "All MCP Tools State Cleared",
                            message: "Settings cleared for all MCP nodes",
                            preserveState: true,
                          });
                        }}
                      >
                        Clear All MCP Nodes' Settings
                      </Button>
                    </div>
                  )}
                </div>
              );
            }
            
            return (
              <div key={inputDef.name} className="pb-2">
                <Label
                  htmlFor={`config-${node?.id}-${inputDef.name}`}
                  className="flex items-center justify-between text-xs font-medium"
                >
                  <span>{inputDef.display_name}</span>
                  {inputDef.required && (
                    <Badge
                      variant="destructive"
                      className="h-4 px-1 text-[9px]"
                    >
                      Required
                    </Badge>
                  )}
                </Label>
                {inputDef.info && (
                  <p className="text-muted-foreground mt-0.5 mb-1.5 text-xs">
                    {inputDef.info}
                  </p>
                )}
                <InputRenderer
                  inputDef={inputDef}
                  value={getConfigValue(inputDef.name, inputDef.value)}
                  onChange={onConfigChange}
                  isDisabled={shouldDisableInput(inputDef.name)}
                  isConnected={isInputConnected(inputDef.name)}
                  connectionInfo={getConnectionInfo(inputDef.name)}
                  nodeId={node.id}
                />
              </div>
            );
          })}
          
        {/* Connection buttons */}
        <div className="space-y-2">
          {node.data.definition?.inputs
            .filter(inputDef => 
              inputDef.input_type === "button" && 
              ["fetch_stdio_tools", "fetch_sse_tools"].includes(inputDef.name)
            )
            .map((inputDef) => {
              // Check if this button should be visible based on the current mode
              const currentMode = getConfigValue("mode", "Stdio");
              const isVisible = 
                (inputDef.name === "fetch_stdio_tools" && currentMode === "Stdio") ||
                (inputDef.name === "fetch_sse_tools" && currentMode === "SSE");
                
              if (!isVisible) return null;
              
              return (
                <div key={inputDef.name} className="pb-2">
                  <Button
                    onClick={() => handleMCPButtonClick(inputDef)}
                    className="h-8 w-full text-xs"
                    variant="default"
                  >
                    {inputDef.display_name || "Connect"}
                  </Button>
                </div>
              );
            })}
        </div>
      </div>
      
      {/* Tool Selection */}
      {node.data.config?.connection_status === "Connected" && (
        <div className="space-y-4">
          <h3 className="text-sm font-medium">Tool Selection</h3>
          {node.data.definition?.inputs
            .filter(inputDef => inputDef.name === "selected_tool_name")
            .map((inputDef) => {
              // Get the options from the config
              const rawConfig = node.data.config || {};
              const rawInputs = rawConfig.inputs || [];
              
              // Find the selected_tool_name input in the raw config
              const toolInput = rawInputs.find((input: any) => input.name === "selected_tool_name");
              
              if (toolInput && Array.isArray(toolInput.options) && toolInput.options.length > 0) {
                // Process options from the raw config
                const processedOptions = toolInput.options.map((option: any) => {
                  if (typeof option === "string") {
                    return { value: option, label: option };
                  }
                  return option;
                });
                
                // Create a modified input definition with the processed options
                const modifiedInputDef = {
                  ...inputDef,
                  options: processedOptions,
                };
                
                return (
                  <div key={inputDef.name} className="pb-2">
                    <Label
                      htmlFor={`config-${node?.id}-${inputDef.name}`}
                      className="flex items-center justify-between text-xs font-medium"
                    >
                      <span>{inputDef.display_name}</span>
                      {inputDef.required && (
                        <Badge
                          variant="destructive"
                          className="h-4 px-1 text-[9px]"
                        >
                          Required
                        </Badge>
                      )}
                    </Label>
                    {inputDef.info && (
                      <p className="text-muted-foreground mt-0.5 mb-1.5 text-xs">
                        {inputDef.info}
                      </p>
                    )}
                    <InputRenderer
                      inputDef={modifiedInputDef}
                      value={getConfigValue(inputDef.name, inputDef.value)}
                      onChange={(name, value) => {
                        onConfigChange(name, value);
                        
                        // Handle tool selection
                        setTimeout(() => {
                          if (!node) return;
                          const currentConfig = node.data.config || {};
                          const selectedToolSchema = toolSchemas[value];
                          let dynamicInputs = [];
                          
                          if (selectedToolSchema && selectedToolSchema.schema) {
                            dynamicInputs = generateDynamicInputs(value, selectedToolSchema.schema);
                          } else {
                            dynamicInputs = [
                              {
                                name: "tool_arg_input",
                                display_name: "Input",
                                info: "Input for the tool",
                                input_type: "string",
                                value: "",
                              },
                            ];
                          }
                          
                          const defaultToolParams: Record<string, any> = {};
                          if (
                            selectedToolSchema &&
                            selectedToolSchema.schema &&
                            selectedToolSchema.schema.properties
                          ) {
                            for (const [param, def] of Object.entries(selectedToolSchema.schema.properties)) {
                              const typedDef = def as Record<string, any>;
                              if ("default" in typedDef) {
                                defaultToolParams[param] = typedDef.default;
                              } else if (typedDef.type === "number" || typedDef.type === "integer") {
                                defaultToolParams[param] = 0;
                              } else {
                                defaultToolParams[param] = "";
                              }
                            }
                          }
                          
                          const updatedConfig = {
                            ...currentConfig,
                            selected_tool_name: value,
                            tool_params: defaultToolParams,
                            mode: currentConfig.mode || "SSE",
                            connection_status: "Connected",
                            _dynamic_inputs: dynamicInputs,
                            ...Object.fromEntries(
                              Object.entries(currentConfig).filter(([k]) => !k.startsWith("tool_arg_")),
                            ),
                            inputs: [
                              ...(currentConfig.inputs || [])
                                .filter((input: any) => !input.name.startsWith("tool_arg_"))
                                .map((input: any) => {
                                  if (input.name === "selected_tool_name") {
                                    return {
                                      ...input,
                                      value: value,
                                    };
                                  }
                                  return input;
                                }),
                              ...dynamicInputs,
                            ],
                          };
                          
                          const newData = {
                            ...node.data,
                            config: updatedConfig,
                          };
                          
                          onNodeDataChange(node.id, newData);
                          
                          setNotification({
                            isOpen: true,
                            title: "Tool Selected",
                            message: `Selected tool: ${value}`,
                            preserveState: true,
                          });
                        }, 100);
                      }}
                      isDisabled={shouldDisableInput(inputDef.name)}
                      isConnected={isInputConnected(inputDef.name)}
                      connectionInfo={getConnectionInfo(inputDef.name)}
                      nodeId={node.id}
                    />
                  </div>
                );
              }
              
              return null;
            })}
        </div>
      )}
      
      {/* Tool Parameters */}
      {node.data.config?.selected_tool_name && (
        <div className="space-y-4">
          <h3 className="text-sm font-medium">Tool Parameters</h3>
          {node.data.config?.inputs
            ?.filter((inputDef: any) => inputDef.name.startsWith("tool_arg_"))
            .map((inputDef: any) => {
              // Check if this input should be visible
              const isVisible = checkInputVisibility(
                inputDef,
                node,
                node.data.config || {},
              );
              if (!isVisible) return null;
              
              // Format the display name to remove the tool_arg_ prefix
              const cleanName = inputDef.name.replace("tool_arg_", "");
              const displayName = inputDef.display_name || cleanName.replace("_", " ");
              
              // Check if this input is connected via a handle
              const isConnected = isInputConnected(inputDef.name);
              const isDisabled = shouldDisableInput(inputDef.name);
              
              return (
                <div key={inputDef.name} className="pb-2">
                  <Label
                    htmlFor={`config-${node?.id}-${inputDef.name}`}
                    className="flex items-center justify-between text-xs font-medium"
                  >
                    <span>{displayName}</span>
                    {inputDef.required && (
                      <Badge
                        variant="destructive"
                        className="h-4 px-1 text-[9px]"
                      >
                        Required
                      </Badge>
                    )}
                  </Label>
                  {inputDef.info && (
                    <p className="text-muted-foreground mt-0.5 mb-1.5 text-xs">
                      {inputDef.info}
                    </p>
                  )}
                  <InputRenderer
                    inputDef={inputDef}
                    value={node.data.config?.[inputDef.name]}
                    onChange={onConfigChange}
                    isDisabled={isDisabled}
                    isConnected={isConnected}
                    connectionInfo={getConnectionInfo(inputDef.name)}
                    nodeId={node.id}
                  />
                </div>
              );
            })}
        </div>
      )}
    </div>
  );
}
