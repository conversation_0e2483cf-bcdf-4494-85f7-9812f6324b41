"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { validatePassword } from "@/lib/schemas/auth";

import { toast } from "sonner";
import { signupSchema, SignupType } from "@/lib/schemas/auth";
import { authApi } from "@/lib/authApi";
import { PasswordValidationIndicator } from "./PasswordValidationIndicator";
import { ConfirmationScreen } from "./ConfirmationScreen";

export function SignupForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isPasswordFocused, setIsPasswordFocused] = useState(false);
  const [passwordValidations, setPasswordValidations] = useState({
    length: false,
    hasNumber: false,
    hasSymbol: false,
  });
  const [isFormValid, setIsFormValid] = useState(false);
  const [formSubmitAttempted, setFormSubmitAttempted] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // default config for signup form validation
  const form = useForm<SignupType>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
      termsAccepted: false,
    },
  });

  // Update password validations when password changes
  const updatePasswordValidations = (password: string) => {
    setPasswordValidations(validatePassword(password));
  };

  // Watch for all form changes to update validations and button enablement
  useEffect(() => {
    const subscription = form.watch((values) => {
      // Update password validations for the indicator
      updatePasswordValidations(values.password || "");

      // Check for button enablement: fields non-empty and terms accepted
      const { fullName, email, password, termsAccepted } = values;

      // Use Boolean() to ensure truthiness check and handle potential empty strings correctly
      const fieldsFilled = Boolean(fullName) && Boolean(email) && Boolean(password);

      // Explicitly check if termsAccepted is true
      const areTermsAccepted = termsAccepted === true;

      // Ensure the final value is always boolean - only check if fields are filled and terms accepted
      setIsFormValid(!!(fieldsFilled && areTermsAccepted));
    });
    return () => subscription.unsubscribe();
  }, [form]);

  async function onSubmit(values: SignupType) {
    setFormSubmitAttempted(true);
    setIsLoading(true);
    try {
      const response = await authApi.signup(values);

      if (response.success) {
        setShowConfirmation(true);
      } else {
        // Handle cases where the API returns success: false but doesn't throw
        toast.error(response.message || "Signup failed. Please try again.");
      }
    } catch (error: any) {
      // Handle errors thrown by the API call (e.g., network issues, specific status codes)
      toast.error(error.message || "An unexpected error occurred.");
      console.error("Signup Error:", error);
    } finally {
      setIsLoading(false);
    }
  }

  const handleBackToLogin = () => {
    setShowConfirmation(false);
    form.reset();
    form.clearErrors();
    setIsFormValid(false);
    setFormSubmitAttempted(false);
  };

  if (showConfirmation) {
    return (
      <ConfirmationScreen
        title="Verify Your Email"
        message="We have sent a verification link to your email. Please verify your email to continue."
        email={form.getValues("email")}
        onBackToLogin={handleBackToLogin}
      />
    );
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(
          onSubmit,
          // This runs when form validation fails
          () => setFormSubmitAttempted(true),
        )}
        className="space-y-6"
      >
        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter your full name" {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter your email address"
                  type="email"
                  {...field}
                  disabled={isLoading}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    placeholder="Enter a unique password"
                    type={showPassword ? "text" : "password"}
                    {...field}
                    disabled={isLoading}
                    onFocus={() => setIsPasswordFocused(true)}
                    onBlur={() => setIsPasswordFocused(false)}
                    onChange={(e) => {
                      field.onChange(e);
                      updatePasswordValidations(e.target.value);
                    }}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isLoading}
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? (
                      <EyeOffIcon className="h-4 w-4 text-gray-500" />
                    ) : (
                      <EyeIcon className="h-4 w-4 text-gray-500" />
                    )}
                  </Button>
                </div>
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="termsAccepted"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  disabled={isLoading}
                />
              </FormControl>
              <div className="">
                <FormLabel className="text-sm font-semibold">
                  I agree to the{" "}
                  <a href="#" className="text-primary hover:underline hover:opacity-80">
                    Terms
                  </a>{" "}
                  &{" "}
                  <a href="#" className="text-primary hover:underline hover:opacity-80">
                    Privacy Policy
                  </a>
                </FormLabel>
                <FormMessage />
              </div>
            </FormItem>
          )}
        />

        <PasswordValidationIndicator
          password={form.getValues("password") || ""}
          showValidation={formSubmitAttempted}
        />

        <Button type="submit" className="w-full" disabled={!isFormValid || isLoading}>
          {isLoading ? "Signing up..." : "Signup"}
        </Button>
      </form>
    </Form>
  );
}
