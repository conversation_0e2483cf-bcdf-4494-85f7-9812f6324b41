# MCP Execution Service

A microservice designed to execute Multi-modal Conversational Processor (MCP) tools via Kafka messaging. This service consumes messages from a Kafka topic, executes the requested MCP tools, and sends the results back to a results topic.

## Architecture Overview

```mermaid
flowchart TB
    subgraph Client
        A[Client Application]
    end
    
    subgraph Messaging
        B[Kafka Broker]
    end
    
    subgraph "MCP Execution Service"
        C[Kafka Consumer]
        D[MCP Executor]
        E[Kafka Producer]
    end
    
    subgraph "MCP Servers"
        F[MCP Server 1]
        G[MCP Server 2]
        H[MCP Server N]
    end
    
    A -->|Send Request| B
    B -->|Consume Request| C
    C -->|Process Request| D
    D -->|Execute Tool| F & G & H
    F & G & H -->|Return Results| D
    D -->|Format Results| E
    E -->|Send Results| B
    B -->|Deliver Results| A
    
    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    class D primary;
    class A,B,C,E,F,G,H secondary;
```

## Features

- Asynchronous processing of MCP tool execution requests via Kafka
- Automatic retry mechanism for failed executions
- Concurrency control with semaphores
- Dead letter queue for permanently failed executions
- JSON parsing of MCP tool results
- Configurable via environment variables

## Prerequisites

- Python 3.11 or higher
- Poetry (Python package manager)
- Kafka broker
- MCP servers accessible via HTTP/HTTPS

## Installation

### Using Poetry (Recommended)

1. Clone the repository:
   ```bash
   git clone https://gitlab.rapidinnovation.tech/ruh-catalyst/mcp-execution-service.git
   cd mcp-execution-service
   ```

2. Install dependencies:
   ```bash
   poetry install
   ```

### Using Docker

1. Build the Docker image:
   ```bash
   docker build -t mcp-execution-service .
   ```

2. Run the container:
   ```bash
   docker run -d --name mcp-execution-service \
     -e KAFKA_BOOTSTRAP_SERVERS=kafka:9092 \
     -e KAFKA_CONSUMER_TOPIC=mcp-execution-request \
     -e KAFKA_CONSUMER_GROUP_ID=mcp_executor_service \
     -e KAFKA_RESULTS_TOPIC=mcp_results \
     -e LOG_LEVEL=INFO \
     -e MAX_CONCURRENT_TASKS=10 \
     mcp-execution-service \
     python -m app.main
   ```

## Configuration

Create a `.env` file based on the provided `.env.example`:

```bash
# Application settings
APP_NAME="MCP-executor"
APP_ENV="Production"
APP_DEBUG="False"

# Kafka settings
KAFKA_BOOTSTRAP_SERVERS="kafka:9092"
KAFKA_CONSUMER_TOPIC="mcp-execution-request"
KAFKA_CONSUMER_GROUP_ID="mcp_executor_service"
KAFKA_RESULTS_TOPIC="mcp_results"
DEFAULT_MCP_RETRIES=3
LOG_LEVEL="INFO"
MAX_CONCURRENT_TASKS=10
```

### Configuration Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| KAFKA_BOOTSTRAP_SERVERS | Kafka broker addresses | localhost:9092 |
| KAFKA_CONSUMER_TOPIC | Topic to consume execution requests from | mcp-execution-request |
| KAFKA_CONSUMER_GROUP_ID | Consumer group ID for the service | mcp_executor_service |
| KAFKA_RESULTS_TOPIC | Topic to publish execution results to | mcp_results |
| DEFAULT_MCP_RETRIES | Number of retry attempts for failed executions | 3 |
| LOG_LEVEL | Logging level (INFO, DEBUG, WARNING, ERROR) | INFO |
| MAX_CONCURRENT_TASKS | Maximum number of concurrent MCP tool executions | 10 |

## Running the Service

### Local Development

Use the provided script:

```bash
./run_local.sh
```

Or run manually:

```bash
poetry run python -m app.main
```

### Production Deployment

For production, it's recommended to use Docker or a similar containerization approach with proper environment configuration.

## Message Format

### Input Message (Request)

```json
{
  "request_id": "unique-request-id",
  "server_script_path": "http://mcp-server:8080/sse",
  "tool_name": "example_tool",
  "tool_parameters": {
    "param1": "value1",
    "param2": "value2"
  },
  "retries": 3
}
```

### Output Message (Success)

```json
{
  "request_id": "unique-request-id",
  "result": [
    {
      "key1": "value1",
      "key2": "value2"
    }
  ],
  "status": "success"
}
```

### Output Message (Error)

```json
{
  "request_id": "unique-request-id",
  "error": "Error message",
  "status": "error"
}
```

## Error Handling

- Failed executions are retried with exponential backoff
- After all retries are exhausted, errors are sent to the results topic
- Additional error information is sent to a dead letter queue (`mcp_dead_letter_queue`)

## Development

### Project Structure

```
mcp-execution-service/
├── app/
│   ├── __init__.py
│   ├── main.py             # Entry point
│   ├── config/             # Configuration
│   │   ├── __init__.py
│   │   └── config.py       # Settings using Pydantic
│   └── core_/              # Core functionality
│       ├── __init__.py
│       ├── client.py       # MCP client wrapper
│       ├── kafka_service.py # Kafka consumer/producer
│       └── mcp_executor.py  # MCP execution logic
├── .env.example            # Example environment variables
├── Dockerfile              # Docker configuration
├── poetry.lock             # Poetry lock file
├── pyproject.toml          # Poetry dependencies
├── README.md               # This file
└── run_local.sh            # Script to run locally
```

## License

[Specify license information here]

## Contributors

[List contributors here]
