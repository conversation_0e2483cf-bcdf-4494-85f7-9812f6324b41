import React from "react";
import { Info } from "lucide-react";

/**
 * Component to display when no node is selected
 */
export function EmptyState() {
  return (
    <div className="text-muted-foreground flex h-full flex-col items-center justify-center p-8 text-center" data-testid="inspector-empty-state">
      <Info className="text-muted-foreground/50 mb-2 h-8 w-8" />
      <p>Select a node to inspect and configure its properties.</p>
    </div>
  );
}
