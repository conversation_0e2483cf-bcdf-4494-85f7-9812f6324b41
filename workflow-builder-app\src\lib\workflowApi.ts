/**
 * Workflow API Module
 *
 * This module re-exports functions from the feature-specific API module
 * to maintain backward compatibility.
 *
 * IMPORTANT: This file is deprecated. Please import directly from:
 * @/app/(features)/workflows/api
 */

// Re-export everything from the feature-specific API module
export * from "@/app/(features)/workflows/api";

// Also export a default object for easier imports
import {
  fetchWorkflowsByUser,
  createEmptyWorkflow,
  fetchWorkflowById,
} from "@/app/(features)/workflows/api";

// Add the missing fetchWorkflowFromBuilderUrl function that's not in the feature API
import axios from "axios";

/**
 * Fetches workflow data from a builder URL
 * @param url The URL to fetch the workflow data from
 * @returns The workflow data
 */
export async function fetchWorkflowFromBuilderUrl(url: string): Promise<any> {
  try {
    // For external URLs, we need to use axios directly
    const response = await axios.get(url, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(
        `Failed to fetch workflow data: ${error.response.status} ${error.response.statusText}`,
      );
    }
    throw error;
  }
}

// Export default object for easier imports
export default {
  fetchWorkflowsByUser,
  createEmptyWorkflow,
  fetchWorkflowFromBuilderUrl,
  fetchWorkflowById,
};
