"""
Tool Executor - A layer on top of components that handles component selection and execution.
"""

import json
import logging
import traceback
from typing import Dict, Any, Optional

from app.core_.component_system import COMPONENT_REGISTRY, get_component_manager

# Import the specialized tool executor logger
try:
    # First try to import the specialized Kafka logger for tool executor
    from app.utils.tool_executor_kafka_logger import setup_tool_executor_logger
    logger = setup_tool_executor_logger()
except ImportError:
    # If that fails, fall back to the standard logger
    try:
        from app.utils.logging_config import setup_logger
        logger = setup_logger("ToolExecutor")
    except ImportError:
        # Fallback to basic logging if logging_config is not available
        logging.basicConfig(
            level=logging.DEBUG,
            format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        logger = logging.getLogger(__name__)


def format_api_response(result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format API component response to match the desired structure.

    Args:
        result: The original API component result

    Returns:
        A formatted response with the desired structure
    """
    if not isinstance(result, dict):
        return result

    # Create a new response structure with the updated format
    formatted_response = {
        "result": result.get("data"),  # Move 'data' to 'result'
        "status_code": result.get("status_code"),
        "response_headers": result.get("headers", {}),  # Rename 'headers' to 'response_headers'
        "error": result.get("error")  # Include error field
    }

    # Keep URL and method for reference but not in the example
    if "url" in result:
        formatted_response["url"] = result["url"]
    if "method" in result:
        formatted_response["method"] = result["method"]

    return formatted_response


class ToolExecutor:
    """
    A layer on top of components that handles component selection and execution.

    This class is responsible for:
    1. Selecting the appropriate component based on the tool_name parameter
    2. Executing the component with the provided parameters
    3. Returning the result
    """

    def __init__(self):
        """
        Initialize the ToolExecutor.
        """
        logger.info("Initializing ToolExecutor")
        self.component_manager = get_component_manager()
        logger.info("ToolExecutor initialized successfully")

    async def execute_tool(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a tool based on the tool_name parameter in the payload.

        Args:
            payload: The payload containing tool_name, tool_parameters, and request_id

        Returns:
            The result of the tool execution

        Raises:
            ValueError: If the tool_name is not provided or not found
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Executing tool for request_id: {request_id}")

        # Log the complete payload
        logger.info(f"ToolExecutor received payload: {json.dumps(payload, indent=2)}")

        # Extract tool_name from payload
        tool_name = payload.get("tool_name")
        if not tool_name:
            error_msg = f"tool_name is required in payload for request_id: {request_id}"
            logger.error(error_msg)
            error_response = {
                "request_id": request_id,
                "status": "error",
                "result": {
                    "error": error_msg,
                }
            }
            logger.info(
                f"ToolExecutor returning error: {json.dumps(error_response, indent=2)}"
            )
            return error_response

        logger.info(f"Tool name: {tool_name} for request_id: {request_id}")

        # Extract tool_parameters from payload
        tool_parameters = payload.get("tool_parameters", {})
        logger.debug(f"Tool parameters for request_id {request_id}: {tool_parameters}")

        # Check if the component exists
        if tool_name not in COMPONENT_REGISTRY:
            error_msg = (
                f"Component '{tool_name}' not found for request_id: {request_id}"
            )
            logger.error(error_msg)
            error_response = {
                "request_id": request_id,
                "status": "error",
                "result": {
                    "error": error_msg,
                }
            }
            logger.info(
                f"ToolExecutor returning error: {json.dumps(error_response, indent=2)}"
            )
            return error_response

        try:
            # Get the component instance
            component = self.component_manager.get_component_instance(tool_name)
            logger.debug(
                f"Got component instance for {tool_name}: {component.__class__.__name__}"
            )

            # Create a new payload with the tool parameters
            component_payload = {**tool_parameters, "request_id": request_id}

            # Process the payload with the component
            logger.info(
                f"Processing payload with component {tool_name} for request_id: {request_id}"
            )
            result = await component.process(component_payload)
            logger.info(
                f"Component {tool_name} processed payload successfully for request_id: {request_id}"
            )

            # Check if the result contains an error field
            # This handles the case where a component returns an error response
            if isinstance(result, dict) and (result.get("error") or (isinstance(result, dict) and result.get("status") == "error")):
                # Create a copy of the result without the status field to avoid duplication
                result_copy = result.copy()
                if "status" in result_copy:
                    logger.debug(f"Removing status field from result to avoid duplication for RequestID={request_id}")
                    del result_copy["status"]

                # This is an error response, format it according to the new structure
                if tool_name == "ApiRequestNode":
                    # Format API error responses to match the desired structure
                    error_response = {
                        "component_type": tool_name,
                        "request_id": request_id,
                        "status": "error",
                        "response": format_api_response(result_copy)
                    }
                else:
                    # Standard error response format for non-API components
                    error_response = {
                        "request_id": request_id,
                        "status": "error",
                        "result": result_copy
                    }

                # Log the error response
                logger.info(
                    f"ToolExecutor returning error from component: {json.dumps(error_response, indent=2)}"
                )

                return error_response

            # Create the success response
            # Clean up the result by removing any error fields
            if isinstance(result, dict):
                result_copy = result.copy()
                # Remove error field if it exists and is None
                if "error" in result_copy and result_copy["error"] is None:
                    del result_copy["error"]

                # Format API component responses differently
                if tool_name == "ApiRequestNode":
                    # Format the API response according to the desired structure
                    formatted_api_response = format_api_response(result_copy)
                    success_response = {
                        "component_type": tool_name,
                        "request_id": request_id,
                        "status": "success",
                        "response": formatted_api_response
                    }
                else:
                    # Standard response format for non-API components
                    success_response = {
                        "request_id": request_id,
                        "status": "success",
                        "result": result_copy
                    }
            else:
                success_response = {
                    "request_id": request_id,
                    "status": "success",
                    "result": result
                }

            # Log the complete success response
            logger.info(
                f"ToolExecutor returning success: {json.dumps(success_response, indent=2)}"
            )

            # Return the result
            return success_response
        except Exception as e:
            error_msg = f"Error executing tool {tool_name} for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details: {traceback.format_exc()}")

            # Create the error response
            if tool_name == "ApiRequestNode":
                # Format API error responses to match the desired structure
                error_response = {
                    "component_type": tool_name,
                    "request_id": request_id,
                    "status": "error",
                    "response": {
                        "result": None,
                        "status_code": 500,
                        "response_headers": {},
                        "error": error_msg
                    }
                }
            else:
                # Standard error response format for non-API components
                error_response = {
                    "request_id": request_id,
                    "status": "error",
                    "result": {
                        "error": error_msg
                    }
                }

            # Log the complete error response
            logger.info(
                f"ToolExecutor returning error: {json.dumps(error_response, indent=2)}"
            )

            return error_response


# Create a global tool executor
tool_executor = None


def get_tool_executor() -> ToolExecutor:
    """
    Get or create the global tool executor.

    Returns:
        The tool executor
    """
    global tool_executor
    if tool_executor is None:
        logger.info("Creating new global ToolExecutor instance")
        tool_executor = ToolExecutor()
    logger.debug("Returning global ToolExecutor instance")
    return tool_executor


async def register_dynamic_tool(tool_name: str, process_function: callable) -> bool:
    """
    Dynamically register a new tool at runtime.

    This function creates a new component class and registers it with the component system,
    making it immediately available as a tool.

    Args:
        tool_name: The name of the tool to register
        process_function: The async function that will process the tool's payload
                          Should accept a dict and return a dict

    Returns:
        True if registration was successful, False otherwise
    """
    from app.core_.base_component import BaseComponent
    from app.core_.component_system import COMPONENT_REGISTRY

    logger.info(f"Dynamically registering new tool: {tool_name}")

    # Check if the tool already exists
    if tool_name in COMPONENT_REGISTRY:
        logger.warning(f"Tool {tool_name} already exists, cannot register")
        return False

    try:
        # Create a new component class
        class DynamicComponent(BaseComponent):
            """Dynamically created component."""

            def __init__(self):
                """Initialize the dynamic component."""
                logger.info(f"Initializing dynamic component: {tool_name}")
                super().__init__()
                logger.info(f"Dynamic component {tool_name} initialized successfully")

            async def process(self, payload):
                print(
                    "************************** \n**************************",
                    payload,
                    "************************** \n**************************",
                )
                """Process the payload using the provided function."""
                request_id = payload.get("request_id", "unknown")
                logger.info(
                    f"Processing dynamic component {tool_name} for request_id: {request_id}"
                )
                try:
                    result = await process_function(payload)
                    logger.info(
                        f"Dynamic component {tool_name} processed successfully for request_id: {request_id}"
                    )
                    return result
                except Exception as e:
                    error_msg = f"Error in dynamic component {tool_name}: {str(e)}"
                    logger.error(f"{error_msg} for request_id: {request_id}")
                    logger.debug(f"Exception details: {traceback.format_exc()}")
                    return {
                        "request_id": request_id,
                        "status": "error",
                        "result": {
                            "error": error_msg
                        }
                    }

        # Register the component
        DynamicComponent.component_type = tool_name
        COMPONENT_REGISTRY[tool_name] = DynamicComponent

        # Get the component manager and create an instance
        from app.core_.component_system import get_component_manager

        manager = get_component_manager()
        manager.components[tool_name] = DynamicComponent()

        logger.info(f"Successfully registered dynamic tool: {tool_name}")
        return True

    except Exception as e:
        logger.error(f"Failed to register dynamic tool {tool_name}: {str(e)}")
        logger.debug(f"Exception details: {traceback.format_exc()}")
        return False
