"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  loginSchema,
  LoginType,
  forgotPasswordSchema,
  ForgotPasswordType,
} from "@/lib/schemas/auth";
import { authApi } from "@/lib/authApi";
import { useRouter } from "next/navigation";
import { ConfirmationScreen } from "./ConfirmationScreen";

export function LoginForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [isForgotPassword, setIsForgotPassword] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const router = useRouter();

  // default config for login form validation
  const form = useForm<LoginType | ForgotPasswordType>({
    resolver: zodResolver(isForgotPassword ? forgotPasswordSchema : loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Initialize useMutation
  const { mutate: loginUser, isPending: isLoginPending } = useMutation({
    mutationFn: authApi.login,
    onSuccess: (data) => {
      toast.success("Login successful!");
      router.push(data.redirectPath);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Login failed. Please try again.");
    },
  });

  // Forgot password mutation using the updated authApi
  const { mutate: sendResetLink, isPending: isForgotPending } = useMutation({
    mutationFn: authApi.forgotPassword,
    onSuccess: (data) => {
      setShowConfirmation(true);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to send reset link. Please try again.");
    },
  });

  const isPending = isLoginPending || isForgotPending;

  // Check if the form is valid for submission based on non-empty fields
  const checkFormValidity = () => {
    // Get specific values to avoid union type issues
    const email = form.getValues("email");

    if (isForgotPassword) {
      // For forgot password, only email needs to be non-empty
      return Boolean(email && email.length > 0);
    } else {
      // For login, both email and password need to be non-empty
      // We know 'password' exists in this branch due to the form type and schema
      const password = form.getValues("password");
      return Boolean(email && email.length > 0 && password && password.length > 0);
    }
  };

  // Update form validity when values change
  useEffect(() => {
    const subscription = form.watch(() => {
      setIsFormValid(checkFormValidity());
    });
    return () => subscription.unsubscribe();
  }, [form, isForgotPassword]);

  async function onSubmit(data: LoginType | ForgotPasswordType) {
    if (isForgotPassword) {
      // Pass only the email string to the mutation
      sendResetLink((data as ForgotPasswordType).email);
    } else {
      loginUser(data as LoginType);
    }
  }

  const handleBackToLogin = () => {
    setShowConfirmation(false);
    setIsForgotPassword(false);
    form.reset();
    form.clearErrors();
    setIsFormValid(false);
  };

  if (showConfirmation) {
    return (
      <ConfirmationScreen
        title="Reset Password"
        message="We have sent a link to your email to reset your password. Kindly check your inbox (and if you don't find it there, kindly check your spam folder)."
        email={form.getValues("email")}
        onBackToLogin={handleBackToLogin}
      />
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {isForgotPassword ? (
          <h2 className="text-center text-2xl font-semibold">Reset Your Password</h2>
        ) : (
          <h2 className="text-center text-2xl font-semibold">Login</h2>
        )}

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {isForgotPassword ? "Enter your email address" : "Email Address"}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter your email address"
                  type="email"
                  {...field}
                  disabled={isPending}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {!isForgotPassword && (
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="Enter your password"
                      type={showPassword ? "text" : "password"}
                      {...field}
                      disabled={isPending}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword((prev) => !prev)}
                      disabled={isPending}
                      aria-label={showPassword ? "Hide password" : "Show password"}
                    >
                      {showPassword ? (
                        <EyeOffIcon className="h-4 w-4 text-gray-500" />
                      ) : (
                        <EyeIcon className="h-4 w-4 text-gray-500" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <Button type="submit" className="w-full" disabled={!isFormValid || isPending}>
          {isForgotPassword
            ? isForgotPending
              ? "Sending Link..."
              : "Send Reset Link"
            : isLoginPending
              ? "Logging in..."
              : "Login"}
        </Button>

        {!isForgotPassword && (
          <div className="text-right">
            <Button
              type="button"
              variant="link"
              className="text-primary h-auto p-0 text-sm hover:underline"
              onClick={() => {
                setIsForgotPassword(true);
                const currentEmail = form.getValues("email");
                form.reset({
                  email: currentEmail,
                  password: "",
                }); // Keep email, clear password
                form.clearErrors(); // Clear any existing errors

                // Update form validity immediately
                setIsFormValid(Boolean(currentEmail && currentEmail.length > 0));
              }}
            >
              Forget Password?
            </Button>
          </div>
        )}

        {isForgotPassword && (
          <div className="text-right">
            <Button
              type="button"
              variant="link"
              className="text-primary h-auto p-0 text-sm hover:underline"
              onClick={handleBackToLogin}
            >
              Back to Login
            </Button>
          </div>
        )}
      </form>
    </Form>
  );
}
