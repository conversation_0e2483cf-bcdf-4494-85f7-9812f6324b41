/**
 * Authentication related types
 */

export interface User {
  id: string;
  email: string;
  name?: string;
  role: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
  // Additional fields for onboarding
  department?: string;
  jobRole?: string;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupCredentials {
  email: string;
  password: string;
  name?: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface UpdatePasswordRequest {
  token: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken?: string;
}
