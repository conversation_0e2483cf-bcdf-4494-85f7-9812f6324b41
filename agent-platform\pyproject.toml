[tool.poetry]
name = "agent-platform"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.12"
tiktoken = "^0.9.0"
openai = "^1.76.0"
python-dotenv = "1.0.0"
autogen-agentchat = "^0.5.5"
mcp = "^1.6.0"
json-schema-to-pydantic = "^0.2.4"
aiohttp = "^3.11.18"
pydantic = "^2.11.3"
autogen-ext = "^0.5.5"
aiokafka = "^0.12.0"
redis = "^5.2.1"
# Remove the kafka package as we'll use aiokafka exclusively
asgiref = "^3.8.1"
anthropic = "^0.51.0"
python-slugify = "^8.0.4"

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
pytest-asyncio = "^0.23.5"
pytest-cov = "^4.1.0"
httpx = "^0.27.0"
coverage = "^7.4.1"

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=app --cov-report=html --cov-report=term-missing"
pythonpath = ["."]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
