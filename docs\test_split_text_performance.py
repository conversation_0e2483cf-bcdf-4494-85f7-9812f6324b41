#!/usr/bin/env python3
"""
Performance testing for Split Text Component.
Run this from the node-executor-service directory.
"""

import asyncio
import time
import sys
import os
import statistics

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

async def performance_test():
    """Test the performance of Split Text Component."""
    print("🚀 Split Text Component - Performance Testing")
    print("=" * 60)
    
    try:
        from app.components.split_text_component import SplitTextComponent
        
        component = SplitTextComponent()
        print("✅ Component initialized successfully")
        
        # Performance test cases
        test_cases = [
            {
                "name": "Small text (100 chars)",
                "text": "a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z," * 2,
                "delimiter": ",",
                "iterations": 1000
            },
            {
                "name": "Medium text (1KB)",
                "text": "word" + ",word" * 200,
                "delimiter": ",", 
                "iterations": 500
            },
            {
                "name": "Large text (10KB)",
                "text": "data" + ",data" * 2000,
                "delimiter": ",",
                "iterations": 100
            },
            {
                "name": "Very large text (100KB)",
                "text": "item" + ",item" * 20000,
                "delimiter": ",",
                "iterations": 10
            }
        ]
        
        for test_case in test_cases:
            print(f"\n📊 {test_case['name']}")
            print("-" * 40)
            print(f"Text length: {len(test_case['text']):,} characters")
            print(f"Iterations: {test_case['iterations']}")
            
            # Prepare payload
            payload = {
                "request_id": "perf_test",
                "tool_parameters": {
                    "input_text": test_case["text"],
                    "delimiter": test_case["delimiter"],
                    "max_splits": -1,
                    "include_delimiter": False
                }
            }
            
            # Warm up
            await component.process(payload)
            
            # Performance test
            times = []
            for i in range(test_case["iterations"]):
                start_time = time.perf_counter()
                result = await component.process(payload)
                end_time = time.perf_counter()
                
                if result.get("status") == "success":
                    times.append((end_time - start_time) * 1000)  # Convert to milliseconds
                else:
                    print(f"❌ Error in iteration {i}: {result.get('error', 'Unknown')}")
                    break
            
            if times:
                # Calculate statistics
                avg_time = statistics.mean(times)
                min_time = min(times)
                max_time = max(times)
                median_time = statistics.median(times)
                
                print(f"Average time: {avg_time:.2f} ms")
                print(f"Median time:  {median_time:.2f} ms")
                print(f"Min time:     {min_time:.2f} ms")
                print(f"Max time:     {max_time:.2f} ms")
                
                # Calculate throughput
                chars_per_second = len(test_case["text"]) / (avg_time / 1000)
                print(f"Throughput:   {chars_per_second:,.0f} chars/second")
                
                # Performance rating
                if avg_time < 1:
                    rating = "🚀 Excellent"
                elif avg_time < 10:
                    rating = "✅ Good"
                elif avg_time < 100:
                    rating = "⚠️  Acceptable"
                else:
                    rating = "❌ Slow"
                
                print(f"Performance:  {rating}")
        
        # Memory usage test
        print(f"\n🧠 Memory Usage Test")
        print("-" * 40)
        
        import psutil
        import gc
        
        process = psutil.Process()
        
        # Baseline memory
        gc.collect()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Process large text multiple times
        large_text = "data," * 100000  # ~500KB
        payload = {
            "request_id": "memory_test",
            "tool_parameters": {
                "input_text": large_text,
                "delimiter": ",",
                "max_splits": -1,
                "include_delimiter": False
            }
        }
        
        for i in range(50):
            await component.process(payload)
        
        # Check memory after processing
        gc.collect()
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - baseline_memory
        
        print(f"Baseline memory: {baseline_memory:.1f} MB")
        print(f"Final memory:    {final_memory:.1f} MB")
        print(f"Memory increase: {memory_increase:.1f} MB")
        
        if memory_increase < 10:
            print("✅ Memory usage is acceptable")
        else:
            print("⚠️  High memory usage detected")
        
        print(f"\n🎉 Performance testing completed!")
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(performance_test())
