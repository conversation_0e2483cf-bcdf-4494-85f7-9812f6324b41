import { InputDefinition, WorkflowNodeData } from "@/types";
import { Node } from "reactflow";
import { shouldShowInput } from "@/utils/visibility-rules";

/**
 * Determines if an input should be visible based on node type and configuration
 * @param inputDef The input definition
 * @param node The workflow node
 * @param config The current configuration
 * @returns True if the input should be visible, false otherwise
 */
export function checkInputVisibility(
  inputDef: InputDefinition,
  node: Node<WorkflowNodeData> | null,
  config: Record<string, any>
): boolean {
  if (!node) return false;

  // Special handling for DynamicCombineTextComponent
  if (
    node.data.type === "DynamicCombineTextComponent" &&
    inputDef.name.startsWith("input_") &&
    !inputDef.is_handle
  ) {
    // Extract the index from the input name (e.g., "input_3" -> 3)
    const match = inputDef.name.match(/input_(\d+)/);
    if (match && match[1]) {
      const inputIndex = parseInt(match[1], 10);
      const numAdditionalInputs = parseInt(config.num_additional_inputs || "0", 10);

      // Show the input if its index is less than or equal to the number of additional inputs
      return inputIndex <= numAdditionalInputs;
    }
  }

  // Special handling for ConditionalNode
  if (node.data.originalType === "ConditionalNode") {
    // Handle dynamic condition inputs (condition_3_*, condition_4_*, etc.)
    const conditionMatch = inputDef.name.match(/condition_(\d+)_/);
    if (conditionMatch && conditionMatch[1]) {
      const conditionIndex = parseInt(conditionMatch[1], 10);
      const numAdditionalConditions = parseInt(config.num_additional_conditions || "0", 10);
      const totalConditions = 2 + numAdditionalConditions; // Base 2 + additional

      // For conditions 3 and above, check if they should be visible
      if (conditionIndex > 2) {
        // Show if condition index is within total conditions
        if (conditionIndex > totalConditions) {
          return false;
        }
      }

      // For input handles, also check the source setting
      if (inputDef.name.endsWith("_input_handle")) {
        const sourceValue = config[`condition_${conditionIndex}_source`];
        return sourceValue === "node_output";
      }
    }
  }

  // Special handling for MCP Marketplace components
  if (isMCPMarketplaceComponent(node)) {
    return checkMCPMarketplaceInputVisibility(inputDef, node, config);
  }

  // Special handling for MCP Tools component
  if (node.data.type === "MCPToolsComponent") {
    return checkMCPToolsInputVisibility(inputDef, config);
  }

  // Use the utility function for standard visibility rules
  return shouldShowInput(inputDef, config);
}

/**
 * Checks if a node is an MCP Marketplace component
 */
function isMCPMarketplaceComponent(node: Node<WorkflowNodeData> | null): boolean {
  if (!node) return false;
  return node.data.type === "MCPMarketplaceComponent" ||
         (node.data.definition?.category === "MCP Marketplace");
}

/**
 * Checks visibility for MCP Marketplace component inputs
 */
function checkMCPMarketplaceInputVisibility(
  inputDef: InputDefinition,
  node: Node<WorkflowNodeData>,
  config: Record<string, any>
): boolean {
  // For explicit handle inputs (ending with _handle), always show them
  if (inputDef.input_type === "handle" || inputDef.name.endsWith("_handle")) {
    // Always show explicit handle inputs
    return true;
  }

  // Hide connection fields that have a direct input equivalent
  if (inputDef.name.endsWith("_connection")) {
    // Check if there's a direct input with the same base name
    const baseName = inputDef.name.replace("_connection", "");
    const hasDirectInput =
      node.data?.definition?.inputs?.some((input) => input.name === baseName) || false;
    if (hasDirectInput) {
      return false;
    }
  }

  // For inputs with is_handle=true, always show them
  if (inputDef.is_handle) {
    return true;
  }

  // For regular inputs, check if there's a corresponding handle
  if (hasCorrespondingHandle(inputDef.name, node)) {
    // If the handle is connected, hide the direct input
    // This would require the isInputConnected function from useConnectedHandles
    // For now, we'll return true and handle this in the component
    return true;
  }

  // Default to showing the input
  return true;
}

/**
 * Checks if an input has a corresponding handle
 */
function hasCorrespondingHandle(inputName: string, node: Node<WorkflowNodeData>): boolean {
  if (!node?.data?.definition?.inputs) return false;

  // Check for handle with a suffix pattern (e.g., input_dict_handle for input_dict)
  const handleSuffix = node.data.definition.inputs.find(
    (input) =>
      (input.is_handle || input.input_type === "handle") && input.name === `${inputName}_handle`,
  );

  return !!handleSuffix;
}

/**
 * Checks visibility for MCP Tools component inputs
 */
function checkMCPToolsInputVisibility(
  inputDef: InputDefinition,
  config: Record<string, any>
): boolean {
  // For selected_tool_name, check connection_status
  if (inputDef.name === "selected_tool_name") {
    // Check connection status
    const connectionStatus = config.connection_status || "Not Connected";

    // Show if connected
    return connectionStatus === "Connected";
  }

  // For refresh_tools and disconnect buttons, always hide them
  if (inputDef.name === "refresh_tools" || inputDef.name === "disconnect") {
    return false; // Always hide these buttons
  }

  // Use the utility function for standard visibility rules
  return shouldShowInput(inputDef, config);
}
