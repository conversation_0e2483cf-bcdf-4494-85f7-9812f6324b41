import re
import asyncio
from typing import List, Dict, Any, Optional, Literal

# Assuming these imports point to your framework's definitions
from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    StringInput,
    ListInput,  # Use ListInput for dynamic cases
    MultilineInput,
    BoolInput,
    InputVisibilityRule,
    HandleInput,
)
from app.models.workflow_builder.components import Output


class SwitchNodeDynamic(BaseNode):
    """
    Routes an input value based on matching against a dynamically configured
    list of case values. Requires FRONTEND support to render dynamic outputs.

    **Frontend Requirements:**
    - Render UI controls (+/-) for the 'Cases (Match Values)' list input.
    - For each item at index `i` in the 'Cases' list, dynamically render an
      output handle named `case_output_{i}` on the node canvas.
    - Store connections from these dynamic handles with `sourceHandle="case_output_{i}"`.
    """

    name = "SwitchNodeDynamic"
    display_name = "Switch (Dynamic Cases)"
    description = "Routes data based on matching against a user-defined list of cases. Requires Frontend support for dynamic handles."
    category = "Logic"
    icon = "GitMerge"  # Different icon maybe?
    beta = True  # Mark as beta due to frontend dependency

    # --- Define Inputs ---
    inputs: List[InputBase] = [
        # Input value to compare - connection handle
        HandleInput(
            name="input_value_compare_handle",
            display_name="Input Value (to Compare)",
            info="Connect the value to compare against the different cases.",
            required=True,
            is_handle=True,
            input_types=["Any"],
        ),
        # Input value to compare - direct input in inspector
        StringInput(
            name="input_value_compare",
            display_name="Input Value (Direct)",
            info="The value to compare against the different cases. Used if no connection is provided.",
            required=False,
            is_handle=False,
            value="",
        ),
        # --- Dynamic Case Configuration ---
        ListInput(
            name="cases",
            display_name="Cases (Match Values)",
            info="List of string values to match against. Add/remove cases using UI controls. Frontend renders corresponding outputs.",
            value=[],  # Default to an empty list
            list_item_type="string",  # Specify that items in the list should be strings
            required=False,
        ),
        # --- Comparison Options ---
        BoolInput(
            name="case_sensitive",
            display_name="Case Sensitive Match",
            info="If checked, the comparison will be case-sensitive.",
            value=False,
            advanced=True,
        ),
        # --- Value to Route - connection handle ---
        HandleInput(
            name="routed_input_handle",
            display_name="Input Value (to Route)",
            info="Connect the actual data/message to pass through the selected output route.",
            required=False,
            is_handle=True,
            input_types=["Any"],
        ),
        # Value to route - direct input in inspector
        MultilineInput(
            name="routed_input",
            display_name="Input Value (Direct)",
            info="The data/message to pass through the selected output route. Used if no connection is provided.",
            required=False,
            is_handle=False,
            value="",
        ),
    ]

    # --- Define Outputs (Only Default is Static) ---
    # *** The frontend MUST dynamically render outputs for each item in 'cases' ***
    outputs: List[Output] = [
        Output(
            name="default_output",
            display_name="Default",
            output_type="Any",  # Type matches 'routed_input'
            info="Outputs the routed value if the input does not match any defined case.",
        ),
        # NOTE: 'case_output_0', 'case_output_1', etc. are NOT defined here statically.
        # They are expected to be handled dynamically by the frontend and executor.
    ]

    def _compare_values(self, value1: Any, value2: Any, case_sensitive: bool) -> bool:
        """Helper to compare two values, handling string conversion and case sensitivity."""
        str1 = str(value1) if value1 is not None else None
        str2 = str(value2) if value2 is not None else None
        if str1 is None or str2 is None:
            return str1 == str2
        return str1.lower() == str2.lower() if not case_sensitive else str1 == str2

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the SwitchNodeDynamic.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Executes the dynamic switch logic. The returned dictionary includes keys for
        all potential dynamic outputs ('case_output_i') plus the default.
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        # Prioritize handle inputs over direct inputs
        input_value_compare_handle = kwargs.get("input_value_compare_handle")
        input_value_compare_direct = kwargs.get("input_value_compare", "")
        input_value_compare = (
            input_value_compare_handle
            if input_value_compare_handle is not None
            else input_value_compare_direct
        )

        # Prioritize handle inputs over direct inputs for routed input
        routed_input_handle = kwargs.get("routed_input_handle")
        routed_input_direct = kwargs.get("routed_input", "")
        routed_input_value = (
            routed_input_handle if routed_input_handle is not None else routed_input_direct
        )

        case_sensitive = kwargs.get("case_sensitive", False)
        # Get the list of case match values configured by the user
        cases_list = kwargs.get("cases", [])

        print(f"  - Input Value (to Compare): {repr(input_value_compare)}")
        print(f"  - Case Sensitive: {case_sensitive}")
        print(f"  - Configured Cases: {cases_list}")
        print(f"  - Input Value (to Route): {repr(routed_input_value)}")

        # --- Determine Match and Prepare Output Keys ---
        matched_output_name = "default_output"  # Default assumption
        # Create keys for all potential dynamic outputs + the default
        output_keys = [f"case_output_{i}" for i in range(len(cases_list))] + ["default_output"]
        output_data = {key: None for key in output_keys}  # Initialize all to None

        for index, match_value in enumerate(cases_list):
            if self._compare_values(input_value_compare, match_value, case_sensitive):
                matched_output_name = f"case_output_{index}"
                print(
                    f"  - Match found at index {index}! Routing to '{matched_output_name}' (Matched on '{match_value}')"
                )
                break  # First match wins

        if matched_output_name == "default_output":
            print("  - No specific case matched. Routing to 'default_output'.")

        # Set the value for the matched output branch in the prepared dict
        if (
            matched_output_name in output_data
        ):  # Should always be true with the initialization above
            output_data[matched_output_name] = routed_input_value
        else:
            # This case should technically not happen if initialization is correct
            print(
                f"  Warning: Matched output name '{matched_output_name}' not found in prepared output keys. Routing to default."
            )
            output_data["default_output"] = routed_input_value

        print(f"  Output Data Prepared: {output_data}")
        return output_data
