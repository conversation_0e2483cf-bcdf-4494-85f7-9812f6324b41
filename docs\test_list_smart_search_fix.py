#!/usr/bin/env python3
"""
Test for the List Smart Search fix.

This test verifies that the Select Data Component correctly handles Smart Search
mode when working with arrays of objects, specifically the use case where
the MCP Script Generator outputs an array and we want to extract specific fields.
"""

import asyncio
import sys
import os

# Add the app directory to sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

# Import the components
try:
    from components.processing.select_data import SelectDataComponent
    from models.workflow_builder.context import WorkflowContext
    print("✓ Successfully imported workflow-service components")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


def create_context(input_data, selector, search_mode="Exact Path", data_type="Auto-Detect"):
    """Helper function to create WorkflowContext with given inputs."""
    context = WorkflowContext()
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_data": input_data,
        "selector": selector,
        "search_mode": search_mode,
        "data_type": data_type
    }
    return context


async def test_mcp_script_generator_output():
    """Test the exact scenario from the MCP Script Generator workflow."""
    print("🧪 Testing MCP Script Generator Output Scenario")
    print("=" * 60)
    
    # This is the exact data structure from the workflow logs
    mcp_output = [
        {"data": "marketing", "data_type": "string", "property_name": "title"},
        {"data": "[Scene: A sleek office buzzing with energy — tech gadgets, young guys at computers, the hum of creativity. You walk in with confidence, taking center stage.]\n\nYou: \"Master marketing in 1 minute... Male Edition! So, you ever wondered how to cut through the digital noise and make your brand stand out? Well, you're in the right place.\"\n\n[Cut to fast-paced clips — social media buzzing, phones lighting up with notifications, and rising graphs showing growth.]\n\nYou (voiceover): \"It's more than just creativity, it's about knowing your audience and using technology to your advantage. And hey, I've got the playbook.\"\n\n[Back to you pointing at visual aids showing strategies and their impact.]\n\nYou: \"Look, our world's jam-packed with content. To really connect, you've gotta understand what clicks — like hyper-targeted ads that fit seamlessly into our digital days.\"\n\n[Scenes of young men on various platforms, streaming, gaming, socializing.]\n\nYou (voiceover): \"Capturing loyalty means being real and telling compelling stories.\"\n\n[Back to you, locking eyes with the camera.]\n\nYou: \"So, let's ditch those stale playbooks, okay? It's about crafting messages that speak directly to your audience. Imagine turning casual scrollers into devoted fans.\"\n\n[Screen highlights 'Join the Marketing Mastery!' in bold.]\n\nYou: \"Ready to kick up your marketing game? Well, make sure to hit like and subscribe for real strategies that connect and elevate your brand to new heights.\"\n\n[Scene closes with your logo and a simple call-to-action, inviting viewers to engage.]\n\nVoiceover: \"Blown away? Drop a comment if you're all set to level up!\"", "data_type": "string", "property_name": "script"},
        {"data": "TOPIC", "data_type": "string", "property_name": "script_type"},
        {"data": "SHORT", "data_type": "string", "property_name": "video_type"}
    ]
    
    component = SelectDataComponent()
    
    # Test cases that should work with the fix
    test_cases = [
        {
            "description": "🎯 Extract 'script' field using Smart Search",
            "selector": "script",
            "search_mode": "Smart Search",
            "expected_contains": "Master marketing in 1 minute"
        },
        {
            "description": "🎯 Extract 'title' field using Smart Search", 
            "selector": "title",
            "search_mode": "Smart Search",
            "expected": "marketing"
        },
        {
            "description": "🎯 Extract 'script_type' field using Smart Search",
            "selector": "script_type", 
            "search_mode": "Smart Search",
            "expected": "TOPIC"
        },
        {
            "description": "🎯 Extract 'video_type' field using Smart Search",
            "selector": "video_type",
            "search_mode": "Smart Search", 
            "expected": "SHORT"
        },
        {
            "description": "❌ Field not found using Smart Search",
            "selector": "nonexistent",
            "search_mode": "Smart Search",
            "expected": None
        },
        {
            "description": "📍 Traditional index access still works",
            "selector": "0",
            "search_mode": "Exact Path",
            "expected_type": dict
        }
    ]
    
    print(f"Running {len(test_cases)} test cases...")
    print("-" * 60)
    
    passed = 0
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['description']}")
        print(f"   Selector: '{test_case['selector']}'")
        print(f"   Mode: {test_case['search_mode']}")
        
        # Create context and execute
        context = create_context(
            mcp_output,
            test_case['selector'], 
            test_case['search_mode']
        )
        
        result = await component.execute(context)
        output = result.outputs.get("output_data")
        error = result.outputs.get("error")
        
        # Check result
        if error is not None:
            print(f"   ❌ FAIL: Error occurred: {error}")
            continue
            
        if "expected_contains" in test_case:
            if output and test_case["expected_contains"] in str(output):
                print(f"   ✅ PASS: Found expected content in script")
                passed += 1
            else:
                print(f"   ❌ FAIL: Expected content not found in output")
                
        elif "expected_type" in test_case:
            if isinstance(output, test_case["expected_type"]):
                print(f"   ✅ PASS: Got expected type {test_case['expected_type'].__name__}")
                passed += 1
            else:
                print(f"   ❌ FAIL: Expected {test_case['expected_type'].__name__}, got {type(output).__name__}")
                
        elif output == test_case.get("expected"):
            print(f"   ✅ PASS: Found '{output}'")
            passed += 1
        else:
            print(f"   ❌ FAIL: Expected '{test_case.get('expected')}', got '{output}'")
    
    print(f"\n📈 Results: {passed}/{len(test_cases)} tests passed")
    
    if passed == len(test_cases):
        print("🎉 All tests passed! The List Smart Search fix is working correctly.")
        return True
    else:
        print("❌ Some tests failed. The fix needs more work.")
        return False


async def test_smart_search_logic():
    """Test the underlying smart search logic for arrays."""
    print("\n\n🔍 Testing Smart Search Logic")
    print("=" * 60)
    
    # Test data that matches the MCP output structure
    test_data = [
        {"property_name": "title", "data": "marketing"},
        {"property_name": "script", "data": "script content here"},
        {"property_name": "script_type", "data": "TOPIC"}
    ]
    
    component = SelectDataComponent()
    
    # Test the _smart_search_field method directly
    result = component._smart_search_field(test_data, "script")
    print(f"Direct smart search for 'script': {result}")
    
    result = component._smart_search_field(test_data, "title") 
    print(f"Direct smart search for 'title': {result}")
    
    result = component._smart_search_field(test_data, "nonexistent")
    print(f"Direct smart search for 'nonexistent': {result}")


async def main():
    """Run all tests."""
    print("List Smart Search Fix - Verification Tests")
    print("=" * 80)
    
    # Test the main scenario
    success = await test_mcp_script_generator_output()
    
    # Test the underlying logic
    await test_smart_search_logic()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 Fix verified! The Select Data Component now correctly handles Smart Search for Lists.")
        return 0
    else:
        print("❌ Fix incomplete. Please review the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
