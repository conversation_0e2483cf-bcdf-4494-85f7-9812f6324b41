#!/usr/bin/env python3
"""
Test for Property-Based Field Matching enhancement in SelectDataComponent.

This test follows Test-Driven Development principles to ensure robust implementation
of property-based field matching for complex data structures where field names
are stored as property values rather than object keys.
"""

import asyncio
import sys
import os
import pytest

# Add the app directory to sys.path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), "app"))

# Import the components
try:
    from components.processing.select_data import SelectDataComponent
    from models.workflow_builder.context import WorkflowContext
    print("✓ Successfully imported workflow-service components")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


class TestPropertyBasedFieldMatching:
    """Test class for Property-Based Field Matching functionality."""

    @pytest.fixture
    def component(self):
        """Create a SelectDataComponent instance for testing."""
        return SelectDataComponent()

    @pytest.fixture
    def mcp_script_data(self):
        """Sample MCP Script Generator output data."""
        return {
            "result": [
                {
                    "data": "${audio_script}",
                    "data_type": "string", 
                    "property_name": "title"
                },
                {
                    "data": "**[Background: Professional yet engaging music starts...]**\n\nNarrator: \"Welcome to the future of workflow automation...\"",
                    "data_type": "string",
                    "property_name": "script"
                },
                {
                    "data": "TOPIC",
                    "data_type": "string",
                    "property_name": "script_type"
                },
                {
                    "data": "SHORT",
                    "data_type": "string",
                    "property_name": "video_type"
                }
            ]
        }

    @pytest.fixture
    def nested_property_data(self):
        """Sample nested data with property-based structures."""
        return {
            "config": {
                "settings": [
                    {
                        "data": "production",
                        "data_type": "string",
                        "property_name": "environment"
                    },
                    {
                        "data": "postgresql://localhost:5432/db",
                        "data_type": "string",
                        "property_name": "database_url"
                    }
                ],
                "metadata": {
                    "version": "1.0",
                    "properties": [
                        {
                            "data": "<EMAIL>",
                            "data_type": "string",
                            "property_name": "admin_email"
                        }
                    ]
                }
            }
        }

    @pytest.fixture
    def mixed_structure_data(self):
        """Sample data with both traditional keys and property-based structures."""
        return {
            "user": {
                "name": "John Doe",  # Traditional key-based
                "email": "<EMAIL>",  # Traditional key-based
                "preferences": [
                    {
                        "data": "dark",
                        "data_type": "string",
                        "property_name": "theme"  # Property-based
                    },
                    {
                        "data": True,
                        "data_type": "boolean",
                        "property_name": "notifications"  # Property-based
                    }
                ]
            }
        }

    @pytest.fixture
    def context_factory(self):
        """Factory function to create WorkflowContext with given inputs."""
        def _create_context(input_data, selector, search_mode="Smart Search", data_type="Auto-Detect", field_matching_mode="Auto-detect"):
            context = WorkflowContext()
            context.current_node_id = "test_node"
            context.node_outputs["test_node"] = {
                "input_data": input_data,
                "selector": selector,
                "search_mode": search_mode,
                "data_type": data_type,
                "field_matching_mode": field_matching_mode
            }
            return context
        return _create_context

    # === Smart Search Mode Tests ===

    @pytest.mark.asyncio
    async def test_smart_search_property_based_flat_structure(self, component, mcp_script_data, context_factory):
        """Test Smart Search finds property-based fields in flat structure."""
        # Arrange
        context = context_factory(mcp_script_data, "script", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert "Professional yet engaging music" in result.outputs["output_data"]

    @pytest.mark.asyncio
    async def test_smart_search_property_based_multiple_fields(self, component, mcp_script_data, context_factory):
        """Test Smart Search finds different property-based fields."""
        test_cases = [
            ("title", "${audio_script}"),
            ("script_type", "TOPIC"),
            ("video_type", "SHORT")
        ]
        
        for field_name, expected_value in test_cases:
            # Arrange
            context = context_factory(mcp_script_data, field_name, "Smart Search")
            
            # Act
            result = await component.execute(context)
            
            # Assert
            assert result.outputs["error"] is None
            assert result.outputs["output_data"] == expected_value

    @pytest.mark.asyncio
    async def test_smart_search_property_based_nested_structure(self, component, nested_property_data, context_factory):
        """Test Smart Search finds property-based fields in nested structures."""
        # Arrange
        context = context_factory(nested_property_data, "environment", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] == "production"

    @pytest.mark.asyncio
    async def test_smart_search_mixed_structure_key_based_priority(self, component, mixed_structure_data, context_factory):
        """Test Smart Search prioritizes key-based over property-based when both exist."""
        # Arrange
        context = context_factory(mixed_structure_data, "name", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] == "John Doe"  # Key-based match

    @pytest.mark.asyncio
    async def test_smart_search_mixed_structure_property_based_fallback(self, component, mixed_structure_data, context_factory):
        """Test Smart Search falls back to property-based when key-based not found."""
        # Arrange
        context = context_factory(mixed_structure_data, "theme", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] == "dark"  # Property-based match

    # === Exact Path Mode with @ Notation Tests ===

    @pytest.mark.asyncio
    async def test_exact_path_at_notation_simple(self, component, mcp_script_data, context_factory):
        """Test Exact Path with @ notation for property-based lookup."""
        # Arrange
        context = context_factory(mcp_script_data, "result.@script", "Exact Path")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert "Professional yet engaging music" in result.outputs["output_data"]

    @pytest.mark.asyncio
    async def test_exact_path_at_notation_with_array_index(self, component, mcp_script_data, context_factory):
        """Test Exact Path with mixed array index and @ notation."""
        # Arrange
        context = context_factory(mcp_script_data, "result.1.@script", "Exact Path")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert "Professional yet engaging music" in result.outputs["output_data"]

    @pytest.mark.asyncio
    async def test_exact_path_at_notation_nested(self, component, nested_property_data, context_factory):
        """Test Exact Path with @ notation in nested structures."""
        # Arrange
        context = context_factory(nested_property_data, "config.settings.@environment", "Exact Path")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] == "production"

    @pytest.mark.asyncio
    async def test_exact_path_at_notation_deeply_nested(self, component, nested_property_data, context_factory):
        """Test Exact Path with @ notation in deeply nested structures."""
        # Arrange
        context = context_factory(nested_property_data, "config.metadata.properties.@admin_email", "Exact Path")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] == "<EMAIL>"

    # === Field Matching Mode Tests ===

    @pytest.mark.asyncio
    async def test_field_matching_mode_key_based_only(self, component, mixed_structure_data, context_factory):
        """Test Field Matching Mode set to 'Key-based Only'."""
        # Arrange
        context = context_factory(mixed_structure_data, "theme", "Smart Search", "Auto-Detect", "Key-based Only")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] is None  # Should not find property-based field

    @pytest.mark.asyncio
    async def test_field_matching_mode_property_based_only(self, component, mixed_structure_data, context_factory):
        """Test Field Matching Mode set to 'Property-based Only'."""
        # Arrange
        context = context_factory(mixed_structure_data, "name", "Smart Search", "Auto-Detect", "Property-based Only")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] is None  # Should not find key-based field

    # === Edge Cases and Error Handling ===

    @pytest.mark.asyncio
    async def test_property_based_missing_data_field(self, component, context_factory):
        """Test handling of property-based structure with missing 'data' field."""
        # Arrange
        malformed_data = {
            "result": [
                {
                    "property_name": "script",
                    # Missing 'data' field
                    "data_type": "string"
                }
            ]
        }
        context = context_factory(malformed_data, "script", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] is None  # Should handle gracefully

    @pytest.mark.asyncio
    async def test_property_based_duplicate_property_names(self, component, context_factory):
        """Test handling of duplicate property names (should return first match)."""
        # Arrange
        duplicate_data = {
            "result": [
                {
                    "data": "first_value",
                    "property_name": "script"
                },
                {
                    "data": "second_value", 
                    "property_name": "script"  # Duplicate
                }
            ]
        }
        context = context_factory(duplicate_data, "script", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] == "first_value"  # Should return first match

    @pytest.mark.asyncio
    async def test_backward_compatibility_traditional_structures(self, component, context_factory):
        """Test that traditional key-based structures still work perfectly."""
        # Arrange
        traditional_data = {
            "user": {
                "profile": {
                    "email": "<EMAIL>",
                    "name": "Jane Doe"
                }
            }
        }
        context = context_factory(traditional_data, "email", "Smart Search")
        
        # Act
        result = await component.execute(context)
        
        # Assert
        assert result.outputs["error"] is None
        assert result.outputs["output_data"] == "<EMAIL>"


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
