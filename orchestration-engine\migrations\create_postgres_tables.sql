-- PostgreSQL migration script for Orchestration Engine
-- Creates tables for workflow state and transition results

-- Create workflow_state table
CREATE TABLE IF NOT EXISTS workflow_state (
    id SERIAL PRIMARY KEY,
    correlation_id VARCHAR(255) UNIQUE NOT NULL,
    workflow_id VARCHAR(255) NOT NULL,
    state_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    archived BOOLEAN DEFAULT FALSE
);

-- Create index on correlation_id
CREATE INDEX IF NOT EXISTS idx_workflow_state_correlation_id ON workflow_state(correlation_id);

-- Create transition_results table
CREATE TABLE IF NOT EXISTS transition_results (
    id SERIAL PRIMARY KEY,
    correlation_id VARCHAR(255) NOT NULL,
    transition_id VARCHAR(255) NOT NULL,
    result_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    archived BOOLEAN DEFAULT FALSE
);

-- <PERSON>reate composite index on correlation_id and transition_id
CREATE UNIQUE INDEX IF NOT EXISTS idx_transition_results_composite ON transition_results(correlation_id, transition_id);

-- Add comments to tables and columns
COMMENT ON TABLE workflow_state IS 'Stores workflow state data for persistence beyond Redis';
COMMENT ON COLUMN workflow_state.correlation_id IS 'Unique identifier for the workflow execution';
COMMENT ON COLUMN workflow_state.workflow_id IS 'Identifier for the workflow';
COMMENT ON COLUMN workflow_state.state_data IS 'JSON data containing the workflow state';
COMMENT ON COLUMN workflow_state.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN workflow_state.updated_at IS 'Timestamp when the record was last updated';
COMMENT ON COLUMN workflow_state.archived IS 'Flag indicating if the record is archived';

COMMENT ON TABLE transition_results IS 'Stores transition execution results for persistence beyond Redis';
COMMENT ON COLUMN transition_results.correlation_id IS 'Identifier for the workflow execution';
COMMENT ON COLUMN transition_results.transition_id IS 'Identifier for the transition';
COMMENT ON COLUMN transition_results.result_data IS 'JSON data containing the transition result';
COMMENT ON COLUMN transition_results.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN transition_results.updated_at IS 'Timestamp when the record was last updated';
COMMENT ON COLUMN transition_results.archived IS 'Flag indicating if the record is archived';
