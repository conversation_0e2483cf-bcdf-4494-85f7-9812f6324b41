"""
Text Analysis Component - Performs various text analysis operations.
"""
import logging
import re
import traceback # Import traceback
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component
from app.config.config import settings # Import settings
# Removed security_config and audit_logger imports
# from app.core_.security_config import get_security_settings
# from app.core_.audit_logger import log_security_event

logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class TextAnalysisRequest(BaseModel):
    """Schema for text analysis requests."""
    text: str = Field(..., min_length=1, description="The text to analyze")
    operations: List[str] = Field(..., description="List of operations to perform")
    language: Optional[str] = Field("en", description="Language code (default: en)")
    # Use settings for default and limit
    max_results: Optional[int] = Field(
        settings.text_analysis_default_max_results, # Load default from settings
        ge=1,
        le=settings.text_analysis_max_results_limit, # Load limit from settings
        description="Maximum number of results to return"
    )


@register_component("text_analysis")
class TextAnalysisComponent(BaseComponent):
    """
    Component for performing text analysis operations.
    
    This component can:
    1. Count words, sentences, and characters
    2. Extract entities (names, places, etc.)
    3. Analyze sentiment
    4. Extract keywords
    """
    
    def __init__(self):
        """
        Initialize the TextAnalysisComponent.
        """
        logger.info("Initializing Text Analysis Component")
        super().__init__()
        # Removed security settings initialization
        # self.security_settings = get_security_settings()
        # Set the request schema for automatic validation
        self.request_schema = TextAnalysisRequest
        
        # Define supported operations (keeping hardcoded as they map to methods)
        self.supported_operations = [
            "count", "entities", "sentiment", "keywords"
        ]
        logger.info(f"Supported operations: {self.supported_operations}")
        logger.info("Text Analysis Component initialized successfully")

        
    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a text analysis payload.
        
        Args:
            payload: The payload to validate
            
        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating text analysis payload for request_id: {request_id}")
        logger.debug(f"Payload to validate for request_id {request_id}: {payload}")

        try:
            # First use schema validation (from parent class)
            schema_validation = await super().validate(payload)
            if not schema_validation.is_valid:
                logger.error(f"Schema validation failed for request_id {request_id}: {schema_validation.error_message}")
                return schema_validation
            logger.debug(f"Schema validation passed for request_id {request_id}")

            # Additional custom validation (keeping operation validation as it's logic validation)
            operations = payload.get("operations", [])
            logger.debug(f"Validating requested operations: {operations} for request_id {request_id}")

            # Validate operations
            invalid_operations = [op for op in operations if op not in self.supported_operations]
            if invalid_operations:
                error_msg = f"Unsupported operations: {', '.join(invalid_operations)} for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"operations": f"must be one of: {', '.join(self.supported_operations)}"}
                )
            logger.debug(f"Operations validation passed for request_id {request_id}")

            # Validate text length (already handled by Pydantic min_length=1, but adding log)
            text = payload.get("text", "")
            if not text:
                 # This should be caught by Pydantic, but double-checking
                 error_msg = f"Input text is empty for request_id {request_id}"
                 logger.error(error_msg)
                 return ValidationResult(is_valid=False, error_message=error_msg, error_details={"text": "cannot be empty"})
            logger.debug(f"Input text length check passed for request_id {request_id}")


            logger.info(f"Text analysis payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(is_valid=False, error_message=error_msg, error_details={"validation_error": str(e)})

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the text analysis operations.
        
        Args:
            payload: The request payload containing:
                - text: The text to analyze
                - operations: List of operations to perform
                - language: Language code (optional)
                - max_results: Maximum number of results (optional)
                
        Returns:
            A dictionary containing the analysis results
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing text analysis request for request_id: {request_id}")
        logger.debug(f"Full payload for request_id {request_id}: {payload}")

        try:
            # Get inputs from payload
            text = payload["text"]
            operations = payload["operations"]
            language = payload.get("language", "en")
            # max_results is validated by Pydantic schema, use the value directly
            max_results = payload.get("max_results", settings.text_analysis_default_max_results) # Use default from settings if not provided

            logger.info(f"Analyzing text (length {len(text)}) for request_id {request_id}. Operations: {operations}, Language: {language}, Max Results: {max_results}")

            # Initialize results
            results = {}
            
            # Perform requested operations
            for operation in operations:
                logger.debug(f"Performing operation: {operation} for request_id {request_id}")
                try:
                    if operation == "count":
                        results["count"] = self._count_text_elements(text)
                        logger.debug(f"Count operation completed for request_id {request_id}")
                    elif operation == "entities":
                        results["entities"] = self._extract_entities(text, language, max_results)
                        logger.debug(f"Entities operation completed for request_id {request_id}")
                    elif operation == "sentiment":
                        results["sentiment"] = self._analyze_sentiment(text, language)
                        logger.debug(f"Sentiment operation completed for request_id {request_id}")
                    elif operation == "keywords":
                        results["keywords"] = self._extract_keywords(text, language, max_results)
                        logger.debug(f"Keywords operation completed for request_id {request_id}")
                    else:
                        # This case should be caught by validation, but included for safety
                        logger.warning(f"Attempted to perform unsupported operation: {operation} for request_id {request_id}")
                        results[operation] = {"error": f"Unsupported operation: {operation}"}
                except Exception as op_e:
                    error_msg = f"Error during operation '{operation}' for request_id {request_id}: {str(op_e)}"
                    logger.error(error_msg)
                    logger.debug(f"Exception details for operation '{operation}', request_id {request_id}: {traceback.format_exc()}")
                    results[operation] = {"error": error_msg}


            logger.info(f"Text analysis completed successfully for request_id {request_id}. Performed operations: {list(results.keys())}")
            logger.debug(f"Analysis results for request_id {request_id}: {results}")
            return {
                "status": "success",
                "result": results
            }
            
        except KeyError as e:
            error_msg = f"Missing required parameter for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg
            }
        except Exception as e:
            error_msg = f"Unexpected error analyzing text for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg
            }
        
    def _count_text_elements(self, text: str) -> Dict[str, int]:
        """
        Count words, sentences, and characters in text.
        
        Args:
            text: The text to analyze
            
        Returns:
            Dictionary with counts
        """
        logger.debug("Counting text elements")
        # Count characters
        char_count = len(text)
        logger.debug(f"Character count: {char_count}")

        # Count words
        words = re.findall(r'\b\w+\b', text)
        word_count = len(words)
        logger.debug(f"Word count: {word_count}")

        # Count sentences (simple approximation)
        sentences = re.split(r'[.!?]+', text)
        sentence_count = len([s for s in sentences if s.strip()])
        logger.debug(f"Sentence count: {sentence_count}")

        counts = {
            "characters": char_count,
            "words": word_count,
            "sentences": sentence_count
        }
        logger.debug(f"Text element counts: {counts}")
        return counts
        
    def _extract_entities(self, text: str, language: str, max_results: int) -> List[Dict[str, Any]]:
        """
        Extract entities from text (simplified implementation).
        
        Args:
            text: The text to analyze
            language: Language code
            max_results: Maximum number of results
            
        Returns:
            List of extracted entities
        """
        logger.debug(f"Extracting entities (simplified) for language '{language}', max_results {max_results}")
        # This is a simplified implementation
        # In a real component, you would use a proper NLP library
        
        # Look for capitalized words as potential entities
        potential_entities = re.findall(r'\b[A-Z][a-z]+\b', text)
        logger.debug(f"Found {len(potential_entities)} potential entities")

        # Deduplicate and limit results
        unique_entities = list(set(potential_entities))[:max_results]
        logger.debug(f"Extracted {len(unique_entities)} unique entities (up to max_results)")

        # Format results
        result_list = [{"text": entity, "type": "UNKNOWN"} for entity in unique_entities]
        logger.debug(f"Formatted entity results: {result_list}")
        return result_list
        
    def _analyze_sentiment(self, text: str, language: str) -> Dict[str, Any]:
        """
        Analyze sentiment of text (simplified implementation).
        
        Args:
            text: The text to analyze
            language: Language code
            
        Returns:
            Sentiment analysis results
        """
        logger.debug(f"Analyzing sentiment (simplified) for language '{language}'")
        # This is a simplified implementation
        # In a real component, you would use a proper sentiment analysis library
        
        # Simple keyword-based sentiment analysis (keeping hardcoded as part of simplified logic)
        positive_words = ["good", "great", "excellent", "happy", "positive", "wonderful", "best"]
        negative_words = ["bad", "terrible", "awful", "sad", "negative", "worst", "hate"]
        
        # Count occurrences
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        logger.debug(f"Positive keyword count: {positive_count}, Negative keyword count: {negative_count}")

        # Determine sentiment
        if positive_count > negative_count:
            sentiment = "positive"
            score = min(0.5 + (positive_count - negative_count) * 0.1, 1.0)
        elif negative_count > positive_count:
            sentiment = "negative"
            score = max(0.5 - (negative_count - positive_count) * 0.1, 0.0)
        else:
            sentiment = "neutral"
            score = 0.5
            
        sentiment_result = {
            "sentiment": sentiment,
            "score": score,
            "positive_count": positive_count,
            "negative_count": negative_count
        }
        logger.debug(f"Sentiment analysis result: {sentiment_result}")
        return sentiment_result
        
    def _extract_keywords(self, text: str, language: str, max_results: int) -> List[Dict[str, Any]]:
        """
        Extract keywords from text (simplified implementation).
        
        Args:
            text: The text to analyze
            language: Language code
            max_results: Maximum number of results
            
        Returns:
            List of extracted keywords
        """
        logger.debug(f"Extracting keywords (simplified) for language '{language}', max_results {max_results}")
        # This is a simplified implementation
        # In a real component, you would use a proper keyword extraction library
        
        # Remove common stop words (simplified - keeping hardcoded as part of simplified logic)
        stop_words = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "with", "by"]
        logger.debug(f"Using simplified stop words: {stop_words}")

        # Tokenize and clean text
        words = re.findall(r'\b\w+\b', text.lower())
        logger.debug(f"Tokenized text into {len(words)} words")

        # Filter out stop words
        filtered_words = [word for word in words if word not in stop_words and len(word) > 2]
        logger.debug(f"Filtered down to {len(filtered_words)} words after removing stop words and short words")

        # Count word frequencies
        word_counts = {}
        for word in filtered_words:
            word_counts[word] = word_counts.get(word, 0) + 1
        logger.debug(f"Calculated word frequencies for {len(word_counts)} unique words")

        # Sort by frequency
        sorted_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)
        logger.debug(f"Sorted words by frequency")

        # Limit results
        top_keywords = sorted_words[:max_results]
        logger.debug(f"Selected top {len(top_keywords)} keywords")

        # Format results
        result_list = [{"text": word, "count": count} for word, count in top_keywords]
        logger.debug(f"Formatted keyword results: {result_list}")
        return result_list
