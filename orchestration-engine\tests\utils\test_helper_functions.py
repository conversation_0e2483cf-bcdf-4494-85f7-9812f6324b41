import pytest
import json
from unittest.mock import patch, mock_open
from app.utils.helper_functions import (
    load_schema,
    fix_invalid_escapes,
    format_execution_result
)


class TestHelperFunctions:
    """
    Test suite for helper functions.
    Tests schema loading, JSON string fixing, and execution result formatting.
    """

    def test_load_schema_valid_json(self):
        """
        Test loading a valid JSON schema from file.
        """
        mock_schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer"}
            }
        }
        mock_json = json.dumps(mock_schema)
        
        with patch("builtins.open", mock_open(read_data=mock_json)):
            result = load_schema("dummy/path.json")
            assert result == mock_schema
            assert isinstance(result, dict)
            assert "properties" in result

    def test_load_schema_invalid_json(self):
        """
        Test loading an invalid JSON schema raises JSONDecodeError.
        """
        with patch("builtins.open", mock_open(read_data="invalid json")):
            with pytest.raises(json.JSONDecodeError):
                load_schema("dummy/path.json")

    def test_load_schema_file_not_found(self):
        """
        Test attempting to load schema from non-existent file.
        """
        with pytest.raises(FileNotFoundError):
            load_schema("nonexistent/path.json")

    def test_fix_invalid_escapes_basic(self):
        """
        Test fixing basic invalid escape sequences in JSON string.
        """
        input_str = r'{"key": "value with \invalid escape"}'
        expected = r'{"key": "value with \\invalid escape"}'
        result = fix_invalid_escapes(input_str)
        assert result == expected

    def test_fix_invalid_escapes_valid_escapes(self):
        """
        Test that valid escape sequences remain unchanged.
        """
        valid_escapes = r'{"key": "\\\"\/\b\f\n\r\t"}'
        result = fix_invalid_escapes(valid_escapes)
        assert result == valid_escapes

    def test_fix_invalid_escapes_mixed(self):
        """
        Test fixing string with both valid and invalid escapes.
        """
        input_str = r'{"key": "\valid \invalid \n \escape"}'
        expected = r'{"key": "\valid \\invalid \n \\escape"}'
        result = fix_invalid_escapes(input_str)
        assert result == expected

    def test_format_execution_result_empty_input(self):
        """
        Test formatting empty execution result.
        """
        output_schema = {
            "type": "object",
            "properties": {}
        }
        result = format_execution_result(output_schema, [])
        assert isinstance(result, list)
        assert len(result) == 0

    def test_format_execution_result_basic(self):
        """
        Test basic execution result formatting with simple schema.
        """
        output_schema = {
            "type": "object",
            "properties": {
                "test_field": {
                    "type": "string",
                    "description": "A test field"
                }
            }
        }
        execution_result = [{"test_field": "test value"}]
        
        result = format_execution_result(output_schema, execution_result)
        
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "test_field"
        assert result[0]["data"] == "test value"
        assert result[0]["data_type"] == "string"

    def test_format_execution_result_unknown_property(self):
        """
        Test formatting result with unknown property.
        """
        output_schema = {
            "type": "object",
            "properties": {}
        }
        execution_result = [{"unknown_field": "test value"}]
        
        result = format_execution_result(output_schema, execution_result)
        
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "unknown_field"
        assert result[0]["data"] == "test value"
        assert result[0]["data_type"] == "unknown"

    def test_format_execution_result_multiple_properties(self):
        """
        Test formatting result with multiple properties.
        """
        output_schema = {
            "type": "object",
            "properties": {
                "string_field": {
                    "type": "string",
                    "description": "A string field"
                },
                "number_field": {
                    "type": "number",
                    "description": "A number field"
                }
            }
        }
        execution_result = [{
            "string_field": "test string",
            "number_field": 42
        }]
        
        result = format_execution_result(output_schema, execution_result)
        
        assert isinstance(result, list)
        assert len(result) == 2
        
        # Find and verify string field
        string_item = next(item for item in result if item["property_name"] == "string_field")
        assert string_item["data"] == "test string"
        assert string_item["data_type"] == "string"
        
        # Find and verify number field
        number_item = next(item for item in result if item["property_name"] == "number_field")
        assert number_item["data"] == 42
        assert number_item["data_type"] == "number"

    def test_format_execution_result_nested_objects(self):
        """
        Test formatting result with nested object structures.
        """
        output_schema = {
            "type": "object",
            "properties": {
                "nested_obj": {
                    "type": "object",
                    "description": "A nested object",
                    "properties": {
                        "inner_field": {
                            "type": "string"
                        }
                    }
                }
            }
        }
        execution_result = [{
            "nested_obj": {
                "inner_field": "inner value"
            }
        }]
        
        result = format_execution_result(output_schema, execution_result)
        
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "nested_obj"
        assert isinstance(result[0]["data"], dict)
        assert result[0]["data"]["inner_field"] == "inner value"
        assert result[0]["data_type"] == "object"

    def test_format_execution_result_array_type(self):
        """
        Test formatting result with array type fields.
        """
        output_schema = {
            "type": "object",
            "properties": {
                "array_field": {
                    "type": "array",
                    "description": "An array field",
                    "items": {
                        "type": "string"
                    }
                }
            }
        }
        execution_result = [{
            "array_field": ["item1", "item2", "item3"]
        }]
        
        result = format_execution_result(output_schema, execution_result)
        
        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "array_field"
        assert isinstance(result[0]["data"], list)
        assert result[0]["data"] == ["item1", "item2", "item3"]
        assert result[0]["data_type"] == "array"