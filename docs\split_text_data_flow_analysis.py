#!/usr/bin/env python3
"""
Complete data flow analysis for Split Text Component.
This demonstrates the exact data transformations at each stage.
"""

import json
from typing import Dict, Any
from dataclasses import dataclass, asdict
from enum import Enum

class ServiceStage(Enum):
    FRONTEND = "frontend"
    API_GATEWAY = "api_gateway"
    WORKFLOW_SERVICE = "workflow_service"
    ORCHESTRATION_ENGINE = "orchestration_engine"
    NODE_EXECUTOR_SERVICE = "node_executor_service"

@dataclass
class DataFlowStage:
    stage: ServiceStage
    service_name: str
    data_format: str
    payload: Dict[str, Any]
    transformations: list
    protocols: list

def demonstrate_split_text_data_flow():
    """Demonstrate the complete data flow for Split Text Component."""
    
    print("🔄 Split Text Component - Complete Data Flow Analysis")
    print("=" * 70)
    
    # Stage 1: Frontend Request
    frontend_request = DataFlowStage(
        stage=ServiceStage.FRONTEND,
        service_name="Workflow Builder App",
        data_format="HTTP JSON",
        payload={
            "workflow_id": "text_processing_001",
            "execution_context": {
                "user_id": "user_123",
                "session_id": "session_456"
            },
            "nodes": [
                {
                    "id": "split_node_789",
                    "type": "SplitTextComponent",
                    "inputs": {
                        "input_text": "apple,banana,cherry,date,elderberry",
                        "delimiter": ",",
                        "max_splits": 3,
                        "include_delimiter": False
                    },
                    "position": {"x": 100, "y": 200}
                }
            ]
        },
        transformations=[
            "User interaction captured",
            "Form validation applied",
            "JSON serialization"
        ],
        protocols=["HTTP POST", "REST API"]
    )
    
    # Stage 2: API Gateway Processing
    api_gateway_request = DataFlowStage(
        stage=ServiceStage.API_GATEWAY,
        service_name="API Gateway",
        data_format="gRPC",
        payload={
            "workflow_request": {
                "workflow_id": "text_processing_001",
                "correlation_id": "corr_abc123",
                "user_context": {
                    "user_id": "user_123",
                    "permissions": ["execute_workflows"]
                },
                "workflow_data": {
                    "nodes": [
                        {
                            "id": "split_node_789",
                            "type": "SplitTextComponent",
                            "inputs": {
                                "input_text": "apple,banana,cherry,date,elderberry",
                                "delimiter": ",",
                                "max_splits": 3,
                                "include_delimiter": False
                            }
                        }
                    ]
                }
            }
        },
        transformations=[
            "Authentication & authorization",
            "Rate limiting applied",
            "HTTP to gRPC conversion",
            "Correlation ID generation"
        ],
        protocols=["gRPC", "Protocol Buffers"]
    )
    
    # Stage 3: Workflow Service Processing
    workflow_service_context = DataFlowStage(
        stage=ServiceStage.WORKFLOW_SERVICE,
        service_name="Workflow Service",
        data_format="WorkflowContext + NodeResult",
        payload={
            "workflow_context": {
                "workflow_id": "text_processing_001",
                "current_node_id": "split_node_789",
                "execution_id": "exec_def456",
                "global_context": {},
                "node_outputs": {},
                "visited_nodes": ["split_node_789"]
            },
            "component_execution": {
                "component_class": "SplitTextComponent",
                "method": "execute",
                "kwargs": {
                    "input_text": "apple,banana,cherry,date,elderberry",
                    "delimiter": ",",
                    "max_splits": 3,
                    "include_delimiter": False
                }
            },
            "expected_node_result": {
                "node_id": "split_node_789",
                "status": "success",
                "outputs": {
                    "output_list": ["apple", "banana", "cherry", "date,elderberry"]
                },
                "execution_time": 0.045
            }
        },
        transformations=[
            "gRPC to internal models",
            "WorkflowContext creation",
            "Component instantiation",
            "Input parameter extraction",
            "NodeResult formatting"
        ],
        protocols=["Internal Python objects", "Async method calls"]
    )
    
    # Stage 4: Orchestration Engine Communication
    orchestration_request = DataFlowStage(
        stage=ServiceStage.ORCHESTRATION_ENGINE,
        service_name="Orchestration Engine",
        data_format="Kafka JSON",
        payload={
            "kafka_message": {
                "topic": "node-execution-request",
                "key": "split_node_789",
                "value": {
                    "tool_name": "SplitTextComponent",
                    "tool_parameters": {
                        "input_text": "apple,banana,cherry,date,elderberry",
                        "delimiter": ",",
                        "max_splits": 3,
                        "include_delimiter": False
                    },
                    "request_id": "req_ghi789",
                    "correlation_id": "corr_abc123",
                    "workflow_id": "text_processing_001",
                    "node_id": "split_node_789"
                },
                "headers": {
                    "content-type": "application/json",
                    "correlation-id": "corr_abc123"
                }
            }
        },
        transformations=[
            "NodeResult to Kafka message",
            "tool_parameters wrapper creation",
            "Request ID generation",
            "Topic routing logic"
        ],
        protocols=["Apache Kafka", "JSON serialization"]
    )
    
    # Stage 5: Node Executor Service Processing
    node_executor_processing = DataFlowStage(
        stage=ServiceStage.NODE_EXECUTOR_SERVICE,
        service_name="Node Executor Service",
        data_format="Component Response",
        payload={
            "received_payload": {
                "request_id": "req_ghi789",
                "tool_parameters": {
                    "input_text": "apple,banana,cherry,date,elderberry",
                    "delimiter": ",",
                    "max_splits": 3,
                    "include_delimiter": False
                }
            },
            "validation_result": {
                "is_valid": True,
                "error_message": None,
                "error_details": {}
            },
            "processing_steps": [
                {
                    "step": "parameter_extraction",
                    "input_text": "apple,banana,cherry,date,elderberry",
                    "delimiter": ",",
                    "max_splits": 3,
                    "include_delimiter": False
                },
                {
                    "step": "text_splitting",
                    "algorithm": "standard_split",
                    "result": ["apple", "banana", "cherry", "date,elderberry"]
                }
            ],
            "component_response": {
                "status": "success",
                "output_list": ["apple", "banana", "cherry", "date,elderberry"]
            },
            "tool_executor_response": {
                "request_id": "req_ghi789",
                "status": "success",
                "result": {
                    "status": "success",
                    "output_list": ["apple", "banana", "cherry", "date,elderberry"]
                }
            }
        },
        transformations=[
            "Kafka message consumption",
            "tool_parameters unwrapping",
            "Component validation",
            "Text splitting algorithm",
            "Response formatting",
            "Kafka result publishing"
        ],
        protocols=["Apache Kafka", "BaseComponent interface"]
    )
    
    # Display the complete flow
    stages = [
        frontend_request,
        api_gateway_request, 
        workflow_service_context,
        orchestration_request,
        node_executor_processing
    ]
    
    for i, stage in enumerate(stages, 1):
        print(f"\n📋 Stage {i}: {stage.service_name}")
        print("-" * 50)
        print(f"Service: {stage.stage.value}")
        print(f"Data Format: {stage.data_format}")
        print(f"Protocols: {', '.join(stage.protocols)}")
        
        print(f"\n🔄 Transformations:")
        for transform in stage.transformations:
            print(f"  • {transform}")
        
        print(f"\n📦 Payload Structure:")
        print(json.dumps(stage.payload, indent=2)[:500] + "..." if len(json.dumps(stage.payload, indent=2)) > 500 else json.dumps(stage.payload, indent=2))
        
        if i < len(stages):
            print(f"\n⬇️  Flows to: {stages[i].service_name}")

def demonstrate_error_handling():
    """Demonstrate error handling across the pipeline."""
    
    print(f"\n\n🚨 Error Handling Flow")
    print("=" * 50)
    
    error_scenarios = [
        {
            "error_type": "Validation Error",
            "stage": "Node Executor Service",
            "trigger": "Missing input_text parameter",
            "response_chain": [
                {
                    "service": "Node Executor Service",
                    "response": {
                        "request_id": "req_error_001",
                        "status": "error",
                        "error": "Missing required field 'input_text' in payload"
                    }
                },
                {
                    "service": "Orchestration Engine",
                    "response": {
                        "correlation_id": "corr_error_001",
                        "node_id": "split_node_error",
                        "status": "error",
                        "error_details": {
                            "component": "SplitTextComponent",
                            "error": "Missing required field 'input_text' in payload"
                        }
                    }
                },
                {
                    "service": "Workflow Service",
                    "response": {
                        "node_id": "split_node_error",
                        "status": "error",
                        "outputs": {
                            "error": "Missing required field 'input_text' in payload"
                        },
                        "execution_time": 0.002
                    }
                }
            ]
        },
        {
            "error_type": "Processing Error",
            "stage": "Node Executor Service",
            "trigger": "Invalid delimiter type",
            "response_chain": [
                {
                    "service": "Node Executor Service", 
                    "response": {
                        "request_id": "req_error_002",
                        "status": "error",
                        "error": "Field 'delimiter' must be a string, got int"
                    }
                }
            ]
        }
    ]
    
    for scenario in error_scenarios:
        print(f"\n📋 {scenario['error_type']}")
        print(f"Trigger: {scenario['trigger']}")
        print(f"Origin: {scenario['stage']}")
        
        for i, response in enumerate(scenario['response_chain']):
            print(f"\n  {i+1}. {response['service']} Response:")
            print(f"     {json.dumps(response['response'], indent=6)}")

def demonstrate_performance_metrics():
    """Demonstrate performance tracking across services."""
    
    print(f"\n\n⚡ Performance Metrics")
    print("=" * 50)
    
    performance_data = {
        "request_id": "req_perf_001",
        "total_execution_time": 156.7,  # milliseconds
        "stage_breakdown": {
            "api_gateway": {
                "duration_ms": 12.3,
                "operations": ["auth", "validation", "routing"]
            },
            "workflow_service": {
                "duration_ms": 8.9,
                "operations": ["context_creation", "component_instantiation"]
            },
            "orchestration_engine": {
                "duration_ms": 45.2,
                "operations": ["kafka_send", "result_wait"]
            },
            "node_executor_service": {
                "duration_ms": 89.1,
                "operations": ["validation", "processing", "response_format"]
            },
            "network_overhead": {
                "duration_ms": 1.2,
                "operations": ["serialization", "kafka_transport"]
            }
        },
        "resource_usage": {
            "memory_peak_mb": 45.6,
            "cpu_usage_percent": 12.3,
            "network_bytes": 2048
        }
    }
    
    print(json.dumps(performance_data, indent=2))

if __name__ == "__main__":
    demonstrate_split_text_data_flow()
    demonstrate_error_handling()
    demonstrate_performance_metrics()
