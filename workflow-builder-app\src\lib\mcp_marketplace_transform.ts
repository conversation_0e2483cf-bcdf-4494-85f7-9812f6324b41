/**
 * Utility functions to transform MCP Marketplace components for the orchestration service
 */
import { Node, Edge } from "reactflow";
import { WorkflowNodeData, MCPSchemaInfo } from "@/types";
import { useComponentStateStore } from "@/store/mcpToolsStore";

/**
 * Transform a workflow for the orchestration service
 *
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @returns The transformed workflow data
 */
export function transformWorkflowForOrchestration(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
): { nodes: Node<WorkflowNodeData>[]; edges: Edge[] } {
  // Create deep copies to avoid modifying the originals
  const transformedNodes = JSON.parse(JSON.stringify(nodes));
  const transformedEdges = JSON.parse(JSON.stringify(edges));

  // Fix each node
  for (let i = 0; i < transformedNodes.length; i++) {
    transformedNodes[i] = fixMCPMarketplaceNode(transformedNodes[i]);
  }

  return {
    nodes: transformedNodes,
    edges: transformedEdges,
  };
}

/**
 * Fix an MCP Marketplace node to match the expected template
 *
 * @param node The node to fix
 * @returns The fixed node
 */
export function fixMCPMarketplaceNode(node: Node<WorkflowNodeData>): Node<WorkflowNodeData> {
  // Create a deep copy to avoid modifying the original
  const fixedNode = JSON.parse(JSON.stringify(node));

  // Check if this is an MCP Marketplace component
  const nodeData = fixedNode.data;
  const nodeType = nodeData?.type;
  const originalType = nodeData?.originalType;

  const isMCPMarketplace =
    nodeType === "mcp" ||
    nodeType === "MCPMarketplaceComponent" ||
    (originalType && originalType.includes("mcp_"));

  if (!isMCPMarketplace) {
    return fixedNode;
  }

  // Get the node definition and config
  const definition = nodeData?.definition;
  const config = nodeData?.config || {};

  // Extract input values
  const inputValues: Record<string, any> = {};

  // PRIORITY 1: Get values from the component state store (highest priority)
  // These are the most up-to-date values from the UI
  const stateStore = useComponentStateStore.getState();
  const nodeState = stateStore.nodes[node.id] || {};

  // Check if there are any values in the component state
  if (nodeState) {
    // Get values from the config in the component state
    const stateConfig = nodeState.config || {};
    for (const [key, value] of Object.entries(stateConfig)) {
      if (value !== null && value !== undefined && value !== "") {
        inputValues[key] = value;
      }
    }

    // Also check for direct values in the component state
    for (const [key, value] of Object.entries(nodeState)) {
      if (key !== "config" && value !== null && value !== undefined && value !== "") {
        inputValues[key] = value;
      }
    }
  }

  // PRIORITY 2: Get values from the node data config
  // These are values that might have been set programmatically
  if (node.data.config) {
    for (const [key, value] of Object.entries(node.data.config)) {
      // Skip special keys that are not user inputs and skip the inputs array
      if (
        ![
          "mode",
          "selected_tool_name",
          "stdio_command",
          "tool_args",
          "node_id",
          "_internal_state",
          "inputs",
        ].includes(key) &&
        value !== null &&
        value !== undefined &&
        value !== ""
      ) {
        // Only add if not already set from component state
        if (!(key in inputValues)) {
          inputValues[key] = value;
        }
      }
    }
  }

  // PRIORITY 3: Get values from the inputs in the definition
  // These are the default values defined in the component
  if (definition && definition.inputs) {
    const inputs = definition.inputs;
    for (const input of inputs) {
      const name = input.name;
      const value = input.value;

      // Skip empty values and special handle inputs
      if (
        value === null ||
        value === undefined ||
        value === "" ||
        (typeof value === "object" && Object.keys(value).length === 0) ||
        name.endsWith("_handle")
      ) {
        continue;
      }

      // Only add if not already set from higher priority sources
      if (!(name in inputValues)) {
        inputValues[name] = value;
      }
    }
  }

  // PRIORITY 4: Get values from tool_args if it exists
  // These are values that might have been set for MCP Tools
  if (config.tool_args) {
    const toolArgs = config.tool_args;
    for (const [name, value] of Object.entries(toolArgs)) {
      if (value !== null && value !== undefined && value !== "") {
        // Only add if not already set from higher priority sources
        if (!(name in inputValues)) {
          inputValues[name] = value;
        }
      }
    }
  }

  // Log the values for debugging
  console.log(`[MCP Transform] Node ID: ${node.id}`);
  console.log(`[MCP Transform] Original type: ${originalType}`);
  console.log(`[MCP Transform] Node type: ${nodeType}`);
  console.log(`[MCP Transform] Is MCP Marketplace: ${isMCPMarketplace}`);
  console.log(`[MCP Transform] Final input values:`, inputValues);

  // Special handling for API request nodes to ensure method is preserved
  if (nodeData.originalType === "ApiRequestNode" || nodeData.type === "ApiRequestNode") {
    console.log(`[MCP Transform] Preserving API request node configuration`);

    // Make sure to preserve the method value from the original config
    if (nodeData.config && nodeData.config.method) {
      console.log(
        `[MCP Transform] Preserving method ${nodeData.config.method} for API request node`,
      );
      inputValues.method = nodeData.config.method;
    }
  }

  // Create a new config with the input values directly
  nodeData.config = inputValues;

  return fixedNode;
}
