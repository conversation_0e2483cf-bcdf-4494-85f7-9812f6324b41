2025-05-28 16:17:37 - ApiRequestNode - INFO - [setup_logger:467] Logger ApiRequestNode configured with log file: logs\2025-05-28\ApiRequestNode_16-17-37.log
2025-05-28 16:17:37 - ApiRequestNode - INFO - [__init__:64] Initializing API Component
2025-05-28 16:17:37 - ApiRequestNode - INFO - [__init__:67] API Component initialized successfully
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:206] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Starting API request processing for request_id: 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:224] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Extracting request new parameters https://interview.rapidinnovation.dev/api/v1/interviews/, POST, {'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo'}, {} for request_id 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:232] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{   "candidate_name": "ac",   "candidate_email": "<EMAIL>",   "skill_set": "AI",   "job_role": "dev",   "experience_level": 0,   "available_from": "1748467200",   "available_until": "1748481600",   "interview_duration": 30,   "resume_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf",   "jd_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf" }'}
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:234] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Extracted body value: {   "candidate_name": "ac",   "candidate_email": "<EMAIL>",   "skill_set": "AI",   "job_role": "dev",   "experience_level": 0,   "available_from": "1748467200",   "available_until": "1748481600",   "interview_duration": 30,   "resume_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf",   "jd_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf" } (type: <class 'str'>)
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:240] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Successfully parsed JSON string body: {'candidate_name': 'ac', 'candidate_email': '<EMAIL>', 'skill_set': 'AI', 'job_role': 'dev', 'experience_level': 0, 'available_from': '1748467200', 'available_until': '1748481600', 'interview_duration': 30, 'resume_link': 'https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf', 'jd_link': 'https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf'}
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:260] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Final request body values - raw: {'candidate_name': 'ac', 'candidate_email': '<EMAIL>', 'skill_set': 'AI', 'job_role': 'dev', 'experience_level': 0, 'available_from': '1748467200', 'available_until': '1748481600', 'interview_duration': 30, 'resume_link': 'https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf', 'jd_link': 'https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf'}, json: None for request_id 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:267] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Request parameters extracted for request_id 2980ce0c-d1e1-4f80-9fe5-4bd325752312: URL=https://interview.rapidinnovation.dev/api/v1/interviews/, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:304] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Serializing dict from 'body' to JSON string for data parameter (request_id=2980ce0c-d1e1-4f80-9fe5-4bd325752312)
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:345] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP REQUEST START] Method: POST, URL: https://interview.rapidinnovation.dev/api/v1/interviews/, Timeout: Nones, RequestID: 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:351] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP REQUEST HEADERS] {
  "Content-Type": "application/json",
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo"
}, RequestID: 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:383] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP REQUEST BODY] JSON string (preview): {"candidate_name": "ac", "candidate_email": "<EMAIL>", "skill_set": "AI", "job_role": "dev", "experience_level": 0, "available_from": "1748467200", "available_until": "1748481600", "interview..., RequestID: 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:391] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP Method]: POST,[HTTP URL] : https://interview.rapidinnovation.dev/api/v1/interviews/ RequestID: 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:17:44 - ApiRequestNode - INFO - [process:392] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP BODY] {"candidate_name": "ac", "candidate_email": "<EMAIL>", "skill_set": "AI", "job_role": "dev", "experience_level": 0, "available_from": "1748467200", "available_until": "1748481600", "interview_duration": 30, "resume_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf", "jd_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf"}
2025-05-28 16:18:08 - ApiRequestNode - INFO - [process:408] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP REQUEST COMPLETED] Duration: 23.555s, Status: 200, RequestID: 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:18:08 - ApiRequestNode - INFO - [process:413] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP RESPONSE] Status: 200, URL: https://interview.rapidinnovation.dev/api/v1/interviews/, Method: POST, RequestID: 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:18:08 - ApiRequestNode - INFO - [process:418] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP RESPONSE HEADERS] {
  "Date": "Wed, 28 May 2025 10:48:07 GMT",
  "Content-Type": "application/json",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Server": "cloudflare",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Cf-Cache-Status": "DYNAMIC",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=8ijgwuKLbzbIKZ1uJNEU0svQ6YZh%2B90YDj9fXGhRTw%2B9u%2F2e55citUXNBYM6Rjag6H6hx%2BbdAREjtA54zLs9LnqzZ8AJGa6CLwghgP0NEpacfOhi9hsRLOqk%2FU1Tw%2BfypfUwqiEc0s0L\"}]}",
  "Content-Encoding": "gzip",
  "CF-RAY": "946d2cf918bd9d86-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:18:08 - ApiRequestNode - INFO - [process:429] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP RESPONSE CONTENT] Type: application/json, Length: unknown, RequestID: 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:18:08 - ApiRequestNode - INFO - [process:574] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP RESPONSE BODY] JSON: {
  "status": "success",
  "message": "Interview scheduled successfully",
  "interview_id": "c2c5817a-223a-4425-8853-eea54620145d"
}, RequestID: 2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:18:08 - ApiRequestNode - INFO - [process:597] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] API request successful: Status=200, RequestID=2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:206] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Starting API request processing for request_id: 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:224] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Extracting request new parameters https://interview.rapidinnovation.dev/api/v1/interviews/c2c5817a-223a-4425-8853-eea54620145d, PUT, {'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo'}, {} for request_id 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:232] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{   "candidate_name": "ac",   "candidate_email": "<EMAIL>",   "skill_set": "AI",   "job_role": "dev",   "experience_level": 0,   "available_from": "1748467200",   "available_until": "1748481600",   "interview_duration": 30,   "resume_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf",   "jd_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf" }'}
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:234] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Extracted body value: {   "candidate_name": "ac",   "candidate_email": "<EMAIL>",   "skill_set": "AI",   "job_role": "dev",   "experience_level": 0,   "available_from": "1748467200",   "available_until": "1748481600",   "interview_duration": 30,   "resume_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf",   "jd_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf" } (type: <class 'str'>)
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:240] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Successfully parsed JSON string body: {'candidate_name': 'ac', 'candidate_email': '<EMAIL>', 'skill_set': 'AI', 'job_role': 'dev', 'experience_level': 0, 'available_from': '1748467200', 'available_until': '1748481600', 'interview_duration': 30, 'resume_link': 'https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf', 'jd_link': 'https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf'}
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:260] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Final request body values - raw: {'candidate_name': 'ac', 'candidate_email': '<EMAIL>', 'skill_set': 'AI', 'job_role': 'dev', 'experience_level': 0, 'available_from': '1748467200', 'available_until': '1748481600', 'interview_duration': 30, 'resume_link': 'https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf', 'jd_link': 'https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf'}, json: None for request_id 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:267] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Request parameters extracted for request_id 0d794a48-6a1e-48e2-bc85-790a69b5d2d5: URL=https://interview.rapidinnovation.dev/api/v1/interviews/c2c5817a-223a-4425-8853-eea54620145d, Method=PUT, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:304] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Serializing dict from 'body' to JSON string for data parameter (request_id=0d794a48-6a1e-48e2-bc85-790a69b5d2d5)
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:345] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP REQUEST START] Method: PUT, URL: https://interview.rapidinnovation.dev/api/v1/interviews/c2c5817a-223a-4425-8853-eea54620145d, Timeout: Nones, RequestID: 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:351] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP REQUEST HEADERS] {
  "Content-Type": "application/json",
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo"
}, RequestID: 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:383] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP REQUEST BODY] JSON string (preview): {"candidate_name": "ac", "candidate_email": "<EMAIL>", "skill_set": "AI", "job_role": "dev", "experience_level": 0, "available_from": "1748467200", "available_until": "1748481600", "interview..., RequestID: 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:391] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP Method]: PUT,[HTTP URL] : https://interview.rapidinnovation.dev/api/v1/interviews/c2c5817a-223a-4425-8853-eea54620145d RequestID: 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:392] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP BODY] {"candidate_name": "ac", "candidate_email": "<EMAIL>", "skill_set": "AI", "job_role": "dev", "experience_level": 0, "available_from": "1748467200", "available_until": "1748481600", "interview_duration": 30, "resume_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf", "jd_link": "https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf"}
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:408] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP REQUEST COMPLETED] Duration: 0.562s, Status: 200, RequestID: 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:413] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP RESPONSE] Status: 200, URL: https://interview.rapidinnovation.dev/api/v1/interviews/c2c5817a-223a-4425-8853-eea54620145d, Method: PUT, RequestID: 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:418] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP RESPONSE HEADERS] {
  "Date": "Wed, 28 May 2025 10:50:24 GMT",
  "Content-Type": "application/json",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Server": "cloudflare",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Cf-Cache-Status": "DYNAMIC",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=t3QLGjmSAZQLoDqeLpgSAS8So4cN99PmlVDdWJyFcomm26BhpaU2VVPVScfNmLnmooLP2Et4gG6iSbC4PZZYxGigFBWnb9enxTc%2FU8wRMzjT7ii5nj8so0dvhSypojuX%2FZHv%2BNrOzHgZ\"}]}",
  "Content-Encoding": "gzip",
  "CF-RAY": "946d30dc1da5a65d-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:429] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP RESPONSE CONTENT] Type: application/json, Length: unknown, RequestID: 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:574] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] [HTTP RESPONSE BODY] JSON: {
  "status": "upcoming",
  "message": "Interview updated successfully",
  "interview_id": "c2c5817a-223a-4425-8853-eea54620145d",
  "candidate_name": "ac",
  "candidate_email": "<EMAIL>",
  "skill_set": "AI",
  "job_role": "dev",
  "experience_level": "0",
  "available_from": "2025-05-28T21:20:00+00:00",
  "available_until": "2025-05-29T01:20:00+00:00",
  "interview_duration": 30,
  "candidate_suitability": null,
  "is_completed": false,
  "scheduled_by_type": "organization",
  "created_at": "2025-05-28T10:48:07.724089+00:00",
  "updated_at": "2025-05-28T10:50:24.174019+00:00",
  "candidate_profile": [],
  "agenda": null,
  "questions": null
}, RequestID: 0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ApiRequestNode - INFO - [process:597] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] API request successful: Status=200, RequestID=0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:24:05 - ApiRequestNode - INFO - [process:206] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Starting API request processing for request_id: 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:05 - ApiRequestNode - INFO - [process:224] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Extracting request new parameters https://interview.rapidinnovation.dev/api/v1/interviews/, POST, {'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo'}, {} for request_id 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:05 - ApiRequestNode - INFO - [process:260] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Final request body values - raw: None, json: None for request_id 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:05 - ApiRequestNode - INFO - [process:267] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Request parameters extracted for request_id 42942d4a-17e8-485b-be1c-b34b66261193: URL=https://interview.rapidinnovation.dev/api/v1/interviews/, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-28 16:24:05 - ApiRequestNode - INFO - [process:345] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] [HTTP REQUEST START] Method: POST, URL: https://interview.rapidinnovation.dev/api/v1/interviews/, Timeout: Nones, RequestID: 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:05 - ApiRequestNode - INFO - [process:351] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] [HTTP REQUEST HEADERS] {
  "Content-Type": "application/json",
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo"
}, RequestID: 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:05 - ApiRequestNode - INFO - [process:391] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] [HTTP Method]: POST,[HTTP URL] : https://interview.rapidinnovation.dev/api/v1/interviews/ RequestID: 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:05 - ApiRequestNode - INFO - [process:392] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] [HTTP BODY] None
2025-05-28 16:24:06 - ApiRequestNode - INFO - [process:408] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] [HTTP REQUEST COMPLETED] Duration: 0.630s, Status: 422, RequestID: 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:06 - ApiRequestNode - INFO - [process:413] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] [HTTP RESPONSE] Status: 422, URL: https://interview.rapidinnovation.dev/api/v1/interviews/, Method: POST, RequestID: 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:06 - ApiRequestNode - INFO - [process:418] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] [HTTP RESPONSE HEADERS] {
  "Date": "Wed, 28 May 2025 10:54:06 GMT",
  "Content-Type": "application/json",
  "Content-Length": "82",
  "Connection": "keep-alive",
  "Server": "cloudflare",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Cf-Cache-Status": "DYNAMIC",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=%2BsrHdecIwVOkemDM%2FU3cNuzE13cmuZQ0yGtHQVpUq4RvUlwss9PMqUj40xTkuyyw766z3YclT8RiiDoayqKJAYWdP0R2ex55XFv%2BDrZ3UQffjZ%2B5t2elyDMKGVETKIdrAkMc05pwseDG\"}]}",
  "CF-RAY": "946d36468ba4e23f-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:06 - ApiRequestNode - INFO - [process:429] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] [HTTP RESPONSE CONTENT] Type: application/json, Length: 82, RequestID: 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:06 - ApiRequestNode - INFO - [process:574] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] [HTTP RESPONSE BODY] JSON: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "body"
      ],
      "msg": "Field required",
      "input": null
    }
  ]
}, RequestID: 42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:06 - ApiRequestNode - WARNING - [process:656] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] API request failed (Client Error): Status=422 (Unprocessable Entity), RequestID=42942d4a-17e8-485b-be1c-b34b66261193, Error: API request failed with status 422 (Unprocessable Entity)
2025-05-28 16:26:30 - ApiRequestNode - INFO - [process:206] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Starting API request processing for request_id: 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:30 - ApiRequestNode - INFO - [process:224] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Extracting request new parameters https://interview.rapidinnovation.dev/api/v1/interviews/, POST, {'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo'}, {} for request_id 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:30 - ApiRequestNode - INFO - [process:260] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Final request body values - raw: None, json: None for request_id 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:30 - ApiRequestNode - INFO - [process:267] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Request parameters extracted for request_id 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3: URL=https://interview.rapidinnovation.dev/api/v1/interviews/, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-28 16:26:30 - ApiRequestNode - INFO - [process:345] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] [HTTP REQUEST START] Method: POST, URL: https://interview.rapidinnovation.dev/api/v1/interviews/, Timeout: Nones, RequestID: 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:30 - ApiRequestNode - INFO - [process:351] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] [HTTP REQUEST HEADERS] {
  "Content-Type": "application/json",
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo"
}, RequestID: 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:30 - ApiRequestNode - INFO - [process:391] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] [HTTP Method]: POST,[HTTP URL] : https://interview.rapidinnovation.dev/api/v1/interviews/ RequestID: 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:30 - ApiRequestNode - INFO - [process:392] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] [HTTP BODY] None
2025-05-28 16:26:31 - ApiRequestNode - INFO - [process:408] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] [HTTP REQUEST COMPLETED] Duration: 0.536s, Status: 422, RequestID: 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:31 - ApiRequestNode - INFO - [process:413] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] [HTTP RESPONSE] Status: 422, URL: https://interview.rapidinnovation.dev/api/v1/interviews/, Method: POST, RequestID: 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:31 - ApiRequestNode - INFO - [process:418] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] [HTTP RESPONSE HEADERS] {
  "Date": "Wed, 28 May 2025 10:56:31 GMT",
  "Content-Type": "application/json",
  "Content-Length": "82",
  "Connection": "keep-alive",
  "Server": "cloudflare",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Cf-Cache-Status": "DYNAMIC",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=0CPcXuhlmfhEPfB2PBwVo8vL0y66qHkd5XrbaYl5yj3RMlOg9YlPeY2SwkvR9O0JwZ57OrGGXRFA9BveIsSHgT9qLpBtolg2u4kdgMlmaqwzRwh3Mzcrod6mwVOURkOtuS5cFZH%2BCqQD\"}]}",
  "CF-RAY": "946d39d09945da31-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:31 - ApiRequestNode - INFO - [process:429] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] [HTTP RESPONSE CONTENT] Type: application/json, Length: 82, RequestID: 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:31 - ApiRequestNode - INFO - [process:574] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] [HTTP RESPONSE BODY] JSON: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "body"
      ],
      "msg": "Field required",
      "input": null
    }
  ]
}, RequestID: 8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:31 - ApiRequestNode - WARNING - [process:656] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] API request failed (Client Error): Status=422 (Unprocessable Entity), RequestID=8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3, Error: API request failed with status 422 (Unprocessable Entity)
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:206] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Starting API request processing for request_id: ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:224] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Extracting request new parameters https://www.postb.in/1748429970692-5225765898358, POST, {}, {} for request_id ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:232] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"body":"testing"}'}
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:234] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Extracted body value: {"body":"testing"} (type: <class 'str'>)
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:240] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Successfully parsed JSON string body: {'body': 'testing'}
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:260] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Final request body values - raw: {'body': 'testing'}, json: None for request_id ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:267] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Request parameters extracted for request_id ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0: URL=https://www.postb.in/1748429970692-5225765898358, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:345] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748429970692-5225765898358, Timeout: Nones, RequestID: ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:361] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] [HTTP REQUEST BODY] JSON: {
  "body": "testing"
}, RequestID: ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:391] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748429970692-5225765898358 RequestID: ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:45 - ApiRequestNode - INFO - [process:392] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] [HTTP BODY] None
2025-05-28 16:33:46 - ApiRequestNode - INFO - [process:408] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] [HTTP REQUEST COMPLETED] Duration: 0.729s, Status: 200, RequestID: ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:46 - ApiRequestNode - INFO - [process:413] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748429970692-5225765898358, Method: POST, RequestID: ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:46 - ApiRequestNode - INFO - [process:418] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] [HTTP RESPONSE HEADERS] {
  "Date": "Wed, 28 May 2025 11:03:45 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-vt3F1VR/5QKULG4DA2bKbXboGfg\"",
  "X-Response-Time": "0.64749ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=zbRKssYVuDzdx1DoK7kbNCW9IVCNcldxZdT1%2FcZ8E39%2Fxyr6fuxjBrJJzudSMXWcA8VWRgzxJDHUXD%2BE%2B1AWNlOjsQU23Ajs6WWmUxB418f46Ry81aaYAw%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "946d446d3b32e1ae-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:46 - ApiRequestNode - INFO - [process:429] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:46 - ApiRequestNode - INFO - [process:587] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] [HTTP RESPONSE BODY] Text: 1748430225745-2642260401044, RequestID: ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:46 - ApiRequestNode - INFO - [process:597] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] API request successful: Status=200, RequestID=ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
