2025-05-28 16:44:55 - ApiRequestNode - INFO - [setup_logger:467] Logger ApiRequestNode configured with log file: logs\2025-05-28\ApiRequestNode_16-44-55.log
2025-05-28 16:44:55 - ApiRequestNode - INFO - [__init__:64] Initializing API Component
2025-05-28 16:44:55 - ApiRequestNode - INFO - [__init__:67] API Component initialized successfully
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:206] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Starting API request processing for request_id: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:224] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Extracting request new parameters https://www.postb.in/1748429970692-5225765898358, POST, {}, {} for request_id 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:232] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"body":"testing"}'}
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:234] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Extracted body value: {"body":"testing"} (type: <class 'str'>)
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:240] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Successfully parsed JSON string body: {'body': 'testing'}
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:260] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Final request body values - raw: {'body': 'testing'}, json: None for request_id 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:267] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] Request parameters extracted for request_id 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b: URL=https://www.postb.in/1748429970692-5225765898358, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:345] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748429970692-5225765898358, Timeout: Nones, RequestID: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:361] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] [HTTP REQUEST BODY] JSON: {
  "body": "testing"
}, RequestID: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:391] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748429970692-5225765898358 RequestID: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:49 - ApiRequestNode - INFO - [process:392] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] [HTTP BODY] None
2025-05-28 16:45:50 - ApiRequestNode - INFO - [process:408] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] [HTTP REQUEST COMPLETED] Duration: 0.811s, Status: 200, RequestID: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:50 - ApiRequestNode - INFO - [process:413] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748429970692-5225765898358, Method: POST, RequestID: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:50 - ApiRequestNode - INFO - [process:418] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] [HTTP RESPONSE HEADERS] {
  "Date": "Wed, 28 May 2025 11:15:49 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-qoErpg1IcfOTTlnhNFvoCHoIOwM\"",
  "X-Response-Time": "0.72954ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=LxDDrOXIfRUKKq7UwVWEesN0VDjAa4wz%2FUPmf%2BeiVUot4MNvoNoSKa75CzlnNWe9hKR9UuEhHDhNi47EgvO913jzaU9ev7m6R10l7ghImdBYukfKjFNGCQ%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "946d5619ad5ae22f-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:50 - ApiRequestNode - INFO - [process:429] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:50 - ApiRequestNode - INFO - [process:587] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] [HTTP RESPONSE BODY] Text: 1748430949659-2249077649321, RequestID: 9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
2025-05-28 16:45:50 - ApiRequestNode - INFO - [process:597] [ReqID:9ecb6624-6ae0-43b4-9ed0-c727dd127b0b] [CorrID:e22a7320-ed4c-4e62-990a-d3a2e5132645] API request successful: Status=200, RequestID=9ecb6624-6ae0-43b4-9ed0-c727dd127b0b
