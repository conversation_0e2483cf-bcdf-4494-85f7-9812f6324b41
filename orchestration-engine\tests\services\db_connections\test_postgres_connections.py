import pytest
from unittest.mock import Mock, patch, MagicMock
from app.services.db_connections.postgres_connections import (
    PostgresManager,
    get_postgres_manager,
)


class TestPostgresManager:
    """
    Test suite for PostgresManager class.
    Tests PostgreSQL connection management, CRUD operations, and error handling.
    """

    @pytest.fixture
    def mock_settings(self):
        """
        Provides mock settings for PostgreSQL configuration.
        """
        with patch(
            "app.services.db_connections.postgres_connections.settings"
        ) as mock_settings:
            mock_settings.db_host = "localhost"
            mock_settings.db_port = "5432"
            mock_settings.db_user = "postgres"
            mock_settings.db_password = Mock(get_secret_value=lambda: "test_password")
            mock_settings.db_name = "test_db"
            yield mock_settings

    @pytest.fixture
    def mock_pool(self):
        """
        Provides a mock connection pool.
        """
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool.getconn.return_value = mock_conn
        return mock_pool, mock_conn, mock_cursor

    @pytest.fixture
    def postgres_manager(self, mock_settings, mock_pool):
        """
        Provides a PostgresManager instance with mocked settings and connection pool.
        """
        mock_pool_instance, mock_conn, mock_cursor = mock_pool

        with patch(
            "app.services.db_connections.postgres_connections._connection_pool",
            mock_pool_instance,
        ), patch(
            "app.services.db_connections.postgres_connections.get_connection_pool",
            return_value=mock_pool_instance,
        ):

            # Reset the singleton instance
            with patch(
                "app.services.db_connections.postgres_connections._postgres_manager_instance",
                None,
            ):
                manager = get_postgres_manager()
                manager.pool = mock_pool_instance
                yield manager, mock_conn, mock_cursor

    def test_init_with_valid_settings(self, mock_settings):
        """
        Test initialization with valid settings.
        """
        with patch("psycopg2.pool.ThreadedConnectionPool") as mock_pool_class, patch(
            "app.services.db_connections.postgres_connections._connection_pool", None
        ), patch(
            "app.services.db_connections.postgres_connections._postgres_manager_instance",
            None,
        ):

            mock_pool = MagicMock()
            mock_pool_class.return_value = mock_pool

            manager = get_postgres_manager()

            # Verify pool creation attempt was made with correct parameters
            mock_pool_class.assert_called_once_with(
                minconn=1,
                maxconn=10,
                host="localhost",
                port=5432,
                user="postgres",
                password="test_password",
                dbname="test_db",
            )

            assert manager.db_host == "localhost"
            assert manager.db_port == "5432"
            assert manager.db_user == "postgres"
            assert manager.db_password == "test_password"
            assert manager.db_name == "test_db"

    def test_init_with_missing_settings(self, mock_settings):
        """
        Test initialization with missing settings.
        """
        mock_settings.db_host = None

        with pytest.raises(ValueError) as excinfo:
            PostgresManager()

        assert "PostgreSQL connection details are missing" in str(excinfo.value)

    def test_is_connected_true(self, postgres_manager):
        """
        Test is_connected method when connection is active.
        """
        # Unpack the fixture
        manager, mock_conn, mock_cursor = postgres_manager

        # Setup cursor to return a value for SELECT 1
        mock_cursor.execute.return_value = None

        assert manager.is_connected() is True
        mock_cursor.execute.assert_called_once_with("SELECT 1")

    def test_is_connected_false_no_connection(self, postgres_manager):
        """
        Test is_connected method when no connection exists.
        """
        # Unpack the fixture
        manager, _, _ = postgres_manager

        # Set pool to None
        manager.pool = None
        assert manager.is_connected() is False

    def test_is_connected_false_error(self, postgres_manager):
        """
        Test is_connected method when connection check raises an error.
        """
        # Unpack the fixture
        manager, _, mock_cursor = postgres_manager

        # Setup cursor to raise an exception
        mock_cursor.execute.side_effect = Exception("Connection error")

        assert manager.is_connected() is False

    def test_ensure_tables_exist(self, postgres_manager):
        """
        Test _ensure_tables_exist method.
        """
        # Unpack the fixture
        manager, mock_conn, mock_cursor = postgres_manager

        result = manager._ensure_tables_exist()

        assert result is True
        assert (
            mock_cursor.execute.call_count == 4
        )  # Called for each table and index creation
        mock_conn.commit.assert_called_once()

    def test_ensure_tables_exist_not_connected(self, postgres_manager):
        """
        Test _ensure_tables_exist method when not connected.
        """
        # Unpack the fixture
        manager, _, _ = postgres_manager

        with patch.object(manager, "is_connected", return_value=False):
            result = manager._ensure_tables_exist()
            assert result is False

    def test_ensure_tables_exist_error(self, postgres_manager):
        """
        Test _ensure_tables_exist method when an error occurs.
        """
        # Unpack the fixture
        manager, mock_conn, mock_cursor = postgres_manager

        # Setup cursor to raise an exception
        mock_cursor.execute.side_effect = Exception("Database error")

        result = manager._ensure_tables_exist()

        assert result is False
        mock_conn.rollback.assert_called_once()

    def test_store_workflow_state_new(self, postgres_manager):
        """
        Test store_workflow_state method for a new record.
        """
        # Unpack the fixture
        manager, mock_conn, mock_cursor = postgres_manager

        # Setup cursor to return no existing record
        mock_cursor.fetchone.return_value = None

        # Test data
        correlation_id = "test-correlation-id"
        workflow_id = "test-workflow-id"
        state_data = {"status": "running", "step": 1}

        with patch.object(manager, "_ensure_tables_exist", return_value=True):
            result = manager.store_workflow_state(
                correlation_id, workflow_id, state_data
            )

            assert result is True
            # Verify the INSERT query was executed
            mock_cursor.execute.assert_any_call(
                """
                        INSERT INTO workflow_state (correlation_id, workflow_id, state_data)
                        VALUES (%s, %s, %s)
                        """,
                (correlation_id, workflow_id, state_data),
            )
            mock_conn.commit.assert_called_once()

    def test_store_workflow_state_existing(self, postgres_manager):
        """
        Test store_workflow_state method for an existing record.
        """
        # Unpack the fixture
        manager, mock_conn, mock_cursor = postgres_manager

        # Setup cursor to return an existing record
        mock_cursor.fetchone.return_value = (1,)  # Return an ID

        # Test data
        correlation_id = "test-correlation-id"
        workflow_id = "test-workflow-id"
        state_data = {"status": "running", "step": 1}

        with patch.object(manager, "_ensure_tables_exist", return_value=True):
            result = manager.store_workflow_state(
                correlation_id, workflow_id, state_data
            )

            assert result is True
            # Verify the UPDATE query was executed
            mock_cursor.execute.assert_any_call(
                """
                        UPDATE workflow_state
                        SET workflow_id = %s, state_data = %s, updated_at = NOW()
                        WHERE correlation_id = %s
                        """,
                (workflow_id, state_data, correlation_id),
            )
            mock_conn.commit.assert_called_once()

    def test_get_workflow_state(self, postgres_manager):
        """
        Test get_workflow_state method.
        """
        # Unpack the fixture
        manager, _, mock_cursor = postgres_manager

        # Setup cursor to return a record
        mock_record = {
            "correlation_id": "test-correlation-id",
            "workflow_id": "test-workflow-id",
            "state_data": {"status": "running", "step": 1},
            "created_at": "2023-01-01T00:00:00Z",
            "updated_at": "2023-01-01T01:00:00Z",
        }
        mock_cursor.fetchone.return_value = mock_record

        # Test
        correlation_id = "test-correlation-id"
        result = manager.get_workflow_state(correlation_id)

        assert result == mock_record
        # Verify the SELECT query was executed with the correct parameters
        mock_cursor.execute.assert_called_with(
            """
                    SELECT correlation_id, workflow_id, state_data, created_at, updated_at
                    FROM workflow_state
                    WHERE correlation_id = %s
                    """,
            (correlation_id,),
        )

    def test_get_workflow_state_not_found(self, postgres_manager):
        """
        Test get_workflow_state method when no record is found.
        """
        # Unpack the fixture
        manager, _, mock_cursor = postgres_manager

        # Setup cursor to return no record
        mock_cursor.fetchone.return_value = None

        # Test
        correlation_id = "test-correlation-id"
        result = manager.get_workflow_state(correlation_id)

        assert result is None

    def test_store_transition_result(self, postgres_manager):
        """
        Test store_transition_result method.
        """
        # Unpack the fixture
        manager, mock_conn, mock_cursor = postgres_manager

        # Setup cursor
        mock_cursor.fetchone.return_value = None  # No existing record

        # Test data
        correlation_id = "test-correlation-id"
        transition_id = "test-transition-id"
        result_data = {"output": "test output", "status": "success"}

        with patch.object(manager, "_ensure_tables_exist", return_value=True):
            result = manager.store_transition_result(
                correlation_id, transition_id, result_data
            )

            assert result is True
            # Verify the INSERT query was executed
            mock_cursor.execute.assert_any_call(
                """
                        INSERT INTO transition_results (correlation_id, transition_id, result_data)
                        VALUES (%s, %s, %s)
                        """,
                (correlation_id, transition_id, result_data),
            )
            mock_conn.commit.assert_called_once()

    def test_get_transition_result(self, postgres_manager):
        """
        Test get_transition_result method.
        """
        # Unpack the fixture
        manager, _, mock_cursor = postgres_manager

        # Setup cursor to return a record
        mock_record = {"result_data": {"output": "test output", "status": "success"}}
        mock_cursor.fetchone.return_value = mock_record

        # Test
        correlation_id = "test-correlation-id"
        transition_id = "test-transition-id"
        result = manager.get_transition_result(correlation_id, transition_id)

        assert result == mock_record["result_data"]
        # Verify the SELECT query was executed with the correct parameters
        mock_cursor.execute.assert_called_with(
            """
                    SELECT result_data
                    FROM transition_results
                    WHERE correlation_id = %s AND transition_id = %s
                    """,
            (correlation_id, transition_id),
        )

    def test_close_connection(self, postgres_manager):
        """
        Test close_connection method.
        """
        # Unpack the fixture
        manager, _, _ = postgres_manager

        # Mock the global connection pool
        with patch(
            "app.services.db_connections.postgres_connections._connection_pool",
            manager.pool,
        ):
            manager.close_connection()
            manager.pool.closeall.assert_called_once()
            assert manager.pool is None
