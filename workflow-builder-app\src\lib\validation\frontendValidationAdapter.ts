import { Node, <PERSON> } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { validateWorkflowBeforeExecution } from "./smartValidation";
import { convertToBackendFormat } from "./smartValidation";
import { ValidationResponse } from "@/lib/workflow-api";

/**
 * Frontend implementation of the backend validation API
 * This function replaces the backend /validate_workflow endpoint
 * 
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @returns A ValidationResponse object in the same format as the backend API
 */
export async function validateWorkflowFrontend(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): Promise<ValidationResponse> {
  try {
    // Use our frontend validation implementation
    const validationResult = await validateWorkflowBeforeExecution(nodes, edges);
    
    // Convert to backend format
    const backendFormat = convertToBackendFormat(validationResult);
    
    return {
      is_valid: backendFormat.is_valid,
      missing_fields: backendFormat.missing_fields,
      error: backendFormat.error
    };
  } catch (error) {
    console.error("Error in frontend validation:", error);
    return {
      is_valid: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
