// This needs to be a server component for Next.js to properly type the params
// We'll create a client component for the actual content

import React from "react";
import { Metadata } from "next";
import WorkflowDetailsClient from "./WorkflowDetailsClient";

// Define the page props type according to Next.js 15 requirements
type WorkflowDetailsPageProps = {
  params: Promise<{
    id: string;
  }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

// Generate metadata for the page
export async function generateMetadata({ params }: WorkflowDetailsPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  return {
    title: `Workflow: ${resolvedParams.id}`,
    description: "View workflow details and metadata",
  };
}

// Simple server component that renders the client component
export default async function WorkflowDetailsPage({ params }: WorkflowDetailsPageProps) {
  // Access the ID directly from params - using await since params is a Promise in Next.js 15
  const resolvedParams = await params;
  const { id } = resolvedParams;

  return (
    <WorkflowDetailsClient id={id} />
  );
}
