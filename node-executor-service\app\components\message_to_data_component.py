"""
Message to Data Component - Extracts fields from Message objects.

This component takes a Message object and extracts specified fields into a dictionary.
"""
import logging
import traceback
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class MessageToDataRequest(BaseModel):
    """
    Schema for message to data extraction request.
    """
    input_message: Any = Field(..., description="The Message object to extract fields from")
    fields_to_extract: Optional[List[str]] = Field(
        default=[],
        description="List of field names to extract from the message. Leave empty to extract all fields."
    )


@register_component("MessageToDataComponent")
class MessageToDataExecutor(BaseComponent):
    """
    Component for extracting fields from Message objects.

    This component takes a Message object and extracts specified fields
    into a dictionary.
    """

    def __init__(self):
        """
        Initialize the MessageToDataExecutor.
        """
        logger.info("Initializing Message to Data Component")
        super().__init__()
        # Set the request schema for automatic validation
        self.request_schema = MessageToDataRequest
        logger.info("Message to Data Component initialized successfully")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a message to data extraction payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult indicating whether the payload is valid
        """
        request_id = payload.get("request_id", "unknown")
        logger.debug(f"Validating message to data payload for request_id: {request_id}")

        try:
            # Extract tool_parameters if present (from orchestration engine)
            tool_parameters = payload.get("tool_parameters", {})
            if tool_parameters:
                # Validate the tool_parameters using the schema
                self.request_schema(**tool_parameters)
            else:
                # Validate the payload directly
                self.request_schema(**payload)

            logger.debug(f"Validation successful for request_id: {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Validation failed for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Validation error details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(
                is_valid=False,
                error_message=error_msg,
                error_details={"validation_error": str(e)}
            )

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the message to data extraction operation.

        Args:
            payload: The request payload containing:
                - input_message: The Message object to extract from
                - fields_to_extract: List of field names to extract (optional)

        Returns:
            A dictionary containing the extracted data or an error message
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing message to data extraction request for request_id: {request_id}")
        # Note: Skipping full payload logging to avoid issues with complex objects

        try:
            # Extract parameters from tool_parameters if present (from orchestration engine)
            tool_parameters = payload.get("tool_parameters", {})
            if tool_parameters:
                input_message = tool_parameters.get("input_message")
                fields_to_extract = tool_parameters.get("fields_to_extract", [])
            else:
                # Extract parameters directly from payload
                input_message = payload.get("input_message")
                fields_to_extract = payload.get("fields_to_extract", [])

            # Validate input message
            if input_message is None:
                error_msg = "Input message is missing. Please provide a Message object."
                logger.error(f"Validation error for request_id {request_id}: {error_msg}")
                return {
                    "status": "error",
                    "error": error_msg,
                    "request_id": request_id
                }

            # Extract fields from the message
            result = {}

            # If the input is a dictionary, we can extract fields directly
            if isinstance(input_message, dict):
                # If no fields specified, extract all
                if not fields_to_extract:
                    result = input_message.copy()
                else:
                    # Extract only specified fields
                    for field in fields_to_extract:
                        if field in input_message:
                            result[field] = input_message[field]

            # If the input is an object with attributes, extract them
            else:
                # If no fields specified, try to get all attributes
                if not fields_to_extract:
                    # Try to convert object to dictionary using __dict__
                    try:
                        result = input_message.__dict__.copy()
                    except (AttributeError, TypeError):
                        error_msg = "Cannot extract all fields from the input message. Please specify fields to extract."
                        logger.error(f"Processing error for request_id {request_id}: {error_msg}")
                        return {
                            "status": "error",
                            "error": error_msg,
                            "request_id": request_id
                        }
                else:
                    # Extract only specified fields
                    for field in fields_to_extract:
                        try:
                            result[field] = getattr(input_message, field)
                        except (AttributeError, TypeError):
                            # Skip fields that don't exist
                            pass

            logger.info(f"Fields extracted successfully for request_id {request_id}. Keys: {list(result.keys())}")
            return {
                "status": "success",
                "output_data": result,
                "request_id": request_id
            }

        except Exception as e:
            error_msg = f"Unexpected error during message to data extraction for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg,
                "request_id": request_id
            }
