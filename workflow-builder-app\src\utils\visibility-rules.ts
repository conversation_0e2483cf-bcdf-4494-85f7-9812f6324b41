import { InputDefinition, InputVisibilityRule } from "@/types";

/**
 * Evaluates a single visibility rule against the current configuration
 *
 * @param rule The visibility rule to evaluate
 * @param config The current component configuration
 * @returns True if the rule passes, false otherwise
 */
export function evaluateVisibilityRule(
  rule: InputVisibilityRule,
  config: Record<string, any>,
): boolean {
  // Get the target field value from config
  const targetValue = config?.[rule.field_name];

  // Simple equality check
  if (rule.operator === undefined || rule.operator === "equals") {
    return targetValue === rule.field_value;
  }

  // Not equals
  if (rule.operator === "not_equals") {
    return targetValue !== rule.field_value;
  }

  // Contains (for arrays and strings)
  if (rule.operator === "contains") {
    if (Array.isArray(targetValue)) {
      return targetValue.includes(rule.field_value);
    }
    if (typeof targetValue === "string") {
      return targetValue.includes(String(rule.field_value));
    }
    return false;
  }

  // Greater than (for numbers)
  if (rule.operator === "greater_than") {
    return (
      typeof targetValue === "number" &&
      typeof rule.field_value === "number" &&
      targetValue > rule.field_value
    );
  }

  // Less than (for numbers)
  if (rule.operator === "less_than") {
    return (
      typeof targetValue === "number" &&
      typeof rule.field_value === "number" &&
      targetValue < rule.field_value
    );
  }

  // Exists (field exists and is not null/undefined)
  if (rule.operator === "exists") {
    return targetValue !== undefined && targetValue !== null;
  }

  // Not exists (field doesn't exist or is null/undefined)
  if (rule.operator === "not_exists") {
    return targetValue === undefined || targetValue === null;
  }

  // Default to false for unknown operators
  return false;
}

/**
 * Evaluates a set of visibility rules against the current configuration
 *
 * @param rules The visibility rules to evaluate
 * @param config The current component configuration
 * @param logicOperator The logic operator to use when combining rules (default: 'OR')
 * @returns True if the rules pass, false otherwise
 */
export function evaluateVisibilityRules(
  rules: InputVisibilityRule[],
  config: Record<string, any>,
  logicOperator: "AND" | "OR" = "OR",
): boolean {
  // If no rules, always show
  if (!rules || rules.length === 0) {
    return true;
  }

  // Evaluate each rule
  const results = rules.map((rule) => evaluateVisibilityRule(rule, config));

  // Combine results based on logic operator
  if (logicOperator === "AND") {
    return results.every((result) => result);
  } else {
    return results.some((result) => result);
  }
}

/**
 * Determines if an input should be visible based on its visibility rules
 *
 * @param input The input definition
 * @param config The current component configuration
 * @returns True if the input should be visible, false otherwise
 */
export function shouldShowInput(input: InputDefinition, config: Record<string, any>): boolean {
  // If no visibility rules, always show
  if (!input.visibility_rules || input.visibility_rules.length === 0) {
    return true;
  }

  // Get the logic operator from the input definition or default to 'OR'
  const logicOperator = input.visibility_logic || "OR";

  // Evaluate the visibility rules
  return evaluateVisibilityRules(input.visibility_rules, config, logicOperator as "AND" | "OR");
}

/**
 * Filters a list of inputs based on visibility rules
 *
 * @param inputs The list of input definitions
 * @param config The current component configuration
 * @returns A filtered list of inputs that should be visible
 */
export function filterVisibleInputs(
  inputs: InputDefinition[],
  config: Record<string, any>,
): InputDefinition[] {
  return inputs.filter((input) => shouldShowInput(input, config));
}
