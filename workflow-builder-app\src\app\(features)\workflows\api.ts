/**
 * Workflow API Module
 *
 * This module provides functions for interacting with the workflow API endpoints.
 */

import axios from "axios";
import { getAccessToken } from "@/lib/cookies";
import { getClientAccessToken } from "@/lib/clientCookies";
import { API_BASE_URL, API_ENDPOINTS } from "@/lib/apiConfig";

// Types for workflow API responses
export interface WorkflowMetadata {
  total: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface WorkflowDetails {
  id: string;
  name: string;
  description: string;
  workflow_url?: string;
  builder_url?: string;
  start_nodes?: string[];
  owner_id?: string;
  user_id?: string; // Some APIs use user_id instead of owner_id
  user_ids?: string[];
  owner_type?: string;
  workflow_template_id?: string;
  parent_workflow_id?: string;
  url?: string;
  is_imported?: boolean;
  execution_count?: number;
  version?: string;
  visibility?: string;
  category?: string;
  tags?: Record<string, any> | string[];
  status?: string;
  created_at: string;
  updated_at: string;
}

export interface WorkflowSummary {
  success?: boolean;
  message?: string;
  workflow: WorkflowDetails;
}

export interface WorkflowListItem {
  id: string;
  name: string;
  description: string;
  workflow_url?: string;
  builder_url?: string;
  start_nodes?: string[];
  owner_id?: string;
  user_ids?: string[];
  owner_type?: string;
  workflow_template_id?: string;
  parent_workflow_id?: string;
  url?: string;
  is_imported?: boolean;
  execution_count?: number;
  version?: string;
  visibility?: string;
  category?: string;
  tags?: Record<string, any>;
  status?: string;
  created_at: string;
  updated_at: string;
}

export interface WorkflowListResponse {
  data: WorkflowListItem[];
  metadata: WorkflowMetadata;
}

export interface CreateWorkflowRequest {
  name: string;
  description: string;
  workflow_data: {
    nodes: Record<string, any>[];
    edges: Record<string, any>[];
  };
  start_node_data?: string[];
}

export interface CreateWorkflowResponse {
  success: boolean;
  message: string;
  workflow_id: WorkflowDetails;
}

// Create axios instance with interceptors
const workflowAxios = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: false,
});

// Add request interceptor to include token
workflowAxios.interceptors.request.use(
  async (config) => {
    // Get token using the appropriate method based on environment
    let token;
    if (typeof window !== "undefined") {
      // Client-side
      token = getClientAccessToken();
    } else {
      // Server-side
      token = await getAccessToken();
    }

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error),
);

/**
 * Fetches workflows for the current user
 *
 * This function uses the /user/me endpoint which automatically identifies the user
 * from their authentication token, eliminating the need to pass a user ID.
 */
export async function fetchWorkflowsByUser(
  page: number = 1,
  pageSize: number = 10,
  accessToken?: string,
): Promise<WorkflowListResponse> {
  try {
    console.log(`[DEBUG] Fetching workflows with page=${page}, pageSize=${pageSize}`);
    console.log(`[DEBUG] Using endpoint: ${API_ENDPOINTS.WORKFLOWS.LIST}`);

    // If no token is provided, try to get it from client or server
    if (!accessToken) {
      try {
        if (typeof window !== "undefined") {
          // Client-side
          accessToken = getClientAccessToken();
          console.log(
            `[DEBUG] Retrieved client token (length: ${accessToken ? accessToken.length : 0})`,
          );
        } else {
          // Server-side
          accessToken = await getAccessToken();
          console.log(
            `[DEBUG] Retrieved server token (length: ${accessToken ? accessToken.length : 0})`,
          );
        }
      } catch (tokenError) {
        console.error(`[DEBUG] Error retrieving token:`, tokenError);
      }
    } else {
      console.log(`[DEBUG] Using provided access token (length: ${accessToken.length})`);
    }

    // Prepare config with authentication if token is provided
    const config: any = {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: false,
      params: {
        page: page,
        page_size: pageSize,
      },
    };

    // Add Authorization header if access token is provided
    if (accessToken) {
      // Ensure token has Bearer prefix
      if (accessToken.startsWith("Bearer ")) {
        config.headers.Authorization = accessToken;
      } else {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      console.log(`[DEBUG] Added Authorization header`);
    } else {
      console.warn(`[DEBUG] No access token available for request`);
    }

    // Use the workflowAxios instance that has the interceptors
    // and the correct endpoint from API_ENDPOINTS
    console.log(`[DEBUG] Making request to: ${API_ENDPOINTS.WORKFLOWS.LIST}`);
    const response = await workflowAxios.get(API_ENDPOINTS.WORKFLOWS.LIST, config);
    console.log(`[DEBUG] Successful response with ${response.data?.data?.length || 0} workflows`);

    return response.data;
  } catch (error: any) {
    console.error("Error fetching workflows:", error);
    // Add more detailed error logging
    if (error.response) {
      console.error(`[DEBUG] Response status: ${error.response.status}`);
      console.error(`[DEBUG] Response data:`, error.response.data);
      throw new Error(
        `Failed to fetch workflows: ${error.response.status} ${error.response.statusText}`,
      );
    } else if (error.request) {
      // The request was made but no response was received
      console.error(`[DEBUG] No response received:`, error.request);
      throw new Error("Failed to fetch workflows: No response received from server");
    } else {
      // Something happened in setting up the request
      console.error(`[DEBUG] Request setup error:`, error.message);
      throw new Error(`Failed to fetch workflows: ${error.message}`);
    }
  }
}

/**
 * Creates a new workflow with a StartNode already added
 */
export async function createEmptyWorkflow(): Promise<CreateWorkflowResponse> {
  try {
    // Create a StartNode for the initial workflow
    const startNode = {
      id: "start-node",
      type: "WorkflowNode",
      position: { x: 100, y: 100 },
      data: {
        label: "Start",
        type: "component",
        originalType: "StartNode",
        definition: {
          name: "StartNode",
          display_name: "Start",
          description:
            "The starting point for all workflows. Only nodes connected to this node will be executed.",
          category: "Input/Output",
          icon: "Play",
          beta: false,
          inputs: [],
          outputs: [
            {
              name: "flow",
              display_name: "Flow",
              output_type: "Any",
            },
          ],
          is_valid: true,
          path: "components.io.start_node",
        },
        config: {
          collected_parameters: {},
        },
      },
    };

    const payload: CreateWorkflowRequest = {
      name: "Untitled Workflow",
      description: "New workflow",
      workflow_data: {
        nodes: [startNode],
        edges: [],
      },
      start_node_data: [],
    };

    // Get token for authorization
    let token;
    if (typeof window !== "undefined") {
      // Client-side
      token = getClientAccessToken();
    } else {
      // Server-side
      token = await getAccessToken();
    }

    const config = {
      headers: {
        "Content-Type": "application/json",
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      withCredentials: false,
    };

    const response = await axios.post(API_ENDPOINTS.WORKFLOWS.CREATE, payload, config);

    return response.data;
  } catch (error: any) {
    console.error("Error creating workflow:", error);
    if (error.response) {
      throw new Error(
        `Failed to create workflow: ${error.response.status} ${error.response.statusText}`,
      );
    }
    throw error;
  }
}

/**
 * Fetches a specific workflow by ID
 *
 * @returns A WorkflowSummary object with the workflow details nested in the workflow property
 */
export async function fetchWorkflowById(id: string): Promise<WorkflowSummary> {
  try {
    // Get token for authorization
    let token;
    if (typeof window !== "undefined") {
      // Client-side
      token = getClientAccessToken();
    } else {
      // Server-side
      token = await getAccessToken();
    }

    const config = {
      headers: {
        "Content-Type": "application/json",
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      withCredentials: false,
    };

    const response = await axios.get(API_ENDPOINTS.WORKFLOWS.GET(id), config);
    console.log("Workflow API response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("Error fetching workflow:", error);
    if (error.response) {
      throw new Error(
        `Failed to fetch workflow: ${error.response.status} ${error.response.statusText}`,
      );
    }
    throw error;
  }
}

/**
 * Fetches workflow data from a builder URL
 * @param url The URL to fetch the workflow data from
 * @returns The workflow data
 */
export async function fetchWorkflowFromBuilderUrl(url: string): Promise<any> {
  try {
    // For external URLs, we need to use axios directly
    const response = await axios.get(url, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    return response.data;
  } catch (error: any) {
    console.error("Error fetching workflow from builder URL:", error);
    if (error.response) {
      throw new Error(
        `Failed to fetch workflow data: ${error.response.status} ${error.response.statusText}`,
      );
    }
    throw error;
  }
}

/**
 * Deletes a workflow by ID
 *
 * @param id The ID of the workflow to delete
 * @returns A response object indicating success or failure
 */
export async function deleteWorkflow(id: string): Promise<{ success: boolean; message: string }> {
  try {
    // Get token for authorization
    let token;
    if (typeof window !== "undefined") {
      // Client-side
      token = getClientAccessToken();
    } else {
      // Server-side
      token = await getAccessToken();
    }

    const config = {
      headers: {
        "Content-Type": "application/json",
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      withCredentials: false,
    };

    const response = await axios.delete(API_ENDPOINTS.WORKFLOWS.DELETE(id), config);
    console.log("Workflow delete response:", response.data);
    return {
      success: true,
      message: response.data?.message || "Workflow deleted successfully",
    };
  } catch (error: any) {
    console.error("Error deleting workflow:", error);
    if (error.response) {
      throw new Error(
        `Failed to delete workflow: ${error.response.status} ${error.response.statusText}`,
      );
    }
    throw error;
  }
}

/**
 * Updates a workflow's metadata (name and description)
 */
export async function updateWorkflowMetadata(
  id: string,
  data: { name?: string; description?: string },
): Promise<WorkflowSummary> {
  try {
    // Get token for authorization
    let token;
    if (typeof window !== "undefined") {
      // Client-side
      token = getClientAccessToken();
    } else {
      // Server-side
      token = await getAccessToken();
    }

    const config = {
      headers: {
        "Content-Type": "application/json",
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      withCredentials: false,
    };

    const response = await axios.patch(API_ENDPOINTS.WORKFLOWS.UPDATE(id), data, config);

    return response.data;
  } catch (error: any) {
    console.error("Error updating workflow metadata:", error);
    if (error.response) {
      throw new Error(
        `Failed to update workflow: ${error.response.status} ${error.response.statusText}`,
      );
    }
    throw error;
  }
}

export default {
  fetchWorkflowsByUser,
  createEmptyWorkflow,
  fetchWorkflowById,
  fetchWorkflowFromBuilderUrl,
  deleteWorkflow,
  updateWorkflowMetadata,
};
