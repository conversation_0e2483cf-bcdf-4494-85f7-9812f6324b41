"""
Component logger utility for workflow builder components.

This module provides utility functions for logging component loading and other events.
"""

import logging

logger = logging.getLogger("component_logger")


def log_component_loaded(component_name: str, component_type: str, is_abstract: bool = False) -> None:
    """
    Log that a component has been loaded.

    Args:
        component_name: The name of the component.
        component_type: The type of the component (e.g., "AI", "Data", "Logic").
        is_abstract: Whether the component is abstract.
    """
    abstract_str = "abstract " if is_abstract else ""
    logger.info(f"Loaded {abstract_str}{component_type} component: {component_name}")
    print(f"DEBUG: {abstract_str}{component_type} component {component_name} loaded")
