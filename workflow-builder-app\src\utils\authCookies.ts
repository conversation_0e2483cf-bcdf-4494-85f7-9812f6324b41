// src/utils/authCookies.ts
import { getC<PERSON>ie, setC<PERSON>ie, deleteCookie } from "cookies-next";

// <PERSON><PERSON> names
export const ACCESS_TOKEN_NAME = "access_token";
export const REFRESH_TOKEN_NAME = "refresh_token";

/**
 * Get the access token from cookies
 * @returns The access token or null if not found
 */
export const getAccessToken = async (): Promise<string | null> => {
  const token = getCookie(ACCESS_TOKEN_NAME);
  return token ? String(token) : null;
};

/**
 * Get the refresh token from cookies
 * @returns The refresh token or null if not found
 */
export const getRefreshToken = async (): Promise<string | null> => {
  const token = getCookie(REFRESH_TOKEN_NAME);
  return token ? String(token) : null;
};

/**
 * Set the access token in cookies
 * @param token The access token to set
 * @param expiresIn Expiration time in seconds
 */
export const setAccessToken = (token: string, expiresIn: number = 3600): void => {
  const expirationDate = new Date();
  expirationDate.setSeconds(expirationDate.getSeconds() + expiresIn);

  setCookie(ACCESS_TOKEN_NAME, token, {
    expires: expirationDate,
    path: "/",
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });
};

/**
 * Set the refresh token in cookies
 * @param token The refresh token to set
 * @param expiresIn Expiration time in seconds (default: 30 days)
 */
export const setRefreshToken = (token: string, expiresIn: number = 30 * 24 * 60 * 60): void => {
  const expirationDate = new Date();
  expirationDate.setSeconds(expirationDate.getSeconds() + expiresIn);

  setCookie(REFRESH_TOKEN_NAME, token, {
    expires: expirationDate,
    path: "/",
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });
};

/**
 * Clear all authentication cookies
 */
export const clearAuthCookies = async (): Promise<void> => {
  deleteCookie(ACCESS_TOKEN_NAME);
  deleteCookie(REFRESH_TOKEN_NAME);
};
