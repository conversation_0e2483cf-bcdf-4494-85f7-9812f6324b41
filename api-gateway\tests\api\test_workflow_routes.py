import pytest
from unittest.mock import patch, Mock
from fastapi.testclient import TestClient

class TestWorkflowRoutes:
    @pytest.fixture(autouse=True)
    def setup(self, test_client: TestClient, user_headers):
        self.client = test_client
        self.headers = user_headers

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_create_workflow_success(self, mock_workflow_service):
        # Arrange
        mock_workflow_service.return_value.create_workflow.return_value = {
            "id": "workflow_123",
            "status": "created"
        }
        
        workflow_data = {
            "name": "Test Workflow",
            "description": "Test workflow description",
            "steps": [{"id": "step1", "type": "task"}]
        }

        # Act
        response = self.client.post(
            "/api/v1/workflows",
            json=workflow_data,
            headers=self.headers
        )

        # Assert
        assert response.status_code == 200
        assert "id" in response.json()

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_get_workflow_status_success(self, mock_workflow_service):
        mock_workflow_service.return_value.get_workflow_status.return_value = {
            "id": "workflow_123",
            "status": "running"
        }
        
        response = self.client.get(
            "/api/v1/workflows/status/workflow_123",
            headers=self.headers
        )
        
        assert response.status_code == 200
        assert response.json()["status"] == "running"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_create_workflow_request_success(self, mock_workflow_service):
        workflow_data = {
            "workflow_id": "test_workflow",
            "payload": {
                "user_dependent_fields": ["field1", "field2"],
                "user_payload_template": {
                    "field1": "value1",
                    "field2": "value2"
                }
            }
        }
        
        response = self.client.post(
            "/api/v1/kafka/workflow-requests",
            json=workflow_data,
            headers=self.headers
        )
        
        assert response.status_code == 200
