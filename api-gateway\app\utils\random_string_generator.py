import random
import string


def generate_random_string(n: int) -> str:
    """Function to generate a random string of length n.

    Args:
        n (int): The length of the random string.

    Raises:
        ValueError: If the length is less than 5.

    Returns:
        str: The generated random string.
    """

    if n < 5:
        raise ValueError("Length must be at least 5 to include all required character types")

    chars = string.ascii_letters + string.digits
    result = [
        random.choice(string.digits),
        random.choice(string.ascii_lowercase),
        random.choice(string.ascii_uppercase),
    ]

    result += random.choices(chars, k=n - 5)
    random.shuffle(result)

    return "".join(result)
