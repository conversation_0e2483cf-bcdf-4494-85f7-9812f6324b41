#!/usr/bin/env python3
"""
Test script to verify that the AgenticAI component uses dual-purpose inputs correctly.

This script tests the AgenticAI component to ensure it:
1. Uses create_dual_purpose_input for unified inputs
2. Has the correct get_input_value method for dual-purpose inputs
3. Does not have separate handle/direct inputs
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.components.ai.agentic_ai import AgenticAI
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input


def test_agentic_ai_inputs():
    """Test that AgenticAI uses dual-purpose inputs correctly."""
    print("Testing AgenticAI component inputs...")

    # Get the component inputs
    inputs = AgenticAI.inputs

    # Check for dual-purpose inputs
    dual_purpose_inputs = []
    handle_only_inputs = []
    direct_only_inputs = []

    for input_def in inputs:
        if hasattr(input_def, 'is_handle') and input_def.is_handle:
            # Check if this is a dual-purpose input or handle-only input
            # Dual-purpose inputs have input_types that include multiple types
            # Handle-only inputs typically have a single type or are explicitly handle-only
            if hasattr(input_def, 'input_types') and input_def.input_types and len(input_def.input_types) > 1:
                dual_purpose_inputs.append(input_def.name)
            else:
                handle_only_inputs.append(input_def.name)
        else:
            direct_only_inputs.append(input_def.name)

    print(f"Dual-purpose inputs: {dual_purpose_inputs}")
    print(f"Handle-only inputs: {handle_only_inputs}")
    print(f"Direct-only inputs: {direct_only_inputs}")

    # Check that we have the expected dual-purpose inputs
    expected_dual_purpose = ['objective', 'input_variables', 'tools']
    for expected in expected_dual_purpose:
        if expected not in dual_purpose_inputs:
            print(f"❌ ERROR: Expected dual-purpose input '{expected}' not found!")
            return False
        else:
            print(f"✅ Found dual-purpose input: {expected}")

    # Check that we don't have separate handle inputs for dual-purpose ones
    problematic_handles = []
    for dual_input in expected_dual_purpose:
        handle_name = f"{dual_input}_handle"
        if handle_name in handle_only_inputs:
            problematic_handles.append(handle_name)

    if problematic_handles:
        print(f"❌ ERROR: Found separate handle inputs that should be dual-purpose: {problematic_handles}")
        return False

    # Check that memory is handle-only (this is expected)
    if 'memory' not in handle_only_inputs:
        print("❌ ERROR: Memory input should be handle-only")
        return False
    else:
        print("✅ Memory input is correctly handle-only")

    print("✅ All input checks passed!")
    return True


def test_get_input_value_method():
    """Test that the get_input_value method works correctly for dual-purpose inputs."""
    print("\nTesting get_input_value method...")

    # Create a mock context
    class MockContext:
        def __init__(self):
            self.current_node_id = "test_node"
            self.node_outputs = {
                "test_node": {
                    "objective": "Test objective from context",
                    "input_variables": {"key": "value"},
                    "tools": ["tool1", "tool2"]
                }
            }

    # Create an instance of AgenticAI
    component = AgenticAI()
    context = MockContext()

    # Test getting values from context
    objective = component.get_input_value("objective", context, "default_objective")
    input_variables = component.get_input_value("input_variables", context, {})
    tools = component.get_input_value("tools", context, [])

    # Verify the values
    if objective != "Test objective from context":
        print(f"❌ ERROR: Expected 'Test objective from context', got '{objective}'")
        return False
    else:
        print("✅ Objective retrieved correctly from context")

    if input_variables != {"key": "value"}:
        print(f"❌ ERROR: Expected {{'key': 'value'}}, got {input_variables}")
        return False
    else:
        print("✅ Input variables retrieved correctly from context")

    if tools != ["tool1", "tool2"]:
        print(f"❌ ERROR: Expected ['tool1', 'tool2'], got {tools}")
        return False
    else:
        print("✅ Tools retrieved correctly from context")

    # Test default values when not in context
    missing_value = component.get_input_value("missing_input", context, "default_value")
    if missing_value != "default_value":
        print(f"❌ ERROR: Expected 'default_value', got '{missing_value}'")
        return False
    else:
        print("✅ Default value returned correctly for missing input")

    print("✅ All get_input_value tests passed!")
    return True


def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing AgenticAI Dual-Purpose Input Fix")
    print("=" * 60)

    success = True

    # Test inputs
    if not test_agentic_ai_inputs():
        success = False

    # Test get_input_value method
    if not test_get_input_value_method():
        success = False

    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED! AgenticAI component is correctly configured.")
        print("\nNext steps:")
        print("1. Restart the workflow-service to clear any cached component definitions")
        print("2. Or call the API endpoint: GET /api/v1/components?refresh=true")
        print("3. Test in the workflow builder UI")
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
    print("=" * 60)

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
