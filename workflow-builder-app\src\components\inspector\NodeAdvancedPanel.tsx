import React from "react";
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";

interface NodeAdvancedPanelProps {
  node: Node<WorkflowNodeData>;
  onDefinitionChange: (propertyName: string, value: any) => void;
}

/**
 * Component for displaying advanced node settings in the inspector
 */
export function NodeAdvancedPanel({ node, onDefinitionChange }: NodeAdvancedPanelProps) {
  return (
    <ScrollArea className="h-full flex-grow overflow-auto p-4">
      <div className="space-y-4">
        <h3 className="mb-3 text-sm font-medium">Advanced Settings</h3>

        {/* Approval Toggle */}
        <div className="mb-4 space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="requiresApproval" className="text-xs font-medium">
              Requires Approval
            </Label>
            <Switch
              id="requiresApproval"
              checked={node.data.definition?.requires_approval || false}
              onCheckedChange={(checked) =>
                onDefinitionChange("requires_approval", checked)
              }
            />
          </div>
          <p className="text-muted-foreground text-xs">
            When enabled, this component will require approval before execution. This
            setting is stored in the component definition.
          </p>
        </div>

        {/* JSON Configuration View */}
        <div>
          <Label htmlFor="jsonConfig" className="text-xs font-medium">
            Raw Configuration
          </Label>
          <Textarea
            id="jsonConfig"
            value={JSON.stringify(node.data.config || {}, null, 2)}
            className="bg-background/50 mt-1.5 font-mono text-xs"
            rows={10}
            readOnly
          />
        </div>
      </div>
    </ScrollArea>
  );
}
