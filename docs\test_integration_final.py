#!/usr/bin/env python3
"""
Final Integration Test for SelectDataComponent Consistency.

This test demonstrates that the SelectDataComponent in workflow-service
and SelectDataExecutor in node-executor-service work consistently together.
"""

import asyncio
import sys
import os

# Add paths for both services
workflow_service_path = os.path.join(os.path.dirname(__file__), "workflow-service", "app")
node_executor_path = os.path.join(os.path.dirname(__file__), "node-executor-service", "app")

sys.path.insert(0, workflow_service_path)
sys.path.insert(0, node_executor_path)

# Import components from both services
try:
    from components.processing.select_data import SelectDataComponent
    from models.workflow_builder.context import WorkflowContext
    from components.select_data_component import SelectDataExecutor
    print("✓ Successfully imported components from both services")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


async def test_consistency(test_name, test_data, data_type, selector, expected_result):
    """Test consistency between workflow-service and node-executor-service."""
    print(f"\n=== {test_name} ===")
    
    # Test workflow-service component
    workflow_component = SelectDataComponent()
    context = WorkflowContext()
    context.current_node_id = "test_node"
    context.node_outputs["test_node"] = {
        "input_data": test_data,
        "data_type": data_type,
        "selector": selector
    }
    
    workflow_result = await workflow_component.execute(context)
    workflow_output = workflow_result.outputs.get("output_data")
    workflow_error = workflow_result.outputs.get("error")
    
    print(f"Workflow Service: output={workflow_output}, error={workflow_error}")
    
    # Test node-executor-service component
    executor_component = SelectDataExecutor()
    payload = {
        "request_id": f"test_{test_name.lower().replace(' ', '_')}",
        "input_data": test_data,
        "data_type": data_type,
        "selector": selector
    }
    
    executor_result = await executor_component.process(payload)
    executor_output = executor_result.get("output_data")
    executor_error = executor_result.get("error")
    
    print(f"Node Executor: output={executor_output}, error={executor_error}")
    
    # Check consistency
    if expected_result is None:
        # Expecting an error
        if workflow_error and executor_error:
            print("✓ Both services correctly returned errors")
            return True
        else:
            print(f"✗ Error handling inconsistent - workflow error: {workflow_error}, executor error: {executor_error}")
            return False
    else:
        # Expecting a successful result
        if workflow_output == executor_output == expected_result and not workflow_error and not executor_error:
            print(f"✓ Both services returned consistent result: {expected_result}")
            return True
        else:
            print(f"✗ Results inconsistent - workflow: {workflow_output}, executor: {executor_output}, expected: {expected_result}")
            return False


async def main():
    """Run comprehensive integration tests."""
    print("SelectDataComponent Final Integration Test")
    print("=" * 60)
    print("Testing consistency between workflow-service and node-executor-service")
    
    test_cases = [
        {
            "name": "List Selection - Index 0",
            "data": ["apple", "banana", "cherry"],
            "type": "List",
            "selector": "0",
            "expected": "apple"
        },
        {
            "name": "List Selection - Index 2",
            "data": ["red", "green", "blue", "yellow"],
            "type": "List",
            "selector": "2",
            "expected": "blue"
        },
        {
            "name": "List Selection - Negative Index",
            "data": ["first", "second", "third"],
            "type": "List",
            "selector": "-1",
            "expected": "third"
        },
        {
            "name": "Dictionary Selection - Simple Key",
            "data": {"name": "Alice", "age": 25, "city": "New York"},
            "type": "Dictionary",
            "selector": "name",
            "expected": "Alice"
        },
        {
            "name": "Dictionary Selection - Nested Key",
            "data": {
                "user": {"profile": {"name": "Bob", "email": "<EMAIL>"}},
                "status": "active"
            },
            "type": "Dictionary",
            "selector": "user.profile.name",
            "expected": "Bob"
        },
        {
            "name": "Auto-Detection - List",
            "data": [10, 20, 30, 40],
            "type": "Auto-Detect",
            "selector": "1",
            "expected": 20
        },
        {
            "name": "Auto-Detection - Dictionary",
            "data": {"x": 100, "y": 200, "z": 300},
            "type": "Auto-Detect",
            "selector": "y",
            "expected": 200
        },
        {
            "name": "Error - List Index Out of Range",
            "data": ["one", "two"],
            "type": "List",
            "selector": "5",
            "expected": None  # Expecting error
        },
        {
            "name": "Error - Dictionary Key Not Found",
            "data": {"a": 1, "b": 2},
            "type": "Dictionary",
            "selector": "nonexistent",
            "expected": None  # Expecting error
        },
        {
            "name": "Error - Invalid Nested Path",
            "data": {"user": {"name": "Charlie"}},
            "type": "Dictionary",
            "selector": "user.profile.email",
            "expected": None  # Expecting error
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        try:
            if await test_consistency(
                test_case["name"],
                test_case["data"],
                test_case["type"],
                test_case["selector"],
                test_case["expected"]
            ):
                passed += 1
        except Exception as e:
            print(f"✗ Test '{test_case['name']}' failed with exception: {e}")
    
    print(f"\n=== Final Integration Test Results ===")
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 PERFECT CONSISTENCY ACHIEVED!")
        print("✅ SelectDataComponent works identically across both services")
        print("✅ End-to-end workflow execution will work correctly")
        print("✅ Component patterns are properly established")
        return 0
    else:
        print(f"\n❌ CONSISTENCY ISSUES DETECTED!")
        print(f"❌ {total - passed} test(s) failed")
        print("❌ Please review component implementations")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
