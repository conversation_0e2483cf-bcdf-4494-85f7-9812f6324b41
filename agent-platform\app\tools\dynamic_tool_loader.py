from autogen_core.tools import BaseTool
from pydantic import create_model, BaseModel
from autogen_core import CancellationToken
import requests
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.messages import (
    TextMessage,
    ToolCallRequestEvent,
    ToolCallExecutionEvent,
    ToolCallSummaryMessage,
    ModelClientStreamingChunkEvent,
)
import os
from autogen_agentchat.ui import Console
from autogen_agentchat.base import TaskResult
from .workflow_tool_loader import WorkflowToolLoader
from .mcp_tool_loader import load_all_mcp_tool_adapters_from_schema_list, McpToolLoader
import asyncio
import logging

# Configure logger
logger = logging.getLogger(__name__)

# Map JSON Schema types to Python types
json_type_to_py = {
    "string": str,
    "integer": int,
    "number": float,
    "boolean": bool,
    "object": dict,
    "array": list,
}


class DynamicToolLoader:
    @staticmethod
    def create_api_tool_from_json(tool_json):
        """
        Create a dynamic tool from a tool_json describing an API endpoint.
        tool_json: dict with keys:
            - name: str
            - description: str
            - parameters: JSON schema dict
            - api_url: str
            - method: str (default POST)
            - stream: bool (default False)
        Returns: BaseTool instance
        """
        name = tool_json["name"]
        description = tool_json.get("description", "")
        json_schema = tool_json["parameters"]
        api_url = tool_json["api_url"]
        method = tool_json.get("method", "POST").upper()
        stream = tool_json.get("stream", False)

        logger.info(f"Creating API tool: {name}")
        logger.debug(
            f"Tool details - API URL: {api_url}, Method: {method}, Stream: {stream}"
        )

        # Build fields dict with correct Python types
        fields = {}
        for k, v in json_schema.get("properties", {}).items():
            py_type = json_type_to_py.get(v.get("type", "string"), str)
            default = ... if k in json_schema.get("required", []) else None
            fields[k] = (py_type, default)
            logger.debug(f"Added field {k} with type {py_type} to tool schema")

        ArgsModel = create_model(f"{name}_Args", **fields)
        ArgsModel.model_rebuild()  # Ensure the model is fully defined
        logger.debug(f"Created args model for {name}")

        class APITool(BaseTool[ArgsModel, BaseModel]):
            def __init__(self):
                super().__init__(
                    args_type=ArgsModel,
                    return_type=BaseModel,
                    name=name,
                    description=description,
                )
                logger.debug(f"Initialized APITool for {name}")

            async def run(self, args: ArgsModel, cancellation_token: CancellationToken):
                payload = args.model_dump(exclude_none=True)
                logger.info(f"Executing API tool {name} with payload: {payload}")

                # Use requests in a thread to avoid blocking event loop
                try:

                    def sync_stream():
                        logger.debug(f"Making {method} request to {api_url}")
                        with requests.request(
                            method, api_url, json=payload, stream=stream
                        ) as resp:
                            resp.raise_for_status()
                            logger.debug(f"API response status: {resp.status_code}")

                            if stream:
                                logger.debug("Processing streaming response")
                                for chunk in resp.iter_content(chunk_size=1024):
                                    logger.debug(
                                        f"Received chunk of size: {len(chunk)} bytes"
                                    )
                                    if chunk:
                                        yield chunk.decode("utf-8")
                            else:
                                logger.debug(
                                    f"Processing non-streaming response of size: {len(resp.text)} bytes"
                                )
                                yield resp.text

                    if stream:
                        # Wrap the sync generator as an async generator
                        logger.debug(f"Creating async stream wrapper for {name}")

                        async def async_stream():
                            loop = asyncio.get_event_loop()
                            for chunk in await loop.run_in_executor(
                                None, lambda: list(sync_stream())
                            ):
                                yield chunk

                        return async_stream()
                    else:
                        logger.debug(f"Executing non-streaming request for {name}")
                        loop = asyncio.get_event_loop()
                        result = await loop.run_in_executor(
                            None, lambda: list(sync_stream())
                        )
                        logger.debug(
                            f"Received result of size: {len(''.join(result))} bytes"
                        )
                        return "".join(result)

                except Exception as e:
                    logger.error(
                        f"Error executing API tool {name}: {str(e)}", exc_info=True
                    )
                    return f"Error: {str(e)}"

            async def join_stream(self, value):
                # Helper to join all chunks from an async generator
                logger.debug(f"Joining stream for {name}")
                if hasattr(value, "__aiter__"):
                    chunks = []
                    async for chunk in value:
                        chunks.append(chunk)
                    result = "".join(chunks)
                    logger.debug(f"Joined stream, total size: {len(result)} bytes")
                    return result
                logger.debug("Value is not a stream, returning as string")
                return str(value)

        logger.info(f"Successfully created API tool: {name}")
        return APITool()

    @staticmethod
    def assign_tool_to_agent(agent, tool):
        """
        Assign a tool (BaseTool or FunctionTool) to the agent's tools.
        """
        agent_name = getattr(agent, "name", "unnamed_agent")
        tool_name = getattr(tool, "name", "unnamed_tool")

        logger.info(f"Assigning tool {tool_name} to agent {agent_name}")

        if hasattr(agent, "tools"):
            original_count = len(agent.tools) if agent.tools else 0
            agent.tools.append(tool)
            logger.debug(f"Added tool to existing {original_count} tools")
        else:
            agent.tools = [tool]
            logger.debug("Created new tools list with the tool")

        logger.info(f"Successfully assigned tool {tool_name} to agent {agent_name}")
        return tool


tools = []
# # Example tool JSON for API tool
# tool_json = {
#     "name": "get_weather",
#     "description": "Get the weather for a city on a given date.",
#     "parameters": {
#         "type": "object",
#         "properties": {
#             "city": {"type": "string", "description": "City name"},
#             "date": {
#                 "type": "string",
#                 "description": "Date (YYYY-MM-DD)",
#                 "format": "date",
#             },
#         },
#         "required": ["city"],
#     },
#     # Use a real streaming API endpoint for real tests
#     "api_url": "https://postman-echo.com/post",  # This is a public echo endpoint for demo
#     "method": "POST",
#     "stream": True,
# }

# logger.info("Creating example API tool")
# # Dynamically create the API tool
# tools = [DynamicToolLoader.create_api_tool_from_json(tool_json)]
# logger.info(f"API Tool created: {[tool.name for tool in tools]}")


async def test_agent_with_tool():
    """
    Test function to demonstrate using a dynamic API tool with an agent.
    """

    # mcp_tools = [
    #     {
    #         "mcp": {
    #             "sse_url": "https://script-generation-mcp-dev-624209391722.us-central1.run.app/sse"
    #         }
    #     },
    # ]

    # tools = await load_all_mcp_tool_adapters_from_schema_list(mcp_tools)

    # tools_loader = McpToolLoader()

    # mcps = [
    #     {
    #         "id": "mcp1",
    #         "name": "MCP 1",
    #         "sse_url": "https://script-generation-mcp-dev-624209391722.us-central1.run.app/sse",
    #     }
    # ]

    # tools = await tools_loader.load_mcps_as_tools(mcps)

    workflow_loader = WorkflowToolLoader()

    workflows = [
        {
            "id": "ciny",
            "name": "video_generation",
            "description": "Generates a video based on the provided topic, script type and video type.",
            "parameters": {
                "type": "object",
                "properties": {
                    "topic": {
                        "type": "string",
                        "description": "The main subject of the video.",
                    },
                    "video_type": {
                        "type": "string",
                        "description": "The type of video to be generated.",
                    },
                    "script_type": {
                        "type": "string",
                        "description": "The type of script to be generated.",
                    },
                },
                "required": ["topic", "video_type", "script_type"],
            },
        }
    ]

    tools = await workflow_loader.load_workflows_as_tools("1234", workflows)

    print(f"Loaded {len(tools)} MCP tools: {tools}")

    logger.info("Starting test_agent_with_tool")

    # Create the model client (requires OPENAI_API_KEY in env)
    logger.info("Creating OpenAI model client")

    model_client = OpenAIChatCompletionClient(
        model="gpt-4o",
        api_key=os.getenv("OPENAI_API_KEY"),
    )

    # Create the agent and assign the tool
    logger.info("Creating assistant agent")
    agent = AssistantAgent(
        name="marketing_agent",
        model_client=model_client,
        tools=tools,
        system_message="You are a helpful assistant. Use tools to solve tasks.",
        reflect_on_tool_use=True,
        model_client_stream=True,
    )
    logger.debug("Agent created successfully")

    print("=== AutoGen CLI Chat (type 'exit' to quit) ===")
    while True:
        user_message = input("\nYou: ")
        if user_message.strip().lower() in {"exit", "quit"}:
            logger.info("User requested exit")
            break

        logger.info(f"Processing user message: {user_message}")
        print("Assistant (streaming):")
        last_summary = None
        last_text = None

        logger.debug("Starting agent.run_stream")
        async for message in agent.run_stream(task=user_message):
            # Print message type and content for clarity
            if isinstance(message, TextMessage):
                who = message.source if hasattr(message, "source") else "assistant"
                logger.debug(f"Received TextMessage from {who}")
                print(f"[TextMessage] {who}: {message.content}")
                if who == agent.name:
                    last_text = message.content
            elif isinstance(message, ToolCallRequestEvent):
                logger.debug("Received ToolCallRequestEvent")
                print(f"[ToolCallRequestEvent] {message.content}")
            elif isinstance(message, ToolCallExecutionEvent):
                logger.debug("Received ToolCallExecutionEvent")
                print(f"[ToolCallExecutionEvent] {message.content}")
            elif isinstance(message, ToolCallSummaryMessage):
                logger.debug("Received ToolCallSummaryMessage")
                print(f"[ToolCallSummaryMessage] {message.content}")
                last_summary = message.content
            elif isinstance(message, TaskResult):
                stop_reason = getattr(message, "stop_reason", None)
                logger.debug(f"Received TaskResult with stop_reason: {stop_reason}")
                print(f"[TaskResult] stop_reason={stop_reason}")
            else:
                logger.debug(f"Received message of type: {type(message).__name__}")
                print(
                    f"[{type(message).__name__}] {getattr(message, 'content', str(message))}"
                )

        # Print the final assistant response
        if last_summary:
            logger.info("Printing final response from summary")
            print(f"\nAssistant: {last_summary}")
        elif last_text:
            logger.info("Printing final response from text")
            print(f"\nAssistant: {last_text}")
        else:
            logger.warning("No response received from assistant")
            print("\nAssistant: [No response]")

    logger.info("Closing model client")
    await model_client.close()
    logger.info("Test completed")


# logger.info("Starting dynamic tool loader test")
# asyncio.run(test_agent_with_tool())
