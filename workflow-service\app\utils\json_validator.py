import json
from jsonschema import Draft7Validator

def _load_schema(schema_path: str):
    with open(schema_path, 'r') as f:
        return json.load(f)

def _parse_data(input_data):
    if isinstance(input_data, str):
        try:
            return json.loads(input_data)
        except json.JSONDecodeError:
            raise ValueError("Provided string is not valid JSON.")
    elif isinstance(input_data, dict):
        return input_data
    else:
        raise TypeError("Data must be a dict or a JSON string.")

def validate_transition_schema(schema_path: str, data_input):
    schema = _load_schema(schema_path)
    data = _parse_data(data_input)

    validator = Draft7Validator(schema)
    errors = sorted(validator.iter_errors(data), key=lambda e: list(e.path))
    if errors:
        print("❌ Validation Errors in transition schema:")
        for error in errors:
            print(f"- Path: {'/'.join([str(p) for p in error.path])}")
            print(f"  Message: {error.message}")
        raise ValueError("Transition schema validation failed.")
    print("✅ Transition schema is valid.")

def validate_capture_schema(schema_path: str, data_input):
    schema = _load_schema(schema_path)
    data = _parse_data(data_input)

    validator = Draft7Validator(schema)
    errors = sorted(validator.iter_errors(data), key=lambda e: list(e.path))
    if errors:
        print("❌ Validation Errors in validate capture schema:")
        for error in errors:
            print(f"- Path: {'/'.join([str(p) for p in error.path])}")
            print(f"  Message: {error.message}")
        raise ValueError("Validate capture schema validation failed.")
    print("✅ Validate capture schema is valid.")
