# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import agent_pb2 as agent__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in agent_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AgentServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.createAgent = channel.unary_unary(
                '/agent.AgentService/createAgent',
                request_serializer=agent__pb2.CreateAgentRequest.SerializeToString,
                response_deserializer=agent__pb2.CreateAgentResponse.FromString,
                _registered_method=True)
        self.getAgent = channel.unary_unary(
                '/agent.AgentService/getAgent',
                request_serializer=agent__pb2.GetAgentRequest.SerializeToString,
                response_deserializer=agent__pb2.AgentResponse.FromString,
                _registered_method=True)
        self.deleteAgent = channel.unary_unary(
                '/agent.AgentService/deleteAgent',
                request_serializer=agent__pb2.DeleteAgentRequest.SerializeToString,
                response_deserializer=agent__pb2.DeleteAgentResponse.FromString,
                _registered_method=True)
        self.listAgents = channel.unary_unary(
                '/agent.AgentService/listAgents',
                request_serializer=agent__pb2.ListAgentsRequest.SerializeToString,
                response_deserializer=agent__pb2.ListAgentsResponse.FromString,
                _registered_method=True)
        self.createAgentFromTemplate = channel.unary_unary(
                '/agent.AgentService/createAgentFromTemplate',
                request_serializer=agent__pb2.CreateAgentFromTemplateRequest.SerializeToString,
                response_deserializer=agent__pb2.CreateAgentFromTemplateResponse.FromString,
                _registered_method=True)
        self.UpdateAgentCoreDetails = channel.unary_unary(
                '/agent.AgentService/UpdateAgentCoreDetails',
                request_serializer=agent__pb2.UpdateAgentCoreDetailsRequest.SerializeToString,
                response_deserializer=agent__pb2.UpdateAgentPartResponse.FromString,
                _registered_method=True)
        self.UpdateAgentKnowledge = channel.unary_unary(
                '/agent.AgentService/UpdateAgentKnowledge',
                request_serializer=agent__pb2.UpdateAgentKnowledgeRequest.SerializeToString,
                response_deserializer=agent__pb2.UpdateAgentPartResponse.FromString,
                _registered_method=True)
        self.UpdateAgentMcpServers = channel.unary_unary(
                '/agent.AgentService/UpdateAgentMcpServers',
                request_serializer=agent__pb2.UpdateAgentMcpServersRequest.SerializeToString,
                response_deserializer=agent__pb2.UpdateAgentPartResponse.FromString,
                _registered_method=True)
        self.UpdateAgentWorkflows = channel.unary_unary(
                '/agent.AgentService/UpdateAgentWorkflows',
                request_serializer=agent__pb2.UpdateAgentWorkflowsRequest.SerializeToString,
                response_deserializer=agent__pb2.UpdateAgentPartResponse.FromString,
                _registered_method=True)
        self.UpdateAgentSettings = channel.unary_unary(
                '/agent.AgentService/UpdateAgentSettings',
                request_serializer=agent__pb2.UpdateAgentSettingsRequest.SerializeToString,
                response_deserializer=agent__pb2.UpdateAgentPartResponse.FromString,
                _registered_method=True)
        self.UpdateAgentCapabilities = channel.unary_unary(
                '/agent.AgentService/UpdateAgentCapabilities',
                request_serializer=agent__pb2.UpdateAgentCapabilitiesRequest.SerializeToString,
                response_deserializer=agent__pb2.UpdateAgentPartResponse.FromString,
                _registered_method=True)
        self.ToggleAgentVisibility = channel.unary_unary(
                '/agent.AgentService/ToggleAgentVisibility',
                request_serializer=agent__pb2.ToggleAgentVisibilityRequest.SerializeToString,
                response_deserializer=agent__pb2.ToggleAgentVisibilityResponse.FromString,
                _registered_method=True)
        self.getTemplate = channel.unary_unary(
                '/agent.AgentService/getTemplate',
                request_serializer=agent__pb2.GetTemplateRequest.SerializeToString,
                response_deserializer=agent__pb2.GetTemplateResponse.FromString,
                _registered_method=True)
        self.listTemplates = channel.unary_unary(
                '/agent.AgentService/listTemplates',
                request_serializer=agent__pb2.ListTemplatesRequest.SerializeToString,
                response_deserializer=agent__pb2.ListTemplatesResponse.FromString,
                _registered_method=True)
        self.getMarketplaceAgents = channel.unary_unary(
                '/agent.AgentService/getMarketplaceAgents',
                request_serializer=agent__pb2.GetMarketplaceAgentsRequest.SerializeToString,
                response_deserializer=agent__pb2.GetMarketplaceAgentsResponse.FromString,
                _registered_method=True)
        self.getMarketplaceAgentDetail = channel.unary_unary(
                '/agent.AgentService/getMarketplaceAgentDetail',
                request_serializer=agent__pb2.GetMarketplaceAgentDetailRequest.SerializeToString,
                response_deserializer=agent__pb2.GetMarketplaceAgentDetailResponse.FromString,
                _registered_method=True)
        self.rateAgent = channel.unary_unary(
                '/agent.AgentService/rateAgent',
                request_serializer=agent__pb2.RateAgentRequest.SerializeToString,
                response_deserializer=agent__pb2.RateAgentResponse.FromString,
                _registered_method=True)
        self.useAgent = channel.unary_unary(
                '/agent.AgentService/useAgent',
                request_serializer=agent__pb2.UseAgentRequest.SerializeToString,
                response_deserializer=agent__pb2.UseAgentResponse.FromString,
                _registered_method=True)
        self.createAgentAvatar = channel.unary_unary(
                '/agent.AgentService/createAgentAvatar',
                request_serializer=agent__pb2.CreateAgentAvatarRequest.SerializeToString,
                response_deserializer=agent__pb2.CreateAgentAvatarResponse.FromString,
                _registered_method=True)
        self.getAgentAvatar = channel.unary_unary(
                '/agent.AgentService/getAgentAvatar',
                request_serializer=agent__pb2.GetAgentAvatarRequest.SerializeToString,
                response_deserializer=agent__pb2.GetAgentAvatarResponse.FromString,
                _registered_method=True)
        self.listAgentAvatars = channel.unary_unary(
                '/agent.AgentService/listAgentAvatars',
                request_serializer=agent__pb2.ListAgentAvatarsRequest.SerializeToString,
                response_deserializer=agent__pb2.ListAgentAvatarsResponse.FromString,
                _registered_method=True)
        self.deleteAgentAvatar = channel.unary_unary(
                '/agent.AgentService/deleteAgentAvatar',
                request_serializer=agent__pb2.DeleteAgentAvatarRequest.SerializeToString,
                response_deserializer=agent__pb2.DeleteAgentAvatarResponse.FromString,
                _registered_method=True)


class AgentServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def createAgent(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getAgent(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteAgent(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listAgents(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createAgentFromTemplate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateAgentCoreDetails(self, request, context):
        """New Granular Update RPCs
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateAgentKnowledge(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateAgentMcpServers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateAgentWorkflows(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateAgentSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateAgentCapabilities(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ToggleAgentVisibility(self, request, context):
        """Visibility Toggle
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getTemplate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listTemplates(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getMarketplaceAgents(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getMarketplaceAgentDetail(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def rateAgent(self, request, context):
        """Rating and usage tracking RPCs
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def useAgent(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createAgentAvatar(self, request, context):
        """Agent Avatar RPCs
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getAgentAvatar(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listAgentAvatars(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteAgentAvatar(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AgentServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'createAgent': grpc.unary_unary_rpc_method_handler(
                    servicer.createAgent,
                    request_deserializer=agent__pb2.CreateAgentRequest.FromString,
                    response_serializer=agent__pb2.CreateAgentResponse.SerializeToString,
            ),
            'getAgent': grpc.unary_unary_rpc_method_handler(
                    servicer.getAgent,
                    request_deserializer=agent__pb2.GetAgentRequest.FromString,
                    response_serializer=agent__pb2.AgentResponse.SerializeToString,
            ),
            'deleteAgent': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteAgent,
                    request_deserializer=agent__pb2.DeleteAgentRequest.FromString,
                    response_serializer=agent__pb2.DeleteAgentResponse.SerializeToString,
            ),
            'listAgents': grpc.unary_unary_rpc_method_handler(
                    servicer.listAgents,
                    request_deserializer=agent__pb2.ListAgentsRequest.FromString,
                    response_serializer=agent__pb2.ListAgentsResponse.SerializeToString,
            ),
            'createAgentFromTemplate': grpc.unary_unary_rpc_method_handler(
                    servicer.createAgentFromTemplate,
                    request_deserializer=agent__pb2.CreateAgentFromTemplateRequest.FromString,
                    response_serializer=agent__pb2.CreateAgentFromTemplateResponse.SerializeToString,
            ),
            'UpdateAgentCoreDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAgentCoreDetails,
                    request_deserializer=agent__pb2.UpdateAgentCoreDetailsRequest.FromString,
                    response_serializer=agent__pb2.UpdateAgentPartResponse.SerializeToString,
            ),
            'UpdateAgentKnowledge': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAgentKnowledge,
                    request_deserializer=agent__pb2.UpdateAgentKnowledgeRequest.FromString,
                    response_serializer=agent__pb2.UpdateAgentPartResponse.SerializeToString,
            ),
            'UpdateAgentMcpServers': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAgentMcpServers,
                    request_deserializer=agent__pb2.UpdateAgentMcpServersRequest.FromString,
                    response_serializer=agent__pb2.UpdateAgentPartResponse.SerializeToString,
            ),
            'UpdateAgentWorkflows': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAgentWorkflows,
                    request_deserializer=agent__pb2.UpdateAgentWorkflowsRequest.FromString,
                    response_serializer=agent__pb2.UpdateAgentPartResponse.SerializeToString,
            ),
            'UpdateAgentSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAgentSettings,
                    request_deserializer=agent__pb2.UpdateAgentSettingsRequest.FromString,
                    response_serializer=agent__pb2.UpdateAgentPartResponse.SerializeToString,
            ),
            'UpdateAgentCapabilities': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAgentCapabilities,
                    request_deserializer=agent__pb2.UpdateAgentCapabilitiesRequest.FromString,
                    response_serializer=agent__pb2.UpdateAgentPartResponse.SerializeToString,
            ),
            'ToggleAgentVisibility': grpc.unary_unary_rpc_method_handler(
                    servicer.ToggleAgentVisibility,
                    request_deserializer=agent__pb2.ToggleAgentVisibilityRequest.FromString,
                    response_serializer=agent__pb2.ToggleAgentVisibilityResponse.SerializeToString,
            ),
            'getTemplate': grpc.unary_unary_rpc_method_handler(
                    servicer.getTemplate,
                    request_deserializer=agent__pb2.GetTemplateRequest.FromString,
                    response_serializer=agent__pb2.GetTemplateResponse.SerializeToString,
            ),
            'listTemplates': grpc.unary_unary_rpc_method_handler(
                    servicer.listTemplates,
                    request_deserializer=agent__pb2.ListTemplatesRequest.FromString,
                    response_serializer=agent__pb2.ListTemplatesResponse.SerializeToString,
            ),
            'getMarketplaceAgents': grpc.unary_unary_rpc_method_handler(
                    servicer.getMarketplaceAgents,
                    request_deserializer=agent__pb2.GetMarketplaceAgentsRequest.FromString,
                    response_serializer=agent__pb2.GetMarketplaceAgentsResponse.SerializeToString,
            ),
            'getMarketplaceAgentDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.getMarketplaceAgentDetail,
                    request_deserializer=agent__pb2.GetMarketplaceAgentDetailRequest.FromString,
                    response_serializer=agent__pb2.GetMarketplaceAgentDetailResponse.SerializeToString,
            ),
            'rateAgent': grpc.unary_unary_rpc_method_handler(
                    servicer.rateAgent,
                    request_deserializer=agent__pb2.RateAgentRequest.FromString,
                    response_serializer=agent__pb2.RateAgentResponse.SerializeToString,
            ),
            'useAgent': grpc.unary_unary_rpc_method_handler(
                    servicer.useAgent,
                    request_deserializer=agent__pb2.UseAgentRequest.FromString,
                    response_serializer=agent__pb2.UseAgentResponse.SerializeToString,
            ),
            'createAgentAvatar': grpc.unary_unary_rpc_method_handler(
                    servicer.createAgentAvatar,
                    request_deserializer=agent__pb2.CreateAgentAvatarRequest.FromString,
                    response_serializer=agent__pb2.CreateAgentAvatarResponse.SerializeToString,
            ),
            'getAgentAvatar': grpc.unary_unary_rpc_method_handler(
                    servicer.getAgentAvatar,
                    request_deserializer=agent__pb2.GetAgentAvatarRequest.FromString,
                    response_serializer=agent__pb2.GetAgentAvatarResponse.SerializeToString,
            ),
            'listAgentAvatars': grpc.unary_unary_rpc_method_handler(
                    servicer.listAgentAvatars,
                    request_deserializer=agent__pb2.ListAgentAvatarsRequest.FromString,
                    response_serializer=agent__pb2.ListAgentAvatarsResponse.SerializeToString,
            ),
            'deleteAgentAvatar': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteAgentAvatar,
                    request_deserializer=agent__pb2.DeleteAgentAvatarRequest.FromString,
                    response_serializer=agent__pb2.DeleteAgentAvatarResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'agent.AgentService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('agent.AgentService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AgentService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def createAgent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/createAgent',
            agent__pb2.CreateAgentRequest.SerializeToString,
            agent__pb2.CreateAgentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getAgent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/getAgent',
            agent__pb2.GetAgentRequest.SerializeToString,
            agent__pb2.AgentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteAgent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/deleteAgent',
            agent__pb2.DeleteAgentRequest.SerializeToString,
            agent__pb2.DeleteAgentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listAgents(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/listAgents',
            agent__pb2.ListAgentsRequest.SerializeToString,
            agent__pb2.ListAgentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createAgentFromTemplate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/createAgentFromTemplate',
            agent__pb2.CreateAgentFromTemplateRequest.SerializeToString,
            agent__pb2.CreateAgentFromTemplateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateAgentCoreDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/UpdateAgentCoreDetails',
            agent__pb2.UpdateAgentCoreDetailsRequest.SerializeToString,
            agent__pb2.UpdateAgentPartResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateAgentKnowledge(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/UpdateAgentKnowledge',
            agent__pb2.UpdateAgentKnowledgeRequest.SerializeToString,
            agent__pb2.UpdateAgentPartResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateAgentMcpServers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/UpdateAgentMcpServers',
            agent__pb2.UpdateAgentMcpServersRequest.SerializeToString,
            agent__pb2.UpdateAgentPartResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateAgentWorkflows(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/UpdateAgentWorkflows',
            agent__pb2.UpdateAgentWorkflowsRequest.SerializeToString,
            agent__pb2.UpdateAgentPartResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateAgentSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/UpdateAgentSettings',
            agent__pb2.UpdateAgentSettingsRequest.SerializeToString,
            agent__pb2.UpdateAgentPartResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateAgentCapabilities(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/UpdateAgentCapabilities',
            agent__pb2.UpdateAgentCapabilitiesRequest.SerializeToString,
            agent__pb2.UpdateAgentPartResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ToggleAgentVisibility(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/ToggleAgentVisibility',
            agent__pb2.ToggleAgentVisibilityRequest.SerializeToString,
            agent__pb2.ToggleAgentVisibilityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getTemplate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/getTemplate',
            agent__pb2.GetTemplateRequest.SerializeToString,
            agent__pb2.GetTemplateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listTemplates(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/listTemplates',
            agent__pb2.ListTemplatesRequest.SerializeToString,
            agent__pb2.ListTemplatesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getMarketplaceAgents(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/getMarketplaceAgents',
            agent__pb2.GetMarketplaceAgentsRequest.SerializeToString,
            agent__pb2.GetMarketplaceAgentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getMarketplaceAgentDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/getMarketplaceAgentDetail',
            agent__pb2.GetMarketplaceAgentDetailRequest.SerializeToString,
            agent__pb2.GetMarketplaceAgentDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def rateAgent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/rateAgent',
            agent__pb2.RateAgentRequest.SerializeToString,
            agent__pb2.RateAgentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def useAgent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/useAgent',
            agent__pb2.UseAgentRequest.SerializeToString,
            agent__pb2.UseAgentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createAgentAvatar(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/createAgentAvatar',
            agent__pb2.CreateAgentAvatarRequest.SerializeToString,
            agent__pb2.CreateAgentAvatarResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getAgentAvatar(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/getAgentAvatar',
            agent__pb2.GetAgentAvatarRequest.SerializeToString,
            agent__pb2.GetAgentAvatarResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listAgentAvatars(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/listAgentAvatars',
            agent__pb2.ListAgentAvatarsRequest.SerializeToString,
            agent__pb2.ListAgentAvatarsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteAgentAvatar(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent.AgentService/deleteAgentAvatar',
            agent__pb2.DeleteAgentAvatarRequest.SerializeToString,
            agent__pb2.DeleteAgentAvatarResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
