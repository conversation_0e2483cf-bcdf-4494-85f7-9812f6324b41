# Workflow Home Page

This directory contains the implementation of the workflow home page, which displays a list of workflows associated with the current user.

## Features

- **Workflow Listing**: Displays workflow cards with title, description, and updated date
- **Search**: Allows users to search for workflows by title or description
- **Sorting**: Provides options to sort workflows by update date or name
- **Pagination**: Handles large numbers of workflows with pagination controls
- **Create Workflow**: Button to create a new empty workflow
- **Workflow Selection**: Click on a workflow card to open it in the canvas

## Components

### Page Component

The main page component (`page.tsx`) handles:
- Fetching workflows from the API
- Managing state for search, sort, and pagination
- Rendering the workflow list and UI controls

### WorkflowCard Component

The `WorkflowCard` component (`@/components/workflow/WorkflowCard.tsx`) is a memoized component that displays a single workflow card. It includes:
- Title and description display
- Updated date formatting
- Keyboard navigation support
- ARIA attributes for accessibility

## API Integration

The page uses the following API functions from `@/lib/workflowApi.ts`:
- `fetchWorkflowsByUser`: Fetches workflows for the current user with pagination
- `createEmptyWorkflow`: Creates a new empty workflow
- `fetchWorkflowById`: Fetches a specific workflow by ID
- `fetchWorkflowFromBuilderUrl`: Fetches workflow data from a builder URL

## State Management

The page uses React's built-in state management with:
- `useState` for local state (workflows, search, sort, pagination)
- `useCallback` for memoized event handlers
- `useMemo` for optimized derived state (sorted workflows)

## Accessibility

The implementation includes:
- Keyboard navigation support
- ARIA attributes for screen readers
- Sufficient color contrast
- Semantic HTML structure

## Performance Optimization

Performance is optimized through:
- Memoized components to prevent unnecessary re-renders
- Pagination to limit the number of items rendered
- Efficient state updates
- Lazy loading of workflow data

## Usage

The home page is the entry point after login. Users can:
1. View their workflows
2. Search and sort to find specific workflows
3. Create new workflows
4. Select workflows to open in the canvas

## Related Files

- `frontend/src/lib/workflowApi.ts`: API functions for workflow operations
- `frontend/src/components/workflow/WorkflowCard.tsx`: Memoized workflow card component
- `frontend/src/app/page.tsx`: Canvas page that loads selected workflows
