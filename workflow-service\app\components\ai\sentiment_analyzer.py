from typing import Dict, Any, List, ClassVar
import importlib

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput
from app.models.workflow_builder.components import Output


class SentimentAnalyzer(BaseAgentComponent):
    """
    Analyzes input text to determine its sentiment.

    This component evaluates the sentiment of the provided text and
    categorizes it as Positive, Negative, or Neutral.
    """

    name: ClassVar[str] = "SentimentAnalyzer"
    display_name: ClassVar[str] = "Sentiment Analyzer"
    description: ClassVar[str] = (
        "Analyzes text to determine its sentiment (Positive, Negative, Neutral)."
    )

    icon: ClassVar[str] = "BarChart"

    # Define component-specific inputs
    component_inputs: ClassVar[List[InputBase]] = [
        # Text to analyze - connection handle
        HandleInput(
            name="text_handle",
            display_name="Text to Analyze",
            required=True,
            is_handle=True,
            input_types=["string", "Any"],
            info="Connect the text whose sentiment needs to be evaluated.",
        ),
        # Text to analyze - direct input in inspector
        StringInput(
            name="text",
            display_name="Text to Analyze (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The text whose sentiment needs to be evaluated. Used if no connection is provided.",
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="sentiment", display_name="Sentiment", output_type="string"),
        Output(name="confidence", display_name="Confidence", output_type="float"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the SentimentAnalyzer.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Analyzes the sentiment of the provided text.

        Args:
            **kwargs: Contains the input values:
                - text: The text to analyze
                - api_key: API key for the LLM service
                - model_name: Model to use
                - temperature: Temperature for generation

        Returns:
            A dictionary with:
                - sentiment: The detected sentiment (Positive, Negative, or Neutral)
                - confidence: A confidence score between 0 and 1
                - error: An error message if the operation failed
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        temperature = kwargs.get(
            "temperature", 0.3
        )  # Lower temperature for more deterministic analysis

        text_handle = kwargs.get("text_handle")
        text_direct = kwargs.get("text")

        # Process inputs - prioritize handle inputs over direct inputs
        text = text_handle if text_handle is not None else text_direct

        # Validate inputs
        if not text:
            return {
                "error": "Text to analyze is missing. Please connect text or provide it directly."
            }
        if not api_key:
            return {"error": "API key is required."}

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Create system prompt for sentiment analysis
            system_prompt = """You are a sentiment analysis assistant. Analyze the sentiment of the provided text and categorize it as one of the following:
1. Positive
2. Negative
3. Neutral

Also provide a confidence score between 0 and 1, where 1 is the highest confidence.
Format your response as a JSON object with two fields: "sentiment" and "confidence".
Example: {"sentiment": "Positive", "confidence": 0.85}"""

            # Create user prompt
            user_prompt = f"Text to analyze: {text}"

            # Make API call
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )

            # Extract content from response
            result_text = response.choices[0].message.content.strip()

            # Parse the JSON response
            import json

            try:
                result = json.loads(result_text)
                sentiment = result.get("sentiment", "Unknown")
                confidence = float(result.get("confidence", 0.0))

                print(
                    f"  Sentiment analysis completed successfully: {sentiment} (confidence: {confidence})"
                )
                return {"sentiment": sentiment, "confidence": confidence}
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract sentiment directly from text
                if "positive" in result_text.lower():
                    sentiment = "Positive"
                elif "negative" in result_text.lower():
                    sentiment = "Negative"
                else:
                    sentiment = "Neutral"

                print(f"  Sentiment analysis completed with fallback parsing: {sentiment}")
                return {
                    "sentiment": sentiment,
                    "confidence": 0.5,  # Default confidence when parsing fails
                }

        except Exception as e:
            error_msg = f"Error analyzing sentiment: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
