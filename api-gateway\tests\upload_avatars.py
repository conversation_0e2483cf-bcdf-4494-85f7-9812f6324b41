#!/usr/bin/env python3
"""
Upload SVG Files as Agent Avatars

This script:
1. Finds all SVG files in the Downloads folder
2. Gets presigned URLs for uploading to GCS using the GCSUtility class
3. Uploads the SVG files to GCS
4. Creates agent avatars with the uploaded URLs

Usage:
    python upload_avatars.py
"""

import os
import glob
import requests
import mimetypes
import sys
from pprint import pprint

# Add the current directory to the path so we can import the GCSUtility
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.utils.GCSUtility.gcs import GCSUtility

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"  # Update with your API gateway URL
DOWNLOADS_PATH = "/Users/<USER>/Downloads/employee-avatars"
SVG_PATTERN = "*.svg"  # Pattern to match SVG files
MAX_FILES = 30  # Maximum number of files to process

# Replace with your actual auth token or login credentials
AUTH_TOKEN = "your_auth_token_here"  # Replace with your actual token
# Or use these credentials to get a token
EMAIL = ""  # Replace with your admin email
PASSWORD = ""   # Replace with your admin password

def get_auth_token():
    """Get authentication token by logging in."""
    login_url = f"{API_BASE_URL}/auth/login"
    login_data = {
        "email": EMAIL,
        "password": PASSWORD
    }

    try:
        response = requests.post(login_url, json=login_data)
        response.raise_for_status()
        return response.json().get("access_token")
    except requests.exceptions.RequestException as e:
        print(f"Login failed: {e}")
        return None

def get_headers(token):
    """Get headers with authentication token."""
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def get_presigned_url(file_name, file_type):
    """Get a presigned URL for uploading to GCS using the GCSUtility class directly."""
    try:
        # Initialize the GCSUtility class
        gcs = GCSUtility()

        # Generate the presigned URL
        result = gcs.generate_presigned_url(
            file_name=file_name,
            file_type=file_type,
            file_path="agent-avatars"
        )

        if not result["success"]:
            print(f"Failed to generate presigned URL: {result.get('message', 'Unknown error')}")
            return None

        return result["url"]
    except Exception as e:
        print(f"Failed to get presigned URL: {e}")
        return None

def upload_to_gcs(presigned_url, file_path, file_type):
    """Upload file to GCS using presigned URL."""
    try:
        with open(file_path, 'rb') as file:
            headers = {"Content-Type": file_type}
            response = requests.put(presigned_url, data=file, headers=headers)
            response.raise_for_status()

            # Extract the GCS URL from the presigned URL
            gcs_url = presigned_url.split('?')[0]
            return gcs_url
    except requests.exceptions.RequestException as e:
        print(f"Failed to upload file to GCS: {e}")
        return None
    except IOError as e:
        print(f"Failed to read file: {e}")
        return None

def create_agent_avatar(token, url):
    """Create an agent avatar with the given URL."""
    api_url = f"{API_BASE_URL}/agent-avatars"
    payload = {"url": url}

    try:
        response = requests.post(api_url, json=payload, headers=get_headers(token))
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Failed to create agent avatar: {e}")
        if hasattr(response, 'text'):
            print(f"Response: {response.text}")
        return None

def main():
    """Main function to upload SVG files as agent avatars."""
    # Get authentication token
    token = AUTH_TOKEN
    if not token or token == "your_auth_token_here":
        token = get_auth_token()
        if not token:
            print("Authentication failed. Please check your credentials.")
            return

    print("Authentication successful.")

    # Find SVG files in Downloads folder
    svg_files = glob.glob(os.path.join(DOWNLOADS_PATH, SVG_PATTERN))
    if not svg_files:
        print(f"No SVG files found in {DOWNLOADS_PATH}")
        return

    print(f"Found {len(svg_files)} SVG files.")

    # Process files (limit to MAX_FILES)
    created_avatars = []
    for file_path in svg_files[:MAX_FILES]:
        file_name = os.path.basename(file_path)
        file_type = mimetypes.guess_type(file_path)[0] or "image/svg+xml"

        print(f"\nProcessing file: {file_name}")

        # Get presigned URL using GCSUtility directly
        presigned_url = get_presigned_url(file_name, file_type)
        if not presigned_url:
            print(f"Skipping {file_name} due to presigned URL failure.")
            continue

        print(f"Got presigned URL for {file_name}")

        # Upload to GCS
        gcs_url = upload_to_gcs(presigned_url, file_path, file_type)
        if not gcs_url:
            print(f"Skipping {file_name} due to GCS upload failure.")
            continue

        print(f"Uploaded {file_name} to GCS: {gcs_url}")

        # Create agent avatar
        avatar_response = create_agent_avatar(token, gcs_url)
        if not avatar_response:
            print(f"Failed to create agent avatar for {file_name}")
            continue

        print(f"Created agent avatar: {avatar_response.get('message')}")
        created_avatar = avatar_response.get('avatar')
        if created_avatar:
            created_avatars.append(created_avatar)
            print(f"Avatar ID: {created_avatar.get('id')}")

    # Print summary
    print(f"\nSuccessfully created {len(created_avatars)} agent avatars.")
    if created_avatars:
        print("\nCreated avatars:")
        for avatar in created_avatars:
            print(f"ID: {avatar.get('id')}, URL: {avatar.get('url')}")

if __name__ == "__main__":
    main()
