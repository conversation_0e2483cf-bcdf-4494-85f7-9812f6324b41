"""
Specialized Kafka logger for the ToolExecutor.

This module provides a specialized logger for the ToolExecutor that sends logs to a dedicated Kafka topic.
"""

import logging
import re

from app.utils.logging_config import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, setup_logger
from app.config.config import settings

# Regular expressions to match the specific log messages we want to send to Kafka
# These patterns help filter which log messages get sent to the dedicated Kafka topic
LOG_PATTERNS = [
    # Tool execution related patterns
    r"Tool name: .+ for request_id: .+",
    r"Processing payload with component .+ for request_id: .+",
    r"Component .+ processed payload successfully for request_id: .+",
    r"ToolExecutor returning (success|error).*",
    r"ToolExecutor received payload: .*",
    r"Executing tool for request_id: .*",
    r"Error executing tool .+ for request_id .*"
]

class ToolExecutorKafkaFilter(logging.Filter):
    """
    A filter that only allows specific ToolExecutor log messages to be sent to Kafka.

    This ensures that only relevant tool executor logs are sent to the dedicated Kafka topic,
    while other logs continue to be processed by the standard logging handlers.
    """
    def __init__(self):
        super().__init__()
        # Compile the patterns for efficiency
        self.patterns = [re.compile(pattern) for pattern in LOG_PATTERNS]

    def filter(self, record):
        """
        Check if the log record matches any of our patterns.

        Args:
            record: The log record to check

        Returns:
            True if the record should be logged, False otherwise
        """
        # Get the formatted message
        message = record.getMessage()

        # Check if the message matches any of our patterns
        for pattern in self.patterns:
            if pattern.search(message):
                return True

        # If we get here, the message didn't match any pattern
        return False


def setup_tool_executor_logger() -> logging.Logger:
    """
    Set up a specialized logger for the ToolExecutor that sends logs to a dedicated Kafka topic.

    This function creates a logger that:
    1. Logs to console and file like normal loggers
    2. Additionally sends filtered logs to a dedicated Kafka topic for tool executor logs

    Returns:
        The configured logger
    """
    # First, set up a normal logger with Kafka disabled
    # We need to set with_request_id=False to get the actual logger, not the adapter
    logger_name = "ToolExecutor"
    base_logger = logging.getLogger(logger_name)

    # Remove any existing handlers
    for handler in base_logger.handlers[:]:
        base_logger.removeHandler(handler)

    # Set up the logger with normal handlers (console and file)
    logger = setup_logger(logger_name, use_kafka=False, with_request_id=False)

    # Now add a Kafka handler with our filter, using the dedicated topic
    try:
        kafka_handler = KafkaLogHandler(
            bootstrap_servers=settings.kafka_bootstrap_servers,
            topic=settings.kafka_tool_executor_logs_topic,
            level=logging.INFO
        )

        # Add our filter to the Kafka handler
        kafka_filter = ToolExecutorKafkaFilter()
        kafka_handler.addFilter(kafka_filter)

        # Add the handler to the logger
        logger.addHandler(kafka_handler)
        logger.info(f"Kafka logging enabled for ToolExecutor, sending to dedicated topic: {settings.kafka_tool_executor_logs_topic}")
    except Exception as e:
        logger.error(f"Failed to initialize Kafka logging for ToolExecutor: {e}")

    # Wrap with RequestIdAdapter and return
    from app.utils.logging_config import RequestIdAdapter
    return RequestIdAdapter(logger, {})
