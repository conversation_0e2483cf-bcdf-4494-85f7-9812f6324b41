from typing import Optional

import grpc
from app.core.config import settings
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.shared.constants import (
    CHANNEL_TYPE_TO_INT,
    CHAT_TYPE_TO_INT,
    INT_TO_CHANNEL_TYPE,
    INT_TO_CHAT_TYPE,
    INT_TO_SENDER_TYPE,
    SENDER_TYPE_TO_INT,
    ChannelType,
    ChatType,
    SenderType,
)
from fastapi import HTTPException
from google.protobuf import any_pb2, struct_pb2, wrappers_pb2
from google.protobuf.json_format import MessageToDict

# Reference wrapper types to ensure registration for Any unpacking
_ = (
    wrappers_pb2.StringValue,
    wrappers_pb2.Int64Value,
    wrappers_pb2.DoubleValue,
    wrappers_pb2.BoolValue,
    struct_pb2.Struct,
    struct_pb2.ListValue,
)


class CommunicationServiceClient:
    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.COMMUNICATION_SERVICE_HOST}:{settings.COMMUNICATION_SERVICE_PORT}"
        )
        self.stub = communication_pb2_grpc.CommunicationServiceStub(self.channel)

    def _handle_error(self, e: grpc._channel._InactiveRpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        else:
            raise HTTPException(status_code=500, detail=details)

    def _convert_grpc_response_enums(self, response_dict):
        """Convert integer enum values in the gRPC response to string enum values."""
        if "channel" in response_dict and isinstance(response_dict["channel"], int):
            response_dict["channel"] = INT_TO_CHANNEL_TYPE.get(
                response_dict["channel"], ChannelType.CHANNEL_TYPE_UNSPECIFIED
            )

        if "chatType" in response_dict and isinstance(response_dict["chatType"], int):
            response_dict["chatType"] = INT_TO_CHAT_TYPE.get(
                response_dict["chatType"], ChatType.CHAT_TYPE_UNSPECIFIED
            )

        if "senderType" in response_dict and isinstance(
            response_dict["senderType"], int
        ):
            response_dict["senderType"] = INT_TO_SENDER_TYPE.get(
                response_dict["senderType"], SenderType.SENDER_TYPE_UNSPECIFIED
            )

        return response_dict

    def _process_conversations_list(self, response_dict):
        """Process conversation list to convert enum values and ensure complete metadata."""
        # Ensure data key exists
        if "data" not in response_dict:
            response_dict["data"] = []
        elif isinstance(response_dict["data"], list):
            for conv in response_dict["data"]:
                self._convert_grpc_response_enums(conv)

        # Ensure metadata has all required fields
        if "metadata" not in response_dict:
            response_dict["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": 1,
                "pageSize": 10,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }
        else:
            metadata = response_dict["metadata"]
            metadata.setdefault("total", 0)
            metadata.setdefault("totalPages", 0)
            metadata.setdefault("currentPage", 1)
            metadata.setdefault("pageSize", 10)
            metadata.setdefault("hasNextPage", False)
            metadata.setdefault("hasPreviousPage", False)

        return response_dict

    def _process_messages_list(self, response_dict):
        """Process message list to convert enum values and ensure complete metadata."""
        # Ensure data key exists
        if "data" not in response_dict:
            response_dict["data"] = []
        elif isinstance(response_dict["data"], list):
            for msg in response_dict["data"]:
                self._convert_grpc_response_enums(msg)

        # Ensure metadata has all required fields
        if "metadata" not in response_dict:
            response_dict["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": 1,
                "pageSize": 10,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }
        else:
            metadata = response_dict["metadata"]
            metadata.setdefault("total", 0)
            metadata.setdefault("totalPages", 0)
            metadata.setdefault("currentPage", 1)
            metadata.setdefault("pageSize", 10)
            metadata.setdefault("hasNextPage", False)
            metadata.setdefault("hasPreviousPage", False)

        return response_dict

    async def create_conversation(
        self,
        userId: str,
        agentId: str,
        title: str,
        channel: ChannelType,
        chatType: str,
        summary: Optional[str] = None,
    ):
        # Convert string enum to integer for gRPC
        channel_int = CHANNEL_TYPE_TO_INT.get(channel, 0)

        # Convert chatType to string if it's an enum
        chat_type_int = CHAT_TYPE_TO_INT.get(chatType, 0)

        request = communication_pb2.CreateConversationRequest(
            userId=userId,
            agentId=agentId,
            title=title,
            channel=channel_int,
            chatType=chat_type_int,
            summary=summary,
        )

        try:
            response = self.stub.createConversation(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            print(response_dict)
            # Convert integer enums to string enums
            return self._convert_grpc_response_enums(response_dict)
        except grpc._channel._InactiveRpcError as e:
            raise self._handle_error(e)

    async def get_conversation(self, conversationId: str, userId: str):
        request = communication_pb2.GetConversationRequest(
            conversationId=conversationId, userId=userId
        )

        try:
            response = self.stub.getConversation(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Convert integer enums to string enums
            return self._convert_grpc_response_enums(response_dict)
        except grpc._channel._InactiveRpcError as e:
            raise self._handle_error(e)

    async def delete_conversation(self, conversationId: str, userId: str):
        request = communication_pb2.DeleteConversationRequest(
            conversationId=conversationId, userId=userId
        )

        try:
            response = self.stub.deleteConversation(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc._channel._InactiveRpcError as e:
            raise self._handle_error(e)

    async def list_conversations(
        self,
        userId: str,
        agentId: str,
        channel: Optional[ChannelType] = None,
        chatType: Optional[str] = None,
        page: int = 1,
        limit: int = 10,
    ):
        # Convert string enum to integer for gRPC if provided
        channel_int = CHANNEL_TYPE_TO_INT.get(channel, None) if channel else None

        # Convert chatType to string if it's an enum
        chat_type_int = CHAT_TYPE_TO_INT.get(chatType, None) if chatType else None

        request = communication_pb2.ListConversationsRequest(
            userId=userId,
            agentId=agentId,
            channel=channel_int,
            chatType=chat_type_int,
            page=page,
            limit=limit,
        )

        try:
            response = self.stub.listConversations(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Debug log the raw response
            print(f"Raw listConversations response: {response_dict}")
            # Process conversation list to convert enum values
            processed_response = self._process_conversations_list(response_dict)
            print(f"Processed listConversations response: {processed_response}")
            return processed_response
        except grpc._channel._InactiveRpcError as e:
            raise self._handle_error(e)

    async def create_message(
        self,
        conversationId: str,
        senderType: SenderType,
        content: Optional[str] = None,
        workflowId: Optional[str] = None,
        workflowResponse: Optional[dict] = None,
        userId: Optional[str] = None,
    ):
        # Convert string enums to integers for gRPC
        sender_type_int = SENDER_TYPE_TO_INT.get(senderType, 0)

        request = communication_pb2.CreateMessageRequest(
            conversationId=conversationId,
            senderType=sender_type_int,
            content=content,
            workflowId=workflowId,
            workflowResponse=workflowResponse,
            userId=userId,
        )

        try:
            response = self.stub.createMessage(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Convert integer enums to string enums
            return self._convert_grpc_response_enums(response_dict)
        except grpc._channel._InactiveRpcError as e:
            raise self._handle_error(e)

    async def delete_message(self, messageId: str, userId: str):
        request = communication_pb2.DeleteMessageRequest(
            messageId=messageId, userId=userId
        )

        try:
            response = self.stub.deleteMessage(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc._channel._InactiveRpcError as e:
            raise self._handle_error(e)

    async def list_messages(
        self, conversationId: str, userId: str, page: int = 1, limit: int = 10
    ):
        request = communication_pb2.ListMessagesRequest(
            conversationId=conversationId, page=page, limit=limit, userId=userId
        )

        try:
            response = self.stub.listMessages(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Debug log the raw response
            print(f"Raw listMessages response: {response_dict}")
            # Process messages list to convert enum values
            processed_response = self._process_messages_list(response_dict)
            print(f"Processed listMessages response: {processed_response}")
            return processed_response
        except grpc._channel._InactiveRpcError as e:
            raise self._handle_error(e)
