# OpenAI API Key
OPENAI_API_KEY=your-openai-api-key-here
LLM_MODEL=gpt-4o-mini


# Kafka configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9094
# KAFKA_BOOTSTRAP_SERVERS=localhost:9092
K<PERSON>KA_AGENT_CREATION_TOPIC=agent_creation_requests
KAFKA_AGENT_CHAT_TOPIC=agent_chat_requests
KAFKA_AGENT_RESPONSE_TOPIC=agent_chat_responses
KAFKA_CONSUMER_GROUP=autogen-agent-service-group

# Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# API Gateway
API_GATEWAY_URL=h
API_GATEWAY_KEY=your_api_gateway_key_here

# Workflow API Gateway
WORKFLOW_API_GATEWAY_URL="API_GATEWAY_URL"
WORKFLOW_API_GATEWAY_KEY=your_workflow_api_key_here
