2025-05-28 16:17:36 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-05-28\ComponentSystem_16-17-36.log
2025-05-28 16:17:36 - ComponentSystem - INFO - [get_component_manager:1418] Creating new global ComponentManager instance
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:87] Initializing ComponentManager with Kafka bootstrap servers: **************:9092
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:92] Kafka Consumer Topic: node-execution-request
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:93] Kafka Results Topic: node_results
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:94] Kafka Consumer Group ID: node_executor_service
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:95] Kafka Producer Request Timeout: 60000ms
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:98] Kafka Consumer Fetch Min Bytes: 100
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:101] Kafka Consumer Fetch Max Wait: 500ms
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:104] Kafka Consumer Session Timeout: 10000ms
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:107] Kafka Consumer Heartbeat Interval: 3000ms
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:110] Default Node Retries: 3
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:128] Initializing ThreadPoolExecutor with 4 workers
2025-05-28 16:17:36 - ComponentSystem - INFO - [__init__:132] ThreadPoolExecutor initialized
2025-05-28 16:17:36 - ComponentSystem - INFO - [discover_components:141] Discovering components
2025-05-28 16:17:36 - ComponentSystem - INFO - [discover_components:142] Component registry before discovery: []
2025-05-28 16:17:36 - ComponentSystem - INFO - [discover_components:153] Discovered components: []
2025-05-28 16:17:36 - ComponentSystem - INFO - [discover_component_modules:1360] Discovering component modules
2025-05-28 16:17:36 - ComponentSystem - INFO - [discover_component_modules:1376] Found 17 potential component files: ['alter_metadata_component.py', 'api_component.py', 'combine_text_component.py', 'combine_text_component_new.py', 'conditional_component.py', 'convert_script_data_component.py', 'data_to_dataframe_component.py', 'doc_component.py', 'dynamic_combine_text_component.py', 'gmail_component.py', 'gmail_tracker_component.py', 'id_generator_component.py', 'merge_data_component.py', 'message_to_data_component.py', 'select_data_component.py', 'split_text_component.py', 'text_analysis_component.py']
2025-05-28 16:17:36 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.alter_metadata_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.alter_metadata_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.api_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.api_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.combine_text_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.combine_text_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.combine_text_component_new
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.combine_text_component_new
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.conditional_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: ConditionalNode -> ConditionalExecutor
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode']
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.conditional_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.convert_script_data_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.convert_script_data_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.data_to_dataframe_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.data_to_dataframe_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.doc_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.doc_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.dynamic_combine_text_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextExecutor -> CombineTextExecutor
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor']
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.dynamic_combine_text_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.gmail_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor', 'GmailComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.gmail_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.gmail_tracker_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.gmail_tracker_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.id_generator_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.id_generator_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.merge_data_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-05-28 16:17:37 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'ConditionalNode', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.merge_data_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.message_to_data_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.message_to_data_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.select_data_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.select_data_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.split_text_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.split_text_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.text_analysis_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.text_analysis_component
2025-05-28 16:17:37 - ComponentSystem - INFO - [discover_component_modules:1396] Imported 17 component modules: ['app.components.alter_metadata_component', 'app.components.api_component', 'app.components.combine_text_component', 'app.components.combine_text_component_new', 'app.components.conditional_component', 'app.components.convert_script_data_component', 'app.components.data_to_dataframe_component', 'app.components.doc_component', 'app.components.dynamic_combine_text_component', 'app.components.gmail_component', 'app.components.gmail_tracker_component', 'app.components.id_generator_component', 'app.components.merge_data_component', 'app.components.message_to_data_component', 'app.components.select_data_component', 'app.components.split_text_component', 'app.components.text_analysis_component']
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: **************:9092
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-05-28 16:17:37 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-05-28 16:17:44 - ComponentSystem - INFO - [start_component:356] Kafka consumer started successfully for component: ApiRequestNode
2025-05-28 16:17:44 - ComponentSystem - INFO - [start_component:378] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-05-28 16:17:44 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-05-28 16:17:44 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=258, TaskID=ApiRequestNode-node-execution-request-0-258-1748429264.6171381
2025-05-28 16:17:44 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-258-1748429264.6171381, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://interview.rapidinnovation.dev/api/v1/interviews/",
    "method": "POST",
    "query_params": null,
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo"
    },
    "body": {
      "value": "{   \"candidate_name\": \"ac\",   \"candidate_email\": \"<EMAIL>\",   \"skill_set\": \"AI\",   \"job_role\": \"dev\",   \"experience_level\": 0,   \"available_from\": \"1748467200\",   \"available_until\": \"1748481600\",   \"interview_duration\": 30,   \"resume_link\": \"https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf\",   \"jd_link\": \"https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf\" }"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "2980ce0c-d1e1-4f80-9fe5-4bd325752312",
  "correlation_id": "1d7e4067-fa4f-411f-bf83-bc606d93790c"
}
2025-05-28 16:17:44 - ComponentSystem - INFO - [_process_message:713] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Executing tool ApiRequestNode for RequestID=2980ce0c-d1e1-4f80-9fe5-4bd325752312, TaskID=ApiRequestNode-node-execution-request-0-258-1748429264.6171381
2025-05-28 16:18:08 - ComponentSystem - INFO - [_process_message:717] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Tool ApiRequestNode executed successfully for RequestID=2980ce0c-d1e1-4f80-9fe5-4bd325752312, TaskID=ApiRequestNode-node-execution-request-0-258-1748429264.6171381
2025-05-28 16:18:08 - ComponentSystem - INFO - [_send_result:1005] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Preparing to send result for component ApiRequestNode, RequestID=2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:18:08 - ComponentSystem - INFO - [get_producer:244] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Creating Kafka producer for component ApiRequestNode with configuration:
2025-05-28 16:18:08 - ComponentSystem - INFO - [get_producer:247] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c]   Bootstrap Servers: **************:9092
2025-05-28 16:18:08 - ComponentSystem - INFO - [get_producer:248] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c]   Acks: all (ensuring message is written to all in-sync replicas)
2025-05-28 16:18:08 - ComponentSystem - INFO - [get_producer:252] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c]   Request Timeout: 60000ms
2025-05-28 16:18:08 - ComponentSystem - INFO - [get_producer:255] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-05-28 16:18:08 - ComponentSystem - INFO - [get_producer:259] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Creating new Kafka producer for component: ApiRequestNode with servers: **************:9092
2025-05-28 16:18:10 - ComponentSystem - INFO - [get_producer:266] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Kafka producer started successfully for component: ApiRequestNode
2025-05-28 16:18:10 - ComponentSystem - INFO - [_send_result:1118] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Sending Kafka response: RequestID=2980ce0c-d1e1-4f80-9fe5-4bd325752312, Response={
  "request_id": "2980ce0c-d1e1-4f80-9fe5-4bd325752312",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "2980ce0c-d1e1-4f80-9fe5-4bd325752312",
    "status": "success",
    "response": {
      "result": {
        "status": "success",
        "message": "Interview scheduled successfully",
        "interview_id": "c2c5817a-223a-4425-8853-eea54620145d"
      },
      "status_code": 200,
      "response_headers": {
        "Date": "Wed, 28 May 2025 10:48:07 GMT",
        "Content-Type": "application/json",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Server": "cloudflare",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Cf-Cache-Status": "DYNAMIC",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=8ijgwuKLbzbIKZ1uJNEU0svQ6YZh%2B90YDj9fXGhRTw%2B9u%2F2e55citUXNBYM6Rjag6H6hx%2BbdAREjtA54zLs9LnqzZ8AJGa6CLwghgP0NEpacfOhi9hsRLOqk%2FU1Tw%2BfypfUwqiEc0s0L\"}]}",
        "Content-Encoding": "gzip",
        "CF-RAY": "946d2cf918bd9d86-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://interview.rapidinnovation.dev/api/v1/interviews/",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748429290.086049
}
2025-05-28 16:18:10 - ComponentSystem - INFO - [_send_result:1127] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Sent result for component ApiRequestNode to topic node_results for RequestID=2980ce0c-d1e1-4f80-9fe5-4bd325752312
2025-05-28 16:18:10 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Successfully committed offset 259 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-258-1748429264.6171381
2025-05-28 16:18:10 - ComponentSystem - INFO - [_process_message:936] [ReqID:2980ce0c-d1e1-4f80-9fe5-4bd325752312] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=258, TaskID=ApiRequestNode-node-execution-request-0-258-1748429264.6171381
2025-05-28 16:18:15 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=259, TaskID=ApiRequestNode-node-execution-request-0-259-1748429295.5941174
2025-05-28 16:18:15 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-259-1748429295.5941174, Payload={
  "tool_name": "SelectDataComponent",
  "tool_parameters": {
    "input_data": {
      "status": "success",
      "message": "Interview scheduled successfully",
      "interview_id": "c2c5817a-223a-4425-8853-eea54620145d"
    },
    "data_type": "Auto-Detect",
    "search_mode": "Smart Search",
    "field_matching_mode": "Key-based Only",
    "selector": "interview_id"
  },
  "request_id": "8b3efd54-60fd-4bae-85a5-fca566afb892",
  "correlation_id": "1d7e4067-fa4f-411f-bf83-bc606d93790c"
}
2025-05-28 16:18:15 - ComponentSystem - INFO - [_process_message:713] [ReqID:8b3efd54-60fd-4bae-85a5-fca566afb892] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Executing tool SelectDataComponent for RequestID=8b3efd54-60fd-4bae-85a5-fca566afb892, TaskID=ApiRequestNode-node-execution-request-0-259-1748429295.5941174
2025-05-28 16:18:15 - ComponentSystem - INFO - [_process_message:717] [ReqID:8b3efd54-60fd-4bae-85a5-fca566afb892] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Tool SelectDataComponent executed successfully for RequestID=8b3efd54-60fd-4bae-85a5-fca566afb892, TaskID=ApiRequestNode-node-execution-request-0-259-1748429295.5941174
2025-05-28 16:18:15 - ComponentSystem - INFO - [_send_result:1005] [ReqID:8b3efd54-60fd-4bae-85a5-fca566afb892] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Preparing to send result for component ApiRequestNode, RequestID=8b3efd54-60fd-4bae-85a5-fca566afb892
2025-05-28 16:18:15 - ComponentSystem - INFO - [_send_result:1118] [ReqID:8b3efd54-60fd-4bae-85a5-fca566afb892] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Sending Kafka response: RequestID=8b3efd54-60fd-4bae-85a5-fca566afb892, Response={
  "request_id": "8b3efd54-60fd-4bae-85a5-fca566afb892",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "8b3efd54-60fd-4bae-85a5-fca566afb892",
    "status": "success",
    "result": {
      "output_data": "c2c5817a-223a-4425-8853-eea54620145d"
    }
  },
  "status": "success",
  "timestamp": 1748429295.6108825
}
2025-05-28 16:18:15 - ComponentSystem - INFO - [_send_result:1127] [ReqID:8b3efd54-60fd-4bae-85a5-fca566afb892] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Sent result for component ApiRequestNode to topic node_results for RequestID=8b3efd54-60fd-4bae-85a5-fca566afb892
2025-05-28 16:18:16 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:8b3efd54-60fd-4bae-85a5-fca566afb892] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Successfully committed offset 260 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-259-1748429295.5941174
2025-05-28 16:18:16 - ComponentSystem - INFO - [_process_message:936] [ReqID:8b3efd54-60fd-4bae-85a5-fca566afb892] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=259, TaskID=ApiRequestNode-node-execution-request-0-259-1748429295.5941174
2025-05-28 16:19:09 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=260, TaskID=ApiRequestNode-node-execution-request-0-260-1748429349.7149599
2025-05-28 16:19:09 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-260-1748429349.7149599, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "https://interview.rapidinnovation.dev/api/v1/interviews",
    "num_additional_inputs": 1,
    "separator": "/",
    "input_1": "c2c5817a-223a-4425-8853-eea54620145d",
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "10d17759-47e3-4168-a05f-5c0607ce6a03",
  "correlation_id": "1d7e4067-fa4f-411f-bf83-bc606d93790c"
}
2025-05-28 16:19:09 - ComponentSystem - INFO - [_process_message:713] [ReqID:10d17759-47e3-4168-a05f-5c0607ce6a03] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Executing tool CombineTextComponent for RequestID=10d17759-47e3-4168-a05f-5c0607ce6a03, TaskID=ApiRequestNode-node-execution-request-0-260-1748429349.7149599
2025-05-28 16:19:09 - ComponentSystem - INFO - [_process_message:717] [ReqID:10d17759-47e3-4168-a05f-5c0607ce6a03] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Tool CombineTextComponent executed successfully for RequestID=10d17759-47e3-4168-a05f-5c0607ce6a03, TaskID=ApiRequestNode-node-execution-request-0-260-1748429349.7149599
2025-05-28 16:19:09 - ComponentSystem - INFO - [_send_result:1005] [ReqID:10d17759-47e3-4168-a05f-5c0607ce6a03] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Preparing to send result for component ApiRequestNode, RequestID=10d17759-47e3-4168-a05f-5c0607ce6a03
2025-05-28 16:19:09 - ComponentSystem - INFO - [_send_result:1118] [ReqID:10d17759-47e3-4168-a05f-5c0607ce6a03] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Sending Kafka response: RequestID=10d17759-47e3-4168-a05f-5c0607ce6a03, Response={
  "request_id": "10d17759-47e3-4168-a05f-5c0607ce6a03",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "10d17759-47e3-4168-a05f-5c0607ce6a03",
    "status": "success",
    "result": {
      "status": "success",
      "result": "https://interview.rapidinnovation.dev/api/v1/interviews/c2c5817a-223a-4425-8853-eea54620145d"
    }
  },
  "status": "success",
  "timestamp": 1748429349.7287674
}
2025-05-28 16:19:09 - ComponentSystem - INFO - [_send_result:1127] [ReqID:10d17759-47e3-4168-a05f-5c0607ce6a03] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Sent result for component ApiRequestNode to topic node_results for RequestID=10d17759-47e3-4168-a05f-5c0607ce6a03
2025-05-28 16:19:10 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:10d17759-47e3-4168-a05f-5c0607ce6a03] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Successfully committed offset 261 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-260-1748429349.7149599
2025-05-28 16:19:10 - ComponentSystem - INFO - [_process_message:936] [ReqID:10d17759-47e3-4168-a05f-5c0607ce6a03] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=260, TaskID=ApiRequestNode-node-execution-request-0-260-1748429349.7149599
2025-05-28 16:20:19 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=261, TaskID=ApiRequestNode-node-execution-request-0-261-1748429419.9923577
2025-05-28 16:20:19 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-261-1748429419.9923577, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "interview_questions": [
        {
          "agenda": "Mutual introductions and interview overview",
          "questions": [
            "Can you briefly introduce yourself and share what excites you about the field of machine learning?",
            "What motivated you to apply for the Machine Learning Intern position at Rapid Innovation LLC?",
            "How do you see your experience in front-end engineering contributing to a role focused on machine learning and AI?"
          ]
        },
        {
          "agenda": "Discuss understanding of 'Attention is All You Need'",
          "questions": [
            "Can you explain the key concepts of the 'Attention is All You Need' paper and how they apply to transformer models?",
            "How would you implement a transformer model based on the principles outlined in the paper?",
            "What challenges do you anticipate when applying the attention mechanism in real-world NLP tasks?"
          ]
        },
        {
          "agenda": "Explore experience with React and Next.js projects",
          "questions": [
            "Can you describe a project where you used React and Next.js to solve a complex problem?",
            "How do you ensure performance optimization in React and Next.js applications, and how might these skills transfer to optimizing machine learning models?",
            "Given your experience with front-end technologies, how would you approach integrating a machine learning model into a web application?"
          ]
        }
      ]
    },
    "num_additional_inputs": 1,
    "merge_strategy": "Deep Merge",
    "input_1": {
      "agenda": "Mutual introductions and interview overview",
      "questions": [
        "Can you briefly introduce yourself and share what excites you about the field of machine learning?",
        "What motivated you to apply for the Machine Learning Intern position at Rapid Innovation LLC?",
        "How do you see your experience in front-end engineering contributing to a role focused on machine learning and AI?"
      ]
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "38089d77-a0e5-4549-8eb1-93945a27cab0",
  "correlation_id": "1d7e4067-fa4f-411f-bf83-bc606d93790c"
}
2025-05-28 16:20:19 - ComponentSystem - INFO - [_process_message:713] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Executing tool MergeDataComponent for RequestID=38089d77-a0e5-4549-8eb1-93945a27cab0, TaskID=ApiRequestNode-node-execution-request-0-261-1748429419.9923577
2025-05-28 16:20:20 - ComponentSystem - INFO - [_process_message:717] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Tool MergeDataComponent executed successfully for RequestID=38089d77-a0e5-4549-8eb1-93945a27cab0, TaskID=ApiRequestNode-node-execution-request-0-261-1748429419.9923577
2025-05-28 16:20:20 - ComponentSystem - INFO - [_send_result:1005] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Preparing to send result for component ApiRequestNode, RequestID=38089d77-a0e5-4549-8eb1-93945a27cab0
2025-05-28 16:20:20 - ComponentSystem - INFO - [_send_result:1118] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Sending Kafka response: RequestID=38089d77-a0e5-4549-8eb1-93945a27cab0, Response={
  "request_id": "38089d77-a0e5-4549-8eb1-93945a27cab0",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "38089d77-a0e5-4549-8eb1-93945a27cab0",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "interview_questions": [
          {
            "agenda": "Mutual introductions and interview overview",
            "questions": [
              "Can you briefly introduce yourself and share what excites you about the field of machine learning?",
              "What motivated you to apply for the Machine Learning Intern position at Rapid Innovation LLC?",
              "How do you see your experience in front-end engineering contributing to a role focused on machine learning and AI?"
            ]
          },
          {
            "agenda": "Discuss understanding of 'Attention is All You Need'",
            "questions": [
              "Can you explain the key concepts of the 'Attention is All You Need' paper and how they apply to transformer models?",
              "How would you implement a transformer model based on the principles outlined in the paper?",
              "What challenges do you anticipate when applying the attention mechanism in real-world NLP tasks?"
            ]
          },
          {
            "agenda": "Explore experience with React and Next.js projects",
            "questions": [
              "Can you describe a project where you used React and Next.js to solve a complex problem?",
              "How do you ensure performance optimization in React and Next.js applications, and how might these skills transfer to optimizing machine learning models?",
              "Given your experience with front-end technologies, how would you approach integrating a machine learning model into a web application?"
            ]
          }
        ],
        "agenda": "Mutual introductions and interview overview",
        "questions": [
          "Can you briefly introduce yourself and share what excites you about the field of machine learning?",
          "What motivated you to apply for the Machine Learning Intern position at Rapid Innovation LLC?",
          "How do you see your experience in front-end engineering contributing to a role focused on machine learning and AI?"
        ]
      }
    }
  },
  "status": "success",
  "timestamp": 1748429420.0217102
}
2025-05-28 16:20:20 - ComponentSystem - INFO - [_send_result:1127] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Sent result for component ApiRequestNode to topic node_results for RequestID=38089d77-a0e5-4549-8eb1-93945a27cab0
2025-05-28 16:20:20 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Successfully committed offset 262 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-261-1748429419.9923577
2025-05-28 16:20:20 - ComponentSystem - INFO - [_process_message:936] [ReqID:38089d77-a0e5-4549-8eb1-93945a27cab0] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=261, TaskID=ApiRequestNode-node-execution-request-0-261-1748429419.9923577
2025-05-28 16:20:24 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=262, TaskID=ApiRequestNode-node-execution-request-0-262-1748429424.064096
2025-05-28 16:20:24 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-262-1748429424.064096, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://interview.rapidinnovation.dev/api/v1/interviews/c2c5817a-223a-4425-8853-eea54620145d",
    "method": "PUT",
    "query_params": null,
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo"
    },
    "body": {
      "value": "{   \"candidate_name\": \"ac\",   \"candidate_email\": \"<EMAIL>\",   \"skill_set\": \"AI\",   \"job_role\": \"dev\",   \"experience_level\": 0,   \"available_from\": \"1748467200\",   \"available_until\": \"1748481600\",   \"interview_duration\": 30,   \"resume_link\": \"https://interview-project-bucket-20250402.s3.amazonaws.com/ca3b37e0-218e-42c2-b74c-579072bbc6a4/shailesh.pdf\",   \"jd_link\": \"https://interview-project-bucket-20250402.s3.amazonaws.com/job_description.pdf\" }"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "0d794a48-6a1e-48e2-bc85-790a69b5d2d5",
  "correlation_id": "1d7e4067-fa4f-411f-bf83-bc606d93790c"
}
2025-05-28 16:20:24 - ComponentSystem - INFO - [_process_message:713] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Executing tool ApiRequestNode for RequestID=0d794a48-6a1e-48e2-bc85-790a69b5d2d5, TaskID=ApiRequestNode-node-execution-request-0-262-1748429424.064096
2025-05-28 16:20:24 - ComponentSystem - INFO - [_process_message:717] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Tool ApiRequestNode executed successfully for RequestID=0d794a48-6a1e-48e2-bc85-790a69b5d2d5, TaskID=ApiRequestNode-node-execution-request-0-262-1748429424.064096
2025-05-28 16:20:24 - ComponentSystem - INFO - [_send_result:1005] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Preparing to send result for component ApiRequestNode, RequestID=0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:24 - ComponentSystem - INFO - [_send_result:1118] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Sending Kafka response: RequestID=0d794a48-6a1e-48e2-bc85-790a69b5d2d5, Response={
  "request_id": "0d794a48-6a1e-48e2-bc85-790a69b5d2d5",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "0d794a48-6a1e-48e2-bc85-790a69b5d2d5",
    "status": "success",
    "response": {
      "result": {
        "status": "upcoming",
        "message": "Interview updated successfully",
        "interview_id": "c2c5817a-223a-4425-8853-eea54620145d",
        "candidate_name": "ac",
        "candidate_email": "<EMAIL>",
        "skill_set": "AI",
        "job_role": "dev",
        "experience_level": "0",
        "available_from": "2025-05-28T21:20:00+00:00",
        "available_until": "2025-05-29T01:20:00+00:00",
        "interview_duration": 30,
        "candidate_suitability": null,
        "is_completed": false,
        "scheduled_by_type": "organization",
        "created_at": "2025-05-28T10:48:07.724089+00:00",
        "updated_at": "2025-05-28T10:50:24.174019+00:00",
        "candidate_profile": [],
        "agenda": null,
        "questions": null
      },
      "status_code": 200,
      "response_headers": {
        "Date": "Wed, 28 May 2025 10:50:24 GMT",
        "Content-Type": "application/json",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Server": "cloudflare",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Cf-Cache-Status": "DYNAMIC",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=t3QLGjmSAZQLoDqeLpgSAS8So4cN99PmlVDdWJyFcomm26BhpaU2VVPVScfNmLnmooLP2Et4gG6iSbC4PZZYxGigFBWnb9enxTc%2FU8wRMzjT7ii5nj8so0dvhSypojuX%2FZHv%2BNrOzHgZ\"}]}",
        "Content-Encoding": "gzip",
        "CF-RAY": "946d30dc1da5a65d-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://interview.rapidinnovation.dev/api/v1/interviews/c2c5817a-223a-4425-8853-eea54620145d",
      "method": "PUT"
    }
  },
  "status": "success",
  "timestamp": 1748429424.6537864
}
2025-05-28 16:20:24 - ComponentSystem - INFO - [_send_result:1127] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Sent result for component ApiRequestNode to topic node_results for RequestID=0d794a48-6a1e-48e2-bc85-790a69b5d2d5
2025-05-28 16:20:25 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Successfully committed offset 263 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-262-1748429424.064096
2025-05-28 16:20:25 - ComponentSystem - INFO - [_process_message:936] [ReqID:0d794a48-6a1e-48e2-bc85-790a69b5d2d5] [CorrID:1d7e4067-fa4f-411f-bf83-bc606d93790c] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=262, TaskID=ApiRequestNode-node-execution-request-0-262-1748429424.064096
2025-05-28 16:24:05 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=263, TaskID=ApiRequestNode-node-execution-request-0-263-1748429645.833965
2025-05-28 16:24:05 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-263-1748429645.833965, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://interview.rapidinnovation.dev/api/v1/interviews/",
    "method": "POST",
    "query_params": null,
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo"
    },
    "body": null,
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "42942d4a-17e8-485b-be1c-b34b66261193",
  "correlation_id": "bc54f0c2-a369-406c-b28d-049f543311b6"
}
2025-05-28 16:24:05 - ComponentSystem - INFO - [_process_message:713] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Executing tool ApiRequestNode for RequestID=42942d4a-17e8-485b-be1c-b34b66261193, TaskID=ApiRequestNode-node-execution-request-0-263-1748429645.833965
2025-05-28 16:24:06 - ComponentSystem - INFO - [_process_message:717] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Tool ApiRequestNode executed successfully for RequestID=42942d4a-17e8-485b-be1c-b34b66261193, TaskID=ApiRequestNode-node-execution-request-0-263-1748429645.833965
2025-05-28 16:24:06 - ComponentSystem - INFO - [_send_result:1005] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Preparing to send result for component ApiRequestNode, RequestID=42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:06 - ComponentSystem - INFO - [_send_result:1030] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Result contains error status or error field for RequestID=42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:06 - ComponentSystem - INFO - [_send_result:1118] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Sending Kafka response: RequestID=42942d4a-17e8-485b-be1c-b34b66261193, Response={
  "request_id": "42942d4a-17e8-485b-be1c-b34b66261193",
  "status": "error",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "42942d4a-17e8-485b-be1c-b34b66261193",
    "response": {
      "result": {
        "detail": [
          {
            "type": "missing",
            "loc": [
              "body"
            ],
            "msg": "Field required",
            "input": null
          }
        ]
      },
      "status_code": 422,
      "response_headers": {},
      "error": "API request failed with status 422 (Unprocessable Entity)"
    }
  }
}
2025-05-28 16:24:06 - ComponentSystem - INFO - [_send_result:1127] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Sent result for component ApiRequestNode to topic node_results for RequestID=42942d4a-17e8-485b-be1c-b34b66261193
2025-05-28 16:24:07 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Successfully committed offset 264 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-263-1748429645.833965
2025-05-28 16:24:07 - ComponentSystem - INFO - [_process_message:936] [ReqID:42942d4a-17e8-485b-be1c-b34b66261193] [CorrID:bc54f0c2-a369-406c-b28d-049f543311b6] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=263, TaskID=ApiRequestNode-node-execution-request-0-263-1748429645.833965
2025-05-28 16:26:30 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=264, TaskID=ApiRequestNode-node-execution-request-0-264-1748429790.8777626
2025-05-28 16:26:30 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-264-1748429790.8777626, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://interview.rapidinnovation.dev/api/v1/interviews/",
    "method": "POST",
    "query_params": null,
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo"
    },
    "body": null,
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3",
  "correlation_id": "18dcffc6-4211-40ed-800e-d5b6facdf3d7"
}
2025-05-28 16:26:30 - ComponentSystem - INFO - [_process_message:713] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Executing tool ApiRequestNode for RequestID=8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3, TaskID=ApiRequestNode-node-execution-request-0-264-1748429790.8777626
2025-05-28 16:26:31 - ComponentSystem - INFO - [_process_message:717] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Tool ApiRequestNode executed successfully for RequestID=8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3, TaskID=ApiRequestNode-node-execution-request-0-264-1748429790.8777626
2025-05-28 16:26:31 - ComponentSystem - INFO - [_send_result:1005] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Preparing to send result for component ApiRequestNode, RequestID=8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:31 - ComponentSystem - INFO - [_send_result:1030] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Result contains error status or error field for RequestID=8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:31 - ComponentSystem - INFO - [_send_result:1118] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Sending Kafka response: RequestID=8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3, Response={
  "request_id": "8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3",
  "status": "error",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3",
    "response": {
      "result": {
        "detail": [
          {
            "type": "missing",
            "loc": [
              "body"
            ],
            "msg": "Field required",
            "input": null
          }
        ]
      },
      "status_code": 422,
      "response_headers": {},
      "error": "API request failed with status 422 (Unprocessable Entity)"
    }
  }
}
2025-05-28 16:26:31 - ComponentSystem - INFO - [_send_result:1127] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Sent result for component ApiRequestNode to topic node_results for RequestID=8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3
2025-05-28 16:26:31 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Successfully committed offset 265 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-264-1748429790.8777626
2025-05-28 16:26:31 - ComponentSystem - INFO - [_process_message:936] [ReqID:8e03efe5-c90e-4cab-a1fc-53b43e1bcfa3] [CorrID:18dcffc6-4211-40ed-800e-d5b6facdf3d7] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=264, TaskID=ApiRequestNode-node-execution-request-0-264-1748429790.8777626
2025-05-28 16:33:45 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=265, TaskID=ApiRequestNode-node-execution-request-0-265-1748430225.4351547
2025-05-28 16:33:45 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-265-1748430225.4351547, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748429970692-5225765898358",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"body\":\"testing\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0",
  "correlation_id": "09354c4a-cd67-4939-852e-b7403f21528d"
}
2025-05-28 16:33:45 - ComponentSystem - INFO - [_process_message:713] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Executing tool ApiRequestNode for RequestID=ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0, TaskID=ApiRequestNode-node-execution-request-0-265-1748430225.4351547
2025-05-28 16:33:45 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=266, TaskID=ApiRequestNode-node-execution-request-0-266-1748430225.4409764
2025-05-28 16:33:45 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-266-1748430225.4409764, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"social\":\"4\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "marketing": "2"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "fbe24603-f02f-4943-a3c4-a9a2c4e9de7c",
  "correlation_id": "09354c4a-cd67-4939-852e-b7403f21528d"
}
2025-05-28 16:33:45 - ComponentSystem - INFO - [_process_message:713] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Executing tool MergeDataComponent for RequestID=fbe24603-f02f-4943-a3c4-a9a2c4e9de7c, TaskID=ApiRequestNode-node-execution-request-0-266-1748430225.4409764
2025-05-28 16:33:45 - ComponentSystem - INFO - [_process_message:717] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Tool MergeDataComponent executed successfully for RequestID=fbe24603-f02f-4943-a3c4-a9a2c4e9de7c, TaskID=ApiRequestNode-node-execution-request-0-266-1748430225.4409764
2025-05-28 16:33:45 - ComponentSystem - INFO - [_send_result:1005] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Preparing to send result for component ApiRequestNode, RequestID=fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:33:45 - ComponentSystem - INFO - [_send_result:1118] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Sending Kafka response: RequestID=fbe24603-f02f-4943-a3c4-a9a2c4e9de7c, Response={
  "request_id": "fbe24603-f02f-4943-a3c4-a9a2c4e9de7c",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "fbe24603-f02f-4943-a3c4-a9a2c4e9de7c",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "value": "{\"social\":\"4\"}",
        "marketing": "2"
      }
    }
  },
  "status": "success",
  "timestamp": 1748430225.533447
}
2025-05-28 16:33:45 - ComponentSystem - INFO - [_send_result:1127] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Sent result for component ApiRequestNode to topic node_results for RequestID=fbe24603-f02f-4943-a3c4-a9a2c4e9de7c
2025-05-28 16:33:46 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Successfully committed offset 267 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-266-1748430225.4409764
2025-05-28 16:33:46 - ComponentSystem - INFO - [_process_message:936] [ReqID:fbe24603-f02f-4943-a3c4-a9a2c4e9de7c] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=266, TaskID=ApiRequestNode-node-execution-request-0-266-1748430225.4409764
2025-05-28 16:33:46 - ComponentSystem - INFO - [_process_message:717] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Tool ApiRequestNode executed successfully for RequestID=ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0, TaskID=ApiRequestNode-node-execution-request-0-265-1748430225.4351547
2025-05-28 16:33:46 - ComponentSystem - INFO - [_send_result:1005] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Preparing to send result for component ApiRequestNode, RequestID=ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:46 - ComponentSystem - INFO - [_send_result:1118] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Sending Kafka response: RequestID=ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0, Response={
  "request_id": "ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0",
    "status": "success",
    "response": {
      "result": "1748430225745-2642260401044",
      "status_code": 200,
      "response_headers": {
        "Date": "Wed, 28 May 2025 11:03:45 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-vt3F1VR/5QKULG4DA2bKbXboGfg\"",
        "X-Response-Time": "0.64749ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=zbRKssYVuDzdx1DoK7kbNCW9IVCNcldxZdT1%2FcZ8E39%2Fxyr6fuxjBrJJzudSMXWcA8VWRgzxJDHUXD%2BE%2B1AWNlOjsQU23Ajs6WWmUxB418f46Ry81aaYAw%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "946d446d3b32e1ae-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748429970692-5225765898358",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748430226.265252
}
2025-05-28 16:33:46 - ComponentSystem - INFO - [_send_result:1127] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Sent result for component ApiRequestNode to topic node_results for RequestID=ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0
2025-05-28 16:33:46 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Successfully committed offset 266 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-265-1748430225.4351547
2025-05-28 16:33:46 - ComponentSystem - INFO - [_process_message:936] [ReqID:ca4894af-f4d1-4f70-8b82-ae0a2ff5ddc0] [CorrID:09354c4a-cd67-4939-852e-b7403f21528d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=265, TaskID=ApiRequestNode-node-execution-request-0-265-1748430225.4351547
2025-05-28 16:44:44 - ComponentSystem - INFO - [stop_all_components:481] Stopping all running components...
2025-05-28 16:44:44 - ComponentSystem - INFO - [stop_component:413] Stopping component: ApiRequestNode
2025-05-28 16:44:44 - ComponentSystem - INFO - [_consume_messages:601] Consumer task for component ApiRequestNode cancelled
2025-05-28 16:44:44 - ComponentSystem - INFO - [_consume_messages:608] Consumer loop finished for component: ApiRequestNode
