# Python virtual environments
.venv
venv
env
__pycache__
*.pyc
*.pyo
*.pyd
.Python
.pytest_cache
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.egg-info/
.eggs/
*.egg

# Git
.git
.gitignore
.gitlab-ci.yml

# Environment files
.env
.env.local
.env.*.local

# IDE files
.idea
.vscode
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# Kubernetes
k8s-manifest-dev.yml
k8s-manifest-prod.yml

# Local development scripts
run_local.sh
run_tests.sh

# Test files
tests/

# Generated proto files (will be regenerated during build)
proto-definitions/
