# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: agent.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'agent.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import field_mask_pb2 as google_dot_protobuf_dot_field__mask__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0b\x61gent.proto\x12\x05\x61gent\x1a google/protobuf/field_mask.proto\"H\n\x05Owner\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x11\n\tfull_name\x18\x03 \x01(\t\x12\x11\n\tfcm_token\x18\x04 \x01(\t\"u\n\x15\x41gentCapabilitiesData\x12\x19\n\x11\x63\x61pabilities_json\x18\x01 \x01(\t\x12\x13\n\x0binput_modes\x18\x02 \x03(\t\x12\x14\n\x0coutput_modes\x18\x03 \x03(\t\x12\x16\n\x0eresponse_model\x18\x04 \x03(\t\"\xc7\x01\n\x11\x41gentVariableData\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x18\n\x0b\x64\x65scription\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x1a\n\rdefault_value\x18\x04 \x01(\tH\x01\x88\x01\x01\x12\x0f\n\x02id\x18\x05 \x01(\tH\x02\x88\x01\x01\x12\x12\n\ncreated_at\x18\x06 \x01(\t\x12\x12\n\nupdated_at\x18\x07 \x01(\tB\x0e\n\x0c_descriptionB\x10\n\x0e_default_valueB\x05\n\x03_id\"\xf4\x05\n\x12\x43reateAgentRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x0e\n\x06\x61vatar\x18\x03 \x01(\t\x12\x1b\n\x05owner\x18\x04 \x01(\x0b\x32\x0c.agent.Owner\x12\x10\n\x08user_ids\x18\x05 \x03(\t\x12$\n\nowner_type\x18\x06 \x01(\x0e\x32\x10.agent.OwnerType\x12\x16\n\x0esystem_message\x18\x07 \x01(\t\x12\x16\n\x0emodel_provider\x18\x08 \x01(\t\x12\x12\n\nmodel_name\x18\t \x01(\t\x12\x15\n\rmodel_api_key\x18\n \x01(\t\x12\x14\n\x0cworkflow_ids\x18\x0b \x03(\t\x12\x16\n\x0emcp_server_ids\x18\x0c \x03(\t\x12\x18\n\x10\x61gent_topic_type\x18\r \x01(\t\x12\x15\n\rsubscriptions\x18\x0e \x01(\t\x12%\n\nvisibility\x18\x0f \x01(\x0e\x32\x11.agent.Visibility\x12\x0c\n\x04tags\x18\x10 \x03(\t\x12\x1d\n\x06status\x18\x11 \x01(\x0e\x32\r.agent.Status\x12\x12\n\ndepartment\x18\x12 \x01(\t\x12\x19\n\x04tone\x18\x13 \x01(\x0e\x32\x0b.agent.Tone\x12\r\n\x05\x66iles\x18\x14 \x03(\t\x12\x0c\n\x04urls\x18\x15 \x03(\t\x12\x17\n\x0fruh_credentials\x18\x16 \x01(\x08\x12\x17\n\x0forganization_id\x18\x17 \x01(\t\x12\x0e\n\x06is_a2a\x18\x18 \x01(\x08\x12\x17\n\x0fis_customizable\x18\x19 \x01(\x08\x12\x38\n\x12\x61gent_capabilities\x18\x1a \x01(\x0b\x32\x1c.agent.AgentCapabilitiesData\x12\x17\n\x0f\x65xample_prompts\x18\x1b \x03(\t\x12!\n\x08\x63\x61tegory\x18\x1c \x01(\x0e\x32\x0f.agent.Category\x12+\n\tvariables\x18\x1d \x03(\x0b\x32\x18.agent.AgentVariableData\"T\n\x13\x43reateAgentResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1b\n\x05\x61gent\x18\x03 \x01(\x0b\x32\x0c.agent.Agent\"\x1d\n\x0fGetAgentRequest\x12\n\n\x02id\x18\x01 \x01(\t\"=\n\x12\x44\x65leteAgentRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\"7\n\x13\x44\x65leteAgentResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\xd9\x03\n\x11ListAgentsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x17\n\ndepartment\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\"\n\x06status\x18\x04 \x01(\x0e\x32\r.agent.StatusH\x01\x88\x01\x01\x12*\n\nvisibility\x18\x05 \x01(\x0e\x32\x11.agent.VisibilityH\x02\x88\x01\x01\x12\x15\n\x08owner_id\x18\x06 \x01(\tH\x03\x88\x01\x01\x12\x1c\n\x0forganization_id\x18\x07 \x01(\tH\x04\x88\x01\x01\x12\x1e\n\x11is_bench_employee\x18\x08 \x01(\x08H\x05\x88\x01\x01\x12\x13\n\x06is_a2a\x18\t \x01(\x08H\x06\x88\x01\x01\x12\x1c\n\x0fis_customizable\x18\n \x01(\x08H\x07\x88\x01\x01\x12&\n\x08\x63\x61tegory\x18\x0b \x01(\x0e\x32\x0f.agent.CategoryH\x08\x88\x01\x01\x42\r\n\x0b_departmentB\t\n\x07_statusB\r\n\x0b_visibilityB\x0b\n\t_owner_idB\x12\n\x10_organization_idB\x14\n\x12_is_bench_employeeB\t\n\x07_is_a2aB\x12\n\x10_is_customizableB\x0b\n\t_category\"\x96\x02\n\x19ListAgentsByUserIdRequest\x12\x10\n\x08owner_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x17\n\ndepartment\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\"\n\x06status\x18\x05 \x01(\x0e\x32\r.agent.StatusH\x01\x88\x01\x01\x12*\n\nvisibility\x18\x06 \x01(\x0e\x32\x11.agent.VisibilityH\x02\x88\x01\x01\x12\x1e\n\x11is_bench_employee\x18\x07 \x01(\x08H\x03\x88\x01\x01\x42\r\n\x0b_departmentB\t\n\x07_statusB\r\n\x0b_visibilityB\x14\n\x12_is_bench_employee\"\xa0\x01\n\x11\x41gentCapabilities\x12\n\n\x02id\x18\x01 \x01(\t\x12\x14\n\x0c\x63\x61pabilities\x18\x02 \x01(\t\x12\x13\n\x0binput_modes\x18\x03 \x03(\t\x12\x14\n\x0coutput_modes\x18\x04 \x03(\t\x12\x16\n\x0eresponse_model\x18\x05 \x03(\t\x12\x12\n\ncreated_at\x18\x06 \x01(\t\x12\x12\n\nupdated_at\x18\x07 \x01(\t\"\xe2\x07\n\x05\x41gent\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0e\n\x06\x61vatar\x18\x04 \x01(\t\x12\x10\n\x08owner_id\x18\x05 \x01(\t\x12\x10\n\x08user_ids\x18\x06 \x03(\t\x12\x12\n\nowner_type\x18\x07 \x01(\t\x12\x18\n\x0btemplate_id\x18\x08 \x01(\tH\x00\x88\x01\x01\x12\x19\n\x11template_owner_id\x18\t \x01(\t\x12\x19\n\x11is_bench_employee\x18\n \x01(\x08\x12\x18\n\x0bis_imported\x18\x0b \x01(\x08H\x01\x88\x01\x01\x12\x16\n\x0e\x61gent_category\x18\x0c \x01(\t\x12\x16\n\x0esystem_message\x18\r \x01(\t\x12\x16\n\x0emodel_provider\x18\x0e \x01(\t\x12\x12\n\nmodel_name\x18\x0f \x01(\t\x12\x15\n\rmodel_api_key\x18\x10 \x01(\t\x12\x14\n\x0cworkflow_ids\x18\x11 \x03(\t\x12\x16\n\x0emcp_server_ids\x18\x12 \x03(\t\x12\x1d\n\x10\x61gent_topic_type\x18\x13 \x01(\tH\x02\x88\x01\x01\x12\x1a\n\rsubscriptions\x18\x14 \x01(\tH\x03\x88\x01\x01\x12\x12\n\nvisibility\x18\x15 \x01(\t\x12\x0c\n\x04tags\x18\x16 \x03(\t\x12\x0e\n\x06status\x18\x17 \x01(\t\x12\x12\n\ncreated_at\x18\x18 \x01(\t\x12\x12\n\nupdated_at\x18\x19 \x01(\t\x12\x12\n\ndepartment\x18\x1a \x01(\t\x12\x0c\n\x04tone\x18\x1b \x01(\t\x12\x17\n\x0fruh_credentials\x18\x1c \x01(\x08\x12\r\n\x05\x66iles\x18\x1d \x03(\t\x12\x0c\n\x04urls\x18\x1e \x03(\t\x12\x1e\n\x16is_changes_marketplace\x18\x1f \x01(\x08\x12\x17\n\x0forganization_id\x18  \x01(\t\x12\x0e\n\x06is_a2a\x18! \x01(\x08\x12\x17\n\x0fis_customizable\x18\" \x01(\x08\x12\x17\n\x0f\x63\x61pabilities_id\x18# \x01(\t\x12\x39\n\x12\x61gent_capabilities\x18$ \x01(\x0b\x32\x18.agent.AgentCapabilitiesH\x04\x88\x01\x01\x12\x17\n\x0f\x65xample_prompts\x18% \x03(\t\x12\x10\n\x08\x63\x61tegory\x18& \x01(\t\x12+\n\tvariables\x18\' \x03(\x0b\x32\x18.agent.AgentVariableDataB\x0e\n\x0c_template_idB\x0e\n\x0c_is_importedB\x13\n\x11_agent_topic_typeB\x10\n\x0e_subscriptionsB\x15\n\x13_agent_capabilities\"N\n\rAgentResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1b\n\x05\x61gent\x18\x03 \x01(\x0b\x32\x0c.agent.Agent\"u\n\x12ListAgentsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x1c\n\x06\x61gents\x18\x02 \x03(\x0b\x32\x0c.agent.Agent\x12\r\n\x05total\x18\x03 \x01(\x05\x12\x0c\n\x04page\x18\x04 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x05 \x01(\x05\"x\n\x1e\x43reateAgentFromTemplateRequest\x12\x13\n\x0btemplate_id\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\x12$\n\nowner_type\x18\x03 \x01(\x0e\x32\x10.agent.OwnerType\"C\n\x1f\x43reateAgentFromTemplateResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\xb1\x05\n\x08Template\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0e\n\x06\x61vatar\x18\x04 \x01(\t\x12\x16\n\x0e\x61gent_category\x18\x05 \x01(\t\x12\x16\n\x0esystem_message\x18\x06 \x01(\t\x12\x16\n\x0emodel_provider\x18\x07 \x01(\t\x12\x12\n\nmodel_name\x18\x08 \x01(\t\x12\x15\n\rmodel_api_key\x18\t \x01(\t\x12\x14\n\x0cworkflow_ids\x18\n \x03(\t\x12\x16\n\x0emcp_server_ids\x18\x0b \x03(\t\x12\x18\n\x10\x61gent_topic_type\x18\x0c \x01(\t\x12\x15\n\rsubscriptions\x18\r \x01(\t\x12\x12\n\ndepartment\x18\x0e \x01(\t\x12\x0c\n\x04tags\x18\x0f \x03(\t\x12\x12\n\ncreated_at\x18\x10 \x01(\t\x12\x12\n\nupdated_at\x18\x11 \x01(\t\x12\x0c\n\x04tone\x18\x12 \x01(\t\x12\r\n\x05\x66iles\x18\x13 \x03(\t\x12\x0c\n\x04urls\x18\x14 \x03(\t\x12\x10\n\x08owner_id\x18\x15 \x01(\t\x12\x11\n\tuse_count\x18\x16 \x01(\x05\x12\x17\n\x0forganization_id\x18\x17 \x01(\t\x12\x0e\n\x06is_a2a\x18\x18 \x01(\x08\x12\x17\n\x0fis_customizable\x18\x19 \x01(\x08\x12\x17\n\x0f\x63\x61pabilities_id\x18\x1a \x01(\t\x12\x17\n\x0f\x65xample_prompts\x18\x1b \x03(\t\x12\x39\n\x12\x61gent_capabilities\x18\x1c \x01(\x0b\x32\x18.agent.AgentCapabilitiesH\x00\x88\x01\x01\x12\x15\n\x08is_added\x18\x1d \x01(\x08H\x01\x88\x01\x01\x12\x10\n\x08\x63\x61tegory\x18\x1e \x01(\tB\x15\n\x13_agent_capabilitiesB\x0b\n\t_is_added\"B\n\x12GetTemplateRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x14\n\x07user_id\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\n\n\x08_user_id\"Z\n\x13GetTemplateResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12!\n\x08template\x18\x03 \x01(\x0b\x32\x0f.agent.Template\"#\n\x15\x44\x65leteTemplateRequest\x12\n\n\x02id\x18\x01 \x01(\t\":\n\x16\x44\x65leteTemplateResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\xb5\x01\n\x14ListTemplatesRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x17\n\ndepartment\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x15\n\x08owner_id\x18\x04 \x01(\tH\x01\x88\x01\x01\x12\x1c\n\x0forganization_id\x18\x05 \x01(\tH\x02\x88\x01\x01\x42\r\n\x0b_departmentB\x0b\n\t_owner_idB\x12\n\x10_organization_id\"m\n\x15ListTemplatesResponse\x12\"\n\ttemplates\x18\x01 \x03(\x0b\x32\x0f.agent.Template\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x04 \x01(\x05\"Q\n\x1cListTemplatesByUserIdRequest\x12\x10\n\x08owner_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\"X\n\x17UpdateAgentPartResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1b\n\x05\x61gent\x18\x03 \x01(\x0b\x32\x0c.agent.Agent\"\x92\x03\n\x1dUpdateAgentCoreDetailsRequest\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\x12/\n\x0bupdate_mask\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12\x0e\n\x06\x61vatar\x18\x06 \x01(\t\x12\x16\n\x0esystem_message\x18\x07 \x01(\t\x12\x16\n\x0emodel_provider\x18\x08 \x01(\t\x12\x12\n\nmodel_name\x18\t \x01(\t\x12\x15\n\rmodel_api_key\x18\n \x01(\t\x12\x12\n\ndepartment\x18\x0b \x01(\t\x12\x19\n\x04tone\x18\x0c \x01(\x0e\x32\x0b.agent.Tone\x12\x17\n\x0fruh_credentials\x18\r \x01(\x08\x12\x18\n\x10\x61gent_topic_type\x18\x0e \x01(\t\x12!\n\x08\x63\x61tegory\x18\x0f \x01(\x0e\x32\x0f.agent.Category\"\x9a\x01\n\x1bUpdateAgentKnowledgeRequest\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\x12/\n\x0bupdate_mask\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\r\n\x05\x66iles\x18\x04 \x03(\t\x12\x0c\n\x04urls\x18\x05 \x03(\t\"e\n\x1cUpdateAgentMcpServersRequest\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\x12\x16\n\x0emcp_server_ids\x18\x03 \x03(\t\"b\n\x1bUpdateAgentWorkflowsRequest\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\x12\x14\n\x0cworkflow_ids\x18\x03 \x03(\t\"M\n\x1cToggleAgentVisibilityRequest\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\"^\n\x1dToggleAgentVisibilityResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1b\n\x05\x61gent\x18\x03 \x01(\x0b\x32\x0c.agent.Agent\"\xe9\x02\n\x1aUpdateAgentSettingsRequest\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\x12/\n\x0bupdate_mask\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\x12\x10\n\x08user_ids\x18\x04 \x03(\t\x12\x18\n\x10\x61gent_topic_type\x18\x05 \x01(\t\x12\x15\n\rsubscriptions\x18\x06 \x01(\t\x12\x0c\n\x04tags\x18\x07 \x03(\t\x12\x1d\n\x06status\x18\x08 \x01(\x0e\x32\r.agent.Status\x12\x1e\n\x16is_changes_marketplace\x18\t \x01(\x08\x12\x19\n\x11is_bench_employee\x18\n \x01(\x08\x12\x0e\n\x06is_a2a\x18\x0b \x01(\x08\x12\x17\n\x0fis_customizable\x18\x0c \x01(\x08\x12\x17\n\x0f\x65xample_prompts\x18\r \x03(\t\"\xd9\x01\n\x1eUpdateAgentCapabilitiesRequest\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\x12\x14\n\x0c\x63\x61pabilities\x18\x03 \x01(\t\x12\x13\n\x0binput_modes\x18\x04 \x03(\t\x12\x14\n\x0coutput_modes\x18\x05 \x03(\t\x12\x16\n\x0eresponse_model\x18\x06 \x03(\t\x12/\n\x0bupdate_mask\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.FieldMask\"\xdc\x05\n\x10MarketplaceAgent\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0e\n\x06\x61vatar\x18\x04 \x01(\t\x12\x16\n\x0e\x61gent_category\x18\x05 \x01(\t\x12\x16\n\x0esystem_message\x18\x06 \x01(\t\x12\x16\n\x0emodel_provider\x18\x07 \x01(\t\x12\x12\n\nmodel_name\x18\x08 \x01(\t\x12\x15\n\rmodel_api_key\x18\t \x01(\t\x12\x14\n\x0cworkflow_ids\x18\n \x03(\t\x12\x16\n\x0emcp_server_ids\x18\x0b \x03(\t\x12\x18\n\x10\x61gent_topic_type\x18\x0c \x01(\t\x12\x15\n\rsubscriptions\x18\r \x01(\t\x12\x12\n\ndepartment\x18\x0e \x01(\t\x12\x0c\n\x04tone\x18\x0f \x01(\t\x12\r\n\x05\x66iles\x18\x10 \x03(\t\x12\x0c\n\x04urls\x18\x11 \x03(\t\x12\x10\n\x08owner_id\x18\x12 \x01(\t\x12\x11\n\tuse_count\x18\x13 \x01(\x05\x12\x0c\n\x04tags\x18\x14 \x03(\t\x12\x0e\n\x06status\x18\x15 \x01(\t\x12\x12\n\ncreated_at\x18\x16 \x01(\t\x12\x12\n\nupdated_at\x18\x17 \x01(\t\x12\x12\n\nvisibility\x18\x18 \x01(\t\x12\x16\n\x0e\x61verage_rating\x18\x19 \x01(\x02\x12\x0e\n\x06is_a2a\x18\x1a \x01(\x08\x12\x17\n\x0fis_customizable\x18\x1b \x01(\x08\x12\x17\n\x0f\x63\x61pabilities_id\x18\x1c \x01(\t\x12\x17\n\x0f\x65xample_prompts\x18\x1d \x03(\t\x12\x39\n\x12\x61gent_capabilities\x18\x1e \x01(\x0b\x32\x18.agent.AgentCapabilitiesH\x00\x88\x01\x01\x12\x15\n\x08is_added\x18\x1f \x01(\x08H\x01\x88\x01\x01\x12\x10\n\x08\x63\x61tegory\x18  \x01(\tB\x15\n\x13_agent_capabilitiesB\x0b\n\t_is_added\"\xff\x01\n\x1bGetMarketplaceAgentsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x13\n\x06search\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x17\n\ndepartment\x18\x04 \x01(\tH\x01\x88\x01\x01\x12&\n\x08\x63\x61tegory\x18\x05 \x01(\x0e\x32\x0f.agent.CategoryH\x02\x88\x01\x01\x12\x0c\n\x04tags\x18\x06 \x03(\t\x12\x14\n\x07sort_by\x18\x07 \x01(\tH\x03\x88\x01\x01\x12\x12\n\nvisibility\x18\x08 \x01(\tB\t\n\x07_searchB\r\n\x0b_departmentB\x0b\n\t_categoryB\n\n\x08_sort_by\"\x9e\x02\n\x1cGetMarketplaceAgentsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\'\n\x06\x61gents\x18\x03 \x03(\x0b\x32\x17.agent.MarketplaceAgent\x12\r\n\x05total\x18\x04 \x01(\x05\x12\x0c\n\x04page\x18\x05 \x01(\x05\x12\x11\n\tpage_size\x18\x06 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x07 \x01(\x05\x12\x10\n\x08has_next\x18\x08 \x01(\x08\x12\x10\n\x08has_prev\x18\t \x01(\x08\x12\x16\n\tnext_page\x18\n \x01(\x05H\x00\x88\x01\x01\x12\x16\n\tprev_page\x18\x0b \x01(\x05H\x01\x88\x01\x01\x42\x0c\n\n_next_pageB\x0c\n\n_prev_page\"P\n GetMarketplaceAgentDetailRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x14\n\x07user_id\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\n\n\x08_user_id\"m\n!GetMarketplaceAgentDetailResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12&\n\x05\x61gent\x18\x03 \x01(\x0b\x32\x17.agent.MarketplaceAgent\"E\n\x10RateAgentRequest\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x0e\n\x06rating\x18\x03 \x01(\x02\"M\n\x11RateAgentResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x16\n\x0e\x61verage_rating\x18\x03 \x01(\x02\"4\n\x0fUseAgentRequest\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"G\n\x10UseAgentResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x11\n\tuse_count\x18\x03 \x01(\x05\"N\n\x0b\x41gentAvatar\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\x12\n\ncreated_at\x18\x03 \x01(\t\x12\x12\n\nupdated_at\x18\x04 \x01(\t\"D\n\x18\x43reateAgentAvatarRequest\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\"a\n\x19\x43reateAgentAvatarResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\"\n\x06\x61vatar\x18\x03 \x01(\x0b\x32\x12.agent.AgentAvatar\"#\n\x15GetAgentAvatarRequest\x12\n\n\x02id\x18\x01 \x01(\t\"^\n\x16GetAgentAvatarResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\"\n\x06\x61vatar\x18\x03 \x01(\x0b\x32\x12.agent.AgentAvatar\":\n\x17ListAgentAvatarsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\"\x93\x01\n\x18ListAgentAvatarsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12#\n\x07\x61vatars\x18\x03 \x03(\x0b\x32\x12.agent.AgentAvatar\x12\r\n\x05total\x18\x04 \x01(\x05\x12\x0c\n\x04page\x18\x05 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x06 \x01(\x05\"C\n\x18\x44\x65leteAgentAvatarRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x1b\n\x05owner\x18\x02 \x01(\x0b\x32\x0c.agent.Owner\"=\n\x19\x44\x65leteAgentAvatarResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t*<\n\rAgentCategory\x12\x0e\n\nUSER_PROXY\x10\x00\x12\r\n\tASSISTANT\x10\x01\x12\x0c\n\x08\x41I_AGENT\x10\x02*\x8a\x01\n\x08\x43\x61tegory\x12\x0f\n\x0b\x45NGINEERING\x10\x00\x12\r\n\tMARKETING\x10\x01\x12\t\n\x05SALES\x10\x02\x12\x14\n\x10\x43USTOMER_SUPPORT\x10\x03\x12\x13\n\x0fHUMAN_RESOURCES\x10\x04\x12\x0b\n\x07\x46INANCE\x10\x05\x12\x0e\n\nOPERATIONS\x10\x06\x12\x0b\n\x07GENERAL\x10\x07*%\n\nVisibility\x12\x0b\n\x07PRIVATE\x10\x00\x12\n\n\x06PUBLIC\x10\x01*\"\n\x06Status\x12\n\n\x06\x41\x43TIVE\x10\x00\x12\x0c\n\x08INACTIVE\x10\x01*3\n\tOwnerType\x12\x08\n\x04USER\x10\x00\x12\x0e\n\nENTERPRISE\x10\x01\x12\x0c\n\x08PLATFORM\x10\x02*P\n\x04Tone\x12\x10\n\x0cPROFESSIONAL\x10\x00\x12\x0c\n\x08\x46RIENDLY\x10\x01\x12\n\n\x06\x43\x41SUAL\x10\x02\x12\n\n\x06\x46ORMAL\x10\x03\x12\x10\n\x0c\x45NTHUSIASTIC\x10\x04*V\n\x17MarketplaceItemSortEnum\x12\n\n\x06NEWEST\x10\x00\x12\n\n\x06OLDEST\x10\x01\x12\x10\n\x0cMOST_POPULAR\x10\x02\x12\x11\n\rHIGHEST_RATED\x10\x03\x32\xcb\x0e\n\x0c\x41gentService\x12\x44\n\x0b\x63reateAgent\x12\x19.agent.CreateAgentRequest\x1a\x1a.agent.CreateAgentResponse\x12\x38\n\x08getAgent\x12\x16.agent.GetAgentRequest\x1a\x14.agent.AgentResponse\x12\x44\n\x0b\x64\x65leteAgent\x12\x19.agent.DeleteAgentRequest\x1a\x1a.agent.DeleteAgentResponse\x12\x41\n\nlistAgents\x12\x18.agent.ListAgentsRequest\x1a\x19.agent.ListAgentsResponse\x12h\n\x17\x63reateAgentFromTemplate\x12%.agent.CreateAgentFromTemplateRequest\x1a&.agent.CreateAgentFromTemplateResponse\x12^\n\x16UpdateAgentCoreDetails\x12$.agent.UpdateAgentCoreDetailsRequest\x1a\x1e.agent.UpdateAgentPartResponse\x12Z\n\x14UpdateAgentKnowledge\x12\".agent.UpdateAgentKnowledgeRequest\x1a\x1e.agent.UpdateAgentPartResponse\x12\\\n\x15UpdateAgentMcpServers\x12#.agent.UpdateAgentMcpServersRequest\x1a\x1e.agent.UpdateAgentPartResponse\x12Z\n\x14UpdateAgentWorkflows\x12\".agent.UpdateAgentWorkflowsRequest\x1a\x1e.agent.UpdateAgentPartResponse\x12X\n\x13UpdateAgentSettings\x12!.agent.UpdateAgentSettingsRequest\x1a\x1e.agent.UpdateAgentPartResponse\x12`\n\x17UpdateAgentCapabilities\x12%.agent.UpdateAgentCapabilitiesRequest\x1a\x1e.agent.UpdateAgentPartResponse\x12\x62\n\x15ToggleAgentVisibility\x12#.agent.ToggleAgentVisibilityRequest\x1a$.agent.ToggleAgentVisibilityResponse\x12\x44\n\x0bgetTemplate\x12\x19.agent.GetTemplateRequest\x1a\x1a.agent.GetTemplateResponse\x12J\n\rlistTemplates\x12\x1b.agent.ListTemplatesRequest\x1a\x1c.agent.ListTemplatesResponse\x12_\n\x14getMarketplaceAgents\x12\".agent.GetMarketplaceAgentsRequest\x1a#.agent.GetMarketplaceAgentsResponse\x12n\n\x19getMarketplaceAgentDetail\x12\'.agent.GetMarketplaceAgentDetailRequest\x1a(.agent.GetMarketplaceAgentDetailResponse\x12>\n\trateAgent\x12\x17.agent.RateAgentRequest\x1a\x18.agent.RateAgentResponse\x12;\n\x08useAgent\x12\x16.agent.UseAgentRequest\x1a\x17.agent.UseAgentResponse\x12V\n\x11\x63reateAgentAvatar\x12\x1f.agent.CreateAgentAvatarRequest\x1a .agent.CreateAgentAvatarResponse\x12M\n\x0egetAgentAvatar\x12\x1c.agent.GetAgentAvatarRequest\x1a\x1d.agent.GetAgentAvatarResponse\x12S\n\x10listAgentAvatars\x12\x1e.agent.ListAgentAvatarsRequest\x1a\x1f.agent.ListAgentAvatarsResponse\x12V\n\x11\x64\x65leteAgentAvatar\x12\x1f.agent.DeleteAgentAvatarRequest\x1a .agent.DeleteAgentAvatarResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'agent_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_AGENTCATEGORY']._serialized_start=9171
  _globals['_AGENTCATEGORY']._serialized_end=9231
  _globals['_CATEGORY']._serialized_start=9234
  _globals['_CATEGORY']._serialized_end=9372
  _globals['_VISIBILITY']._serialized_start=9374
  _globals['_VISIBILITY']._serialized_end=9411
  _globals['_STATUS']._serialized_start=9413
  _globals['_STATUS']._serialized_end=9447
  _globals['_OWNERTYPE']._serialized_start=9449
  _globals['_OWNERTYPE']._serialized_end=9500
  _globals['_TONE']._serialized_start=9502
  _globals['_TONE']._serialized_end=9582
  _globals['_MARKETPLACEITEMSORTENUM']._serialized_start=9584
  _globals['_MARKETPLACEITEMSORTENUM']._serialized_end=9670
  _globals['_OWNER']._serialized_start=56
  _globals['_OWNER']._serialized_end=128
  _globals['_AGENTCAPABILITIESDATA']._serialized_start=130
  _globals['_AGENTCAPABILITIESDATA']._serialized_end=247
  _globals['_AGENTVARIABLEDATA']._serialized_start=250
  _globals['_AGENTVARIABLEDATA']._serialized_end=449
  _globals['_CREATEAGENTREQUEST']._serialized_start=452
  _globals['_CREATEAGENTREQUEST']._serialized_end=1208
  _globals['_CREATEAGENTRESPONSE']._serialized_start=1210
  _globals['_CREATEAGENTRESPONSE']._serialized_end=1294
  _globals['_GETAGENTREQUEST']._serialized_start=1296
  _globals['_GETAGENTREQUEST']._serialized_end=1325
  _globals['_DELETEAGENTREQUEST']._serialized_start=1327
  _globals['_DELETEAGENTREQUEST']._serialized_end=1388
  _globals['_DELETEAGENTRESPONSE']._serialized_start=1390
  _globals['_DELETEAGENTRESPONSE']._serialized_end=1445
  _globals['_LISTAGENTSREQUEST']._serialized_start=1448
  _globals['_LISTAGENTSREQUEST']._serialized_end=1921
  _globals['_LISTAGENTSBYUSERIDREQUEST']._serialized_start=1924
  _globals['_LISTAGENTSBYUSERIDREQUEST']._serialized_end=2202
  _globals['_AGENTCAPABILITIES']._serialized_start=2205
  _globals['_AGENTCAPABILITIES']._serialized_end=2365
  _globals['_AGENT']._serialized_start=2368
  _globals['_AGENT']._serialized_end=3362
  _globals['_AGENTRESPONSE']._serialized_start=3364
  _globals['_AGENTRESPONSE']._serialized_end=3442
  _globals['_LISTAGENTSRESPONSE']._serialized_start=3444
  _globals['_LISTAGENTSRESPONSE']._serialized_end=3561
  _globals['_CREATEAGENTFROMTEMPLATEREQUEST']._serialized_start=3563
  _globals['_CREATEAGENTFROMTEMPLATEREQUEST']._serialized_end=3683
  _globals['_CREATEAGENTFROMTEMPLATERESPONSE']._serialized_start=3685
  _globals['_CREATEAGENTFROMTEMPLATERESPONSE']._serialized_end=3752
  _globals['_TEMPLATE']._serialized_start=3755
  _globals['_TEMPLATE']._serialized_end=4444
  _globals['_GETTEMPLATEREQUEST']._serialized_start=4446
  _globals['_GETTEMPLATEREQUEST']._serialized_end=4512
  _globals['_GETTEMPLATERESPONSE']._serialized_start=4514
  _globals['_GETTEMPLATERESPONSE']._serialized_end=4604
  _globals['_DELETETEMPLATEREQUEST']._serialized_start=4606
  _globals['_DELETETEMPLATEREQUEST']._serialized_end=4641
  _globals['_DELETETEMPLATERESPONSE']._serialized_start=4643
  _globals['_DELETETEMPLATERESPONSE']._serialized_end=4701
  _globals['_LISTTEMPLATESREQUEST']._serialized_start=4704
  _globals['_LISTTEMPLATESREQUEST']._serialized_end=4885
  _globals['_LISTTEMPLATESRESPONSE']._serialized_start=4887
  _globals['_LISTTEMPLATESRESPONSE']._serialized_end=4996
  _globals['_LISTTEMPLATESBYUSERIDREQUEST']._serialized_start=4998
  _globals['_LISTTEMPLATESBYUSERIDREQUEST']._serialized_end=5079
  _globals['_UPDATEAGENTPARTRESPONSE']._serialized_start=5081
  _globals['_UPDATEAGENTPARTRESPONSE']._serialized_end=5169
  _globals['_UPDATEAGENTCOREDETAILSREQUEST']._serialized_start=5172
  _globals['_UPDATEAGENTCOREDETAILSREQUEST']._serialized_end=5574
  _globals['_UPDATEAGENTKNOWLEDGEREQUEST']._serialized_start=5577
  _globals['_UPDATEAGENTKNOWLEDGEREQUEST']._serialized_end=5731
  _globals['_UPDATEAGENTMCPSERVERSREQUEST']._serialized_start=5733
  _globals['_UPDATEAGENTMCPSERVERSREQUEST']._serialized_end=5834
  _globals['_UPDATEAGENTWORKFLOWSREQUEST']._serialized_start=5836
  _globals['_UPDATEAGENTWORKFLOWSREQUEST']._serialized_end=5934
  _globals['_TOGGLEAGENTVISIBILITYREQUEST']._serialized_start=5936
  _globals['_TOGGLEAGENTVISIBILITYREQUEST']._serialized_end=6013
  _globals['_TOGGLEAGENTVISIBILITYRESPONSE']._serialized_start=6015
  _globals['_TOGGLEAGENTVISIBILITYRESPONSE']._serialized_end=6109
  _globals['_UPDATEAGENTSETTINGSREQUEST']._serialized_start=6112
  _globals['_UPDATEAGENTSETTINGSREQUEST']._serialized_end=6473
  _globals['_UPDATEAGENTCAPABILITIESREQUEST']._serialized_start=6476
  _globals['_UPDATEAGENTCAPABILITIESREQUEST']._serialized_end=6693
  _globals['_MARKETPLACEAGENT']._serialized_start=6696
  _globals['_MARKETPLACEAGENT']._serialized_end=7428
  _globals['_GETMARKETPLACEAGENTSREQUEST']._serialized_start=7431
  _globals['_GETMARKETPLACEAGENTSREQUEST']._serialized_end=7686
  _globals['_GETMARKETPLACEAGENTSRESPONSE']._serialized_start=7689
  _globals['_GETMARKETPLACEAGENTSRESPONSE']._serialized_end=7975
  _globals['_GETMARKETPLACEAGENTDETAILREQUEST']._serialized_start=7977
  _globals['_GETMARKETPLACEAGENTDETAILREQUEST']._serialized_end=8057
  _globals['_GETMARKETPLACEAGENTDETAILRESPONSE']._serialized_start=8059
  _globals['_GETMARKETPLACEAGENTDETAILRESPONSE']._serialized_end=8168
  _globals['_RATEAGENTREQUEST']._serialized_start=8170
  _globals['_RATEAGENTREQUEST']._serialized_end=8239
  _globals['_RATEAGENTRESPONSE']._serialized_start=8241
  _globals['_RATEAGENTRESPONSE']._serialized_end=8318
  _globals['_USEAGENTREQUEST']._serialized_start=8320
  _globals['_USEAGENTREQUEST']._serialized_end=8372
  _globals['_USEAGENTRESPONSE']._serialized_start=8374
  _globals['_USEAGENTRESPONSE']._serialized_end=8445
  _globals['_AGENTAVATAR']._serialized_start=8447
  _globals['_AGENTAVATAR']._serialized_end=8525
  _globals['_CREATEAGENTAVATARREQUEST']._serialized_start=8527
  _globals['_CREATEAGENTAVATARREQUEST']._serialized_end=8595
  _globals['_CREATEAGENTAVATARRESPONSE']._serialized_start=8597
  _globals['_CREATEAGENTAVATARRESPONSE']._serialized_end=8694
  _globals['_GETAGENTAVATARREQUEST']._serialized_start=8696
  _globals['_GETAGENTAVATARREQUEST']._serialized_end=8731
  _globals['_GETAGENTAVATARRESPONSE']._serialized_start=8733
  _globals['_GETAGENTAVATARRESPONSE']._serialized_end=8827
  _globals['_LISTAGENTAVATARSREQUEST']._serialized_start=8829
  _globals['_LISTAGENTAVATARSREQUEST']._serialized_end=8887
  _globals['_LISTAGENTAVATARSRESPONSE']._serialized_start=8890
  _globals['_LISTAGENTAVATARSRESPONSE']._serialized_end=9037
  _globals['_DELETEAGENTAVATARREQUEST']._serialized_start=9039
  _globals['_DELETEAGENTAVATARREQUEST']._serialized_end=9106
  _globals['_DELETEAGENTAVATARRESPONSE']._serialized_start=9108
  _globals['_DELETEAGENTAVATARRESPONSE']._serialized_end=9169
  _globals['_AGENTSERVICE']._serialized_start=9673
  _globals['_AGENTSERVICE']._serialized_end=11540
# @@protoc_insertion_point(module_scope)
