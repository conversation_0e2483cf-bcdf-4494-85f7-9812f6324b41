import axios, { AxiosError, AxiosRequestConfig } from "axios";
import { getAccessToken, getRefreshToken, clearAuthCookies } from "./authCookies";
import { loginRoute } from "@/shared/routes";
import { useUserStore } from "@/hooks/use-user";

// Import the auth API dynamically to avoid circular dependency
const getAuthApi = async () => {
  const { authApi } = await import("@/app/api/auth");
  return authApi;
};

// Helper function to clear user store
const clearUserStore = () => {
  useUserStore.getState().clearUser();
};

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
});

// Request interceptor to add authorization header
api.interceptors.request.use(
  async (config) => {
    const token = await getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    config.headers["ngrok-skip-browser-warning"] = "true";
    return config;
  },
  (error: any) => {
    return Promise.reject(
      new Error(`Request interceptor error: ${error.message || "Unknown error"}`),
    );
  },
);

// Response interceptor for handling token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & {
      _retry?: boolean;
    };

    // Check if the error is due to an expired token (401 Unauthorized or 403 Forbidden)
    if (
      (error.response?.status === 401 || error.response?.status === 403) &&
      !originalRequest._retry
    ) {
      // Mark this request as retried to prevent infinite loops
      originalRequest._retry = true;

      try {
        // Call the refresh token endpoint
        // This endpoint will use the HTTP-only refresh token cookie automatically
        const response = await axios.post("/api/auth/refresh");

        if (response.data.success && response.data.accessToken) {
          // Update the authorization header with the new token
          originalRequest.headers = {
            ...originalRequest.headers,
            Authorization: `Bearer ${response.data.accessToken}`,
          };

          // Retry the original request with the new token
          return api(originalRequest);
        } else {
          // Token refresh failed
          await clearAuthCookies();
          clearUserStore();
          window.location.href = loginRoute;
          return Promise.reject(new Error("Token refresh failed"));
        }
      } catch (refreshError) {
        // Clear cookies and redirect to login on refresh error
        await clearAuthCookies();
        clearUserStore();
        window.location.href = loginRoute;
        return Promise.reject(refreshError);
      }
    }

    // For other errors, just reject the promise
    return Promise.reject(error);
  },
);

export default api;
