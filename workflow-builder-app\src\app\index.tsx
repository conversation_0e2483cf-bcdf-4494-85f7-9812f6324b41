"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function IndexPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the workflows page using the workflowsRoute constant for consistency
    router.push("/workflows");
  }, [router]);

  return (
    <div className="flex h-screen items-center justify-center">
      <div className="animate-pulse">Redirecting to workflows...</div>
    </div>
  );
}
